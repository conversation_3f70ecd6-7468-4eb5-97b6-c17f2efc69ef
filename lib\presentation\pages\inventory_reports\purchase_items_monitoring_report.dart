import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة مراقبة أصناف المشتريات
/// يعرض تحليل ومراقبة أصناف المشتريات
class PurchaseItemsMonitoringReportPage extends StatefulWidget {
  const PurchaseItemsMonitoringReportPage({super.key});

  @override
  State<PurchaseItemsMonitoringReportPage> createState() => _PurchaseItemsMonitoringReportPageState();
}

class _PurchaseItemsMonitoringReportPageState extends State<PurchaseItemsMonitoringReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedSupplier;
  String? _selectedCategory;
  String? _monitoringType = 'all';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('مراقبة أصناف المشتريات'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: _setAlerts,
            tooltip: 'تعيين تنبيهات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.green[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.fromDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, true),
                        controller: TextEditingController(
                          text: _startDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.toDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, false),
                        controller: TextEditingController(
                          text: _endDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'المورد',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedSupplier,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الموردين')),
                          DropdownMenuItem(value: 'supplier1', child: Text('شركة التقنية المتقدمة')),
                          DropdownMenuItem(value: 'supplier2', child: Text('مؤسسة الجودة العالمية')),
                          DropdownMenuItem(value: 'supplier3', child: Text('شركة الإمداد الشامل')),
                        ],
                        onChanged: (value) => setState(() => _selectedSupplier = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.category,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedCategory,
                        items: [
                          DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                          DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                          DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                          DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                        ],
                        onChanged: (value) => setState(() => _selectedCategory = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'نوع المراقبة',
                          border: OutlineInputBorder(),
                        ),
                        value: _monitoringType,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الأنواع')),
                          DropdownMenuItem(value: 'frequent', child: Text('الأصناف الأكثر شراءً')),
                          DropdownMenuItem(value: 'expensive', child: Text('الأصناف الأعلى تكلفة')),
                          DropdownMenuItem(value: 'new', child: Text('الأصناف الجديدة')),
                          DropdownMenuItem(value: 'discontinued', child: Text('الأصناف المتوقفة')),
                        ],
                        onChanged: (value) => setState(() => _monitoringType = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _generateReport,
                        icon: const Icon(Icons.search),
                        label: Text(localizations.generateReport),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          minimumSize: const Size(double.infinity, 48),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص مراقبة المشتريات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.monitor, color: Colors.green, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'ملخص مراقبة المشتريات',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSummaryCard('إجمالي الأصناف', '1,245', Colors.blue, Icons.inventory),
                              _buildSummaryCard('أصناف نشطة', '985', Colors.green, Icons.trending_up),
                              _buildSummaryCard('أصناف متوقفة', '260', Colors.red, Icons.trending_down),
                              _buildSummaryCard('أصناف جديدة', '85', Colors.orange, Icons.new_releases),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // الأصناف الأكثر شراءً
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'الأصناف الأكثر شراءً',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ..._buildTopPurchasedItemsList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول تفاصيل مراقبة الأصناف
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل مراقبة الأصناف',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('كود الصنف')),
                                DataColumn(label: Text('اسم الصنف')),
                                DataColumn(label: Text('المورد')),
                                DataColumn(label: Text('عدد المشتريات')),
                                DataColumn(label: Text('إجمالي الكمية')),
                                DataColumn(label: Text('إجمالي القيمة')),
                                DataColumn(label: Text('متوسط السعر')),
                                DataColumn(label: Text('آخر شراء')),
                                DataColumn(label: Text('الحالة')),
                              ],
                              rows: _buildMonitoringRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // تحليل اتجاهات الشراء
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.trending_up, color: Colors.blue, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'تحليل اتجاهات الشراء',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildTrendCard('اتجاه صاعد', '285 صنف', '+15%', Colors.green, Icons.arrow_upward),
                              _buildTrendCard('اتجاه ثابت', '650 صنف', '0%', Colors.blue, Icons.trending_flat),
                              _buildTrendCard('اتجاه هابط', '310 صنف', '-8%', Colors.red, Icons.arrow_downward),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // تنبيهات المراقبة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.warning, color: Colors.orange, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'تنبيهات المراقبة',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          ..._buildAlertsList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _createPurchaseOrder,
                                  icon: const Icon(Icons.shopping_cart),
                                  label: const Text('إنشاء أمر شراء'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _updatePrices,
                                  icon: const Icon(Icons.price_change),
                                  label: const Text('تحديث الأسعار'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _setReorderLevels,
                                  icon: const Icon(Icons.inventory_2),
                                  label: const Text('تعيين حدود الطلب'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _analyzeSuppliers,
                                  icon: const Icon(Icons.analytics),
                                  label: const Text('تحليل الموردين'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildTopPurchasedItemsList() {
    final items = [
      {'item': 'لابتوب ديل XPS 13', 'supplier': 'شركة التقنية المتقدمة', 'purchases': '125', 'value': '312,500 ر.س'},
      {'item': 'طابعة HP LaserJet', 'supplier': 'مؤسسة الجودة العالمية', 'purchases': '85', 'value': '255,000 ر.س'},
      {'item': 'هاتف آيفون 15', 'supplier': 'شركة الإمداد الشامل', 'purchases': '65', 'value': '195,000 ر.س'},
      {'item': 'ساعة ذكية', 'supplier': 'مؤسسة التوريد الشاملة', 'purchases': '95', 'value': '142,500 ر.س'},
    ];

    return items.map((item) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.green.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.green.withOpacity(0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.trending_up, color: Colors.green, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item['item']!,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${item['supplier']} • ${item['purchases']} مشتريات',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Text(
            item['value']!,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<Widget> _buildAlertsList() {
    final alerts = [
      {'type': 'تحذير', 'message': 'انخفاض في شراء لابتوب ديل بنسبة 25%', 'severity': 'medium'},
      {'type': 'تنبيه', 'message': 'ارتفاع أسعار الطابعات بنسبة 15%', 'severity': 'low'},
      {'type': 'خطر', 'message': 'توقف شراء الهواتف الذكية لمدة شهر', 'severity': 'high'},
      {'type': 'معلومة', 'message': 'إضافة 15 صنف جديد هذا الشهر', 'severity': 'info'},
    ];

    return alerts.map((alert) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: _getAlertColor(alert['severity']!).withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: _getAlertColor(alert['severity']!).withOpacity(0.05),
      ),
      child: Row(
        children: [
          Icon(_getAlertIcon(alert['severity']!), color: _getAlertColor(alert['severity']!), size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  alert['type']!,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _getAlertColor(alert['severity']!),
                  ),
                ),
                Text(
                  alert['message']!,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildMonitoringRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('ITM-001')),
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('شركة التقنية المتقدمة')),
        const DataCell(Text('125')),
        const DataCell(Text('125')),
        const DataCell(Text('312,500 ر.س')),
        const DataCell(Text('2,500 ر.س')),
        const DataCell(Text('2024-02-15')),
        DataCell(_buildStatusBadge('نشط', Colors.green)),
      ]),
      DataRow(cells: [
        const DataCell(Text('ITM-002')),
        const DataCell(Text('طابعة HP LaserJet')),
        const DataCell(Text('مؤسسة الجودة العالمية')),
        const DataCell(Text('85')),
        const DataCell(Text('85')),
        const DataCell(Text('255,000 ر.س')),
        const DataCell(Text('3,000 ر.س')),
        const DataCell(Text('2024-02-10')),
        DataCell(_buildStatusBadge('نشط', Colors.green)),
      ]),
      DataRow(cells: [
        const DataCell(Text('ITM-003')),
        const DataCell(Text('هاتف قديم')),
        const DataCell(Text('مورد سابق')),
        const DataCell(Text('0')),
        const DataCell(Text('0')),
        const DataCell(Text('0 ر.س')),
        const DataCell(Text('0 ر.س')),
        const DataCell(Text('2023-12-01')),
        DataCell(_buildStatusBadge('متوقف', Colors.red)),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTrendCard(String title, String count, String percentage, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                count,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                percentage,
                style: TextStyle(
                  fontSize: 12,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        status,
        style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold),
      ),
    );
  }

  Color _getAlertColor(String severity) {
    switch (severity) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.yellow;
      case 'info':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  IconData _getAlertIcon(String severity) {
    switch (severity) {
      case 'high':
        return Icons.error;
      case 'medium':
        return Icons.warning;
      case 'low':
        return Icons.info;
      case 'info':
        return Icons.info_outline;
      default:
        return Icons.help;
    }
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير مراقبة أصناف المشتريات بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _setAlerts() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تعيين تنبيهات المراقبة')),
    );
  }

  void _createPurchaseOrder() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء أمر شراء جديد')),
    );
  }

  void _updatePrices() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحديث أسعار الأصناف')),
    );
  }

  void _setReorderLevels() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تعيين حدود إعادة الطلب')),
    );
  }

  void _analyzeSuppliers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحليل أداء الموردين')),
    );
  }
}
