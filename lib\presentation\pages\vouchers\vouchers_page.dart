import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة السندات الرئيسية
/// تعرض ملخص السندات والعمليات السريعة
class VouchersPage extends StatelessWidget {
  const VouchersPage({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.vouchers),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقات الإحصائيات السريعة
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    title: localizations.vouchersToday,
                    value: '15',
                    icon: Icons.today,
                    color: Colors.blue,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    title: localizations.totalAmount,
                    value: '45,750 ر.س',
                    icon: Icons.attach_money,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    title: localizations.receiptVouchers,
                    value: '8',
                    icon: Icons.receipt_long,
                    color: Colors.teal,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    title: localizations.paymentVouchers,
                    value: '7',
                    icon: Icons.payments,
                    color: Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // قائمة العمليات السريعة
            Text(
              localizations.vouchersManagement,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  _buildActionCard(
                    title: localizations.journalEntry,
                    icon: Icons.book,
                    color: Colors.indigo,
                    onTap: () => _navigateToPage(context, 'journal_entry'),
                  ),
                  _buildActionCard(
                    title: localizations.receiptVoucher,
                    icon: Icons.receipt_long,
                    color: Colors.teal,
                    onTap: () => _navigateToPage(context, 'receipt_voucher'),
                  ),
                  _buildActionCard(
                    title: localizations.paymentVoucher,
                    icon: Icons.payments,
                    color: Colors.orange,
                    onTap: () => _navigateToPage(context, 'payment_voucher'),
                  ),
                  _buildActionCard(
                    title: localizations.vouchersReport,
                    icon: Icons.assessment,
                    color: Colors.purple,
                    onTap: () => _navigateToPage(context, 'vouchers_report'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const Spacer(),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة عملية
  Widget _buildActionCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 40,
                color: color,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// التنقل إلى صفحة معينة
  void _navigateToPage(BuildContext context, String page) {
    final localizations = AppLocalizations.of(context);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('${localizations.navigateTo} $page')),
    );
  }
}
