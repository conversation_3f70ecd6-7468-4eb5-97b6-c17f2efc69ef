import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة كشف الحساب الرئيسي
/// تعرض كشف حساب رئيسي مع التفاصيل
class MainAccountStatementPage extends StatefulWidget {
  const MainAccountStatementPage({super.key});

  @override
  State<MainAccountStatementPage> createState() => _MainAccountStatementPageState();
}

class _MainAccountStatementPageState extends State<MainAccountStatementPage> {
  String _selectedMainAccount = 'assets';

  // بيانات تجريبية للحسابات الرئيسية
  final Map<String, Map<String, dynamic>> _mainAccounts = {
    'assets': {
      'name': 'الأصول',
      'code': '1000',
      'totalBalance': 395000.0,
      'subAccounts': [
        {'code': '1100', 'name': 'الأصول المتداولة', 'balance': 265000.0},
        {'code': '1200', 'name': 'الأصول الثابتة', 'balance': 130000.0},
      ],
    },
    'liabilities': {
      'name': 'الخصوم',
      'code': '2000',
      'totalBalance': 160000.0,
      'subAccounts': [
        {'code': '2100', 'name': 'الخصوم المتداولة', 'balance': 48000.0},
        {'code': '2200', 'name': 'الخصوم طويلة الأجل', 'balance': 112000.0},
      ],
    },
    'equity': {
      'name': 'حقوق الملكية',
      'code': '3000',
      'totalBalance': 435000.0,
      'subAccounts': [
        {'code': '3100', 'name': 'رأس المال', 'balance': 300000.0},
        {'code': '3200', 'name': 'الأرباح المحتجزة', 'balance': 135000.0},
      ],
    },
    'revenues': {
      'name': 'الإيرادات',
      'code': '4000',
      'totalBalance': 225000.0,
      'subAccounts': [
        {'code': '4100', 'name': 'إيرادات المبيعات', 'balance': 200000.0},
        {'code': '4200', 'name': 'إيرادات أخرى', 'balance': 25000.0},
      ],
    },
    'expenses': {
      'name': 'المصروفات',
      'code': '5000',
      'totalBalance': 146000.0,
      'subAccounts': [
        {'code': '5100', 'name': 'مصروفات التشغيل', 'balance': 120000.0},
        {'code': '5200', 'name': 'مصروفات أخرى', 'balance': 26000.0},
      ],
    },
  };

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final currentAccount = _mainAccounts[_selectedMainAccount]!;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('كشف الحساب الرئيسي'),
        backgroundColor: Colors.brown,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedMainAccount,
                    decoration: const InputDecoration(
                      labelText: 'الحساب الرئيسي',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'assets', child: Text('الأصول')),
                      DropdownMenuItem(value: 'liabilities', child: Text('الخصوم')),
                      DropdownMenuItem(value: 'equity', child: Text('حقوق الملكية')),
                      DropdownMenuItem(value: 'revenues', child: Text('الإيرادات')),
                      DropdownMenuItem(value: 'expenses', child: Text('المصروفات')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedMainAccount = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
          ),

          // معلومات الحساب الرئيسي
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              color: _getAccountColor(_selectedMainAccount),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      '${currentAccount['code']} - ${currentAccount['name']}',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'الرصيد الإجمالي: ${currentAccount['totalBalance'].toStringAsFixed(2)} ر.س',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'عدد الحسابات الفرعية: ${currentAccount['subAccounts'].length}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                _buildStatCard('الرصيد الإجمالي', '${currentAccount['totalBalance']} ر.س', Colors.blue),
                _buildStatCard('الحسابات الفرعية', currentAccount['subAccounts'].length.toString(), Colors.green),
                _buildStatCard('متوسط الرصيد', '${_getAverageBalance(currentAccount)} ر.س', Colors.orange),
                _buildStatCard('أكبر رصيد', '${_getMaxBalance(currentAccount)} ر.س', Colors.purple),
              ],
            ),
          ),

          // الحسابات الفرعية
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: currentAccount['subAccounts'].length,
              itemBuilder: (context, index) {
                final subAccount = currentAccount['subAccounts'][index];
                double percentage = (subAccount['balance'] / currentAccount['totalBalance']) * 100;
                
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: _getAccountColor(_selectedMainAccount),
                      child: Text(
                        subAccount['code'].substring(0, 2),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    title: Text(
                      '${subAccount['code']} - ${subAccount['name']}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('الرصيد: ${subAccount['balance'].toStringAsFixed(2)} ر.س'),
                        Text('النسبة: ${percentage.toStringAsFixed(1)}%'),
                        const SizedBox(height: 4),
                        LinearProgressIndicator(
                          value: percentage / 100,
                          backgroundColor: Colors.grey[300],
                          valueColor: AlwaysStoppedAnimation<Color>(
                            _getAccountColor(_selectedMainAccount),
                          ),
                        ),
                      ],
                    ),
                    trailing: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '${percentage.toStringAsFixed(1)}%',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _getAccountColor(_selectedMainAccount),
                          ),
                        ),
                        Text(
                          '${subAccount['balance'].toStringAsFixed(0)} ر.س',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                    isThreeLine: true,
                    onTap: () => _showSubAccountDetails(subAccount),
                  ),
                );
              },
            ),
          ),

          // ملخص الحساب
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: _getAccountColor(_selectedMainAccount).withValues(alpha: 0.1),
              border: Border(top: BorderSide(color: _getAccountColor(_selectedMainAccount))),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Text(
                  'إجمالي الرصيد: ${currentAccount['totalBalance'].toStringAsFixed(2)} ر.س',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _getAccountColor(_selectedMainAccount),
                  ),
                ),
                Text(
                  'عدد الحسابات: ${currentAccount['subAccounts'].length}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _getAccountColor(_selectedMainAccount),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshData,
        backgroundColor: Colors.brown,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث البيانات'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getAccountColor(String accountType) {
    switch (accountType) {
      case 'assets':
        return Colors.green;
      case 'liabilities':
        return Colors.red;
      case 'equity':
        return Colors.blue;
      case 'revenues':
        return Colors.purple;
      case 'expenses':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  String _getAverageBalance(Map<String, dynamic> account) {
    List subAccounts = account['subAccounts'];
    if (subAccounts.isEmpty) return '0.00';
    double total = subAccounts.fold(0.0, (sum, sub) => sum + sub['balance']);
    return (total / subAccounts.length).toStringAsFixed(2);
  }

  String _getMaxBalance(Map<String, dynamic> account) {
    List subAccounts = account['subAccounts'];
    if (subAccounts.isEmpty) return '0.00';
    double max = subAccounts.map((sub) => sub['balance'] as double).reduce((a, b) => a > b ? a : b);
    return max.toStringAsFixed(2);
  }

  void _showSubAccountDetails(Map<String, dynamic> subAccount) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(subAccount['name']),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('رقم الحساب: ${subAccount['code']}'),
            Text('الرصيد: ${subAccount['balance'].toStringAsFixed(2)} ر.س'),
            Text('النوع: حساب فرعي'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('عرض تفاصيل ${subAccount['name']}')),
              );
            },
            child: const Text('عرض التفاصيل'),
          ),
        ],
      ),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة كشف الحساب الرئيسي')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير كشف الحساب الرئيسي')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث بيانات الحساب الرئيسي'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
