import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة المنشآت
/// تتيح إدارة المنشآت والمرافق التابعة للشركة
class FacilitiesPage extends StatefulWidget {
  const FacilitiesPage({super.key});

  @override
  State<FacilitiesPage> createState() => _FacilitiesPageState();
}

class _FacilitiesPageState extends State<FacilitiesPage> {
  String _searchQuery = '';
  String _selectedType = 'all';

  // بيانات تجريبية للمنشآت
  final List<Map<String, dynamic>> _facilities = [
    {
      'id': 'FAC001',
      'name': 'المقر الرئيسي',
      'code': 'HQ-001',
      'type': 'مكتب إداري',
      'address': 'الرياض - حي الملك فهد - شارع الملك عبدالعزيز',
      'area': 2500.0,
      'capacity': 150,
      'currentOccupancy': 120,
      'isActive': true,
      'manager': 'أحمد محمد',
      'phone': '+966112345678',
      'facilities': ['مواقف سيارات', 'مطعم', 'قاعة اجتماعات'],
      'establishedDate': '2020-01-15',
    },
    {
      'id': 'FAC002',
      'name': 'مستودع جدة',
      'code': 'WH-JED',
      'type': 'مستودع',
      'address': 'جدة - المنطقة الصناعية الثانية',
      'area': 5000.0,
      'capacity': 50,
      'currentOccupancy': 35,
      'isActive': true,
      'manager': 'فاطمة أحمد',
      'phone': '+966126789012',
      'facilities': ['رافعة شوكية', 'نظام أمان', 'تبريد'],
      'establishedDate': '2021-03-20',
    },
    {
      'id': 'FAC003',
      'name': 'فرع الدمام',
      'code': 'BR-DAM',
      'type': 'فرع تجاري',
      'address': 'الدمام - حي الفيصلية - طريق الملك فهد',
      'area': 800.0,
      'capacity': 25,
      'currentOccupancy': 22,
      'isActive': true,
      'manager': 'محمد علي',
      'phone': '+966138901234',
      'facilities': ['صالة عرض', 'مكاتب إدارية', 'مواقف'],
      'establishedDate': '2022-06-10',
    },
    {
      'id': 'FAC004',
      'name': 'مركز التدريب',
      'code': 'TRN-001',
      'type': 'مركز تدريب',
      'address': 'الرياض - حي النخيل - شارع الأمير سلطان',
      'area': 1200.0,
      'capacity': 80,
      'currentOccupancy': 0,
      'isActive': false,
      'manager': 'سارة خالد',
      'phone': '+966114567890',
      'facilities': ['قاعات تدريب', 'معمل حاسوب', 'كافتيريا'],
      'establishedDate': '2023-01-05',
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('المنشآت'),
        backgroundColor: Colors.lightGreen,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addFacility,
            tooltip: 'إضافة منشأة جديدة',
          ),
          IconButton(
            icon: const Icon(Icons.map),
            onPressed: _showFacilitiesMap,
            tooltip: 'خريطة المنشآت',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                TextField(
                  decoration: const InputDecoration(
                    hintText: 'البحث في المنشآت...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedType,
                        decoration: const InputDecoration(
                          labelText: 'نوع المنشأة',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الأنواع')),
                          DropdownMenuItem(value: 'مكتب إداري', child: Text('مكتب إداري')),
                          DropdownMenuItem(value: 'مستودع', child: Text('مستودع')),
                          DropdownMenuItem(value: 'فرع تجاري', child: Text('فرع تجاري')),
                          DropdownMenuItem(value: 'مركز تدريب', child: Text('مركز تدريب')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedType = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                _buildStatCard('المجموع', _facilities.length.toString(), Colors.blue),
                _buildStatCard('النشطة', _facilities.where((f) => f['isActive']).length.toString(), Colors.green),
                _buildStatCard('معطلة', _facilities.where((f) => !f['isActive']).length.toString(), Colors.red),
                _buildStatCard('الإشغال', '${_getOccupancyRate()}%', Colors.orange),
              ],
            ),
          ),

          // قائمة المنشآت
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _facilities.length,
              itemBuilder: (context, index) {
                final facility = _facilities[index];
                double occupancyRate = (facility['currentOccupancy'] / facility['capacity']) * 100;
                
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: facility['isActive'] ? Colors.lightGreen : Colors.grey,
                      child: Icon(
                        _getFacilityIcon(facility['type']),
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    title: Text(
                      facility['name'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('النوع: ${facility['type']} | الكود: ${facility['code']}'),
                        Text('المدير: ${facility['manager']}'),
                        Row(
                          children: [
                            Text('الإشغال: ${occupancyRate.toStringAsFixed(1)}%'),
                            const SizedBox(width: 8),
                            Expanded(
                              child: LinearProgressIndicator(
                                value: occupancyRate / 100,
                                backgroundColor: Colors.grey[300],
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  occupancyRate > 90 ? Colors.red : 
                                  occupancyRate > 70 ? Colors.orange : Colors.green,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    trailing: PopupMenuButton<String>(
                      onSelected: (value) => _handleAction(value, facility),
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'edit',
                          child: ListTile(
                            leading: Icon(Icons.edit),
                            title: Text('تعديل'),
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'toggle',
                          child: ListTile(
                            leading: Icon(Icons.toggle_on),
                            title: Text('تغيير الحالة'),
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'location',
                          child: ListTile(
                            leading: Icon(Icons.location_on),
                            title: Text('عرض الموقع'),
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'report',
                          child: ListTile(
                            leading: Icon(Icons.assessment),
                            title: Text('تقرير المنشأة'),
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: ListTile(
                            leading: Icon(Icons.delete, color: Colors.red),
                            title: Text('حذف', style: TextStyle(color: Colors.red)),
                          ),
                        ),
                      ],
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Icon(Icons.location_on, size: 16),
                                const SizedBox(width: 8),
                                Expanded(child: Text('العنوان: ${facility['address']}')),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                const Icon(Icons.square_foot, size: 16),
                                const SizedBox(width: 8),
                                Text('المساحة: ${facility['area']} متر مربع'),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                const Icon(Icons.people, size: 16),
                                const SizedBox(width: 8),
                                Text('السعة: ${facility['capacity']} شخص'),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                const Icon(Icons.phone, size: 16),
                                const SizedBox(width: 8),
                                Text('الهاتف: ${facility['phone']}'),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                const Icon(Icons.date_range, size: 16),
                                const SizedBox(width: 8),
                                Text('تاريخ التأسيس: ${facility['establishedDate']}'),
                              ],
                            ),
                            const SizedBox(height: 8),
                            const Text('المرافق المتاحة:', style: TextStyle(fontWeight: FontWeight.bold)),
                            Wrap(
                              spacing: 8,
                              children: facility['facilities'].map<Widget>((facility) {
                                return Chip(
                                  label: Text(facility),
                                  backgroundColor: Colors.lightGreen[100],
                                );
                              }).toList(),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addFacility,
        backgroundColor: Colors.lightGreen,
        icon: const Icon(Icons.add),
        label: const Text('إضافة منشأة'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getFacilityIcon(String type) {
    switch (type) {
      case 'مكتب إداري':
        return Icons.business;
      case 'مستودع':
        return Icons.warehouse;
      case 'فرع تجاري':
        return Icons.store;
      case 'مركز تدريب':
        return Icons.school;
      default:
        return Icons.location_city;
    }
  }

  String _getOccupancyRate() {
    int totalCapacity = _facilities.fold(0, (sum, f) => sum + (f['capacity'] as int));
    int totalOccupancy = _facilities.fold(0, (sum, f) => sum + (f['currentOccupancy'] as int));
    return totalCapacity > 0 ? ((totalOccupancy / totalCapacity) * 100).toStringAsFixed(1) : '0';
  }

  void _addFacility() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إضافة منشأة جديدة')),
    );
  }

  void _showFacilitiesMap() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض خريطة المنشآت')),
    );
  }

  void _handleAction(String action, Map<String, dynamic> facility) {
    switch (action) {
      case 'edit':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تعديل المنشأة ${facility['name']}')),
        );
        break;
      case 'toggle':
        setState(() {
          facility['isActive'] = !facility['isActive'];
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم ${facility['isActive'] ? 'تفعيل' : 'تعطيل'} المنشأة'),
          ),
        );
        break;
      case 'location':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('عرض موقع ${facility['name']}')),
        );
        break;
      case 'report':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تقرير المنشأة ${facility['name']}')),
        );
        break;
      case 'delete':
        _showDeleteConfirmation(facility);
        break;
    }
  }

  void _showDeleteConfirmation(Map<String, dynamic> facility) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المنشأة ${facility['name']}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _facilities.removeWhere((f) => f['id'] == facility['id']);
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف المنشأة ${facility['name']}'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
