import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة نقل أرصدة المخزون
/// تتيح نقل أرصدة المخزون بين المواقع والفروع المختلفة
class TransferStockBalancesPage extends StatefulWidget {
  const TransferStockBalancesPage({super.key});

  @override
  State<TransferStockBalancesPage> createState() =>
      _TransferStockBalancesPageState();
}

class _TransferStockBalancesPageState extends State<TransferStockBalancesPage> {
  final _formKey = GlobalKey<FormState>();
  final _quantityController = TextEditingController();
  final _costController = TextEditingController();
  final _notesController = TextEditingController();

  String? _selectedCategory;
  String? _selectedItem;
  String? _fromLocation;
  String? _toLocation;
  DateTime _transferDate = DateTime.now();
  bool _isProcessing = false;

  final List<Map<String, String>> _categories = [
    {'id': '1', 'name': 'الإلكترونيات'},
    {'id': '2', 'name': 'الأثاث المكتبي'},
    {'id': '3', 'name': 'القرطاسية'},
    {'id': '4', 'name': 'المعدات'},
  ];

  final List<Map<String, dynamic>> _items = [
    {
      'id': '1',
      'name': 'لابتوب HP',
      'category': '1',
      'code': 'ELE001',
      'unit': 'جهاز',
      'cost': '2500.00'
    },
    {
      'id': '2',
      'name': 'مكتب خشبي',
      'category': '2',
      'code': 'FUR001',
      'unit': 'قطعة',
      'cost': '800.00'
    },
    {
      'id': '3',
      'name': 'أقلام حبر',
      'category': '3',
      'code': 'STA001',
      'unit': 'علبة',
      'cost': '25.00'
    },
    {
      'id': '4',
      'name': 'طابعة ليزر',
      'category': '1',
      'code': 'ELE002',
      'unit': 'جهاز',
      'cost': '1200.00'
    },
  ];

  final List<Map<String, String>> _locations = [
    {'id': '1', 'name': 'الفرع الرئيسي - الرياض', 'type': 'فرع'},
    {'id': '2', 'name': 'فرع جدة', 'type': 'فرع'},
    {'id': '3', 'name': 'فرع الدمام', 'type': 'فرع'},
    {'id': '4', 'name': 'مستودع التوزيع المركزي', 'type': 'مستودع'},
    {'id': '5', 'name': 'مستودع الاحتياطي', 'type': 'مستودع'},
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.transferStockBalances),
        backgroundColor: Colors.amber,
        foregroundColor: Colors.black,
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showStockReport,
            tooltip: 'تقرير المخزون',
          ),
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: _showTransferHistory,
            tooltip: 'سجل النقل',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // بطاقة اختيار الصنف
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'اختيار الصنف',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.amber,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // اختيار الفئة
                    DropdownButtonFormField<String>(
                      value: _selectedCategory,
                      decoration: const InputDecoration(
                        labelText: 'فئة الصنف',
                        prefixIcon: Icon(Icons.category),
                        border: OutlineInputBorder(),
                      ),
                      items:
                          _categories.map<DropdownMenuItem<String>>((category) {
                        return DropdownMenuItem<String>(
                          value: category['id'],
                          child: Text(category['name']!),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedCategory = value;
                          _selectedItem = null; // إعادة تعيين الصنف المحدد
                        });
                      },
                    ),

                    const SizedBox(height: 16),

                    // اختيار الصنف
                    DropdownButtonFormField<String>(
                      value: _selectedItem,
                      decoration: const InputDecoration(
                        labelText: 'الصنف المراد نقله',
                        prefixIcon: Icon(Icons.inventory_2),
                        border: OutlineInputBorder(),
                      ),
                      items: _getFilteredItems()
                          .map<DropdownMenuItem<String>>((item) {
                        return DropdownMenuItem<String>(
                          value: item['id'],
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(item['name']),
                              Text(
                                'كود: ${item['code']} | التكلفة: ${item['cost']} ر.س',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedItem = value;
                          if (value != null) {
                            final item = _items
                                .firstWhere((item) => item['id'] == value);
                            _costController.text = item['cost'];
                          }
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار الصنف';
                        }
                        return null;
                      },
                    ),

                    if (_selectedItem != null) ...[
                      const SizedBox(height: 16),
                      _buildItemDetails(),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة تفاصيل النقل
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'تفاصيل عملية النقل',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.amber,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // الموقع المرسل
                    DropdownButtonFormField<String>(
                      value: _fromLocation,
                      decoration: const InputDecoration(
                        labelText: 'من الموقع',
                        prefixIcon: Icon(Icons.location_on),
                        border: OutlineInputBorder(),
                      ),
                      items: _locations.map((location) {
                        return DropdownMenuItem(
                          value: location['id'],
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(location['name']!),
                              Text(
                                'النوع: ${location['type']}',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _fromLocation = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار الموقع المرسل';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // الموقع المستقبل
                    DropdownButtonFormField<String>(
                      value: _toLocation,
                      decoration: const InputDecoration(
                        labelText: 'إلى الموقع',
                        prefixIcon: Icon(Icons.place),
                        border: OutlineInputBorder(),
                      ),
                      items: _locations
                          .where((location) => location['id'] != _fromLocation)
                          .map((location) {
                        return DropdownMenuItem(
                          value: location['id'],
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(location['name']!),
                              Text(
                                'النوع: ${location['type']}',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _toLocation = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار الموقع المستقبل';
                        }
                        if (value == _fromLocation) {
                          return 'لا يمكن النقل إلى نفس الموقع';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    Row(
                      children: [
                        // الكمية
                        Expanded(
                          child: TextFormField(
                            controller: _quantityController,
                            decoration: InputDecoration(
                              labelText: 'الكمية',
                              hintText: '0',
                              prefixIcon: const Icon(Icons.numbers),
                              border: const OutlineInputBorder(),
                              suffixText: _selectedItem != null
                                  ? _getItemUnit(_selectedItem!)
                                  : '',
                            ),
                            keyboardType: TextInputType.number,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال الكمية';
                              }
                              final quantity = double.tryParse(value);
                              if (quantity == null || quantity <= 0) {
                                return 'كمية غير صحيحة';
                              }
                              return null;
                            },
                          ),
                        ),

                        const SizedBox(width: 16),

                        // التكلفة الإجمالية
                        Expanded(
                          child: TextFormField(
                            controller: _costController,
                            decoration: const InputDecoration(
                              labelText: 'التكلفة الإجمالية',
                              hintText: '0.00',
                              prefixIcon: Icon(Icons.attach_money),
                              border: OutlineInputBorder(),
                              suffixText: 'ر.س',
                            ),
                            keyboardType: TextInputType.number,
                            readOnly: true,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // تاريخ النقل
                    ListTile(
                      leading: const Icon(Icons.calendar_today),
                      title: const Text('تاريخ النقل'),
                      subtitle: Text(
                          '${_transferDate.day}/${_transferDate.month}/${_transferDate.year}'),
                      trailing: const Icon(Icons.edit),
                      onTap: _selectDate,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                        side: BorderSide(color: Colors.grey.shade300),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // ملاحظات
                    TextFormField(
                      controller: _notesController,
                      decoration: const InputDecoration(
                        labelText: 'ملاحظات (اختياري)',
                        hintText: 'أدخل أي ملاحظات حول عملية النقل',
                        prefixIcon: Icon(Icons.note),
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // معاينة العملية
            if (_selectedItem != null &&
                _fromLocation != null &&
                _toLocation != null &&
                _quantityController.text.isNotEmpty)
              Card(
                color: Colors.amber.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'معاينة عملية النقل',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.orange,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildPreviewRow('الصنف:', _getItemName(_selectedItem!)),
                      _buildPreviewRow(
                          'من موقع:', _getLocationName(_fromLocation!)),
                      _buildPreviewRow(
                          'إلى موقع:', _getLocationName(_toLocation!)),
                      _buildPreviewRow('الكمية:',
                          '${_quantityController.text} ${_getItemUnit(_selectedItem!)}'),
                      _buildPreviewRow(
                          'التكلفة:', '${_costController.text} ر.س'),
                      _buildPreviewRow('التاريخ:',
                          '${_transferDate.day}/${_transferDate.month}/${_transferDate.year}'),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 24),

            // أزرار العمليات
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isProcessing ? null : _processTransfer,
                    icon: _isProcessing
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.transfer_within_a_station),
                    label: Text(
                        _isProcessing ? 'جاري النقل...' : 'تنفيذ عملية النقل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.amber,
                      foregroundColor: Colors.black,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _resetForm,
                    icon: const Icon(Icons.refresh),
                    label: const Text('إعادة تعيين'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Map<String, dynamic>> _getFilteredItems() {
    if (_selectedCategory == null) return [];
    return _items
        .where((item) => item['category'] == _selectedCategory)
        .toList();
  }

  Widget _buildItemDetails() {
    final item = _items.firstWhere((item) => item['id'] == _selectedItem);
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'تفاصيل الصنف المحدد:',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text('الاسم: ${item['name']}'),
          Text('الكود: ${item['code']}'),
          Text('الوحدة: ${item['unit']}'),
          Text('التكلفة: ${item['cost']} ر.س'),
        ],
      ),
    );
  }

  Widget _buildPreviewRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  String _getItemName(String itemId) {
    final item = _items.firstWhere((item) => item['id'] == itemId);
    return item['name'];
  }

  String _getItemUnit(String itemId) {
    final item = _items.firstWhere((item) => item['id'] == itemId);
    return item['unit'];
  }

  String _getLocationName(String locationId) {
    final location = _locations.firstWhere((loc) => loc['id'] == locationId);
    return location['name']!;
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _transferDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _transferDate) {
      setState(() {
        _transferDate = picked;
      });
    }
  }

  Future<void> _processTransfer() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isProcessing = true;
      });

      // محاكاة عملية النقل
      await Future.delayed(const Duration(seconds: 2));

      setState(() {
        _isProcessing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تنفيذ عملية نقل أرصدة المخزون بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        _resetForm();
      }
    }
  }

  void _resetForm() {
    setState(() {
      _selectedCategory = null;
      _selectedItem = null;
      _fromLocation = null;
      _toLocation = null;
      _quantityController.clear();
      _costController.clear();
      _notesController.clear();
      _transferDate = DateTime.now();
    });
  }

  void _showStockReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تقرير المخزون')),
    );
  }

  void _showTransferHistory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض سجل نقل المخزون')),
    );
  }

  @override
  void dispose() {
    _quantityController.dispose();
    _costController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
