import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير أصناف العملاء
/// يعرض الأصناف المرتبطة بعملاء معينين وتفضيلاتهم الشرائية
class CustomerItemsReportPage extends StatefulWidget {
  const CustomerItemsReportPage({super.key});

  @override
  State<CustomerItemsReportPage> createState() => _CustomerItemsReportPageState();
}

class _CustomerItemsReportPageState extends State<CustomerItemsReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedCustomer;
  String? _selectedCategory;
  String? _analysisType = 'purchase_history';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير أصناف العملاء'),
        backgroundColor: Colors.deepOrange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showAnalytics,
            tooltip: 'تحليلات متقدمة',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.deepOrange[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.fromDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, true),
                        controller: TextEditingController(
                          text: _startDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.toDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, false),
                        controller: TextEditingController(
                          text: _endDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'العميل',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedCustomer,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع العملاء')),
                          DropdownMenuItem(value: 'customer1', child: Text('شركة الأحمد للتجارة')),
                          DropdownMenuItem(value: 'customer2', child: Text('مؤسسة النور')),
                          DropdownMenuItem(value: 'customer3', child: Text('شركة الخليج')),
                          DropdownMenuItem(value: 'customer4', child: Text('مؤسسة الشرق')),
                        ],
                        onChanged: (value) => setState(() => _selectedCustomer = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.category,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedCategory,
                        items: [
                          DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                          DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                          DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                          DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                        ],
                        onChanged: (value) => setState(() => _selectedCategory = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'نوع التحليل',
                          border: OutlineInputBorder(),
                        ),
                        value: _analysisType,
                        items: const [
                          DropdownMenuItem(value: 'purchase_history', child: Text('تاريخ المشتريات')),
                          DropdownMenuItem(value: 'preferences', child: Text('التفضيلات')),
                          DropdownMenuItem(value: 'frequency', child: Text('تكرار الشراء')),
                          DropdownMenuItem(value: 'seasonal', child: Text('التحليل الموسمي')),
                        ],
                        onChanged: (value) => setState(() => _analysisType = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _generateReport,
                        icon: const Icon(Icons.search),
                        label: Text(localizations.generateReport),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.deepOrange,
                          foregroundColor: Colors.white,
                          minimumSize: const Size(double.infinity, 48),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص أصناف العملاء
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.people, color: Colors.deepOrange, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'ملخص أصناف العملاء',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSummaryCard('إجمالي العملاء', '125', Colors.blue, Icons.person),
                              _buildSummaryCard('إجمالي الأصناف', '450', Colors.green, Icons.inventory),
                              _buildSummaryCard('متوسط الأصناف/عميل', '3.6', Colors.orange, Icons.analytics),
                              _buildSummaryCard('أكثر الأصناف طلباً', '85', Colors.purple, Icons.trending_up),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // أفضل العملاء حسب الأصناف
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'أفضل العملاء حسب تنوع الأصناف',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ..._buildTopCustomersList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول أصناف العملاء
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل أصناف العملاء',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('العميل')),
                                DataColumn(label: Text('كود الصنف')),
                                DataColumn(label: Text('اسم الصنف')),
                                DataColumn(label: Text('التصنيف')),
                                DataColumn(label: Text('عدد المرات')),
                                DataColumn(label: Text('إجمالي الكمية')),
                                DataColumn(label: Text('إجمالي القيمة')),
                                DataColumn(label: Text('آخر شراء')),
                                DataColumn(label: Text('التفضيل')),
                              ],
                              rows: _buildCustomerItemRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // تحليل التفضيلات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.favorite, color: Colors.red, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'تحليل تفضيلات العملاء',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildPreferenceCard('الإلكترونيات', '45%', Colors.blue),
                              _buildPreferenceCard('الملابس', '30%', Colors.green),
                              _buildPreferenceCard('المواد الغذائية', '15%', Colors.orange),
                              _buildPreferenceCard('أخرى', '10%', Colors.grey),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // الأصناف الأكثر طلباً
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'الأصناف الأكثر طلباً من العملاء',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('الترتيب')),
                                DataColumn(label: Text('كود الصنف')),
                                DataColumn(label: Text('اسم الصنف')),
                                DataColumn(label: Text('عدد العملاء')),
                                DataColumn(label: Text('إجمالي الطلبات')),
                                DataColumn(label: Text('متوسط الكمية')),
                                DataColumn(label: Text('نسبة الطلب')),
                              ],
                              rows: _buildMostRequestedItemRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // توصيات التسويق
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.lightbulb, color: Colors.amber, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'توصيات التسويق المستهدف',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          _buildRecommendationItem(
                            'عروض مخصصة للعملاء المميزين',
                            'إنشاء عروض خاصة للعملاء الذين يشترون أصناف متنوعة بانتظام',
                            Icons.local_offer,
                            Colors.green,
                          ),
                          _buildRecommendationItem(
                            'تسويق الأصناف المكملة',
                            'اقتراح أصناف مكملة للعملاء بناءً على تاريخ مشترياتهم',
                            Icons.add_shopping_cart,
                            Colors.blue,
                          ),
                          _buildRecommendationItem(
                            'برنامج الولاء للأصناف المفضلة',
                            'إنشاء برنامج نقاط للأصناف التي يشتريها العملاء بكثرة',
                            Icons.star,
                            Colors.orange,
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _createCustomerSegments,
                                  icon: const Icon(Icons.group),
                                  label: const Text('تجميع العملاء'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _generateRecommendations,
                                  icon: const Icon(Icons.recommend),
                                  label: const Text('اقتراحات ذكية'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _createTargetedCampaign,
                                  icon: const Icon(Icons.campaign),
                                  label: const Text('حملة مستهدفة'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _exportCustomerData,
                                  icon: const Icon(Icons.download),
                                  label: const Text('تصدير البيانات'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<DataRow> _buildCustomerItemRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('شركة الأحمد للتجارة')),
        const DataCell(Text('ITEM-001')),
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('8')),
        const DataCell(Text('24')),
        const DataCell(Text('60,000')),
        const DataCell(Text('2024-06-15')),
        DataCell(_buildPreferenceBadge('مفضل', Colors.green)),
      ]),
      DataRow(cells: [
        const DataCell(Text('مؤسسة النور')),
        const DataCell(Text('ITEM-002')),
        const DataCell(Text('طابعة HP LaserJet')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('5')),
        const DataCell(Text('15')),
        const DataCell(Text('12,000')),
        const DataCell(Text('2024-06-10')),
        DataCell(_buildPreferenceBadge('عادي', Colors.orange)),
      ]),
      DataRow(cells: [
        const DataCell(Text('شركة الخليج')),
        const DataCell(Text('ITEM-003')),
        const DataCell(Text('ماوس لوجيتك')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('12')),
        const DataCell(Text('36')),
        const DataCell(Text('1,800')),
        const DataCell(Text('2024-06-20')),
        DataCell(_buildPreferenceBadge('مفضل جداً', Colors.red)),
      ]),
    ];
  }

  List<DataRow> _buildMostRequestedItemRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('1')),
        const DataCell(Text('ITEM-001')),
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('45')),
        const DataCell(Text('180')),
        const DataCell(Text('4.0')),
        const DataCell(Text('36%')),
      ]),
      DataRow(cells: [
        const DataCell(Text('2')),
        const DataCell(Text('ITEM-003')),
        const DataCell(Text('ماوس لوجيتك')),
        const DataCell(Text('38')),
        const DataCell(Text('152')),
        const DataCell(Text('4.0')),
        const DataCell(Text('30%')),
      ]),
      DataRow(cells: [
        const DataCell(Text('3')),
        const DataCell(Text('ITEM-002')),
        const DataCell(Text('طابعة HP LaserJet')),
        const DataCell(Text('25')),
        const DataCell(Text('75')),
        const DataCell(Text('3.0')),
        const DataCell(Text('20%')),
      ]),
    ];
  }

  List<Widget> _buildTopCustomersList() {
    final customers = [
      {'name': 'شركة الأحمد للتجارة', 'items': '25', 'categories': '8', 'value': '125,000'},
      {'name': 'مؤسسة النور', 'items': '18', 'categories': '6', 'value': '85,000'},
      {'name': 'شركة الخليج', 'items': '15', 'categories': '5', 'value': '65,000'},
      {'name': 'مؤسسة الشرق', 'items': '12', 'categories': '4', 'value': '45,000'},
    ];

    return customers.map((customer) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.deepOrange.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.deepOrange.withOpacity(0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.business, color: Colors.deepOrange, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  customer['name']!,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${customer['items']} صنف في ${customer['categories']} تصنيفات',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Text(
            '${customer['value']} ر.س',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.deepOrange,
            ),
          ),
        ],
      ),
    )).toList();
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPreferenceCard(String category, String percentage, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                percentage,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                category,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecommendationItem(String title, String description, IconData icon, Color color) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: color.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: color.withOpacity(0.05),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPreferenceBadge(String preference, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(preference, style: const TextStyle(color: Colors.white, fontSize: 12)),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير أصناف العملاء بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showAnalytics() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض التحليلات المتقدمة')),
    );
  }

  void _createCustomerSegments() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تجميعات العملاء')),
    );
  }

  void _generateRecommendations() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء الاقتراحات الذكية')),
    );
  }

  void _createTargetedCampaign() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء حملة تسويقية مستهدفة')),
    );
  }

  void _exportCustomerData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تصدير بيانات العملاء')),
    );
  }
}
