import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة المندوبين
/// تتيح إدارة بيانات المندوبين ومعلوماتهم
class SalesRepresentativesPage extends StatefulWidget {
  const SalesRepresentativesPage({super.key});

  @override
  State<SalesRepresentativesPage> createState() => _SalesRepresentativesPageState();
}

class _SalesRepresentativesPageState extends State<SalesRepresentativesPage> {
  String _searchQuery = '';
  String _selectedFilter = 'all';

  // بيانات تجريبية للمندوبين
  final List<Map<String, dynamic>> _representatives = [
    {
      'id': 'REP001',
      'name': 'أحمد محمد علي',
      'phone': '0501234567',
      'email': '<EMAIL>',
      'region': 'الرياض',
      'commission': 5.0,
      'isActive': true,
      'totalSales': 125000.0,
      'joinDate': '2023-01-15',
    },
    {
      'id': 'REP002',
      'name': 'فاطمة أحمد',
      'phone': '0507654321',
      'email': '<EMAIL>',
      'region': 'جدة',
      'commission': 4.5,
      'isActive': true,
      'totalSales': 98000.0,
      'joinDate': '2023-03-20',
    },
    {
      'id': 'REP003',
      'name': 'محمد عبدالله',
      'phone': '0551234567',
      'email': '<EMAIL>',
      'region': 'الدمام',
      'commission': 6.0,
      'isActive': false,
      'totalSales': 75000.0,
      'joinDate': '2022-11-10',
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.salesRepresentatives),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addRepresentative,
            tooltip: 'إضافة مندوب جديد',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                TextField(
                  decoration: const InputDecoration(
                    hintText: 'البحث في المندوبين...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedFilter,
                        decoration: const InputDecoration(
                          labelText: 'فلترة حسب الحالة',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع المندوبين')),
                          DropdownMenuItem(value: 'active', child: Text('المندوبين النشطين')),
                          DropdownMenuItem(value: 'inactive', child: Text('المندوبين غير النشطين')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedFilter = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                _buildStatCard('المجموع', _representatives.length.toString(), Colors.blue),
                _buildStatCard('النشطين', _representatives.where((r) => r['isActive']).length.toString(), Colors.green),
                _buildStatCard('غير النشطين', _representatives.where((r) => !r['isActive']).length.toString(), Colors.orange),
              ],
            ),
          ),

          // قائمة المندوبين
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _representatives.length,
              itemBuilder: (context, index) {
                final representative = _representatives[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: representative['isActive'] ? Colors.green : Colors.grey,
                      child: Text(
                        representative['name'].toString().substring(0, 1),
                        style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                      ),
                    ),
                    title: Text(
                      representative['name'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('الكود: ${representative['id']}'),
                        Text('المنطقة: ${representative['region']}'),
                        Text('العمولة: ${representative['commission']}%'),
                        Text('إجمالي المبيعات: ${representative['totalSales']} ر.س'),
                      ],
                    ),
                    trailing: PopupMenuButton<String>(
                      onSelected: (value) => _handleAction(value, representative),
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'edit',
                          child: ListTile(
                            leading: Icon(Icons.edit),
                            title: Text('تعديل'),
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'toggle',
                          child: ListTile(
                            leading: Icon(Icons.toggle_on),
                            title: Text('تغيير الحالة'),
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'sales',
                          child: ListTile(
                            leading: Icon(Icons.analytics),
                            title: Text('تقرير المبيعات'),
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: ListTile(
                            leading: Icon(Icons.delete, color: Colors.red),
                            title: Text('حذف', style: TextStyle(color: Colors.red)),
                          ),
                        ),
                      ],
                    ),
                    isThreeLine: true,
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addRepresentative,
        backgroundColor: Colors.teal,
        icon: const Icon(Icons.add),
        label: const Text('إضافة مندوب'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _addRepresentative() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إضافة مندوب جديد')),
    );
  }

  void _handleAction(String action, Map<String, dynamic> representative) {
    switch (action) {
      case 'edit':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تعديل المندوب ${representative['name']}')),
        );
        break;
      case 'toggle':
        setState(() {
          representative['isActive'] = !representative['isActive'];
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم ${representative['isActive'] ? 'تفعيل' : 'إلغاء تفعيل'} المندوب'),
          ),
        );
        break;
      case 'sales':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تقرير مبيعات ${representative['name']}')),
        );
        break;
      case 'delete':
        _showDeleteConfirmation(representative);
        break;
    }
  }

  void _showDeleteConfirmation(Map<String, dynamic> representative) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المندوب ${representative['name']}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _representatives.removeWhere((r) => r['id'] == representative['id']);
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف المندوب ${representative['name']}'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
