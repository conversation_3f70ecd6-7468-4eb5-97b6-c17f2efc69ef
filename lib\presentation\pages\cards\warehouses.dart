import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة المستودعات
/// تتيح إدارة المستودعات ومعلوماتها
class WarehousesPage extends StatefulWidget {
  const WarehousesPage({super.key});

  @override
  State<WarehousesPage> createState() => _WarehousesPageState();
}

class _WarehousesPageState extends State<WarehousesPage> {
  String _searchQuery = '';
  String _selectedType = 'all';

  // بيانات تجريبية للمستودعات
  final List<Map<String, dynamic>> _warehouses = [
    {
      'id': 'WH001',
      'name': 'المستودع الرئيسي',
      'code': 'MAIN-WH',
      'type': 'رئيسي',
      'location': 'الرياض - المنطقة الصناعية',
      'capacity': 1000.0,
      'currentStock': 750.0,
      'manager': 'أحمد محمد',
      'isActive': true,
      'temperature': 'عادي',
      'security': 'عالي',
    },
    {
      'id': 'WH002',
      'name': 'مستودع التبريد',
      'code': 'COLD-WH',
      'type': 'مبرد',
      'location': 'جدة - منطقة التخزين',
      'capacity': 500.0,
      'currentStock': 320.0,
      'manager': 'فاطمة أحمد',
      'isActive': true,
      'temperature': 'مبرد',
      'security': 'متوسط',
    },
    {
      'id': 'WH003',
      'name': 'مستودع الفرع الشرقي',
      'code': 'EAST-WH',
      'type': 'فرعي',
      'location': 'الدمام - حي الصناعة',
      'capacity': 300.0,
      'currentStock': 180.0,
      'manager': 'محمد عبدالله',
      'isActive': false,
      'temperature': 'عادي',
      'security': 'منخفض',
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.warehouses),
        backgroundColor: Colors.brown,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addWarehouse,
            tooltip: 'إضافة مستودع جديد',
          ),
          IconButton(
            icon: const Icon(Icons.inventory),
            onPressed: _showInventoryReport,
            tooltip: 'تقرير المخزون',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                TextField(
                  decoration: const InputDecoration(
                    hintText: 'البحث في المستودعات...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedType,
                        decoration: const InputDecoration(
                          labelText: 'نوع المستودع',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الأنواع')),
                          DropdownMenuItem(value: 'رئيسي', child: Text('رئيسي')),
                          DropdownMenuItem(value: 'فرعي', child: Text('فرعي')),
                          DropdownMenuItem(value: 'مبرد', child: Text('مبرد')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedType = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                _buildStatCard('المجموع', _warehouses.length.toString(), Colors.blue),
                _buildStatCard('النشطة', _warehouses.where((w) => w['isActive']).length.toString(), Colors.green),
                _buildStatCard('مغلقة', _warehouses.where((w) => !w['isActive']).length.toString(), Colors.red),
                _buildStatCard('الإشغال', '${_getOccupancyRate()}%', Colors.orange),
              ],
            ),
          ),

          // قائمة المستودعات
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _warehouses.length,
              itemBuilder: (context, index) {
                final warehouse = _warehouses[index];
                double occupancyRate = (warehouse['currentStock'] / warehouse['capacity']) * 100;
                
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: warehouse['isActive'] ? Colors.green : Colors.red,
                      child: Icon(
                        _getWarehouseIcon(warehouse['type']),
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    title: Text(
                      warehouse['name'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('الكود: ${warehouse['code']} | النوع: ${warehouse['type']}'),
                        Text('المدير: ${warehouse['manager']}'),
                        Row(
                          children: [
                            Text('الإشغال: ${occupancyRate.toStringAsFixed(1)}%'),
                            const SizedBox(width: 8),
                            Expanded(
                              child: LinearProgressIndicator(
                                value: occupancyRate / 100,
                                backgroundColor: Colors.grey[300],
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  occupancyRate > 80 ? Colors.red : 
                                  occupancyRate > 60 ? Colors.orange : Colors.green,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          warehouse['isActive'] ? Icons.check_circle : Icons.cancel,
                          color: warehouse['isActive'] ? Colors.green : Colors.red,
                        ),
                        PopupMenuButton<String>(
                          onSelected: (value) => _handleAction(value, warehouse),
                          itemBuilder: (context) => [
                            const PopupMenuItem(
                              value: 'edit',
                              child: ListTile(
                                leading: Icon(Icons.edit),
                                title: Text('تعديل'),
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'toggle',
                              child: ListTile(
                                leading: Icon(Icons.toggle_on),
                                title: Text('تغيير الحالة'),
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'inventory',
                              child: ListTile(
                                leading: Icon(Icons.inventory_2),
                                title: Text('إدارة المخزون'),
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'transfer',
                              child: ListTile(
                                leading: Icon(Icons.swap_horiz),
                                title: Text('نقل البضائع'),
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'delete',
                              child: ListTile(
                                leading: Icon(Icons.delete, color: Colors.red),
                                title: Text('حذف', style: TextStyle(color: Colors.red)),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Icon(Icons.location_on, size: 16),
                                const SizedBox(width: 8),
                                Expanded(child: Text('الموقع: ${warehouse['location']}')),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                const Icon(Icons.storage, size: 16),
                                const SizedBox(width: 8),
                                Text('السعة: ${warehouse['capacity']} وحدة'),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                const Icon(Icons.inventory, size: 16),
                                const SizedBox(width: 8),
                                Text('المخزون الحالي: ${warehouse['currentStock']} وحدة'),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                const Icon(Icons.thermostat, size: 16),
                                const SizedBox(width: 8),
                                Text('درجة الحرارة: ${warehouse['temperature']}'),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                const Icon(Icons.security, size: 16),
                                const SizedBox(width: 8),
                                Text('مستوى الأمان: ${warehouse['security']}'),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addWarehouse,
        backgroundColor: Colors.brown,
        icon: const Icon(Icons.add),
        label: const Text('إضافة مستودع'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getWarehouseIcon(String type) {
    switch (type) {
      case 'رئيسي':
        return Icons.warehouse;
      case 'فرعي':
        return Icons.store;
      case 'مبرد':
        return Icons.ac_unit;
      default:
        return Icons.inventory_2;
    }
  }

  String _getOccupancyRate() {
    double totalCapacity = _warehouses.fold(0.0, (sum, w) => sum + w['capacity']);
    double totalStock = _warehouses.fold(0.0, (sum, w) => sum + w['currentStock']);
    return totalCapacity > 0 ? ((totalStock / totalCapacity) * 100).toStringAsFixed(1) : '0';
  }

  void _addWarehouse() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إضافة مستودع جديد')),
    );
  }

  void _showInventoryReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تقرير المخزون')),
    );
  }

  void _handleAction(String action, Map<String, dynamic> warehouse) {
    switch (action) {
      case 'edit':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تعديل المستودع ${warehouse['name']}')),
        );
        break;
      case 'toggle':
        setState(() {
          warehouse['isActive'] = !warehouse['isActive'];
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم ${warehouse['isActive'] ? 'تفعيل' : 'إغلاق'} المستودع'),
          ),
        );
        break;
      case 'inventory':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('إدارة مخزون ${warehouse['name']}')),
        );
        break;
      case 'transfer':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('نقل البضائع من/إلى ${warehouse['name']}')),
        );
        break;
      case 'delete':
        _showDeleteConfirmation(warehouse);
        break;
    }
  }

  void _showDeleteConfirmation(Map<String, dynamic> warehouse) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المستودع ${warehouse['name']}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _warehouses.removeWhere((w) => w['id'] == warehouse['id']);
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف المستودع ${warehouse['name']}'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
