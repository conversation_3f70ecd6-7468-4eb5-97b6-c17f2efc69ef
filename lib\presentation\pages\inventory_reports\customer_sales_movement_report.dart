import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير حركة مبيعات العملاء
/// يعرض تقرير تفصيلي بحركة مبيعات العملاء والأصناف المباعة
class CustomerSalesMovementReportPage extends StatefulWidget {
  const CustomerSalesMovementReportPage({super.key});

  @override
  State<CustomerSalesMovementReportPage> createState() => _CustomerSalesMovementReportPageState();
}

class _CustomerSalesMovementReportPageState extends State<CustomerSalesMovementReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedCustomer;
  String? _selectedCategory;
  String? _salesType = 'all';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('حركة مبيعات العملاء'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showSalesAnalysis,
            tooltip: 'تحليل المبيعات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.green[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.fromDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, true),
                        controller: TextEditingController(
                          text: _startDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.toDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, false),
                        controller: TextEditingController(
                          text: _endDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'العميل',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedCustomer,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع العملاء')),
                          DropdownMenuItem(value: 'customer1', child: Text('شركة الأمل التجارية')),
                          DropdownMenuItem(value: 'customer2', child: Text('مؤسسة النور للتجارة')),
                          DropdownMenuItem(value: 'customer3', child: Text('شركة الفجر الجديد')),
                        ],
                        onChanged: (value) => setState(() => _selectedCustomer = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.category,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedCategory,
                        items: [
                          DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                          DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                          DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                          DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                        ],
                        onChanged: (value) => setState(() => _selectedCategory = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'نوع المبيعات',
                          border: OutlineInputBorder(),
                        ),
                        value: _salesType,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع المبيعات')),
                          DropdownMenuItem(value: 'cash', child: Text('مبيعات نقدية')),
                          DropdownMenuItem(value: 'credit', child: Text('مبيعات آجلة')),
                          DropdownMenuItem(value: 'wholesale', child: Text('مبيعات جملة')),
                          DropdownMenuItem(value: 'retail', child: Text('مبيعات تجزئة')),
                        ],
                        onChanged: (value) => setState(() => _salesType = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _generateReport,
                        icon: const Icon(Icons.search),
                        label: Text(localizations.generateReport),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          minimumSize: const Size(double.infinity, 48),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص مبيعات العملاء
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.trending_up, color: Colors.green, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'ملخص مبيعات العملاء',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSummaryCard('إجمالي المبيعات', '2,850,000 ر.س', Colors.blue, Icons.monetization_on),
                              _buildSummaryCard('عدد الفواتير', '1,245', Colors.green, Icons.receipt),
                              _buildSummaryCard('عدد العملاء', '156', Colors.orange, Icons.people),
                              _buildSummaryCard('متوسط الفاتورة', '2,289 ر.س', Colors.purple, Icons.calculate),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // أفضل العملاء
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'أفضل العملاء (حسب قيمة المبيعات)',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildCustomerCard('شركة الأمل التجارية', '485,000 ر.س', '17%', Colors.blue),
                              _buildCustomerCard('مؤسسة النور للتجارة', '320,000 ر.س', '11%', Colors.green),
                              _buildCustomerCard('شركة الفجر الجديد', '285,000 ر.س', '10%', Colors.orange),
                              _buildCustomerCard('عملاء آخرون', '1,760,000 ر.س', '62%', Colors.grey),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول تفاصيل مبيعات العملاء
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل مبيعات العملاء',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('رقم الفاتورة')),
                                DataColumn(label: Text('العميل')),
                                DataColumn(label: Text('الصنف')),
                                DataColumn(label: Text('الكمية')),
                                DataColumn(label: Text('سعر الوحدة')),
                                DataColumn(label: Text('إجمالي السطر')),
                                DataColumn(label: Text('تاريخ البيع')),
                                DataColumn(label: Text('نوع البيع')),
                                DataColumn(label: Text('المندوب')),
                              ],
                              rows: _buildSalesMovementRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // الأصناف الأكثر مبيعاً
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.star, color: Colors.amber, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'الأصناف الأكثر مبيعاً',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          ..._buildTopSellingItemsList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // تحليل اتجاهات المبيعات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.analytics, color: Colors.indigo, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'تحليل اتجاهات المبيعات',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildTrendCard('نمو المبيعات', '+15.2%', 'مقارنة بالشهر السابق', Colors.green, Icons.trending_up),
                              _buildTrendCard('عملاء جدد', '23 عميل', 'خلال هذا الشهر', Colors.blue, Icons.person_add),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              _buildTrendCard('متوسط الطلب', '8.5 أصناف', 'لكل فاتورة', Colors.orange, Icons.shopping_cart),
                              _buildTrendCard('معدل التكرار', '3.2 مرة', 'شهرياً لكل عميل', Colors.purple, Icons.repeat),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _createCustomerReport,
                                  icon: const Icon(Icons.person),
                                  label: const Text('تقرير عميل'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _sendCustomerStatement,
                                  icon: const Icon(Icons.email),
                                  label: const Text('كشف حساب'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _analyzeCustomerBehavior,
                                  icon: const Icon(Icons.psychology),
                                  label: const Text('تحليل السلوك'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _generateRecommendations,
                                  icon: const Icon(Icons.recommend),
                                  label: const Text('توصيات البيع'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<DataRow> _buildSalesMovementRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('INV-2024-001')),
        const DataCell(Text('شركة الأمل التجارية')),
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('5')),
        const DataCell(Text('2,500 ر.س')),
        const DataCell(Text('12,500 ر.س')),
        const DataCell(Text('2024-02-15')),
        DataCell(_buildSalesTypeBadge('نقدي', Colors.green)),
        const DataCell(Text('أحمد محمد')),
      ]),
      DataRow(cells: [
        const DataCell(Text('INV-2024-002')),
        const DataCell(Text('مؤسسة النور للتجارة')),
        const DataCell(Text('قميص قطني')),
        const DataCell(Text('50')),
        const DataCell(Text('85 ر.س')),
        const DataCell(Text('4,250 ر.س')),
        const DataCell(Text('2024-02-14')),
        DataCell(_buildSalesTypeBadge('آجل', Colors.orange)),
        const DataCell(Text('فاطمة أحمد')),
      ]),
      DataRow(cells: [
        const DataCell(Text('INV-2024-003')),
        const DataCell(Text('شركة الفجر الجديد')),
        const DataCell(Text('هاتف آيفون 15')),
        const DataCell(Text('3')),
        const DataCell(Text('3,000 ر.س')),
        const DataCell(Text('9,000 ر.س')),
        const DataCell(Text('2024-02-13')),
        DataCell(_buildSalesTypeBadge('جملة', Colors.blue)),
        const DataCell(Text('محمد علي')),
      ]),
    ];
  }

  List<Widget> _buildTopSellingItemsList() {
    final items = [
      {'name': 'لابتوب ديل XPS 13', 'quantity': '125 قطعة', 'revenue': '312,500 ر.س', 'customers': '25 عميل'},
      {'name': 'قميص قطني', 'quantity': '450 قطعة', 'revenue': '38,250 ر.س', 'customers': '45 عميل'},
      {'name': 'هاتف آيفون 15', 'quantity': '85 قطعة', 'revenue': '255,000 ر.س', 'customers': '18 عميل'},
      {'name': 'ساعة ذكية', 'quantity': '95 قطعة', 'revenue': '142,500 ر.س', 'customers': '32 عميل'},
    ];

    return items.map((item) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.green.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.green.withOpacity(0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.star, color: Colors.amber, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item['name']!,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${item['quantity']} • ${item['customers']}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Text(
            item['revenue']!,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
        ],
      ),
    )).toList();
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCustomerCard(String customer, String sales, String percentage, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            children: [
              Text(
                sales,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                percentage,
                style: TextStyle(
                  fontSize: 12,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                customer,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTrendCard(String title, String value, String subtitle, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              Text(
                subtitle,
                style: TextStyle(fontSize: 10, color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSalesTypeBadge(String type, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        type,
        style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير حركة مبيعات العملاء بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showSalesAnalysis() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تحليل مفصل للمبيعات')),
    );
  }

  void _createCustomerReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء تقرير مخصص للعميل')),
    );
  }

  void _sendCustomerStatement() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إرسال كشف حساب للعميل')),
    );
  }

  void _analyzeCustomerBehavior() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحليل سلوك العملاء الشرائي')),
    );
  }

  void _generateRecommendations() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء توصيات البيع للعملاء')),
    );
  }
}
