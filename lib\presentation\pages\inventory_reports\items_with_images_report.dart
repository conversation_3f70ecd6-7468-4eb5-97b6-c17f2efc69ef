import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير الأصناف التي لها صور
/// يعرض جميع الأصناف التي تحتوي على صور مرفقة
class ItemsWithImagesReportPage extends StatefulWidget {
  const ItemsWithImagesReportPage({super.key});

  @override
  State<ItemsWithImagesReportPage> createState() => _ItemsWithImagesReportPageState();
}

class _ItemsWithImagesReportPageState extends State<ItemsWithImagesReportPage> {
  String? _selectedCategory;
  String? _imageQuality = 'all';
  String? _sortBy = 'item_name';
  String? _imageCount = 'all';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الأصناف التي لها صور'),
        backgroundColor: Colors.pink,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.photo_library),
            onPressed: _viewImageGallery,
            tooltip: 'معرض الصور',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          _buildFilterSection(localizations),
          
          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildImageStatsSection(),
                  const SizedBox(height: 16),
                  _buildItemsGridSection(),
                  const SizedBox(height: 16),
                  _buildImageQualitySection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.pink[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: localizations.category,
                    border: const OutlineInputBorder(),
                  ),
                  value: _selectedCategory,
                  items: [
                    DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                    DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                    DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                    DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                  ],
                  onChanged: (value) => setState(() => _selectedCategory = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'جودة الصورة',
                    border: OutlineInputBorder(),
                  ),
                  value: _imageQuality,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الجودات')),
                    DropdownMenuItem(value: 'high', child: Text('عالية الجودة')),
                    DropdownMenuItem(value: 'medium', child: Text('متوسطة الجودة')),
                    DropdownMenuItem(value: 'low', child: Text('منخفضة الجودة')),
                  ],
                  onChanged: (value) => setState(() => _imageQuality = value),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'عدد الصور',
                    border: OutlineInputBorder(),
                  ),
                  value: _imageCount,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الأعداد')),
                    DropdownMenuItem(value: 'single', child: Text('صورة واحدة')),
                    DropdownMenuItem(value: 'multiple', child: Text('عدة صور')),
                    DropdownMenuItem(value: 'many', child: Text('أكثر من 5 صور')),
                  ],
                  onChanged: (value) => setState(() => _imageCount = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'ترتيب حسب',
                    border: OutlineInputBorder(),
                  ),
                  value: _sortBy,
                  items: const [
                    DropdownMenuItem(value: 'item_name', child: Text('اسم الصنف')),
                    DropdownMenuItem(value: 'image_count', child: Text('عدد الصور')),
                    DropdownMenuItem(value: 'image_size', child: Text('حجم الصور')),
                    DropdownMenuItem(value: 'upload_date', child: Text('تاريخ الرفع')),
                  ],
                  onChanged: (value) => setState(() => _sortBy = value),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.image, color: Colors.pink, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص الأصناف التي لها صور',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي الأصناف', '485', Colors.pink, Icons.inventory),
                _buildSummaryCard('إجمالي الصور', '1,285', Colors.blue, Icons.photo),
                _buildSummaryCard('متوسط الصور لكل صنف', '2.6', Colors.green, Icons.calculate),
                _buildSummaryCard('حجم الصور الإجمالي', '2.5 GB', Colors.orange, Icons.storage),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageStatsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.bar_chart, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'إحصائيات الصور',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildStatsCard('صورة واحدة', '285 صنف', Colors.blue),
                _buildStatsCard('2-5 صور', '145 صنف', Colors.green),
                _buildStatsCard('أكثر من 5 صور', '55 صنف', Colors.orange),
                _buildStatsCard('عالية الجودة', '385 صنف', Colors.purple),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsGridSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الأصناف مع الصور',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 0.8,
              ),
              itemCount: 6,
              itemBuilder: (context, index) => _buildItemCard(index),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageQualitySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.high_quality, color: Colors.teal, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'تحليل جودة الصور',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildQualityAnalysisList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _optimizeImages,
                    icon: const Icon(Icons.compress),
                    label: const Text('ضغط الصور'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _bulkImageUpload,
                    icon: const Icon(Icons.cloud_upload),
                    label: const Text('رفع جماعي للصور'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _generateCatalog,
                    icon: const Icon(Icons.book),
                    label: const Text('إنشاء كتالوج'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _backupImages,
                    icon: const Icon(Icons.backup),
                    label: const Text('نسخ احتياطي للصور'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemCard(int index) {
    final items = [
      {'name': 'لابتوب ديل XPS 13', 'images': '3', 'quality': 'عالية'},
      {'name': 'هاتف آيفون 15', 'images': '5', 'quality': 'عالية'},
      {'name': 'طابعة HP LaserJet', 'images': '2', 'quality': 'متوسطة'},
      {'name': 'شاشة سامسونج 27"', 'images': '4', 'quality': 'عالية'},
      {'name': 'ماوس لوجيتك', 'images': '1', 'quality': 'متوسطة'},
      {'name': 'كيبورد ميكانيكي', 'images': '2', 'quality': 'عالية'},
    ];

    final item = items[index];
    
    return Card(
      elevation: 4,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
              ),
              child: const Icon(
                Icons.image,
                size: 48,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item['name']!,
                    style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(Icons.photo, size: 12, color: Colors.grey[600]),
                      const SizedBox(width: 4),
                      Text(
                        '${item['images']} صور',
                        style: TextStyle(fontSize: 10, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                  const SizedBox(height: 2),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                    decoration: BoxDecoration(
                      color: _getQualityColor(item['quality']!).withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      item['quality']!,
                      style: TextStyle(
                        fontSize: 8,
                        color: _getQualityColor(item['quality']!),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildQualityAnalysisList() {
    final qualityData = [
      {'quality': 'عالية الجودة (HD+)', 'count': '385 صنف', 'size': '1.8 GB', 'percentage': '79%'},
      {'quality': 'متوسطة الجودة', 'count': '85 صنف', 'size': '0.5 GB', 'percentage': '18%'},
      {'quality': 'منخفضة الجودة', 'count': '15 صنف', 'size': '0.2 GB', 'percentage': '3%'},
    ];

    return qualityData.map((data) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: _getQualityColor(data['quality']!).withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: _getQualityColor(data['quality']!).withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.high_quality, color: Colors.teal, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  data['quality']!,
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  '${data['count']} • ${data['size']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getQualityColor(data['quality']!).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              data['percentage']!,
              style: TextStyle(
                color: _getQualityColor(data['quality']!),
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatsCard(String label, String count, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(label, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Color _getQualityColor(String quality) {
    if (quality.contains('عالية')) return Colors.green;
    if (quality.contains('متوسطة')) return Colors.orange;
    if (quality.contains('منخفضة')) return Colors.red;
    return Colors.grey;
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _viewImageGallery() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض معرض الصور')),
    );
  }

  void _optimizeImages() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ضغط وتحسين الصور')),
    );
  }

  void _bulkImageUpload() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('رفع جماعي للصور')),
    );
  }

  void _generateCatalog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء كتالوج المنتجات')),
    );
  }

  void _backupImages() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء نسخة احتياطية للصور')),
    );
  }
}
