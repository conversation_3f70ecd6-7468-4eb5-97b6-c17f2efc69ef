import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة صيانة أرصدة الحسابات
/// تتيح فحص وإصلاح مشاكل أرصدة الحسابات المالية
class MaintainAccountBalancesPage extends StatefulWidget {
  const MaintainAccountBalancesPage({super.key});

  @override
  State<MaintainAccountBalancesPage> createState() =>
      _MaintainAccountBalancesPageState();
}

class _MaintainAccountBalancesPageState
    extends State<MaintainAccountBalancesPage> {
  final _formKey = GlobalKey<FormState>();

  String? _selectedAccountType;
  String? _selectedBranch;
  String? _maintenanceType;
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 90));
  DateTime _endDate = DateTime.now();
  bool _isProcessing = false;
  bool _autoFix = false;
  bool _createReport = true;
  bool _includeZeroBalances = false;

  final List<Map<String, String>> _accountTypes = [
    {'id': 'all', 'name': 'جميع أنواع الحسابات'},
    {'id': 'assets', 'name': 'حسابات الأصول'},
    {'id': 'liabilities', 'name': 'حسابات الخصوم'},
    {'id': 'equity', 'name': 'حسابات حقوق الملكية'},
    {'id': 'revenue', 'name': 'حسابات الإيرادات'},
    {'id': 'expenses', 'name': 'حسابات المصروفات'},
    {'id': 'customers', 'name': 'حسابات العملاء'},
    {'id': 'suppliers', 'name': 'حسابات الموردين'},
  ];

  final List<Map<String, String>> _branches = [
    {'id': 'all', 'name': 'جميع الفروع'},
    {'id': 'main', 'name': 'الفرع الرئيسي'},
    {'id': 'riyadh', 'name': 'فرع الرياض'},
    {'id': 'jeddah', 'name': 'فرع جدة'},
    {'id': 'dammam', 'name': 'فرع الدمام'},
  ];

  final List<Map<String, String>> _maintenanceTypes = [
    {
      'id': 'unbalanced',
      'name': 'الحسابات غير المتوازنة',
      'description': 'حسابات لا تتطابق مع حركاتها'
    },
    {
      'id': 'negative',
      'name': 'الأرصدة السالبة',
      'description': 'حسابات بأرصدة سالبة غير مبررة'
    },
    {
      'id': 'orphaned',
      'name': 'الحركات المعلقة',
      'description': 'حركات بدون مستندات مرجعية'
    },
    {
      'id': 'duplicates',
      'name': 'الحسابات المكررة',
      'description': 'حسابات مكررة في النظام'
    },
    {
      'id': 'inactive',
      'name': 'الحسابات غير النشطة',
      'description': 'حسابات بدون حركة لفترة طويلة'
    },
    {
      'id': 'reconciliation',
      'name': 'مطابقة البنوك',
      'description': 'مطابقة أرصدة البنوك مع كشوف الحساب'
    },
    {
      'id': 'all',
      'name': 'فحص شامل',
      'description': 'فحص جميع المشاكل المحتملة'
    },
  ];

  final List<Map<String, dynamic>> _detectedIssues = [
    {
      'id': 1,
      'accountCode': '1101',
      'accountName': 'الصندوق الرئيسي',
      'type': 'unbalanced',
      'issue': 'عدم توازن: الرصيد 15,000 ر.س، مجموع الحركات 14,500 ر.س',
      'difference': '500.00',
      'severity': 'high',
      'suggestion': 'مراجعة الحركات وإضافة القيد المفقود'
    },
    {
      'id': 2,
      'accountCode': '2201',
      'accountName': 'حساب مورد الأجهزة',
      'type': 'negative',
      'issue': 'رصيد سالب: -2,500 ر.س',
      'difference': '-2500.00',
      'severity': 'medium',
      'suggestion': 'مراجعة المدفوعات وتصحيح الرصيد'
    },
    {
      'id': 3,
      'accountCode': '1201',
      'accountName': 'البنك الأهلي',
      'type': 'reconciliation',
      'issue': 'عدم مطابقة مع كشف الحساب: النظام 50,000 ر.س، البنك 49,750 ر.س',
      'difference': '250.00',
      'severity': 'medium',
      'suggestion': 'مراجعة الرسوم البنكية والفوائد'
    },
    {
      'id': 4,
      'accountCode': '4105',
      'accountName': 'حساب عميل قديم',
      'type': 'inactive',
      'issue': 'لا توجد حركة منذ 180 يوم',
      'difference': '0.00',
      'severity': 'low',
      'suggestion': 'مراجعة الحساب وتحديد إذا كان نشط أم لا'
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.maintainAccountBalances),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.account_balance),
            onPressed: _showTrialBalance,
            tooltip: 'ميزان المراجعة',
          ),
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: _showMaintenanceHistory,
            tooltip: 'سجل الصيانة',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // بطاقة معايير الفحص
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'معايير الفحص',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.indigo,
                      ),
                    ),
                    const SizedBox(height: 16),

                    Row(
                      children: [
                        // نوع الحساب
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedAccountType,
                            decoration: const InputDecoration(
                              labelText: 'نوع الحساب',
                              prefixIcon: Icon(Icons.account_tree),
                              border: OutlineInputBorder(),
                            ),
                            items: _accountTypes
                                .map<DropdownMenuItem<String>>((type) {
                              return DropdownMenuItem<String>(
                                value: type['id'],
                                child: Text(type['name']!),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedAccountType = value;
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'يرجى اختيار نوع الحساب';
                              }
                              return null;
                            },
                          ),
                        ),

                        const SizedBox(width: 16),

                        // الفرع
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedBranch,
                            decoration: const InputDecoration(
                              labelText: 'الفرع',
                              prefixIcon: Icon(Icons.business),
                              border: OutlineInputBorder(),
                            ),
                            items: _branches
                                .map<DropdownMenuItem<String>>((branch) {
                              return DropdownMenuItem<String>(
                                value: branch['id'],
                                child: Text(branch['name']!),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedBranch = value;
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'يرجى اختيار الفرع';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // نوع الصيانة
                    DropdownButtonFormField<String>(
                      value: _maintenanceType,
                      decoration: const InputDecoration(
                        labelText: 'نوع الصيانة',
                        prefixIcon: Icon(Icons.build),
                        border: OutlineInputBorder(),
                      ),
                      items: _maintenanceTypes
                          .map<DropdownMenuItem<String>>((type) {
                        return DropdownMenuItem<String>(
                          value: type['id'],
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(type['name']!),
                              Text(
                                type['description']!,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _maintenanceType = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار نوع الصيانة';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة الفترة الزمنية
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'الفترة الزمنية للفحص',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.indigo,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        // تاريخ البداية
                        Expanded(
                          child: ListTile(
                            leading: const Icon(Icons.calendar_today),
                            title: const Text('من تاريخ'),
                            subtitle: Text(
                                '${_startDate.day}/${_startDate.month}/${_startDate.year}'),
                            onTap: () => _selectDate(true),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                              side: BorderSide(color: Colors.grey.shade300),
                            ),
                          ),
                        ),

                        const SizedBox(width: 16),

                        // تاريخ النهاية
                        Expanded(
                          child: ListTile(
                            leading: const Icon(Icons.event),
                            title: const Text('إلى تاريخ'),
                            subtitle: Text(
                                '${_endDate.day}/${_endDate.month}/${_endDate.year}'),
                            onTap: () => _selectDate(false),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                              side: BorderSide(color: Colors.grey.shade300),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة خيارات الصيانة
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'خيارات الصيانة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.indigo,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SwitchListTile(
                      title: const Text('تضمين الأرصدة الصفرية'),
                      subtitle: const Text('فحص الحسابات ذات الأرصدة الصفرية'),
                      value: _includeZeroBalances,
                      onChanged: (value) {
                        setState(() {
                          _includeZeroBalances = value;
                        });
                      },
                    ),
                    SwitchListTile(
                      title: const Text('الإصلاح التلقائي'),
                      subtitle: const Text('إصلاح المشاكل البسيطة تلقائياً'),
                      value: _autoFix,
                      onChanged: (value) {
                        setState(() {
                          _autoFix = value;
                        });
                      },
                    ),
                    SwitchListTile(
                      title: const Text('إنشاء تقرير مفصل'),
                      subtitle:
                          const Text('إنشاء تقرير بجميع المشاكل المكتشفة'),
                      value: _createReport,
                      onChanged: (value) {
                        setState(() {
                          _createReport = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة المشاكل المكتشفة
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'المشاكل المكتشفة في الحسابات',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.indigo,
                      ),
                    ),
                    const SizedBox(height: 16),
                    if (_detectedIssues.isEmpty)
                      const Center(
                        child: Text(
                          'لم يتم اكتشاف أي مشاكل بعد\nقم بتشغيل الفحص أولاً',
                          textAlign: TextAlign.center,
                          style: TextStyle(color: Colors.grey),
                        ),
                      )
                    else
                      ...(_detectedIssues.map((issue) => Card(
                            margin: const EdgeInsets.symmetric(vertical: 4),
                            child: ExpansionTile(
                              leading: CircleAvatar(
                                backgroundColor:
                                    _getSeverityColor(issue['severity']),
                                child: Icon(
                                  _getIssueTypeIcon(issue['type']),
                                  color: Colors.white,
                                ),
                              ),
                              title: Text(
                                  '${issue['accountCode']} - ${issue['accountName']}'),
                              subtitle: Text(issue['issue']),
                              children: [
                                Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      if (issue['difference'] != '0.00')
                                        Text(
                                            'الفرق: ${issue['difference']} ر.س'),
                                      const SizedBox(height: 8),
                                      Text(
                                        'الحل المقترح: ${issue['suggestion']}',
                                        style:
                                            const TextStyle(color: Colors.blue),
                                      ),
                                      const SizedBox(height: 16),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.end,
                                        children: [
                                          TextButton.icon(
                                            onPressed: () =>
                                                _viewAccountDetails(
                                                    issue['accountCode']),
                                            icon: const Icon(Icons.visibility),
                                            label: const Text('عرض الحساب'),
                                          ),
                                          const SizedBox(width: 8),
                                          ElevatedButton.icon(
                                            onPressed: () =>
                                                _fixAccountIssue(issue['id']),
                                            icon: const Icon(Icons.build),
                                            label: const Text('إصلاح'),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.orange,
                                              foregroundColor: Colors.white,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ))),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // معاينة العملية
            if (_selectedAccountType != null &&
                _selectedBranch != null &&
                _maintenanceType != null)
              Card(
                color: Colors.indigo.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'معاينة عملية الصيانة',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.indigo,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildPreviewRow('نوع الحساب:', _getAccountTypeName()),
                      _buildPreviewRow('الفرع:', _getBranchName()),
                      _buildPreviewRow(
                          'نوع الصيانة:', _getMaintenanceTypeName()),
                      _buildPreviewRow('الفترة:',
                          '${_startDate.day}/${_startDate.month}/${_startDate.year} - ${_endDate.day}/${_endDate.month}/${_endDate.year}'),
                      _buildPreviewRow('الأرصدة الصفرية:',
                          _includeZeroBalances ? 'مُضمنة' : 'مستبعدة'),
                      _buildPreviewRow(
                          'الإصلاح التلقائي:', _autoFix ? 'مفعل' : 'معطل'),
                      _buildPreviewRow('المشاكل المكتشفة:',
                          '${_detectedIssues.length} مشكلة'),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 24),

            // أزرار العمليات
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isProcessing ? null : _startMaintenance,
                    icon: _isProcessing
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.search),
                    label: Text(
                        _isProcessing ? 'جاري الفحص...' : 'بدء فحص الحسابات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.indigo,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _detectedIssues.isEmpty ? null : _fixAllIssues,
                    icon: const Icon(Icons.build),
                    label: const Text('إصلاح جميع المشاكل'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Color _getSeverityColor(String severity) {
    switch (severity) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.yellow;
      default:
        return Colors.grey;
    }
  }

  IconData _getIssueTypeIcon(String type) {
    switch (type) {
      case 'unbalanced':
        return Icons.balance;
      case 'negative':
        return Icons.trending_down;
      case 'orphaned':
        return Icons.help_outline;
      case 'duplicates':
        return Icons.content_copy;
      case 'inactive':
        return Icons.pause_circle;
      case 'reconciliation':
        return Icons.account_balance;
      default:
        return Icons.error;
    }
  }

  String _getAccountTypeName() {
    if (_selectedAccountType == null) return 'غير محدد';
    final type =
        _accountTypes.firstWhere((t) => t['id'] == _selectedAccountType);
    return type['name']!;
  }

  String _getBranchName() {
    if (_selectedBranch == null) return 'غير محدد';
    final branch = _branches.firstWhere((b) => b['id'] == _selectedBranch);
    return branch['name']!;
  }

  String _getMaintenanceTypeName() {
    if (_maintenanceType == null) return 'غير محدد';
    final type =
        _maintenanceTypes.firstWhere((t) => t['id'] == _maintenanceType);
    return type['name']!;
  }

  Future<void> _selectDate(bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : _endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  Future<void> _startMaintenance() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isProcessing = true;
      });

      // محاكاة عملية الفحص
      await Future.delayed(const Duration(seconds: 3));

      setState(() {
        _isProcessing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'تم اكتشاف ${_detectedIssues.length} مشكلة في أرصدة الحسابات'),
            backgroundColor:
                _detectedIssues.isEmpty ? Colors.green : Colors.orange,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  void _fixAccountIssue(int issueId) {
    setState(() {
      _detectedIssues.removeWhere((issue) => issue['id'] == issueId);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إصلاح مشكلة الحساب رقم $issueId'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _fixAllIssues() {
    final issueCount = _detectedIssues.length;
    setState(() {
      _detectedIssues.clear();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إصلاح جميع مشاكل الحسابات ($issueCount مشكلة)'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _viewAccountDetails(String accountCode) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض تفاصيل الحساب $accountCode')),
    );
  }

  void _showTrialBalance() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض ميزان المراجعة')),
    );
  }

  void _showMaintenanceHistory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض سجل صيانة الحسابات')),
    );
  }
}
