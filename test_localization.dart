import 'package:flutter/material.dart';
import 'lib/core/localization/app_localizations.dart';

void main() {
  // اختبار سريع للتأكد من أن الدمج يعمل
  
  // إنشاء كائن الترجمة للعربية
  final arLocalizations = AppLocalizations(const Locale('ar'));
  
  // إنشاء كائن الترجمة للإنجليزية  
  final enLocalizations = AppLocalizations(const Locale('en'));
  
  print('=== اختبار الترجمة العربية ===');
  print('العنوان: ${arLocalizations.appTitle}');
  print('الرئيسية: ${arLocalizations.home}');
  print('الكروت: ${arLocalizations.cards}');
  print('إدارة النظام: ${arLocalizations.systemManagement}');
  print('الدليل المالي: ${arLocalizations.financialGuide}');
  print('فاتورة مبيعات: ${arLocalizations.salesInvoice}');
  print('تقرير حركة الصنف: ${arLocalizations.itemMovementReport}');
  print('ميزان المراجعة: ${arLocalizations.trialBalance}');
  print('الآلة الحاسبة: ${arLocalizations.calculator}');
  print('اتصل بنا: ${arLocalizations.contactUs}');
  
  print('\n=== اختبار الترجمة الإنجليزية ===');
  print('Title: ${enLocalizations.appTitle}');
  print('Home: ${enLocalizations.home}');
  print('Cards: ${enLocalizations.cards}');
  print('System Management: ${enLocalizations.systemManagement}');
  print('Financial Guide: ${enLocalizations.financialGuide}');
  print('Sales Invoice: ${enLocalizations.salesInvoice}');
  print('Item Movement Report: ${enLocalizations.itemMovementReport}');
  print('Trial Balance: ${enLocalizations.trialBalance}');
  print('Calculator: ${enLocalizations.calculator}');
  print('Contact Us: ${enLocalizations.contactUs}');
  
  print('\n✅ اختبار الدمج مكتمل بنجاح!');
}
