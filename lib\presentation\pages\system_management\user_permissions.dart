import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة صلاحيات المستخدمين
/// تتيح إدارة وتعديل صلاحيات المستخدمين في النظام
class UserPermissionsPage extends StatefulWidget {
  const UserPermissionsPage({super.key});

  @override
  State<UserPermissionsPage> createState() => _UserPermissionsPageState();
}

class _UserPermissionsPageState extends State<UserPermissionsPage> {
  final _formKey = GlobalKey<FormState>();

  String? _selectedUser;
  String? _selectedRole;
  String? _selectedDepartment;
  final bool _isProcessing = false;
  final bool _applyToAll = false;

  final List<Map<String, String>> _users = [
    {
      'id': 'user1',
      'name': 'أحمد محمد',
      'role': 'مدير',
      'department': 'المبيعات'
    },
    {
      'id': 'user2',
      'name': 'فاطمة علي',
      'role': 'محاسب',
      'department': 'المحاسبة'
    },
    {
      'id': 'user3',
      'name': 'محمد سالم',
      'role': 'موظف',
      'department': 'المخازن'
    },
    {
      'id': 'user4',
      'name': 'نورا أحمد',
      'role': 'مشرف',
      'department': 'المشتريات'
    },
    {
      'id': 'user5',
      'name': 'خالد عبدالله',
      'role': 'موظف',
      'department': 'المبيعات'
    },
  ];

  final List<Map<String, String>> _roles = [
    {'id': 'admin', 'name': 'مدير النظام'},
    {'id': 'manager', 'name': 'مدير'},
    {'id': 'supervisor', 'name': 'مشرف'},
    {'id': 'accountant', 'name': 'محاسب'},
    {'id': 'employee', 'name': 'موظف'},
    {'id': 'viewer', 'name': 'مستعرض فقط'},
  ];

  final List<Map<String, String>> _departments = [
    {'id': 'all', 'name': 'جميع الأقسام'},
    {'id': 'sales', 'name': 'المبيعات'},
    {'id': 'purchases', 'name': 'المشتريات'},
    {'id': 'accounting', 'name': 'المحاسبة'},
    {'id': 'warehouse', 'name': 'المخازن'},
    {'id': 'hr', 'name': 'الموارد البشرية'},
  ];

  final Map<String, Map<String, bool>> _permissions = {
    'sales': {
      'view': true,
      'add': true,
      'edit': false,
      'delete': false,
      'print': true,
    },
    'purchases': {
      'view': true,
      'add': false,
      'edit': false,
      'delete': false,
      'print': false,
    },
    'inventory': {
      'view': true,
      'add': true,
      'edit': true,
      'delete': false,
      'print': true,
    },
    'accounting': {
      'view': false,
      'add': false,
      'edit': false,
      'delete': false,
      'print': false,
    },
    'reports': {
      'view': true,
      'add': false,
      'edit': false,
      'delete': false,
      'print': true,
    },
    'system': {
      'view': false,
      'add': false,
      'edit': false,
      'delete': false,
      'print': false,
    },
  };

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.userPermissions),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.group),
            onPressed: _showUsersReport,
            tooltip: 'تقرير المستخدمين',
          ),
          IconButton(
            icon: const Icon(Icons.security),
            onPressed: _showPermissionsTemplate,
            tooltip: 'قوالب الصلاحيات',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // بطاقة اختيار المستخدم
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'اختيار المستخدم',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.deepPurple,
                      ),
                    ),
                    const SizedBox(height: 16),

                    Row(
                      children: [
                        // المستخدم
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedUser,
                            decoration: const InputDecoration(
                              labelText: 'المستخدم',
                              prefixIcon: Icon(Icons.person, size: 20),
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 8),
                            ),
                            items: _users.map<DropdownMenuItem<String>>((user) {
                              return DropdownMenuItem<String>(
                                value: user['id'],
                                child: Text(
                                  '${user['name']} - ${user['role']}',
                                  style: const TextStyle(fontSize: 14),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedUser = value;
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'يرجى اختيار المستخدم';
                              }
                              return null;
                            },
                          ),
                        ),

                        const SizedBox(width: 16),

                        // الدور
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedRole,
                            decoration: const InputDecoration(
                              labelText: 'الدور',
                              prefixIcon: Icon(Icons.badge, size: 20),
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 8),
                            ),
                            items: _roles.map<DropdownMenuItem<String>>((role) {
                              return DropdownMenuItem<String>(
                                value: role['id'],
                                child: Text(role['name']!,
                                    style: const TextStyle(fontSize: 14)),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedRole = value;
                                _updatePermissionsByRole(value);
                              });
                            },
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // القسم
                    DropdownButtonFormField<String>(
                      value: _selectedDepartment,
                      decoration: const InputDecoration(
                        labelText: 'القسم',
                        prefixIcon: Icon(Icons.business, size: 20),
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                      ),
                      items: _departments.map<DropdownMenuItem<String>>((dept) {
                        return DropdownMenuItem<String>(
                          value: dept['id'],
                          child: Text(dept['name']!,
                              style: const TextStyle(fontSize: 14)),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedDepartment = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _updatePermissionsByRole(String? roleId) {
    if (roleId == null) return;

    setState(() {
      switch (roleId) {
        case 'admin':
          _setAllPermissions(true);
          break;
        case 'manager':
          _setAllPermissions(true);
          _permissions['system']!['delete'] = false;
          break;
        case 'supervisor':
          _setAllPermissions(false);
          _permissions['sales']!['view'] = true;
          _permissions['sales']!['add'] = true;
          _permissions['sales']!['edit'] = true;
          _permissions['inventory']!['view'] = true;
          _permissions['inventory']!['add'] = true;
          _permissions['reports']!['view'] = true;
          break;
        case 'accountant':
          _setAllPermissions(false);
          _permissions['accounting']!['view'] = true;
          _permissions['accounting']!['add'] = true;
          _permissions['accounting']!['edit'] = true;
          _permissions['reports']!['view'] = true;
          break;
        case 'employee':
          _setAllPermissions(false);
          _permissions['sales']!['view'] = true;
          _permissions['inventory']!['view'] = true;
          break;
        case 'viewer':
          _setAllPermissions(false);
          _permissions.forEach((module, perms) {
            perms['view'] = true;
          });
          break;
      }
    });
  }

  void _setAllPermissions(bool value) {
    _permissions.forEach((module, perms) {
      perms.forEach((perm, _) {
        perms[perm] = value;
      });
    });
  }

  void _showUsersReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تقرير المستخدمين والصلاحيات')),
    );
  }

  void _showPermissionsTemplate() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض قوالب الصلاحيات المحفوظة')),
    );
  }
}
