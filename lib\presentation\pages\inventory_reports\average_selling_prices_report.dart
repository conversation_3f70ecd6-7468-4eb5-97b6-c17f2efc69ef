import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير متوسط أسعار البيع للأصناف
/// يعرض متوسط أسعار البيع لكل صنف خلال فترة معينة
class AverageSellingPricesReportPage extends StatefulWidget {
  const AverageSellingPricesReportPage({super.key});

  @override
  State<AverageSellingPricesReportPage> createState() => _AverageSellingPricesReportPageState();
}

class _AverageSellingPricesReportPageState extends State<AverageSellingPricesReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedCategory;
  String? _priceRange = 'all';
  String? _sortBy = 'item_name';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('متوسط أسعار البيع للأصناف'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.trending_up),
            onPressed: _showPriceTrends,
            tooltip: 'اتجاهات الأسعار',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          _buildFilterSection(localizations),
          
          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildPriceRangesSection(),
                  const SizedBox(height: 16),
                  _buildPricesTableSection(),
                  const SizedBox(height: 16),
                  _buildPriceAnalysisSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.green[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.fromDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, true),
                  controller: TextEditingController(
                    text: _startDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.toDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, false),
                  controller: TextEditingController(
                    text: _endDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: localizations.category,
                    border: const OutlineInputBorder(),
                  ),
                  value: _selectedCategory,
                  items: [
                    DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                    DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                    DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                    DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                  ],
                  onChanged: (value) => setState(() => _selectedCategory = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'نطاق السعر',
                    border: OutlineInputBorder(),
                  ),
                  value: _priceRange,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الأسعار')),
                    DropdownMenuItem(value: 'low', child: Text('أقل من 100 ر.س')),
                    DropdownMenuItem(value: 'medium', child: Text('100-1000 ر.س')),
                    DropdownMenuItem(value: 'high', child: Text('أكثر من 1000 ر.س')),
                  ],
                  onChanged: (value) => setState(() => _priceRange = value),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'ترتيب حسب',
                    border: OutlineInputBorder(),
                  ),
                  value: _sortBy,
                  items: const [
                    DropdownMenuItem(value: 'item_name', child: Text('اسم الصنف')),
                    DropdownMenuItem(value: 'avg_price', child: Text('متوسط السعر')),
                    DropdownMenuItem(value: 'sales_volume', child: Text('حجم المبيعات')),
                    DropdownMenuItem(value: 'price_variance', child: Text('تباين السعر')),
                  ],
                  onChanged: (value) => setState(() => _sortBy = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.analytics),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.price_check, color: Colors.green, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص متوسط أسعار البيع',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي الأصناف', '1,245', Colors.green, Icons.inventory),
                _buildSummaryCard('متوسط السعر العام', '485 ر.س', Colors.blue, Icons.attach_money),
                _buildSummaryCard('أعلى متوسط سعر', '4,500 ر.س', Colors.orange, Icons.trending_up),
                _buildSummaryCard('أقل متوسط سعر', '15 ر.س', Colors.purple, Icons.trending_down),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriceRangesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.bar_chart, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'توزيع نطاقات الأسعار',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildPriceRangeCard('أقل من 100 ر.س', '485 صنف', Colors.green),
                _buildPriceRangeCard('100-500 ر.س', '425 صنف', Colors.blue),
                _buildPriceRangeCard('500-1000 ر.س', '235 صنف', Colors.orange),
                _buildPriceRangeCard('أكثر من 1000 ر.س', '100 صنف', Colors.red),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPricesTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل متوسط أسعار البيع',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('اسم الصنف')),
                  DataColumn(label: Text('الفئة')),
                  DataColumn(label: Text('متوسط السعر')),
                  DataColumn(label: Text('أعلى سعر')),
                  DataColumn(label: Text('أقل سعر')),
                  DataColumn(label: Text('تباين السعر')),
                  DataColumn(label: Text('عدد المبيعات')),
                  DataColumn(label: Text('إجمالي المبيعات')),
                  DataColumn(label: Text('الاتجاه')),
                ],
                rows: _buildPricesRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriceAnalysisSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.analytics, color: Colors.teal, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'تحليل الأسعار',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildPriceAnalysisList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _updatePrices,
                    icon: const Icon(Icons.update),
                    label: const Text('تحديث الأسعار'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _priceOptimization,
                    icon: const Icon(Icons.tune),
                    label: const Text('تحسين الأسعار'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _competitorAnalysis,
                    icon: const Icon(Icons.compare),
                    label: const Text('مقارنة المنافسين'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _priceAlerts,
                    icon: const Icon(Icons.notifications),
                    label: const Text('تنبيهات الأسعار'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildPriceAnalysisList() {
    final analysis = [
      {'metric': 'الأصناف عالية السعر', 'value': '100 صنف', 'percentage': '8%', 'trend': 'صاعد'},
      {'metric': 'الأصناف متوسطة السعر', 'value': '660 صنف', 'percentage': '53%', 'trend': 'ثابت'},
      {'metric': 'الأصناف منخفضة السعر', 'value': '485 صنف', 'percentage': '39%', 'trend': 'نازل'},
      {'metric': 'تباين الأسعار العالي', 'value': '85 صنف', 'percentage': '7%', 'trend': 'صاعد'},
    ];

    return analysis.map((item) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: _getTrendColor(item['trend']!).withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: _getTrendColor(item['trend']!).withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.analytics, color: Colors.teal, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item['metric']!,
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  '${item['value']} • ${item['percentage']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getTrendColor(item['trend']!).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              item['trend']!,
              style: TextStyle(
                color: _getTrendColor(item['trend']!),
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildPricesRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('4,350 ر.س')),
        const DataCell(Text('4,800 ر.س')),
        const DataCell(Text('4,000 ر.س')),
        DataCell(_buildVarianceBadge('18%', Colors.orange)),
        const DataCell(Text('125')),
        const DataCell(Text('543,750 ر.س')),
        DataCell(_buildTrendBadge('صاعد', Colors.green)),
      ]),
      DataRow(cells: [
        const DataCell(Text('هاتف آيفون 15')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('3,750 ر.س')),
        const DataCell(Text('3,900 ر.س')),
        const DataCell(Text('3,600 ر.س')),
        DataCell(_buildVarianceBadge('8%', Colors.green)),
        const DataCell(Text('95')),
        const DataCell(Text('356,250 ر.س')),
        DataCell(_buildTrendBadge('ثابت', Colors.blue)),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPriceRangeCard(String range, String count, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(range, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVarianceBadge(String variance, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(variance, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Widget _buildTrendBadge(String trend, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(trend, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Color _getTrendColor(String trend) {
    switch (trend) {
      case 'صاعد':
        return Colors.green;
      case 'نازل':
        return Colors.red;
      case 'ثابت':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير متوسط أسعار البيع بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showPriceTrends() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض اتجاهات الأسعار')),
    );
  }

  void _updatePrices() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحديث أسعار الأصناف')),
    );
  }

  void _priceOptimization() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحسين استراتيجية التسعير')),
    );
  }

  void _competitorAnalysis() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مقارنة أسعار المنافسين')),
    );
  }

  void _priceAlerts() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تعيين تنبيهات الأسعار')),
    );
  }
}
