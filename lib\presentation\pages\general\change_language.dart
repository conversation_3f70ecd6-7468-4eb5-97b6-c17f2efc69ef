import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/localization/language_provider.dart';
import '../../../core/localization/app_localizations.dart';

/// معلومات اللغة
class LanguageInfo {
  final Locale locale;
  final String title;
  final String subtitle;
  final String flagEmoji;
  final String nativeName;
  final TextDirection direction;
  final String description;
  final String speakers;

  const LanguageInfo({
    required this.locale,
    required this.title,
    required this.subtitle,
    required this.flagEmoji,
    required this.nativeName,
    required this.direction,
    required this.description,
    required this.speakers,
  });
}

/// صفحة تغيير اللغة المطورة
/// تتيح للمستخدم اختيار اللغة المفضلة من بين اللغات المتوفرة
/// مع ميزات إضافية مثل البحث والمعاينة والإعدادات المتقدمة
class ChangeLanguage extends StatefulWidget {
  const ChangeLanguage({super.key});

  @override
  State<ChangeLanguage> createState() => _ChangeLanguageState();
}

class _ChangeLanguageState extends State<ChangeLanguage>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  String _searchQuery = '';
  bool _showAdvancedOptions = false;

  // قائمة اللغات المتوفرة مع معلومات إضافية
  final List<LanguageInfo> _availableLanguages = [
    LanguageInfo(
      locale: const Locale('ar'),
      title: 'العربية',
      subtitle: 'Arabic',
      flagEmoji: '🇸🇦',
      nativeName: 'العربية',
      direction: TextDirection.rtl,
      description: 'اللغة العربية الفصحى',
      speakers: '422 مليون متحدث',
    ),
    LanguageInfo(
      locale: const Locale('en'),
      title: 'English',
      subtitle: 'الإنجليزية',
      flagEmoji: '🇺🇸',
      nativeName: 'English',
      direction: TextDirection.ltr,
      description: 'International English',
      speakers: '1.5 billion speakers',
    ),
    LanguageInfo(
      locale: const Locale('fr'),
      title: 'Français',
      subtitle: 'الفرنسية',
      flagEmoji: '🇫🇷',
      nativeName: 'Français',
      direction: TextDirection.ltr,
      description: 'French Language',
      speakers: '280 million speakers',
    ),
    LanguageInfo(
      locale: const Locale('es'),
      title: 'Español',
      subtitle: 'الإسبانية',
      flagEmoji: '🇪🇸',
      nativeName: 'Español',
      direction: TextDirection.ltr,
      description: 'Spanish Language',
      speakers: '500 million speakers',
    ),
    LanguageInfo(
      locale: const Locale('id'),
      title: 'Bahasa Indonesia',
      subtitle: 'الإندونيسية',
      flagEmoji: '🇮🇩',
      nativeName: 'Bahasa Indonesia',
      direction: TextDirection.ltr,
      description: 'Indonesian Language',
      speakers: '270 million speakers',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// بناء واجهة صفحة تغيير اللغة المطورة
  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final localizations = AppLocalizations.of(context);

    // تصفية اللغات حسب البحث
    final filteredLanguages = _availableLanguages.where((lang) {
      return lang.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          lang.subtitle.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          lang.nativeName.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.changeLanguage),
        elevation: 0,
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(
                _showAdvancedOptions ? Icons.expand_less : Icons.expand_more),
            onPressed: () {
              setState(() {
                _showAdvancedOptions = !_showAdvancedOptions;
              });
            },
            tooltip: _showAdvancedOptions
                ? 'إخفاء الخيارات المتقدمة'
                : 'إظهار الخيارات المتقدمة',
          ),
        ],
      ),
      body: AnimatedBuilder(
        animation: _fadeAnimation,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Theme.of(context).primaryColor.withOpacity(0.1),
                      Colors.white,
                    ],
                  ),
                ),
                child: Column(
                  children: [
                    // شريط البحث
                    _buildSearchBar(context),

                    // الخيارات المتقدمة
                    if (_showAdvancedOptions) _buildAdvancedOptions(context),

                    // قائمة اللغات
                    Expanded(
                      child: _buildLanguagesList(
                          context, filteredLanguages, languageProvider),
                    ),

                    // معلومات إضافية
                    _buildFooterInfo(context, languageProvider),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// بناء شريط البحث
  Widget _buildSearchBar(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
        decoration: InputDecoration(
          hintText: 'البحث عن لغة...',
          prefixIcon: const Icon(Icons.search, color: Colors.grey),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear, color: Colors.grey),
                  onPressed: () {
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                )
              : null,
          border: InputBorder.none,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
      ),
    );
  }

  /// بناء الخيارات المتقدمة
  Widget _buildAdvancedOptions(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'خيارات متقدمة',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildInfoChip(
                    'عدد اللغات', '${_availableLanguages.length}'),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildInfoChip(
                    'اللغة الحالية',
                    _availableLanguages
                        .firstWhere((lang) =>
                            lang.locale.languageCode ==
                            Provider.of<LanguageProvider>(context,
                                    listen: false)
                                .currentLocale
                                .languageCode)
                        .nativeName),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء رقاقة معلومات
  Widget _buildInfoChip(String label, String value) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة اللغات
  Widget _buildLanguagesList(BuildContext context, List<LanguageInfo> languages,
      LanguageProvider languageProvider) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: languages.length,
      itemBuilder: (context, index) {
        final language = languages[index];
        final isSelected = languageProvider.currentLocale.languageCode ==
            language.locale.languageCode;

        return AnimatedContainer(
          duration: Duration(milliseconds: 300 + (index * 100)),
          margin: const EdgeInsets.only(bottom: 12),
          child: _buildEnhancedLanguageCard(
            context,
            language: language,
            isSelected: isSelected,
            onTap: () =>
                _selectLanguage(context, language.locale, languageProvider),
          ),
        );
      },
    );
  }

  /// بناء معلومات التذييل
  Widget _buildFooterInfo(
      BuildContext context, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.info_outline, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 8),
              Text(
                languageProvider.currentLocale.languageCode == 'ar'
                    ? 'سيتم تطبيق التغييرات فورًا'
                    : 'Changes will be applied immediately',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'إصدار التطبيق: 1.0.0',
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة لغة محسنة
  ///
  /// تقوم هذه الدالة ببناء بطاقة تعرض معلومات اللغة وتتيح للمستخدم اختيارها
  ///
  /// المعلمات:
  /// * [title] - عنوان اللغة
  /// * [subtitle] - العنوان الفرعي للغة
  /// * [flagEmoji] - رمز علم الدولة
  /// * [locale] - كائن اللغة
  /// * [isSelected] - هل اللغة محددة حاليًا
  /// * [onTap] - الإجراء الذي سيتم تنفيذه عند النقر على البطاقة
  Widget _buildLanguageCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required String flagEmoji,
    required Locale locale,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: isSelected ? 4 : 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color:
              isSelected ? Theme.of(context).primaryColor : Colors.transparent,
          width: 2,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              // رمز علم الدولة
              Text(
                flagEmoji,
                style: const TextStyle(fontSize: 40),
              ),
              const SizedBox(width: 16),
              // معلومات اللغة
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              // مؤشر اللغة المحددة
              if (isSelected)
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء بطاقة لغة محسنة
  Widget _buildEnhancedLanguageCard(
    BuildContext context, {
    required LanguageInfo language,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: isSelected ? 8 : 2,
      shadowColor: isSelected
          ? Theme.of(context).primaryColor.withValues(alpha: 0.3)
          : Colors.grey.withValues(alpha: 0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color:
              isSelected ? Theme.of(context).primaryColor : Colors.transparent,
          width: 2,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: isSelected
                ? LinearGradient(
                    colors: [
                      Theme.of(context).primaryColor.withValues(alpha: 0.1),
                      Colors.white,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                : null,
          ),
          child: Row(
            children: [
              // رمز علم الدولة مع تأثير
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Center(
                  child: Text(
                    language.flagEmoji,
                    style: const TextStyle(fontSize: 32),
                  ),
                ),
              ),
              const SizedBox(width: 16),

              // معلومات اللغة
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      language.title,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: isSelected
                            ? Theme.of(context).primaryColor
                            : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      language.subtitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          language.direction == TextDirection.rtl
                              ? Icons.format_textdirection_r_to_l
                              : Icons.format_textdirection_l_to_r,
                          size: 16,
                          color: Colors.grey[500],
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            language.speakers,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[500],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // مؤشر اللغة المحددة مع تحسينات
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                width: isSelected ? 40 : 30,
                height: isSelected ? 40 : 30,
                decoration: BoxDecoration(
                  color: isSelected
                      ? Theme.of(context).primaryColor
                      : Colors.grey[300],
                  shape: BoxShape.circle,
                  boxShadow: isSelected
                      ? [
                          BoxShadow(
                            color: Theme.of(context)
                                .primaryColor
                                .withValues(alpha: 0.4),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ]
                      : null,
                ),
                child: Icon(
                  isSelected ? Icons.check : Icons.language,
                  color: Colors.white,
                  size: isSelected ? 20 : 16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// اختيار اللغة مع تأثيرات بصرية
  void _selectLanguage(BuildContext context, Locale locale,
      LanguageProvider languageProvider) async {
    // إظهار مؤشر التحميل
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    // تأخير قصير لإظهار التأثير
    await Future.delayed(const Duration(milliseconds: 500));

    // تغيير اللغة
    await languageProvider.changeLanguage(locale);

    // إغلاق مؤشر التحميل
    if (context.mounted) {
      Navigator.of(context).pop();

      // إظهار رسالة نجاح
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 8),
              Text(
                locale.languageCode == 'ar'
                    ? 'تم تغيير اللغة بنجاح'
                    : 'Language changed successfully',
              ),
            ],
          ),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }
}
