import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة إعدادات ربط خدمة الواتساب
/// تتيح تكوين الاتصال مع واتساب بيزنس API
class WhatsappServiceConnectionSettings extends StatefulWidget {
  const WhatsappServiceConnectionSettings({super.key});

  @override
  State<WhatsappServiceConnectionSettings> createState() =>
      _WhatsappServiceConnectionSettingsState();
}

class _WhatsappServiceConnectionSettingsState
    extends State<WhatsappServiceConnectionSettings> {
  bool _isWhatsAppEnabled = false;
  final _phoneNumberController = TextEditingController();
  final _apiTokenController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.whatsappConnectionSettings),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    SwitchListTile(
                      title: Text(localizations.enableWhatsappBusiness),
                      value: _isWhatsAppEnabled,
                      onChanged: (value) {
                        setState(() {
                          _isWhatsAppEnabled = value;
                        });
                      },
                    ),
                    if (_isWhatsAppEnabled) ...[
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _phoneNumberController,
                        decoration: InputDecoration(
                          labelText: localizations.whatsappBusinessPhone,
                          border: const OutlineInputBorder(),
                        ),
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _apiTokenController,
                        decoration: InputDecoration(
                          labelText: localizations.apiToken,
                          border: const OutlineInputBorder(),
                        ),
                        obscureText: true,
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _phoneNumberController.dispose();
    _apiTokenController.dispose();
    super.dispose();
  }
}
