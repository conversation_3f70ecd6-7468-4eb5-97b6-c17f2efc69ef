import 'package:flutter/material.dart';

/// نموذج بيانات الحساب
class AccountData {
  String branchNumber;
  String accountNumber;
  String arabicName;
  String englishName;
  String accountType;
  String freezeType;
  String currency;
  String securityLevel;
  String subAccountType;
  String balanceWarning;
  bool showNetMovement;
  bool isFolder;

  AccountData({
    this.branchNumber = '',
    this.accountNumber = '',
    this.arabicName = '',
    this.englishName = '',
    this.accountType = 'ميزانية',
    this.freezeType = 'بدون',
    this.currency = 'ريال سعودي',
    this.securityLevel = 'عادي',
    this.subAccountType = '',
    this.balanceWarning = 'بدون',
    this.showNetMovement = false,
    this.isFolder = false,
  });
}

/// نموذج إدخال بيانات الحساب
class AccountDataForm extends StatefulWidget {
  final AccountData? initialData;
  final bool isEditMode;
  final String? parentAccountCode;
  final Function(AccountData) onSave;
  final VoidCallback? onCancel;

  const AccountDataForm({
    super.key,
    this.initialData,
    this.isEditMode = false,
    this.parentAccountCode,
    required this.onSave,
    this.onCancel,
  });

  @override
  State<AccountDataForm> createState() => _AccountDataFormState();
}

class _AccountDataFormState extends State<AccountDataForm> {
  final _formKey = GlobalKey<FormState>();
  late AccountData _accountData;

  // Controllers
  late TextEditingController _branchNumberController;
  late TextEditingController _accountNumberController;
  late TextEditingController _arabicNameController;
  late TextEditingController _englishNameController;

  @override
  void initState() {
    super.initState();
    _accountData = widget.initialData ?? AccountData();

    // تهيئة Controllers
    _branchNumberController =
        TextEditingController(text: _accountData.branchNumber);
    _accountNumberController =
        TextEditingController(text: _accountData.accountNumber);
    _arabicNameController =
        TextEditingController(text: _accountData.arabicName);
    _englishNameController =
        TextEditingController(text: _accountData.englishName);

    // إذا كان في وضع الإضافة، احسب رقم الحساب التلقائي
    if (!widget.isEditMode && widget.parentAccountCode != null) {
      _accountData.accountNumber =
          _generateNextAccountNumber(widget.parentAccountCode!);
      _accountNumberController.text = _accountData.accountNumber;
    }
  }

  @override
  void dispose() {
    _branchNumberController.dispose();
    _accountNumberController.dispose();
    _arabicNameController.dispose();
    _englishNameController.dispose();
    super.dispose();
  }

  /// توليد رقم الحساب التالي
  String _generateNextAccountNumber(String parentCode) {
    // البحث عن آخر رقم مستخدم في هذا المستوى
    // هذا مثال - يجب ربطه بقاعدة البيانات الفعلية
    int nextNumber = _getNextAvailableNumber(parentCode);

    // تكوين رقم الحساب الجديد
    String newAccountNumber =
        parentCode + nextNumber.toString().padLeft(3, '0');

    return newAccountNumber;
  }

  /// الحصول على الرقم التالي المتاح
  int _getNextAvailableNumber(String parentCode) {
    // هذا مثال - يجب ربطه بقاعدة البيانات الفعلية
    // للآن سنرجع 1 كرقم افتراضي
    return 1;
  }

  /// حفظ البيانات
  void _saveData() {
    if (_formKey.currentState!.validate()) {
      // تحديث البيانات من الحقول
      _accountData.branchNumber = _branchNumberController.text;
      _accountData.accountNumber = _accountNumberController.text;
      _accountData.arabicName = _arabicNameController.text;
      _accountData.englishName = _englishNameController.text;

      // استدعاء دالة الحفظ
      widget.onSave(_accountData);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان
          Row(
            children: [
              Icon(
                widget.isEditMode ? Icons.edit : Icons.add,
                color: Colors.blue[700],
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                widget.isEditMode ? 'تعديل بيانات الحساب' : 'إضافة حساب جديد',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // المحتوى القابل للتمرير
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // نوع الحساب (مجلد أم ملف)
                  if (!widget.isEditMode) ...[
                    Card(
                      color: Colors.blue[50],
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'نوع الحساب',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue[700],
                              ),
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: RadioListTile<bool>(
                                    title: const Row(
                                      children: [
                                        Icon(Icons.folder, color: Colors.blue),
                                        SizedBox(width: 8),
                                        Text('مجلد'),
                                      ],
                                    ),
                                    value: true,
                                    groupValue: _accountData.isFolder,
                                    onChanged: (value) {
                                      setState(() {
                                        _accountData.isFolder = value!;
                                      });
                                    },
                                  ),
                                ),
                                Expanded(
                                  child: RadioListTile<bool>(
                                    title: const Row(
                                      children: [
                                        Icon(Icons.description,
                                            color: Colors.grey),
                                        SizedBox(width: 8),
                                        Text('ملف'),
                                      ],
                                    ),
                                    value: false,
                                    groupValue: _accountData.isFolder,
                                    onChanged: (value) {
                                      setState(() {
                                        _accountData.isFolder = value!;
                                      });
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // رقم فرع الحساب
                  Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: TextFormField(
                          controller: _branchNumberController,
                          keyboardType: TextInputType.number,
                          decoration: const InputDecoration(
                            labelText: 'رقم فرع الحساب',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'مطلوب';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        flex: 3,
                        child: DropdownButtonFormField<String>(
                          value: null,
                          decoration: const InputDecoration(
                            labelText: 'اختر الفرع',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8),
                          ),
                          items: const [
                            DropdownMenuItem(
                                value: '001', child: Text('الفرع الرئيسي')),
                            DropdownMenuItem(
                                value: '002', child: Text('فرع الرياض')),
                            DropdownMenuItem(
                                value: '003', child: Text('فرع جدة')),
                          ],
                          onChanged: (value) {
                            if (value != null) {
                              _branchNumberController.text = value;
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // رقم الحساب
                  TextFormField(
                    controller: _accountNumberController,
                    readOnly: !widget.isEditMode,
                    decoration: InputDecoration(
                      labelText: 'رقم الحساب',
                      border: const OutlineInputBorder(),
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      fillColor: widget.isEditMode ? null : Colors.grey[100],
                      filled: !widget.isEditMode,
                      suffixIcon: widget.parentAccountCode != null
                          ? Tooltip(
                              message:
                                  'سيتم إنشاء الحساب داخل: ${widget.parentAccountCode}',
                              child: Icon(Icons.folder_open,
                                  color: Colors.blue[600]),
                            )
                          : null,
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'مطلوب';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 12),

                  // الاسم بالعربي
                  TextFormField(
                    controller: _arabicNameController,
                    decoration: const InputDecoration(
                      labelText: 'الاسم بالعربي',
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'مطلوب';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 12),

                  // الاسم بالإنجليزي
                  TextFormField(
                    controller: _englishNameController,
                    decoration: const InputDecoration(
                      labelText: 'الاسم بالإنجليزي',
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'مطلوب';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 12),

                  // نوع الحساب
                  DropdownButtonFormField<String>(
                    value: _accountData.accountType,
                    decoration: const InputDecoration(
                      labelText: 'نوع الحساب',
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    items: const [
                      DropdownMenuItem(
                          value: 'ميزانية', child: Text('ميزانية')),
                      DropdownMenuItem(
                          value: 'أرباح وخسائر', child: Text('أرباح وخسائر')),
                      DropdownMenuItem(value: 'إحصائي', child: Text('إحصائي')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _accountData.accountType = value!;
                      });
                    },
                  ),
                  const SizedBox(height: 12),

                  // تجميد الحساب
                  DropdownButtonFormField<String>(
                    value: _accountData.freezeType,
                    decoration: const InputDecoration(
                      labelText: 'تجميد الحساب',
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'مدين', child: Text('مدين')),
                      DropdownMenuItem(value: 'دائن', child: Text('دائن')),
                      DropdownMenuItem(value: 'كلاهما', child: Text('كلاهما')),
                      DropdownMenuItem(value: 'بدون', child: Text('بدون')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _accountData.freezeType = value!;
                      });
                    },
                  ),
                  const SizedBox(height: 12),

                  // العملة
                  DropdownButtonFormField<String>(
                    value: _accountData.currency,
                    decoration: const InputDecoration(
                      labelText: 'العملة',
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    items: const [
                      DropdownMenuItem(
                          value: 'ريال سعودي', child: Text('ريال سعودي')),
                      DropdownMenuItem(
                          value: 'دولار أمريكي', child: Text('دولار أمريكي')),
                      DropdownMenuItem(value: 'يورو', child: Text('يورو')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _accountData.currency = value!;
                      });
                    },
                  ),
                  const SizedBox(height: 12),

                  // درجة السرية
                  DropdownButtonFormField<String>(
                    value: _accountData.securityLevel,
                    decoration: const InputDecoration(
                      labelText: 'درجة السرية',
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'عادي', child: Text('عادي')),
                      DropdownMenuItem(value: 'سري', child: Text('سري')),
                      DropdownMenuItem(
                          value: 'سري جداً', child: Text('سري جداً')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _accountData.securityLevel = value!;
                      });
                    },
                  ),
                  const SizedBox(height: 12),

                  // نوع الحساب المساعد
                  DropdownButtonFormField<String>(
                    value: _accountData.subAccountType.isEmpty
                        ? null
                        : _accountData.subAccountType,
                    decoration: const InputDecoration(
                      labelText: 'نوع الحساب المساعد',
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    items: const [
                      DropdownMenuItem(value: '', child: Text('فراغ')),
                      DropdownMenuItem(value: 'عملاء', child: Text('عملاء')),
                      DropdownMenuItem(value: 'موردين', child: Text('موردين')),
                      DropdownMenuItem(value: 'موظفين', child: Text('موظفين')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _accountData.subAccountType = value ?? '';
                      });
                    },
                  ),
                  const SizedBox(height: 12),

                  // تحذير للرصيد
                  DropdownButtonFormField<String>(
                    value: _accountData.balanceWarning,
                    decoration: const InputDecoration(
                      labelText: 'تحذير للرصيد',
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'مدين', child: Text('مدين')),
                      DropdownMenuItem(value: 'دائن', child: Text('دائن')),
                      DropdownMenuItem(value: 'بدون', child: Text('بدون')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _accountData.balanceWarning = value!;
                      });
                    },
                  ),
                  const SizedBox(height: 12),

                  // صافي الحركة في الميزان
                  CheckboxListTile(
                    title: const Text('صافي الحركة في الميزان'),
                    value: _accountData.showNetMovement,
                    onChanged: (value) {
                      setState(() {
                        _accountData.showNetMovement = value!;
                      });
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                  ),
                ],
              ),
            ),
          ),

          // أزرار الحفظ والإلغاء
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: widget.onCancel,
                child: const Text('إلغاء'),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: _saveData,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
                child: Text(widget.isEditMode ? 'تحديث' : 'حفظ'),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
