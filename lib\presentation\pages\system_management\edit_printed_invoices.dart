import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تعديل الفواتير المطبوعة
/// تتيح تعديل الفواتير التي تم طباعتها مسبقاً
class EditPrintedInvoicesPage extends StatefulWidget {
  const EditPrintedInvoicesPage({super.key});

  @override
  State<EditPrintedInvoicesPage> createState() =>
      _EditPrintedInvoicesPageState();
}

class _EditPrintedInvoicesPageState extends State<EditPrintedInvoicesPage> {
  final _searchController = TextEditingController();

  String _searchQuery = '';
  String? _selectedType;
  String? _selectedStatus;
  DateTime? _fromDate;
  DateTime? _toDate;

  final List<Map<String, String>> _invoiceTypes = [
    {'id': 'sales', 'name': 'فواتير مبيعات'},
    {'id': 'purchase', 'name': 'فواتير مشتريات'},
    {'id': 'return_sales', 'name': 'مرتجع مبيعات'},
    {'id': 'return_purchase', 'name': 'مرتجع مشتريات'},
  ];

  final List<Map<String, String>> _statuses = [
    {'id': 'printed', 'name': 'مطبوعة'},
    {'id': 'edited', 'name': 'معدلة'},
    {'id': 'cancelled', 'name': 'ملغية'},
  ];

  final List<Map<String, dynamic>> _printedInvoices = [
    {
      'id': 'inv1',
      'number': 'INV-2024-001',
      'type': 'فواتير مبيعات',
      'customer': 'شركة الأمل للتجارة',
      'date': '2024/01/25',
      'printDate': '2024/01/25 10:30',
      'amount': 15750.0,
      'status': 'مطبوعة',
      'printCount': 2,
      'canEdit': true,
      'editReason': '',
      'lastEditDate': '',
      'editedBy': '',
    },
    {
      'id': 'inv2',
      'number': 'PINV-2024-012',
      'type': 'فواتير مشتريات',
      'customer': 'شركة التوريدات المتقدمة',
      'date': '2024/01/24',
      'printDate': '2024/01/24 14:15',
      'amount': 28500.0,
      'status': 'معدلة',
      'printCount': 1,
      'canEdit': false,
      'editReason': 'تصحيح خطأ في الكمية',
      'lastEditDate': '2024/01/24 16:20',
      'editedBy': 'أحمد محمد',
    },
    {
      'id': 'inv3',
      'number': 'RET-2024-003',
      'type': 'مرتجع مبيعات',
      'customer': 'مؤسسة النور',
      'date': '2024/01/23',
      'printDate': '2024/01/23 09:45',
      'amount': -2500.0,
      'status': 'مطبوعة',
      'printCount': 1,
      'canEdit': true,
      'editReason': '',
      'lastEditDate': '',
      'editedBy': '',
    },
  ];

  List<Map<String, dynamic>> get _filteredInvoices {
    List<Map<String, dynamic>> filtered = _printedInvoices;

    // فلتر النوع
    if (_selectedType != null) {
      filtered = filtered
          .where((invoice) =>
              invoice['type'] ==
              _invoiceTypes.firstWhere((t) => t['id'] == _selectedType)['name'])
          .toList();
    }

    // فلتر الحالة
    if (_selectedStatus != null) {
      filtered = filtered
          .where((invoice) =>
              invoice['status'] ==
              _statuses.firstWhere((s) => s['id'] == _selectedStatus)['name'])
          .toList();
    }

    // البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered
          .where((invoice) =>
              invoice['number']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              invoice['customer']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()))
          .toList();
    }

    return filtered;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.editPrintedInvoices),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showEditSettings,
            tooltip: 'إعدادات التعديل',
          ),
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: _showEditHistory,
            tooltip: 'سجل التعديلات',
          ),
        ],
      ),
      body: Column(
        children: [
          // بطاقة البحث والفلترة
          Card(
            margin: const EdgeInsets.all(16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // شريط البحث
                  TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      labelText: 'البحث برقم الفاتورة أو العميل',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),

                  const SizedBox(height: 16),

                  Row(
                    children: [
                      // فلتر النوع
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedType,
                          decoration: const InputDecoration(
                            labelText: 'نوع الفاتورة',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 8),
                          ),
                          items: [
                            const DropdownMenuItem(
                                value: null, child: Text('جميع الأنواع')),
                            ..._invoiceTypes.map((type) => DropdownMenuItem(
                                  value: type['id'],
                                  child: Text(type['name']!),
                                )),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedType = value;
                            });
                          },
                        ),
                      ),

                      const SizedBox(width: 16),

                      // فلتر الحالة
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedStatus,
                          decoration: const InputDecoration(
                            labelText: 'الحالة',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 8),
                          ),
                          items: [
                            const DropdownMenuItem(
                                value: null, child: Text('جميع الحالات')),
                            ..._statuses.map((status) => DropdownMenuItem(
                                  value: status['id'],
                                  child: Text(status['name']!),
                                )),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedStatus = value;
                            });
                          },
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // فلتر التاريخ
                  Row(
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () => _selectDate(true),
                          child: InputDecorator(
                            decoration: const InputDecoration(
                              labelText: 'من تاريخ',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 8),
                            ),
                            child: Text(
                              _fromDate != null
                                  ? '${_fromDate!.day}/${_fromDate!.month}/${_fromDate!.year}'
                                  : 'اختر التاريخ',
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: InkWell(
                          onTap: () => _selectDate(false),
                          child: InputDecorator(
                            decoration: const InputDecoration(
                              labelText: 'إلى تاريخ',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 8),
                            ),
                            child: Text(
                              _toDate != null
                                  ? '${_toDate!.day}/${_toDate!.month}/${_toDate!.year}'
                                  : 'اختر التاريخ',
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // إحصائيات سريعة
          Card(
            margin: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatCard('المجموع', _filteredInvoices.length.toString(),
                      Colors.blue),
                  _buildStatCard(
                      'مطبوعة',
                      _filteredInvoices
                          .where((i) => i['status'] == 'مطبوعة')
                          .length
                          .toString(),
                      Colors.green),
                  _buildStatCard(
                      'معدلة',
                      _filteredInvoices
                          .where((i) => i['status'] == 'معدلة')
                          .length
                          .toString(),
                      Colors.orange),
                  _buildStatCard(
                      'قابلة للتعديل',
                      _filteredInvoices
                          .where((i) => i['canEdit'] == true)
                          .length
                          .toString(),
                      Colors.purple),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // قائمة الفواتير المطبوعة
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _filteredInvoices.length,
              itemBuilder: (context, index) {
                final invoice = _filteredInvoices[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: _getStatusColor(invoice['status']),
                      child: Icon(
                        _getInvoiceTypeIcon(invoice['type']),
                        color: Colors.white,
                      ),
                    ),
                    title: Text(
                      invoice['number'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('${invoice['customer']} | ${invoice['type']}'),
                        Text(
                            'التاريخ: ${invoice['date']} | المبلغ: ${invoice['amount']} ر.س'),
                        Text(
                            'طُبعت: ${invoice['printDate']} | عدد الطباعات: ${invoice['printCount']}',
                            style: const TextStyle(fontSize: 12)),
                      ],
                    ),
                    trailing: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color:
                            _getStatusColor(invoice['status']).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        invoice['status'],
                        style: TextStyle(
                          color: _getStatusColor(invoice['status']),
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            _buildDetailRow('رقم الفاتورة:', invoice['number']),
                            _buildDetailRow('النوع:', invoice['type']),
                            _buildDetailRow(
                                'العميل/المورد:', invoice['customer']),
                            _buildDetailRow('تاريخ الفاتورة:', invoice['date']),
                            _buildDetailRow(
                                'تاريخ الطباعة:', invoice['printDate']),
                            _buildDetailRow(
                                'المبلغ:', '${invoice['amount']} ر.س'),
                            _buildDetailRow('عدد الطباعات:',
                                '${invoice['printCount']} مرة'),
                            _buildDetailRow('قابلة للتعديل:',
                                invoice['canEdit'] ? 'نعم' : 'لا'),

                            if (invoice['status'] == 'معدلة') ...[
                              _buildDetailRow(
                                  'سبب التعديل:', invoice['editReason']),
                              _buildDetailRow(
                                  'تاريخ التعديل:', invoice['lastEditDate']),
                              _buildDetailRow(
                                  'معدلة بواسطة:', invoice['editedBy']),
                            ],

                            const SizedBox(height: 16),

                            // أزرار العمليات
                            Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: [
                                ElevatedButton.icon(
                                  onPressed: () => _viewInvoice(invoice),
                                  icon: const Icon(Icons.visibility, size: 16),
                                  label: const Text('عرض'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                                if (invoice['canEdit'])
                                  ElevatedButton.icon(
                                    onPressed: () => _editInvoice(invoice),
                                    icon: const Icon(Icons.edit, size: 16),
                                    label: const Text('تعديل'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.orange,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ElevatedButton.icon(
                                  onPressed: () => _reprintInvoice(invoice),
                                  icon: const Icon(Icons.print, size: 16),
                                  label: const Text('إعادة طباعة'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                                ElevatedButton.icon(
                                  onPressed: () => _cancelInvoice(invoice),
                                  icon: const Icon(Icons.cancel, size: 16),
                                  label: const Text('إلغاء'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                                ElevatedButton.icon(
                                  onPressed: () => _viewEditHistory(invoice),
                                  icon: const Icon(Icons.history, size: 16),
                                  label: const Text('السجل'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshInvoices,
        backgroundColor: Colors.orange,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Column(
      children: [
        CircleAvatar(
          backgroundColor: color,
          child: Text(
            value,
            style: const TextStyle(
                color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'مطبوعة':
        return Colors.green;
      case 'معدلة':
        return Colors.orange;
      case 'ملغية':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getInvoiceTypeIcon(String type) {
    switch (type) {
      case 'فواتير مبيعات':
        return Icons.receipt_long;
      case 'فواتير مشتريات':
        return Icons.shopping_cart;
      case 'مرتجع مبيعات':
        return Icons.keyboard_return;
      case 'مرتجع مشتريات':
        return Icons.undo;
      default:
        return Icons.description;
    }
  }

  Future<void> _selectDate(bool isFromDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      setState(() {
        if (isFromDate) {
          _fromDate = picked;
        } else {
          _toDate = picked;
        }
      });
    }
  }

  void _refreshInvoices() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث قائمة الفواتير المطبوعة'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _viewInvoice(Map<String, dynamic> invoice) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض الفاتورة ${invoice['number']}')),
    );
  }

  void _editInvoice(Map<String, dynamic> invoice) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تعديل الفاتورة ${invoice['number']}')),
    );
  }

  void _reprintInvoice(Map<String, dynamic> invoice) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('إعادة طباعة الفاتورة ${invoice['number']}')),
    );
  }

  void _cancelInvoice(Map<String, dynamic> invoice) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الإلغاء'),
        content: Text('هل أنت متأكد من إلغاء الفاتورة ${invoice['number']}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم إلغاء الفاتورة ${invoice['number']}'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('إلغاء الفاتورة',
                style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _viewEditHistory(Map<String, dynamic> invoice) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض سجل تعديلات الفاتورة ${invoice['number']}')),
    );
  }

  void _showEditSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إعدادات تعديل الفواتير المطبوعة')),
    );
  }

  void _showEditHistory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سجل جميع تعديلات الفواتير')),
    );
  }
}
