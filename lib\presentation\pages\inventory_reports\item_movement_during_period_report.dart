import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير حركة صنف خلال فترة
/// تعرض تفاصيل حركة صنف معين خلال فترة زمنية محددة
class ItemMovementDuringPeriodReport extends StatefulWidget {
  const ItemMovementDuringPeriodReport({super.key});

  @override
  State<ItemMovementDuringPeriodReport> createState() =>
      _ItemMovementDuringPeriodReportState();
}

class _ItemMovementDuringPeriodReportState
    extends State<ItemMovementDuringPeriodReport> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedItem;
  String? _selectedWarehouse;
  String? _movementType = 'all';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.itemMovementDuringPeriod),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.green[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.itemName,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedItem,
                        items: const [
                          DropdownMenuItem(
                              value: 'item1',
                              child: Text('جهاز كمبيوتر محمول')),
                          DropdownMenuItem(
                              value: 'item2', child: Text('طابعة ليزر')),
                          DropdownMenuItem(
                              value: 'item3', child: Text('ماوس لاسلكي')),
                          DropdownMenuItem(
                              value: 'item4', child: Text('كيبورد ميكانيكي')),
                          DropdownMenuItem(
                              value: 'item5', child: Text('شاشة LED')),
                        ],
                        onChanged: (value) =>
                            setState(() => _selectedItem = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.warehouse,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedWarehouse,
                        items: [
                          DropdownMenuItem(
                              value: 'all',
                              child: Text(localizations.allWarehouses)),
                          DropdownMenuItem(
                              value: 'main',
                              child: Text(localizations.mainWarehouse)),
                          DropdownMenuItem(
                              value: 'branch1',
                              child: Text(
                                  '${localizations.branchWarehouse} الأول')),
                          DropdownMenuItem(
                              value: 'branch2',
                              child: Text(
                                  '${localizations.branchWarehouse} الثاني')),
                        ],
                        onChanged: (value) =>
                            setState(() => _selectedWarehouse = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.fromDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, true),
                        controller: TextEditingController(
                          text: _startDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.toDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, false),
                        controller: TextEditingController(
                          text: _endDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'نوع الحركة',
                          border: OutlineInputBorder(),
                        ),
                        value: _movementType,
                        items: const [
                          DropdownMenuItem(
                              value: 'all', child: Text('جميع الحركات')),
                          DropdownMenuItem(
                              value: 'in', child: Text('وارد فقط')),
                          DropdownMenuItem(
                              value: 'out', child: Text('صادر فقط')),
                          DropdownMenuItem(
                              value: 'sales', child: Text('مبيعات')),
                          DropdownMenuItem(
                              value: 'purchases', child: Text('مشتريات')),
                          DropdownMenuItem(
                              value: 'transfers', child: Text('تحويلات')),
                        ],
                        onChanged: (value) =>
                            setState(() => _movementType = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _generateReport,
                        icon: const Icon(Icons.search),
                        label: Text(localizations.generateReport),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          minimumSize: const Size(double.infinity, 48),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات الصنف
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'معلومات الصنف',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildInfoCard('كود الصنف', '001', Colors.blue),
                              _buildInfoCard('اسم الصنف', 'جهاز كمبيوتر محمول',
                                  Colors.green),
                              _buildInfoCard('الوحدة', 'قطعة', Colors.orange),
                              _buildInfoCard(
                                  'سعر التكلفة', '2,500 ر.س', Colors.purple),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // ملخص الحركة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'ملخص الحركة خلال الفترة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSummaryCard(
                                  'الرصيد الافتتاحي', '50', Colors.blue),
                              _buildSummaryCard(
                                  'إجمالي الوارد', '120', Colors.green),
                              _buildSummaryCard(
                                  'إجمالي الصادر', '85', Colors.red),
                              _buildSummaryCard(
                                  'الرصيد الختامي', '85', Colors.purple),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول تفاصيل الحركة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل الحركة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: [
                                const DataColumn(label: Text('التاريخ')),
                                DataColumn(
                                    label: Text(localizations.operationType)),
                                DataColumn(
                                    label: Text(localizations.documentNumber)),
                                DataColumn(label: Text(localizations.quantity)),
                                DataColumn(label: Text(localizations.price)),
                                DataColumn(label: Text(localizations.value)),
                                DataColumn(label: Text(localizations.balance)),
                                const DataColumn(label: Text('المرجع')),
                              ],
                              rows: _buildMovementRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // التوصيات الذكية
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.lightbulb,
                                  color: Colors.amber, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'التوصيات الذكية',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          _buildRecommendationItem(
                            'تحسين مستوى المخزون',
                            'الرصيد الحالي مناسب، يُنصح بالحفاظ على هذا المستوى',
                            Icons.inventory,
                            Colors.green,
                            'إيجابي',
                          ),
                          _buildRecommendationItem(
                            'مراجعة سياسة الشراء',
                            'معدل الشراء جيد، يمكن زيادة الكمية بنسبة 10% لتحسين التوفر',
                            Icons.shopping_cart,
                            Colors.blue,
                            'اقتراح',
                          ),
                          _buildRecommendationItem(
                            'تحليل اتجاه المبيعات',
                            'المبيعات في نمو مستمر، يُنصح بزيادة المخزون للشهر القادم',
                            Icons.trending_up,
                            Colors.orange,
                            'تحذير',
                          ),
                          _buildRecommendationItem(
                            'تحسين دوران المخزون',
                            'معدل دوران المخزون ممتاز (12 مرة سنوياً)، استمر على هذا النهج',
                            Icons.refresh,
                            Colors.purple,
                            'ممتاز',
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات مقترحة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.settings_suggest,
                                  color: Colors.blue, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'إجراءات مقترحة',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _createPurchaseOrder,
                                  icon: const Icon(Icons.shopping_cart),
                                  label: const Text('إنشاء أمر شراء'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _setReorderPoint,
                                  icon: const Icon(Icons.notifications),
                                  label: const Text('تعديل نقطة الطلب'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _analyzeSeasonality,
                                  icon: const Icon(Icons.analytics),
                                  label: const Text('تحليل الموسمية'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _optimizeStock,
                                  icon: const Icon(Icons.tune),
                                  label: const Text('تحسين المخزون'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.teal,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // تحليل الحركة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تحليل الحركة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildAnalysisCard('عدد المعاملات', '15',
                                  Colors.blue, Icons.receipt),
                              _buildAnalysisCard('متوسط الحركة اليومية', '2.3',
                                  Colors.green, Icons.trending_up),
                              _buildAnalysisCard('أعلى كمية وارد', '25',
                                  Colors.orange, Icons.arrow_upward),
                              _buildAnalysisCard('أعلى كمية صادر', '18',
                                  Colors.red, Icons.arrow_downward),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<DataRow> _buildMovementRows() {
    return [
      const DataRow(cells: [
        DataCell(Text('2024-01-15')),
        DataCell(Text('مشتريات')),
        DataCell(Text('PUR-001')),
        DataCell(Text('25')),
        DataCell(Text('2,500')),
        DataCell(Text('62,500')),
        DataCell(Text('75')),
        DataCell(Text('فاتورة مشتريات')),
      ]),
      const DataRow(cells: [
        DataCell(Text('2024-01-16')),
        DataCell(Text('مبيعات')),
        DataCell(Text('SAL-001')),
        DataCell(Text('-8')),
        DataCell(Text('3,200')),
        DataCell(Text('25,600')),
        DataCell(Text('67')),
        DataCell(Text('فاتورة مبيعات')),
      ]),
      const DataRow(cells: [
        DataCell(Text('2024-01-18')),
        DataCell(Text('تحويل')),
        DataCell(Text('TRN-001')),
        DataCell(Text('-5')),
        DataCell(Text('2,500')),
        DataCell(Text('12,500')),
        DataCell(Text('62')),
        DataCell(Text('تحويل للفرع')),
      ]),
      const DataRow(cells: [
        DataCell(Text('2024-01-20')),
        DataCell(Text('مبيعات')),
        DataCell(Text('SAL-002')),
        DataCell(Text('-12')),
        DataCell(Text('3,200')),
        DataCell(Text('38,400')),
        DataCell(Text('50')),
        DataCell(Text('فاتورة مبيعات')),
      ]),
    ];
  }

  Widget _buildInfoCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAnalysisCard(
      String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecommendationItem(
    String title,
    String description,
    IconData icon,
    Color color,
    String priority,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: color.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: color.withOpacity(0.05),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: color,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        priority,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء التقرير بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _createPurchaseOrder() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إنشاء أمر شراء جديد بناءً على التوصيات'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _setReorderPoint() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث نقطة إعادة الطلب بناءً على تحليل الحركة'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _analyzeSeasonality() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تحليل الأنماط الموسمية للصنف...'),
        backgroundColor: Colors.purple,
      ),
    );
  }

  void _optimizeStock() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحسين مستويات المخزون بناءً على التحليل'),
        backgroundColor: Colors.teal,
      ),
    );
  }
}
