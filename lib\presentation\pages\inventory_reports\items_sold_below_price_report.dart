import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير أصناف بيعت أقل من سعر البيع
/// يعرض الأصناف التي تم بيعها بأسعار أقل من السعر المحدد
class ItemsSoldBelowPriceReportPage extends StatefulWidget {
  const ItemsSoldBelowPriceReportPage({super.key});

  @override
  State<ItemsSoldBelowPriceReportPage> createState() => _ItemsSoldBelowPriceReportPageState();
}

class _ItemsSoldBelowPriceReportPageState extends State<ItemsSoldBelowPriceReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedCategory;
  String? _selectedEmployee;
  String? _discountThreshold = '5';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('أصناف بيعت أقل من سعر البيع'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.warning),
            onPressed: _showAlerts,
            tooltip: 'عرض التنبيهات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          _buildFilterSection(localizations),
          
          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildDiscountAnalysisSection(),
                  const SizedBox(height: 16),
                  _buildItemsTableSection(),
                  const SizedBox(height: 16),
                  _buildEmployeeAnalysisSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.red[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.fromDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, true),
                  controller: TextEditingController(
                    text: _startDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.toDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, false),
                  controller: TextEditingController(
                    text: _endDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: localizations.category,
                    border: const OutlineInputBorder(),
                  ),
                  value: _selectedCategory,
                  items: [
                    DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                    DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                    DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                    DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                  ],
                  onChanged: (value) => setState(() => _selectedCategory = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'الموظف',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedEmployee,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الموظفين')),
                    DropdownMenuItem(value: 'emp1', child: Text('أحمد محمد')),
                    DropdownMenuItem(value: 'emp2', child: Text('فاطمة علي')),
                    DropdownMenuItem(value: 'emp3', child: Text('محمد سالم')),
                  ],
                  onChanged: (value) => setState(() => _selectedEmployee = value),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'حد الخصم (%)',
                    border: OutlineInputBorder(),
                  ),
                  value: _discountThreshold,
                  items: const [
                    DropdownMenuItem(value: '5', child: Text('أكثر من 5%')),
                    DropdownMenuItem(value: '10', child: Text('أكثر من 10%')),
                    DropdownMenuItem(value: '15', child: Text('أكثر من 15%')),
                    DropdownMenuItem(value: '20', child: Text('أكثر من 20%')),
                  ],
                  onChanged: (value) => setState(() => _discountThreshold = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.analytics),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.trending_down, color: Colors.red, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص المبيعات بخصم',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي العمليات', '185', Colors.red, Icons.receipt),
                _buildSummaryCard('قيمة الخصم', '45,000 ر.س', Colors.orange, Icons.money_off),
                _buildSummaryCard('متوسط الخصم', '12.5%', Colors.blue, Icons.percent),
                _buildSummaryCard('أعلى خصم', '35%', Colors.purple, Icons.warning),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDiscountAnalysisSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.analytics, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'تحليل الخصومات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildDiscountCard('خصم 5-10%', '85 عملية', Colors.green),
                _buildDiscountCard('خصم 10-20%', '65 عملية', Colors.orange),
                _buildDiscountCard('خصم 20-30%', '25 عملية', Colors.red),
                _buildDiscountCard('خصم +30%', '10 عمليات', Colors.purple),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل الأصناف المباعة بخصم',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('اسم الصنف')),
                  DataColumn(label: Text('السعر الأصلي')),
                  DataColumn(label: Text('سعر البيع')),
                  DataColumn(label: Text('نسبة الخصم')),
                  DataColumn(label: Text('قيمة الخصم')),
                  DataColumn(label: Text('الكمية')),
                  DataColumn(label: Text('الموظف')),
                  DataColumn(label: Text('التاريخ')),
                  DataColumn(label: Text('السبب')),
                ],
                rows: _buildItemsRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmployeeAnalysisSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.person, color: Colors.teal, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'تحليل أداء الموظفين',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildEmployeeAnalysisList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _setDiscountLimits,
                    icon: const Icon(Icons.security),
                    label: const Text('تحديد حدود الخصم'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _sendWarnings,
                    icon: const Icon(Icons.warning),
                    label: const Text('إرسال تحذيرات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _reviewPolicies,
                    icon: const Icon(Icons.policy),
                    label: const Text('مراجعة السياسات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _generateTrainingPlan,
                    icon: const Icon(Icons.school),
                    label: const Text('خطة تدريب'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildEmployeeAnalysisList() {
    final employees = [
      {'name': 'أحمد محمد', 'discounts': '45 خصم', 'avgDiscount': '8.5%', 'totalLoss': '12,500 ر.س', 'status': 'جيد'},
      {'name': 'فاطمة علي', 'discounts': '65 خصم', 'avgDiscount': '15.2%', 'totalLoss': '18,750 ر.س', 'status': 'يحتاج مراجعة'},
      {'name': 'محمد سالم', 'discounts': '35 خصم', 'avgDiscount': '22.8%', 'totalLoss': '25,200 ر.س', 'status': 'تحذير'},
      {'name': 'سارة أحمد', 'discounts': '25 خصم', 'avgDiscount': '6.2%', 'totalLoss': '5,800 ر.س', 'status': 'ممتاز'},
    ];

    return employees.map((emp) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: _getStatusColor(emp['status']!).withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: _getStatusColor(emp['status']!).withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.person, color: Colors.teal, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  emp['name']!,
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  '${emp['discounts']} • متوسط: ${emp['avgDiscount']} • خسارة: ${emp['totalLoss']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getStatusColor(emp['status']!).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              emp['status']!,
              style: TextStyle(
                color: _getStatusColor(emp['status']!),
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildItemsRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('4,500 ر.س')),
        const DataCell(Text('3,600 ر.س')),
        DataCell(_buildDiscountBadge('20%', Colors.red)),
        const DataCell(Text('900 ر.س')),
        const DataCell(Text('2')),
        const DataCell(Text('أحمد محمد')),
        const DataCell(Text('2024-01-15')),
        const DataCell(Text('عميل مميز')),
      ]),
      DataRow(cells: [
        const DataCell(Text('هاتف آيفون 15')),
        const DataCell(Text('3,800 ر.س')),
        const DataCell(Text('3,420 ر.س')),
        DataCell(_buildDiscountBadge('10%', Colors.orange)),
        const DataCell(Text('380 ر.س')),
        const DataCell(Text('1')),
        const DataCell(Text('فاطمة علي')),
        const DataCell(Text('2024-01-16')),
        const DataCell(Text('تصفية مخزون')),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDiscountCard(String range, String count, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(range, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDiscountBadge(String discount, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(discount, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'ممتاز':
        return Colors.green;
      case 'جيد':
        return Colors.blue;
      case 'يحتاج مراجعة':
        return Colors.orange;
      case 'تحذير':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير الأصناف المباعة بخصم بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showAlerts() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تنبيهات الخصومات')),
    );
  }

  void _setDiscountLimits() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحديد حدود الخصم للموظفين')),
    );
  }

  void _sendWarnings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إرسال تحذيرات للموظفين')),
    );
  }

  void _reviewPolicies() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مراجعة سياسات الخصم')),
    );
  }

  void _generateTrainingPlan() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء خطة تدريب للموظفين')),
    );
  }
}
