import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../widgets/global_buttons/global_buttons.dart';

/// صفحة إدارة التصنيفات
class CategoriesPage extends StatefulWidget {
  const CategoriesPage({super.key});

  @override
  State<CategoriesPage> createState() => _CategoriesPageState();
}

class _CategoriesPageState extends State<CategoriesPage> {
  // متحكمات النصوص
  final TextEditingController _categoryTypeController = TextEditingController();
  final TextEditingController _categoryNumberController =
      TextEditingController();
  final TextEditingController _arabicNameController = TextEditingController();
  final TextEditingController _englishNameController = TextEditingController();
  final TextEditingController _commissionController = TextEditingController();

  // متغيرات الحالة
  bool _isLoading = false;
  bool _isEditing = false;
  int? _selectedRowIndex;

  // بيانات التصنيفات
  final List<Map<String, dynamic>> _categories = [
    {
      'type': 'تصنيف رئيسي',
      'number': '001',
      'arabicName': 'إلكترونيات',
      'englishName': 'Electronics',
      'commission': '5.0',
    },
    {
      'type': 'تصنيف فرعي',
      'number': '002',
      'arabicName': 'هواتف ذكية',
      'englishName': 'Smart Phones',
      'commission': '7.5',
    },
    {
      'type': 'تصنيف منتجات',
      'number': '003',
      'arabicName': 'ملابس',
      'englishName': 'Clothes',
      'commission': '10.0',
    },
    {
      'type': 'تصنيف خدمات',
      'number': '004',
      'arabicName': 'صيانة',
      'englishName': 'Maintenance',
      'commission': '15.0',
    },
  ];

  @override
  void dispose() {
    _categoryTypeController.dispose();
    _categoryNumberController.dispose();
    _arabicNameController.dispose();
    _englishNameController.dispose();
    _commissionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة التصنيفات'),
        backgroundColor: Colors.pink,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          // شريط الأزرار العلوي
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              border: Border(
                bottom: BorderSide(color: Colors.grey[300]!),
              ),
            ),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  // زر إضافة
                  GlobalButtons.newButton(
                    onPressed: _addNewCategory,
                    tooltip: 'إضافة تصنيف جديد',
                    isLoading: _isLoading,
                  ),
                  const SizedBox(width: 8),

                  // زر استعلام
                  GlobalButtons.queryButton(
                    onPressed: _queryCategory,
                    tooltip: 'استعلام عن تصنيف',
                    isLoading: _isLoading,
                  ),
                  const SizedBox(width: 8),

                  // زر تعديل
                  GlobalButtons.editButton(
                    onPressed: _editCategory,
                    tooltip: 'تعديل التصنيف المحدد',
                    isLoading: _isLoading,
                    isDisabled: _selectedRowIndex == null,
                  ),
                  const SizedBox(width: 8),

                  // زر طباعة
                  GlobalButtons.printButton(
                    onPressed: _showPrintDialog,
                    tooltip: 'طباعة قائمة التصنيفات',
                    isLoading: _isLoading,
                  ),
                  const SizedBox(width: 8),

                  // زر حذف
                  GlobalButtons.deleteButton(
                    onPressed: _deleteCategory,
                    tooltip: 'حذف التصنيف',
                    isLoading: _isLoading,
                    isDisabled: !_isEditing,
                  ),
                  const SizedBox(width: 16),

                  // زر تراجع
                  GlobalButtons.undoButton(
                    onPressed: _undoChanges,
                    tooltip: 'تراجع عن التغييرات',
                    isLoading: _isLoading,
                    isDisabled: !_isEditing,
                  ),
                  const SizedBox(width: 8),

                  // زر حفظ
                  GlobalButtons.saveButton(
                    onPressed: _saveCategory,
                    tooltip: 'حفظ التصنيف',
                    isLoading: _isLoading,
                    isDisabled: !_isEditing,
                  ),
                ],
              ),
            ),
          ),

          // محتوى الصفحة
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // كرت إدخال البيانات
                  Expanded(
                    flex: 3,
                    child: _buildInputCard(),
                  ),
                  const SizedBox(height: 16),

                  // كرت عرض الجدول
                  Expanded(
                    flex: 2,
                    child: _buildTableCard(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء كرت إدخال البيانات
  Widget _buildInputCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان الكرت
            Row(
              children: [
                Icon(
                  Icons.category,
                  color: Colors.pink[700],
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'بيانات التصنيف',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.pink[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // الحقول في صفين
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // الصف الأول
                    Row(
                      children: [
                        Expanded(
                          child: _buildTextField(
                            label: 'نوع التصنيف',
                            controller: _categoryTypeController,
                            hint: 'ادخل نوع التصنيف',
                            icon: Icons.category,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildNumberField(
                            label: 'رقم التصنيف',
                            controller: _categoryNumberController,
                            hint: 'ادخل رقم التصنيف',
                            icon: Icons.numbers,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),

                    // الصف الثاني
                    Row(
                      children: [
                        Expanded(
                          child: _buildTextField(
                            label: 'الاسم العربي',
                            controller: _arabicNameController,
                            hint: 'ادخل اسم التصنيف بالعربية',
                            icon: Icons.text_fields,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildTextField(
                            label: 'الاسم الإنجليزي',
                            controller: _englishNameController,
                            hint: 'ادخل اسم التصنيف بالإنجليزية',
                            icon: Icons.translate,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),

                    // الصف الثالث
                    Row(
                      children: [
                        Expanded(
                          child: _buildNumberField(
                            label: 'العمولة (%)',
                            controller: _commissionController,
                            hint: 'ادخل نسبة العمولة',
                            icon: Icons.percent,
                            suffix: '%',
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: _selectedRowIndex != null
                                  ? Colors.green[50]
                                  : Colors.blue[50],
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: _selectedRowIndex != null
                                    ? Colors.green[200]!
                                    : Colors.blue[200]!,
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  _selectedRowIndex != null
                                      ? Icons.check_circle_outline
                                      : Icons.info_outline,
                                  color: _selectedRowIndex != null
                                      ? Colors.green[700]
                                      : Colors.blue[700],
                                  size: 18,
                                ),
                                const SizedBox(width: 6),
                                Expanded(
                                  child: Text(
                                    _selectedRowIndex != null
                                        ? 'صف ${_selectedRowIndex! + 1}'
                                        : 'اختر صف للتعديل',
                                    style: TextStyle(
                                      color: _selectedRowIndex != null
                                          ? Colors.green[700]
                                          : Colors.blue[700],
                                      fontSize: 11,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء كرت عرض الجدول
  Widget _buildTableCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان الكرت
            Row(
              children: [
                Icon(
                  Icons.table_chart,
                  color: Colors.pink[700],
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'قائمة التصنيفات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.pink[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // تلميح للمستخدم
            Container(
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: Colors.pink[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.pink[200]!),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.touch_app,
                    color: Colors.pink[600],
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'نقرة واحدة لتحديد الصف • نقرتان متتاليتان للتعديل',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.pink[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // الجدول مع عناوين ثابتة
            Expanded(
              child: Column(
                children: [
                  // صف العناوين الثابت
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.pink[50],
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(8),
                        topRight: Radius.circular(8),
                      ),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Container(
                        width: MediaQuery.of(context).size.width > 800
                            ? null
                            : MediaQuery.of(context).size.width - 64,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Row(
                          children: [
                            Expanded(
                              flex: 1,
                              child: Container(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12),
                                child: const Text(
                                  'الرقم',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                    color: Colors.black87,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              flex: 3,
                              child: Container(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12),
                                child: const Text(
                                  'الاسم العربي',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                    color: Colors.black87,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              flex: 2,
                              child: Container(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12),
                                child: const Text(
                                  'النوع',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                    color: Colors.black87,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              flex: 2,
                              child: Container(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12),
                                child: const Text(
                                  'العمولة %',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                    color: Colors.black87,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // منطقة البيانات القابلة للاسكرول
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border(
                          left: BorderSide(color: Colors.grey[300]!),
                          right: BorderSide(color: Colors.grey[300]!),
                          bottom: BorderSide(color: Colors.grey[300]!),
                        ),
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(8),
                          bottomRight: Radius.circular(8),
                        ),
                      ),
                      child: SingleChildScrollView(
                        child: Column(
                          children: _categories.asMap().entries.map((entry) {
                            final index = entry.key;
                            final category = entry.value;
                            return MouseRegion(
                              cursor: SystemMouseCursors.click,
                              child: GestureDetector(
                                onTap: () {
                                  setState(() {
                                    _selectedRowIndex =
                                        _selectedRowIndex == index
                                            ? null
                                            : index;
                                  });
                                },
                                onDoubleTap: () {
                                  // تعديل التصنيف بالنقر المزدوج
                                  final category = _categories[index];
                                  setState(() {
                                    _isEditing = true;
                                    _categoryTypeController.text =
                                        category['type'];
                                    _categoryNumberController.text =
                                        category['number'];
                                    _arabicNameController.text =
                                        category['arabicName'];
                                    _englishNameController.text =
                                        category['englishName'];
                                    _commissionController.text =
                                        category['commission'];
                                  });

                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Row(
                                        children: [
                                          Icon(
                                            Icons.edit,
                                            color: Colors.white,
                                            size: 20,
                                          ),
                                          const SizedBox(width: 8),
                                          Expanded(
                                            child: Text(
                                              'تم تحميل بيانات التصنيف "${category['arabicName']}" للتعديل',
                                              style:
                                                  const TextStyle(fontSize: 14),
                                            ),
                                          ),
                                        ],
                                      ),
                                      backgroundColor: Colors.pink[600],
                                      duration: const Duration(seconds: 3),
                                      behavior: SnackBarBehavior.floating,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      margin: const EdgeInsets.all(16),
                                    ),
                                  );
                                },
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: _selectedRowIndex == index
                                        ? Colors.grey[100]
                                        : null,
                                    border: Border(
                                      bottom: BorderSide(
                                        color: Colors.grey[200]!,
                                        width: 1,
                                      ),
                                    ),
                                  ),
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 8),
                                    child: Row(
                                      children: [
                                        // عمود الرقم
                                        Expanded(
                                          flex: 1,
                                          child: Text(
                                            category['number'],
                                            style: const TextStyle(
                                              fontSize: 13,
                                              fontWeight: FontWeight.w600,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                        const SizedBox(width: 16),
                                        // عمود الاسم العربي
                                        Expanded(
                                          flex: 3,
                                          child: Text(
                                            category['arabicName'],
                                            style:
                                                const TextStyle(fontSize: 13),
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                        const SizedBox(width: 16),
                                        // عمود النوع
                                        Expanded(
                                          flex: 2,
                                          child: Text(
                                            category['type'],
                                            style:
                                                const TextStyle(fontSize: 13),
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                        const SizedBox(width: 16),
                                        // عمود العمولة
                                        Expanded(
                                          flex: 2,
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8,
                                              vertical: 4,
                                            ),
                                            decoration: BoxDecoration(
                                              color: Colors.green[50],
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                              border: Border.all(
                                                  color: Colors.green[200]!),
                                            ),
                                            child: Text(
                                              '${category['commission']}%',
                                              style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.w600,
                                                color: Colors.green[700],
                                              ),
                                              textAlign: TextAlign.center,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حقل إدخال نصي
  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    required String hint,
    required IconData icon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: Icon(icon, color: Colors.pink[600]),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.pink[600]!, width: 2),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          onChanged: (value) {
            if (!_isEditing) {
              setState(() {
                _isEditing = true;
              });
            }
          },
        ),
      ],
    );
  }

  /// بناء حقل إدخال رقمي
  Widget _buildNumberField({
    required String label,
    required TextEditingController controller,
    required String hint,
    required IconData icon,
    String? suffix,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
          ],
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: Icon(icon, color: Colors.pink[600]),
            suffixText: suffix,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.pink[600]!, width: 2),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          onChanged: (value) {
            if (!_isEditing) {
              setState(() {
                _isEditing = true;
              });
            }
          },
        ),
      ],
    );
  }

  /// إضافة تصنيف جديد
  void _addNewCategory() {
    setState(() {
      _isEditing = true;
      _selectedRowIndex = null;
      _categoryTypeController.clear();
      _categoryNumberController.clear();
      _arabicNameController.clear();
      _englishNameController.clear();
      _commissionController.clear();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاهز لإدخال تصنيف جديد'),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// استعلام عن تصنيف
  void _queryCategory() {
    showDialog(
      context: context,
      builder: (context) => _buildQueryDialog(),
    );
  }

  /// بناء نافذة الاستعلام
  Widget _buildQueryDialog() {
    // متحكمات البحث
    final TextEditingController searchTypeController = TextEditingController();
    final TextEditingController searchNumberController =
        TextEditingController();
    final TextEditingController searchNameController = TextEditingController();
    final TextEditingController searchCommissionController =
        TextEditingController();

    // قائمة النتائج المفلترة
    List<Map<String, dynamic>> filteredResults = List.from(_categories);

    return StatefulBuilder(
      builder: (context, setDialogState) {
        // متغير لتتبع الصف المحدد في جدول البحث
        int? selectedSearchRowIndex;

        // دالة البحث والفلترة
        void performSearch() {
          filteredResults = _categories.where((category) {
            bool matchesType = searchTypeController.text.isEmpty ||
                category['type']
                    .toString()
                    .toLowerCase()
                    .contains(searchTypeController.text.toLowerCase());

            bool matchesNumber = searchNumberController.text.isEmpty ||
                category['number']
                    .toString()
                    .contains(searchNumberController.text);

            bool matchesName = searchNameController.text.isEmpty ||
                category['arabicName']
                    .toString()
                    .toLowerCase()
                    .contains(searchNameController.text.toLowerCase()) ||
                category['englishName']
                    .toString()
                    .toLowerCase()
                    .contains(searchNameController.text.toLowerCase());

            bool matchesCommission = searchCommissionController.text.isEmpty ||
                category['commission']
                    .toString()
                    .contains(searchCommissionController.text);

            return matchesType &&
                matchesNumber &&
                matchesName &&
                matchesCommission;
          }).toList();

          setDialogState(() {});
        }

        // دالة إعادة تعيين البحث
        void resetSearch() {
          searchTypeController.clear();
          searchNumberController.clear();
          searchNameController.clear();
          searchCommissionController.clear();
          filteredResults = List.from(_categories);
          setDialogState(() {});
        }

        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.9,
            height: MediaQuery.of(context).size.height * 0.85,
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // عنوان النافذة
                Row(
                  children: [
                    Icon(
                      Icons.search,
                      color: Colors.blue[700],
                      size: 28,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'استعلام عن التصنيفات',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue[700],
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                      tooltip: 'إغلاق',
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // حقول البحث
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'معايير البحث',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue[700],
                        ),
                      ),
                      const SizedBox(height: 16),

                      // الصف الأول من حقول البحث
                      Row(
                        children: [
                          Expanded(
                            child: _buildSearchField(
                              controller: searchTypeController,
                              label: 'نوع التصنيف',
                              hint: 'ابحث بالنوع',
                              icon: Icons.category,
                              onChanged: (value) => performSearch(),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildSearchField(
                              controller: searchNumberController,
                              label: 'رقم التصنيف',
                              hint: 'ابحث بالرقم',
                              icon: Icons.numbers,
                              onChanged: (value) => performSearch(),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // الصف الثاني من حقول البحث
                      Row(
                        children: [
                          Expanded(
                            child: _buildSearchField(
                              controller: searchNameController,
                              label: 'اسم التصنيف',
                              hint: 'ابحث بالاسم',
                              icon: Icons.text_fields,
                              onChanged: (value) => performSearch(),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildSearchField(
                              controller: searchCommissionController,
                              label: 'العمولة %',
                              hint: 'ابحث بالعمولة',
                              icon: Icons.percent,
                              onChanged: (value) => performSearch(),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // إحصائيات النتائج
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green[200]!),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Colors.green[700],
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'تم العثور على ${filteredResults.length} من أصل ${_categories.length} تصنيف',
                        style: TextStyle(
                          color: Colors.green[700],
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // تلميح للمستخدم
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.touch_app,
                        color: Colors.blue[600],
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'نقرة واحدة لتحديد الصف • نقرتان متتاليتان للتعديل',
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.blue[700],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // جدول النتائج
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        // عناوين الجدول الثابتة
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.blue[50],
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(8),
                              topRight: Radius.circular(8),
                            ),
                          ),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 12),
                          child: Row(
                            children: [
                              Expanded(
                                flex: 1,
                                child: Text(
                                  'الرقم',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                    color: Colors.blue[700],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                flex: 3,
                                child: Text(
                                  'الاسم العربي',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                    color: Colors.blue[700],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                flex: 2,
                                child: Text(
                                  'النوع',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                    color: Colors.blue[700],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                flex: 2,
                                child: Text(
                                  'العمولة %',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                    color: Colors.blue[700],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // بيانات الجدول القابلة للاسكرول
                        Expanded(
                          child: filteredResults.isEmpty
                              ? Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.search_off,
                                        size: 64,
                                        color: Colors.grey[400],
                                      ),
                                      const SizedBox(height: 16),
                                      Text(
                                        'لا توجد نتائج مطابقة لمعايير البحث',
                                        style: TextStyle(
                                          fontSize: 16,
                                          color: Colors.grey[600],
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              : SingleChildScrollView(
                                  child: Column(
                                    children: filteredResults
                                        .asMap()
                                        .entries
                                        .map((entry) {
                                      final index = entry.key;
                                      final category = entry.value;
                                      return MouseRegion(
                                        cursor: SystemMouseCursors.click,
                                        child: GestureDetector(
                                          onTap: () {
                                            // تحديد الصف بالنقر الأيسر
                                            setDialogState(() {
                                              selectedSearchRowIndex =
                                                  selectedSearchRowIndex ==
                                                          index
                                                      ? null
                                                      : index;
                                            });
                                          },
                                          onDoubleTap: () {
                                            // تعديل التصنيف بالنقر المزدوج
                                            Navigator.pop(
                                                context); // إغلاق نافذة البحث
                                            Future.delayed(
                                                const Duration(
                                                    milliseconds: 100), () {
                                              _loadCategoryForEdit(category);
                                            });
                                          },
                                          child: Container(
                                            decoration: BoxDecoration(
                                              color: selectedSearchRowIndex ==
                                                      index
                                                  ? Colors.grey[100]
                                                  : null,
                                              border: Border(
                                                bottom: BorderSide(
                                                  color: Colors.grey[200]!,
                                                  width: 1,
                                                ),
                                              ),
                                            ),
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8,
                                              vertical: 12,
                                            ),
                                            child: Row(
                                              children: [
                                                Expanded(
                                                  flex: 1,
                                                  child: Text(
                                                    category['number'],
                                                    style: const TextStyle(
                                                      fontSize: 13,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                    ),
                                                    textAlign: TextAlign.center,
                                                  ),
                                                ),
                                                const SizedBox(width: 8),
                                                Expanded(
                                                  flex: 3,
                                                  child: Text(
                                                    category['arabicName'],
                                                    style: const TextStyle(
                                                        fontSize: 13),
                                                    textAlign: TextAlign.center,
                                                  ),
                                                ),
                                                const SizedBox(width: 8),
                                                Expanded(
                                                  flex: 2,
                                                  child: Text(
                                                    category['type'],
                                                    style: const TextStyle(
                                                        fontSize: 13),
                                                    textAlign: TextAlign.center,
                                                  ),
                                                ),
                                                const SizedBox(width: 8),
                                                Expanded(
                                                  flex: 2,
                                                  child: Container(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                      horizontal: 8,
                                                      vertical: 4,
                                                    ),
                                                    decoration: BoxDecoration(
                                                      color: Colors.green[50],
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              8),
                                                      border: Border.all(
                                                          color: Colors
                                                              .green[200]!),
                                                    ),
                                                    child: Text(
                                                      '${category['commission']}%',
                                                      style: TextStyle(
                                                        fontSize: 12,
                                                        fontWeight:
                                                            FontWeight.w600,
                                                        color:
                                                            Colors.green[700],
                                                      ),
                                                      textAlign:
                                                          TextAlign.center,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      );
                                    }).toList(),
                                  ),
                                ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                // أزرار النافذة
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // زر إعادة تعيين
                    ElevatedButton.icon(
                      onPressed: resetSearch,
                      icon: const Icon(Icons.refresh, color: Colors.white),
                      label: const Text(
                        'إعادة تعيين',
                        style: TextStyle(color: Colors.white),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange[600],
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),

                    // زر الإغلاق
                    ElevatedButton.icon(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close, color: Colors.white),
                      label: const Text(
                        'إغلاق',
                        style: TextStyle(color: Colors.white),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey[600],
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// تحميل بيانات التصنيف للتعديل
  void _loadCategoryForEdit(Map<String, dynamic> category) {
    setState(() {
      _isEditing = true;
      _selectedRowIndex = null;

      // تحميل البيانات في حقول الإدخال
      _categoryTypeController.text = category['type'];
      _categoryNumberController.text = category['number'];
      _arabicNameController.text = category['arabicName'];
      _englishNameController.text = category['englishName'];
      _commissionController.text = category['commission'];
    });

    // إظهار رسالة تأكيد
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.edit,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'تم تحميل بيانات التصنيف "${category['arabicName']}" للتعديل',
                style: const TextStyle(fontSize: 14),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.blue[600],
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  /// بناء حقل البحث
  Widget _buildSearchField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    required Function(String) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(
              fontSize: 13,
              color: Colors.grey[500],
            ),
            prefixIcon: Icon(icon, color: Colors.blue[600]),
            suffixIcon: controller.text.isNotEmpty
                ? IconButton(
                    icon: Icon(Icons.clear, color: Colors.grey[600]),
                    onPressed: () {
                      controller.clear();
                      onChanged('');
                    },
                  )
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.blue[600]!, width: 2),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            filled: true,
            fillColor: Colors.white,
          ),
          onChanged: onChanged,
        ),
      ],
    );
  }

  /// تعديل التصنيف المحدد
  void _editCategory() {
    if (_selectedRowIndex != null) {
      final category = _categories[_selectedRowIndex!];
      setState(() {
        _isEditing = true;
        _categoryTypeController.text = category['type'];
        _categoryNumberController.text = category['number'];
        _arabicNameController.text = category['arabicName'];
        _englishNameController.text = category['englishName'];
        _commissionController.text = category['commission'];
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم تحميل بيانات التصنيف للتعديل'),
          backgroundColor: Colors.blue,
        ),
      );
    }
  }

  /// حذف التصنيف
  void _deleteCategory() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا التصنيف؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _performDelete();
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  /// تنفيذ عملية الحذف
  void _performDelete() {
    setState(() {
      _isLoading = true;
    });

    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isEditing = false;
          _selectedRowIndex = null;
          _categoryTypeController.clear();
          _categoryNumberController.clear();
          _arabicNameController.clear();
          _englishNameController.clear();
          _commissionController.clear();
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف التصنيف بنجاح'),
            backgroundColor: Colors.red,
          ),
        );
      }
    });
  }

  /// تراجع عن التغييرات
  void _undoChanges() {
    setState(() {
      _isEditing = false;
      _selectedRowIndex = null;
      _categoryTypeController.clear();
      _categoryNumberController.clear();
      _arabicNameController.clear();
      _englishNameController.clear();
      _commissionController.clear();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم التراجع عن التغييرات'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  /// حفظ التصنيف
  void _saveCategory() {
    // التحقق من صحة البيانات
    if (_categoryTypeController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال نوع التصنيف'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (_arabicNameController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال الاسم العربي للتصنيف'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // محاكاة عملية الحفظ
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isEditing = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ التصنيف بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    });
  }

  /// عرض نافذة الطباعة
  void _showPrintDialog() {
    showDialog(
      context: context,
      builder: (context) => _buildPrintDialog(),
    );
  }

  /// بناء نافذة الطباعة
  Widget _buildPrintDialog() {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان النافذة
            Row(
              children: [
                Icon(
                  Icons.print,
                  color: Colors.pink[700],
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  'طباعة قائمة التصنيفات',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.pink[700],
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                  tooltip: 'إغلاق',
                ),
              ],
            ),
            const SizedBox(height: 24),

            // معلومات التقرير
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.blue[700],
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'تقرير يحتوي على ${_categories.length} تصنيف - ${DateTime.now().toString().split(' ')[0]}',
                      style: TextStyle(
                        color: Colors.blue[700],
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // قائمة التصنيفات
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    // رأس الجدول
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.pink[50],
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(8),
                          topRight: Radius.circular(8),
                        ),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            flex: 2,
                            child: Text(
                              'النوع',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.pink[700],
                                fontSize: 16,
                              ),
                            ),
                          ),
                          Expanded(
                            child: Text(
                              'الرقم',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.pink[700],
                                fontSize: 16,
                              ),
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: Text(
                              'الاسم العربي',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.pink[700],
                                fontSize: 16,
                              ),
                            ),
                          ),
                          Expanded(
                            child: Text(
                              'العمولة %',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.pink[700],
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // محتوى الجدول
                    Expanded(
                      child: ListView.builder(
                        itemCount: _categories.length,
                        itemBuilder: (context, index) {
                          final category = _categories[index];
                          return Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              border: Border(
                                bottom: BorderSide(
                                  color: Colors.grey[200]!,
                                  width: 1,
                                ),
                              ),
                              color: index % 2 == 0
                                  ? Colors.white
                                  : Colors.grey[25],
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  flex: 2,
                                  child: Text(
                                    category['type'],
                                    style: const TextStyle(fontSize: 14),
                                  ),
                                ),
                                Expanded(
                                  child: Text(
                                    category['number'],
                                    style: const TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                                Expanded(
                                  flex: 2,
                                  child: Text(
                                    category['arabicName'],
                                    style: const TextStyle(fontSize: 14),
                                  ),
                                ),
                                Expanded(
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 4,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.green[50],
                                      borderRadius: BorderRadius.circular(12),
                                      border:
                                          Border.all(color: Colors.green[200]!),
                                    ),
                                    child: Text(
                                      '${category['commission']}%',
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.green[700],
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // أزرار النافذة
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // زر الإلغاء
                GlobalButtons.cancelButton(
                  onPressed: () => Navigator.pop(context),
                  tooltip: 'إغلاق نافذة الطباعة',
                ),
                const SizedBox(width: 16),

                // زر الطباعة
                GlobalButtons.printButton(
                  onPressed: _performPrint,
                  tooltip: 'طباعة قائمة التصنيفات',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// تنفيذ عملية الطباعة
  void _performPrint() {
    Navigator.pop(context);

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إرسال قائمة التصنيفات للطباعة'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 3),
      ),
    );
  }
}
