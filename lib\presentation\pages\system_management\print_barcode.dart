import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة طباعة باركود
/// تتيح طباعة باركود للأصناف بتنسيقات مختلفة
class PrintBarcodePage extends StatefulWidget {
  const PrintBarcodePage({super.key});

  @override
  State<PrintBarcodePage> createState() => _PrintBarcodePageState();
}

class _PrintBarcodePageState extends State<PrintBarcodePage> {
  final _searchController = TextEditingController();
  final _quantityController = TextEditingController(text: '1');

  String _searchQuery = '';
  String? _selectedItem;
  String? _selectedBarcodeType;
  String? _selectedLabelSize;
  String? _selectedPrinter;
  bool _includeItemName = true;
  bool _includePrice = false;
  bool _includeDate = false;

  final List<Map<String, String>> _barcodeTypes = [
    {'id': 'code128', 'name': 'Code 128', 'description': 'الأكثر شيوعاً'},
    {
      'id': 'code39',
      'name': 'Code 39',
      'description': 'متوافق مع الأنظمة القديمة'
    },
    {'id': 'ean13', 'name': 'EAN-13', 'description': 'للمنتجات التجارية'},
    {'id': 'qr', 'name': 'QR Code', 'description': 'يحتوي على معلومات إضافية'},
  ];

  final List<Map<String, String>> _labelSizes = [
    {
      'id': 'small',
      'name': '2.5 × 1.5 سم',
      'description': 'صغير - للأصناف الصغيرة'
    },
    {
      'id': 'medium',
      'name': '4 × 2 سم',
      'description': 'متوسط - الحجم القياسي'
    },
    {
      'id': 'large',
      'name': '6 × 3 سم',
      'description': 'كبير - للأصناف الكبيرة'
    },
    {'id': 'custom', 'name': 'مخصص', 'description': 'حجم مخصص'},
  ];

  final List<Map<String, String>> _printers = [
    {'id': 'zebra1', 'name': 'Zebra ZD420', 'status': 'متصل'},
    {'id': 'brother1', 'name': 'Brother QL-800', 'status': 'متصل'},
    {'id': 'default', 'name': 'الطابعة الافتراضية', 'status': 'متصل'},
    {'id': 'pdf', 'name': 'حفظ كـ PDF', 'status': 'متاح'},
  ];

  final List<Map<String, dynamic>> _items = [
    {
      'id': 'item1',
      'code': 'A001',
      'name': 'لابتوب ديل XPS 13',
      'barcode': '1234567890123',
      'price': 2500.0,
      'category': 'إلكترونيات',
    },
    {
      'id': 'item2',
      'code': 'B002',
      'name': 'طابعة HP LaserJet Pro',
      'barcode': '2345678901234',
      'price': 450.0,
      'category': 'إلكترونيات',
    },
    {
      'id': 'item3',
      'code': 'C003',
      'name': 'كرسي مكتب جلد طبيعي',
      'barcode': '3456789012345',
      'price': 350.0,
      'category': 'أثاث',
    },
    {
      'id': 'item4',
      'code': 'D004',
      'name': 'شاشة سامسونج 27 بوصة',
      'barcode': '4567890123456',
      'price': 800.0,
      'category': 'إلكترونيات',
    },
  ];

  List<Map<String, dynamic>> get _filteredItems {
    if (_searchQuery.isEmpty) return _items;
    return _items
        .where((item) =>
            item['name']
                .toString()
                .toLowerCase()
                .contains(_searchQuery.toLowerCase()) ||
            item['code']
                .toString()
                .toLowerCase()
                .contains(_searchQuery.toLowerCase()) ||
            item['barcode'].toString().contains(_searchQuery))
        .toList();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _quantityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.printBarcode),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showPrinterSettings,
            tooltip: 'إعدادات الطابعة',
          ),
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: _showPrintHistory,
            tooltip: 'سجل الطباعة',
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          // بطاقة اختيار الصنف
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'اختيار الصنف',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.indigo,
                    ),
                  ),
                  const SizedBox(height: 16),

                  TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      labelText: 'البحث بالاسم أو الكود أو الباركود',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),

                  const SizedBox(height: 16),

                  // قائمة الأصناف
                  Container(
                    height: 200,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: ListView.builder(
                      itemCount: _filteredItems.length,
                      itemBuilder: (context, index) {
                        final item = _filteredItems[index];
                        final isSelected = _selectedItem == item['id'];

                        return ListTile(
                          selected: isSelected,
                          leading: CircleAvatar(
                            backgroundColor:
                                isSelected ? Colors.indigo : Colors.grey,
                            child: Text(
                              item['code'].substring(0, 1),
                              style: const TextStyle(color: Colors.white),
                            ),
                          ),
                          title: Text('${item['code']} - ${item['name']}'),
                          subtitle: Text(
                              'باركود: ${item['barcode']} | السعر: ${item['price']} ر.س'),
                          onTap: () {
                            setState(() {
                              _selectedItem = item['id'];
                            });
                          },
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // بطاقة إعدادات الباركود
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'إعدادات الباركود',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.indigo,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      // نوع الباركود
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedBarcodeType,
                          decoration: const InputDecoration(
                            labelText: 'نوع الباركود',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 8),
                          ),
                          items: _barcodeTypes
                              .map<DropdownMenuItem<String>>((type) {
                            return DropdownMenuItem<String>(
                              value: type['id'],
                              child: Text(
                                type['name']!,
                                overflow: TextOverflow.ellipsis,
                              ),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedBarcodeType = value;
                            });
                          },
                        ),
                      ),

                      const SizedBox(width: 16),

                      // حجم الملصق
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedLabelSize,
                          decoration: const InputDecoration(
                            labelText: 'حجم الملصق',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 8),
                          ),
                          items:
                              _labelSizes.map<DropdownMenuItem<String>>((size) {
                            return DropdownMenuItem<String>(
                              value: size['id'],
                              child: Text(
                                size['name']!,
                                overflow: TextOverflow.ellipsis,
                              ),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedLabelSize = value;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      // الكمية
                      Expanded(
                        child: TextFormField(
                          controller: _quantityController,
                          decoration: const InputDecoration(
                            labelText: 'عدد النسخ',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 8),
                          ),
                          keyboardType: TextInputType.number,
                        ),
                      ),

                      const SizedBox(width: 16),

                      // الطابعة
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedPrinter,
                          decoration: const InputDecoration(
                            labelText: 'الطابعة',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 8),
                          ),
                          items: _printers
                              .map<DropdownMenuItem<String>>((printer) {
                            return DropdownMenuItem<String>(
                              value: printer['id'],
                              child: Text(
                                printer['name']!,
                                overflow: TextOverflow.ellipsis,
                              ),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedPrinter = value;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // بطاقة خيارات إضافية
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'خيارات إضافية',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.indigo,
                    ),
                  ),
                  const SizedBox(height: 16),
                  CheckboxListTile(
                    title: const Text('تضمين اسم الصنف'),
                    subtitle: const Text('إظهار اسم الصنف أسفل الباركود'),
                    value: _includeItemName,
                    onChanged: (value) {
                      setState(() {
                        _includeItemName = value ?? true;
                      });
                    },
                    dense: true,
                  ),
                  CheckboxListTile(
                    title: const Text('تضمين السعر'),
                    subtitle: const Text('إظهار سعر البيع على الملصق'),
                    value: _includePrice,
                    onChanged: (value) {
                      setState(() {
                        _includePrice = value ?? false;
                      });
                    },
                    dense: true,
                  ),
                  CheckboxListTile(
                    title: const Text('تضمين التاريخ'),
                    subtitle: const Text('إظهار تاريخ الطباعة'),
                    value: _includeDate,
                    onChanged: (value) {
                      setState(() {
                        _includeDate = value ?? false;
                      });
                    },
                    dense: true,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // معاينة الباركود
          if (_selectedItem != null)
            Card(
              color: Colors.indigo.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'معاينة الباركود',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.indigo,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Center(
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          children: [
                            // محاكاة الباركود
                            Container(
                              height: 60,
                              width: 200,
                              decoration: BoxDecoration(
                                color: Colors.black,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: const Center(
                                child: Text(
                                  '||||| |||| ||||| ||||',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontFamily: 'monospace',
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              _getSelectedItemBarcode(),
                              style: const TextStyle(
                                fontSize: 12,
                                fontFamily: 'monospace',
                              ),
                            ),
                            if (_includeItemName) ...[
                              const SizedBox(height: 4),
                              Text(
                                _getSelectedItemName(),
                                style: const TextStyle(fontSize: 10),
                                textAlign: TextAlign.center,
                              ),
                            ],
                            if (_includePrice) ...[
                              const SizedBox(height: 4),
                              Text(
                                '${_getSelectedItemPrice()} ر.س',
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                            if (_includeDate) ...[
                              const SizedBox(height: 4),
                              Text(
                                '${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
                                style: const TextStyle(fontSize: 8),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

          const SizedBox(height: 24),

          // أزرار العمليات
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _selectedItem != null ? _previewPrint : null,
                  icon: const Icon(Icons.preview),
                  label: const Text('معاينة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _selectedItem != null ? _printBarcode : null,
                  icon: const Icon(Icons.print),
                  label: const Text('طباعة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.indigo,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getSelectedItemBarcode() {
    if (_selectedItem == null) return '';
    final item = _items.firstWhere((i) => i['id'] == _selectedItem);
    return item['barcode'];
  }

  String _getSelectedItemName() {
    if (_selectedItem == null) return '';
    final item = _items.firstWhere((i) => i['id'] == _selectedItem);
    return item['name'];
  }

  String _getSelectedItemPrice() {
    if (_selectedItem == null) return '';
    final item = _items.firstWhere((i) => i['id'] == _selectedItem);
    return item['price'].toString();
  }

  void _previewPrint() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content:
            Text('معاينة طباعة ${_quantityController.text} نسخة من الباركود'),
      ),
    );
  }

  void _printBarcode() {
    if (_selectedBarcodeType == null ||
        _selectedLabelSize == null ||
        _selectedPrinter == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إكمال جميع الإعدادات المطلوبة'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final item = _items.firstWhere((i) => i['id'] == _selectedItem);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الطباعة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الصنف: ${item['name']}'),
            Text(
                'نوع الباركود: ${_barcodeTypes.firstWhere((t) => t['id'] == _selectedBarcodeType)['name']}'),
            Text(
                'حجم الملصق: ${_labelSizes.firstWhere((s) => s['id'] == _selectedLabelSize)['name']}'),
            Text('عدد النسخ: ${_quantityController.text}'),
            Text(
                'الطابعة: ${_printers.firstWhere((p) => p['id'] == _selectedPrinter)['name']}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إرسال الطباعة بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.indigo),
            child: const Text('طباعة', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showPrinterSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إعدادات الطابعة')),
    );
  }

  void _showPrintHistory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سجل طباعة الباركود')),
    );
  }
}
