import 'package:flutter/material.dart';

class ApproveUnapprovedTransferVouchersButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String? tooltip;
  final bool isLoading;
  final bool isDisabled;

  const ApproveUnapprovedTransferVouchersButton({
    super.key,
    this.onPressed,
    this.tooltip = 'Approve Unapproved Transfer Vouchers',
    this.isLoading = false,
    this.isDisabled = false,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip ?? 'Approve Unapproved Transfer Vouchers',
      child: ElevatedButton.icon(
        onPressed: isDisabled || isLoading ? null : onPressed,
        icon: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              )
            : const Icon(Icons.approval),
        label: const Text('Approve Unapproved Transfer Vouchers'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green.shade800,
          foregroundColor: Colors.white,
        ),
      ),
    );
  }
}
