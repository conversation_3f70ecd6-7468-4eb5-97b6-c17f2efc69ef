import 'package:flutter/material.dart';

/// صفحة التدفقات النقدية
/// تعرض تقرير التدفقات النقدية التشغيلية والاستثمارية والتمويلية
class CashFlowsPage extends StatefulWidget {
  const CashFlowsPage({super.key});

  @override
  State<CashFlowsPage> createState() => _CashFlowsPageState();
}

class _CashFlowsPageState extends State<CashFlowsPage> {
  String _selectedPeriod = 'current_quarter';

  // بيانات تجريبية للتدفقات النقدية
  final Map<String, Map<String, dynamic>> _cashFlowData = {
    'current_quarter': {
      'period': 'الربع الحالي',
      'openingCash': 150000.0,
      'closingCash': 185000.0,
      'netCashFlow': 35000.0,
      'operatingActivities': [
        {'description': 'النقد المحصل من العملاء', 'amount': 450000.0, 'type': 'inflow'},
        {'description': 'النقد المدفوع للموردين', 'amount': -320000.0, 'type': 'outflow'},
        {'description': 'النقد المدفوع للموظفين', 'amount': -85000.0, 'type': 'outflow'},
        {'description': 'النقد المدفوع للمصروفات التشغيلية', 'amount': -25000.0, 'type': 'outflow'},
      ],
      'investingActivities': [
        {'description': 'شراء معدات', 'amount': -15000.0, 'type': 'outflow'},
        {'description': 'بيع أصول ثابتة', 'amount': 8000.0, 'type': 'inflow'},
        {'description': 'استثمارات قصيرة الأجل', 'amount': -5000.0, 'type': 'outflow'},
      ],
      'financingActivities': [
        {'description': 'قرض بنكي جديد', 'amount': 50000.0, 'type': 'inflow'},
        {'description': 'سداد أقساط قروض', 'amount': -20000.0, 'type': 'outflow'},
        {'description': 'توزيعات أرباح', 'amount': -15000.0, 'type': 'outflow'},
      ],
    },
  };

  @override
  Widget build(BuildContext context) {
    final currentData = _cashFlowData[_selectedPeriod]!;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('التدفقات النقدية'),
        backgroundColor: Colors.lightBlue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedPeriod,
                    decoration: const InputDecoration(
                      labelText: 'الفترة',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'current_quarter', child: Text('الربع الحالي')),
                      DropdownMenuItem(value: 'current_year', child: Text('السنة الحالية')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedPeriod = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
          ),

          // ملخص التدفقات النقدية
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              color: Colors.lightBlue[50],
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  children: [
                    Text(
                      'تقرير التدفقات النقدية - ${currentData['period']}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Column(
                          children: [
                            const Text('النقد في بداية الفترة'),
                            Text(
                              '${currentData['openingCash']} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('النقد في نهاية الفترة'),
                            Text(
                              '${currentData['closingCash']} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('صافي التدفق النقدي'),
                            Text(
                              '${currentData['netCashFlow']} ر.س',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: currentData['netCashFlow'] >= 0 ? Colors.green : Colors.red,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                _buildStatCard('التدفق التشغيلي', '${_calculateNetFlow(currentData['operatingActivities'])} ر.س', Colors.green),
                _buildStatCard('التدفق الاستثماري', '${_calculateNetFlow(currentData['investingActivities'])} ر.س', Colors.orange),
                _buildStatCard('التدفق التمويلي', '${_calculateNetFlow(currentData['financingActivities'])} ر.س', Colors.blue),
                _buildStatCard('صافي التدفق', '${currentData['netCashFlow']} ر.س', Colors.purple),
              ],
            ),
          ),

          // تفاصيل التدفقات
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(16.0),
              children: [
                // الأنشطة التشغيلية
                _buildCashFlowSection(
                  'الأنشطة التشغيلية',
                  currentData['operatingActivities'],
                  Colors.green,
                  Icons.business,
                ),

                const SizedBox(height: 8),

                // الأنشطة الاستثمارية
                _buildCashFlowSection(
                  'الأنشطة الاستثمارية',
                  currentData['investingActivities'],
                  Colors.orange,
                  Icons.trending_up,
                ),

                const SizedBox(height: 8),

                // الأنشطة التمويلية
                _buildCashFlowSection(
                  'الأنشطة التمويلية',
                  currentData['financingActivities'],
                  Colors.blue,
                  Icons.account_balance,
                ),

                const SizedBox(height: 16),

                // ملخص التدفقات
                Card(
                  color: Colors.grey[100],
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'ملخص التدفقات النقدية',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 12),
                        _buildSummaryRow('النقد من الأنشطة التشغيلية', _calculateNetFlow(currentData['operatingActivities'])),
                        _buildSummaryRow('النقد من الأنشطة الاستثمارية', _calculateNetFlow(currentData['investingActivities'])),
                        _buildSummaryRow('النقد من الأنشطة التمويلية', _calculateNetFlow(currentData['financingActivities'])),
                        const Divider(),
                        _buildSummaryRow('صافي التغير في النقد', currentData['netCashFlow'], isTotal: true),
                        _buildSummaryRow('النقد في بداية الفترة', currentData['openingCash']),
                        const Divider(),
                        _buildSummaryRow('النقد في نهاية الفترة', currentData['closingCash'], isTotal: true),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshData,
        backgroundColor: Colors.lightBlue,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث البيانات'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCashFlowSection(String title, List<Map<String, dynamic>> activities, Color color, IconData icon) {
    double netFlow = _calculateNetFlow(activities);
    
    return Card(
      child: ExpansionTile(
        leading: CircleAvatar(
          backgroundColor: color,
          child: Icon(icon, color: Colors.white),
        ),
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
        ),
        subtitle: Text(
          'صافي التدفق: ${netFlow.toStringAsFixed(2)} ر.س',
          style: TextStyle(
            color: netFlow >= 0 ? Colors.green : Colors.red,
            fontWeight: FontWeight.bold,
          ),
        ),
        children: activities.map<Widget>((activity) {
          return ListTile(
            leading: Icon(
              activity['amount'] >= 0 ? Icons.add_circle : Icons.remove_circle,
              color: activity['amount'] >= 0 ? Colors.green : Colors.red,
            ),
            title: Text(activity['description']),
            trailing: Text(
              '${activity['amount'].toStringAsFixed(2)} ر.س',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: activity['amount'] >= 0 ? Colors.green : Colors.red,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildSummaryRow(String label, double amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 16 : 14,
            ),
          ),
          Text(
            '${amount.toStringAsFixed(2)} ر.س',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: isTotal ? 16 : 14,
              color: isTotal ? (amount >= 0 ? Colors.green : Colors.red) : Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  double _calculateNetFlow(List<Map<String, dynamic>> activities) {
    return activities.fold(0.0, (sum, activity) => sum + activity['amount']);
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة تقرير التدفقات النقدية')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير تقرير التدفقات النقدية')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث بيانات التدفقات النقدية'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
