import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير تقييم المندوبين
/// يعرض تقييم أداء مندوبي المبيعات
class SalesRepresentativesEvaluationReportPage extends StatefulWidget {
  const SalesRepresentativesEvaluationReportPage({super.key});

  @override
  State<SalesRepresentativesEvaluationReportPage> createState() => _SalesRepresentativesEvaluationReportPageState();
}

class _SalesRepresentativesEvaluationReportPageState extends State<SalesRepresentativesEvaluationReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedRepresentative;
  String? _performanceLevel = 'all';
  String? _sortBy = 'total_sales';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('تقييم المندوبين'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.star),
            onPressed: _showTopPerformers,
            tooltip: 'أفضل المندوبين',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          _buildFilterSection(localizations),
          
          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildPerformanceLevelsSection(),
                  const SizedBox(height: 16),
                  _buildRepresentativesTableSection(),
                  const SizedBox(height: 16),
                  _buildTopPerformersSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.teal[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.fromDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, true),
                  controller: TextEditingController(
                    text: _startDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.toDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, false),
                  controller: TextEditingController(
                    text: _endDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'المندوب',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedRepresentative,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع المندوبين')),
                    DropdownMenuItem(value: 'rep1', child: Text('أحمد محمد')),
                    DropdownMenuItem(value: 'rep2', child: Text('فاطمة علي')),
                    DropdownMenuItem(value: 'rep3', child: Text('محمد سالم')),
                    DropdownMenuItem(value: 'rep4', child: Text('نورا أحمد')),
                    DropdownMenuItem(value: 'rep5', child: Text('خالد يوسف')),
                  ],
                  onChanged: (value) => setState(() => _selectedRepresentative = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'مستوى الأداء',
                    border: OutlineInputBorder(),
                  ),
                  value: _performanceLevel,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع المستويات')),
                    DropdownMenuItem(value: 'excellent', child: Text('ممتاز')),
                    DropdownMenuItem(value: 'good', child: Text('جيد')),
                    DropdownMenuItem(value: 'average', child: Text('متوسط')),
                    DropdownMenuItem(value: 'poor', child: Text('ضعيف')),
                  ],
                  onChanged: (value) => setState(() => _performanceLevel = value),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'ترتيب حسب',
                    border: OutlineInputBorder(),
                  ),
                  value: _sortBy,
                  items: const [
                    DropdownMenuItem(value: 'total_sales', child: Text('إجمالي المبيعات')),
                    DropdownMenuItem(value: 'customer_count', child: Text('عدد العملاء')),
                    DropdownMenuItem(value: 'performance_score', child: Text('نقاط الأداء')),
                    DropdownMenuItem(value: 'commission', child: Text('العمولة')),
                  ],
                  onChanged: (value) => setState(() => _sortBy = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.analytics),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.teal,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.people, color: Colors.teal, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص أداء المندوبين',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي المندوبين', '25', Colors.teal, Icons.people),
                _buildSummaryCard('إجمالي المبيعات', '2,850,000 ر.س', Colors.green, Icons.monetization_on),
                _buildSummaryCard('متوسط الأداء', '78%', Colors.blue, Icons.trending_up),
                _buildSummaryCard('إجمالي العمولات', '285,000 ر.س', Colors.orange, Icons.payment),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceLevelsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.bar_chart, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'توزيع مستويات الأداء',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildPerformanceCard('ممتاز', '8 مندوبين', Colors.green),
                _buildPerformanceCard('جيد', '10 مندوبين', Colors.blue),
                _buildPerformanceCard('متوسط', '5 مندوبين', Colors.orange),
                _buildPerformanceCard('ضعيف', '2 مندوبين', Colors.red),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRepresentativesTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل أداء المندوبين',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('اسم المندوب')),
                  DataColumn(label: Text('إجمالي المبيعات')),
                  DataColumn(label: Text('عدد العملاء')),
                  DataColumn(label: Text('عدد الصفقات')),
                  DataColumn(label: Text('متوسط الصفقة')),
                  DataColumn(label: Text('نقاط الأداء')),
                  DataColumn(label: Text('العمولة')),
                  DataColumn(label: Text('التقييم')),
                  DataColumn(label: Text('الترتيب')),
                ],
                rows: _buildRepresentativesRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopPerformersSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.star, color: Colors.amber, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'أفضل المندوبين هذا الشهر',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildTopPerformersList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _calculateCommissions,
                    icon: const Icon(Icons.calculate),
                    label: const Text('حساب العمولات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _setPerformanceTargets,
                    icon: const Icon(Icons.flag),
                    label: const Text('تحديد أهداف الأداء'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _createIncentivePlan,
                    icon: const Icon(Icons.card_giftcard),
                    label: const Text('خطة حوافز'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _scheduleTraining,
                    icon: const Icon(Icons.school),
                    label: const Text('جدولة تدريب'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildTopPerformersList() {
    final topPerformers = [
      {'name': 'أحمد محمد', 'sales': '485,000 ر.س', 'customers': '125', 'score': '95%', 'rank': '1'},
      {'name': 'فاطمة علي', 'sales': '425,000 ر.س', 'customers': '110', 'score': '88%', 'rank': '2'},
      {'name': 'محمد سالم', 'sales': '385,000 ر.س', 'customers': '95', 'score': '82%', 'rank': '3'},
      {'name': 'نورا أحمد', 'sales': '345,000 ر.س', 'customers': '85', 'score': '78%', 'rank': '4'},
    ];

    return topPerformers.map((performer) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: _getRankColor(performer['rank']!).withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: _getRankColor(performer['rank']!).withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: _getRankColor(performer['rank']!),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Center(
              child: Text(
                performer['rank']!,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  performer['name']!,
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  'مبيعات: ${performer['sales']} • عملاء: ${performer['customers']} • نقاط: ${performer['score']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          const Icon(Icons.star, color: Colors.amber, size: 20),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildRepresentativesRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('أحمد محمد')),
        const DataCell(Text('485,000 ر.س')),
        const DataCell(Text('125')),
        const DataCell(Text('285')),
        const DataCell(Text('1,702 ر.س')),
        const DataCell(Text('95')),
        const DataCell(Text('48,500 ر.س')),
        DataCell(_buildPerformanceBadge('ممتاز', Colors.green)),
        DataCell(_buildRankBadge('1', Colors.amber)),
      ]),
      DataRow(cells: [
        const DataCell(Text('فاطمة علي')),
        const DataCell(Text('425,000 ر.س')),
        const DataCell(Text('110')),
        const DataCell(Text('245')),
        const DataCell(Text('1,735 ر.س')),
        const DataCell(Text('88')),
        const DataCell(Text('42,500 ر.س')),
        DataCell(_buildPerformanceBadge('ممتاز', Colors.green)),
        DataCell(_buildRankBadge('2', Colors.grey)),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPerformanceCard(String level, String count, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(level, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPerformanceBadge(String performance, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(performance, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Widget _buildRankBadge(String rank, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text('#$rank', style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Color _getRankColor(String rank) {
    switch (rank) {
      case '1':
        return Colors.amber;
      case '2':
        return Colors.grey;
      case '3':
        return Colors.brown;
      case '4':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير تقييم المندوبين بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showTopPerformers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض أفضل المندوبين')),
    );
  }

  void _calculateCommissions() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('حساب عمولات المندوبين')),
    );
  }

  void _setPerformanceTargets() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحديد أهداف الأداء للمندوبين')),
    );
  }

  void _createIncentivePlan() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء خطة حوافز للمندوبين')),
    );
  }

  void _scheduleTraining() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جدولة تدريب للمندوبين')),
    );
  }
}
