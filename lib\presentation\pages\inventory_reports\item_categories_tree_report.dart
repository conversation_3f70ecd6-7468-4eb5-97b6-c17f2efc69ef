import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير شجرة تصنيفات الأصناف
/// يعرض التصنيفات في شكل شجرة هرمية
class ItemCategoriesTreeReportPage extends StatefulWidget {
  const ItemCategoriesTreeReportPage({super.key});

  @override
  State<ItemCategoriesTreeReportPage> createState() => _ItemCategoriesTreeReportPageState();
}

class _ItemCategoriesTreeReportPageState extends State<ItemCategoriesTreeReportPage> {
  String? _viewType = 'tree';
  String? _sortBy = 'name';
  bool _showItemCount = true;
  bool _showValues = true;
  final Set<String> _expandedCategories = {};

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('شجرة تصنيفات الأصناف'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.expand_more),
            onPressed: _expandAll,
            tooltip: 'توسيع الكل',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الإعدادات
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.teal[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'نوع العرض',
                          border: OutlineInputBorder(),
                        ),
                        value: _viewType,
                        items: const [
                          DropdownMenuItem(value: 'tree', child: Text('عرض شجري')),
                          DropdownMenuItem(value: 'list', child: Text('عرض قائمة')),
                          DropdownMenuItem(value: 'grid', child: Text('عرض شبكي')),
                          DropdownMenuItem(value: 'chart', child: Text('عرض مخطط')),
                        ],
                        onChanged: (value) => setState(() => _viewType = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'ترتيب حسب',
                          border: OutlineInputBorder(),
                        ),
                        value: _sortBy,
                        items: const [
                          DropdownMenuItem(value: 'name', child: Text('الاسم')),
                          DropdownMenuItem(value: 'item_count', child: Text('عدد الأصناف')),
                          DropdownMenuItem(value: 'value', child: Text('القيمة')),
                          DropdownMenuItem(value: 'creation_date', child: Text('تاريخ الإنشاء')),
                        ],
                        onChanged: (value) => setState(() => _sortBy = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: CheckboxListTile(
                        title: const Text('عرض عدد الأصناف'),
                        value: _showItemCount,
                        onChanged: (value) => setState(() => _showItemCount = value!),
                      ),
                    ),
                    Expanded(
                      child: CheckboxListTile(
                        title: const Text('عرض القيم'),
                        value: _showValues,
                        onChanged: (value) => setState(() => _showValues = value!),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص التصنيفات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.category, color: Colors.teal, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'ملخص التصنيفات',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSummaryCard('إجمالي التصنيفات', '45', Colors.blue, Icons.category),
                              _buildSummaryCard('التصنيفات الرئيسية', '8', Colors.green, Icons.folder),
                              _buildSummaryCard('التصنيفات الفرعية', '37', Colors.orange, Icons.folder_open),
                              _buildSummaryCard('إجمالي الأصناف', '1,245', Colors.purple, Icons.inventory),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // شجرة التصنيفات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Text(
                                'شجرة التصنيفات',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const Spacer(),
                              TextButton(
                                onPressed: _expandAll,
                                child: const Text('توسيع الكل'),
                              ),
                              TextButton(
                                onPressed: _collapseAll,
                                child: const Text('طي الكل'),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          ..._buildCategoryTree(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إحصائيات التصنيفات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.bar_chart, color: Colors.indigo, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'إحصائيات التصنيفات',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildStatCard('الإلكترونيات', '485 صنف', '39%', Colors.blue),
                              _buildStatCard('الملابس', '320 صنف', '26%', Colors.green),
                              _buildStatCard('الأغذية', '285 صنف', '23%', Colors.orange),
                              _buildStatCard('أخرى', '155 صنف', '12%', Colors.purple),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // أكبر التصنيفات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'أكبر التصنيفات (حسب عدد الأصناف)',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ..._buildLargestCategoriesList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _addCategory,
                                  icon: const Icon(Icons.add),
                                  label: const Text('إضافة تصنيف'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _editCategories,
                                  icon: const Icon(Icons.edit),
                                  label: const Text('تعديل التصنيفات'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _reorganizeCategories,
                                  icon: const Icon(Icons.reorder),
                                  label: const Text('إعادة تنظيم'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _analyzeCategories,
                                  icon: const Icon(Icons.analytics),
                                  label: const Text('تحليل التصنيفات'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildCategoryTree() {
    final categories = [
      {
        'name': 'الإلكترونيات',
        'items': 485,
        'value': '2,850,000 ر.س',
        'children': [
          {'name': 'أجهزة الكمبيوتر', 'items': 185, 'value': '1,250,000 ر.س'},
          {'name': 'الهواتف الذكية', 'items': 125, 'value': '850,000 ر.س'},
          {'name': 'الطابعات', 'items': 95, 'value': '450,000 ر.س'},
          {'name': 'الإكسسوارات', 'items': 80, 'value': '300,000 ر.س'},
        ]
      },
      {
        'name': 'الملابس',
        'items': 320,
        'value': '1,650,000 ر.س',
        'children': [
          {'name': 'ملابس رجالية', 'items': 145, 'value': '750,000 ر.س'},
          {'name': 'ملابس نسائية', 'items': 125, 'value': '650,000 ر.س'},
          {'name': 'ملابس أطفال', 'items': 50, 'value': '250,000 ر.س'},
        ]
      },
      {
        'name': 'الأغذية',
        'items': 285,
        'value': '950,000 ر.س',
        'children': [
          {'name': 'المعلبات', 'items': 125, 'value': '450,000 ر.س'},
          {'name': 'المشروبات', 'items': 85, 'value': '300,000 ر.س'},
          {'name': 'الحلويات', 'items': 75, 'value': '200,000 ر.س'},
        ]
      },
    ];

    return categories.map((category) => _buildCategoryItem(category, 0)).toList();
  }

  Widget _buildCategoryItem(Map<String, dynamic> category, int level) {
    final isExpanded = _expandedCategories.contains(category['name']);
    final hasChildren = category['children'] != null && (category['children'] as List).isNotEmpty;

    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(left: level * 20.0, bottom: 4),
          child: InkWell(
            onTap: hasChildren ? () => _toggleCategory(category['name']) : null,
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.teal.withOpacity(0.3)),
                borderRadius: BorderRadius.circular(8),
                color: Colors.teal.withOpacity(0.05),
              ),
              child: Row(
                children: [
                  if (hasChildren)
                    Icon(
                      isExpanded ? Icons.expand_less : Icons.expand_more,
                      color: Colors.teal,
                    )
                  else
                    const SizedBox(width: 24),
                  const SizedBox(width: 8),
                  Icon(
                    hasChildren ? Icons.folder : Icons.folder_open,
                    color: Colors.teal,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      category['name'],
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: hasChildren ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                  ),
                  if (_showItemCount)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.blue.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${category['items']} صنف',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.blue,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  if (_showValues) ...[
                    const SizedBox(width: 8),
                    Text(
                      category['value'],
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.green,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
        if (hasChildren && isExpanded)
          ...(category['children'] as List).map((child) => _buildCategoryItem(child, level + 1)),
      ],
    );
  }

  List<Widget> _buildLargestCategoriesList() {
    final categories = [
      {'name': 'الإلكترونيات', 'items': '485', 'percentage': '39%', 'value': '2,850,000 ر.س'},
      {'name': 'الملابس', 'items': '320', 'percentage': '26%', 'value': '1,650,000 ر.س'},
      {'name': 'الأغذية', 'items': '285', 'percentage': '23%', 'value': '950,000 ر.س'},
      {'name': 'أدوات منزلية', 'items': '155', 'percentage': '12%', 'value': '650,000 ر.س'},
    ];

    return categories.map((category) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.teal.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.teal.withOpacity(0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.category, color: Colors.teal, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  category['name']!,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${category['items']} صنف • ${category['value']}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.teal.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              category['percentage']!,
              style: const TextStyle(
                color: Colors.teal,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatCard(String category, String count, String percentage, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                percentage,
                style: TextStyle(
                  fontSize: 12,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                category,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _toggleCategory(String categoryName) {
    setState(() {
      if (_expandedCategories.contains(categoryName)) {
        _expandedCategories.remove(categoryName);
      } else {
        _expandedCategories.add(categoryName);
      }
    });
  }

  void _expandAll() {
    setState(() {
      _expandedCategories.addAll(['الإلكترونيات', 'الملابس', 'الأغذية']);
    });
  }

  void _collapseAll() {
    setState(() {
      _expandedCategories.clear();
    });
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _addCategory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إضافة تصنيف جديد')),
    );
  }

  void _editCategories() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تعديل التصنيفات')),
    );
  }

  void _reorganizeCategories() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إعادة تنظيم التصنيفات')),
    );
  }

  void _analyzeCategories() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحليل أداء التصنيفات')),
    );
  }
}
