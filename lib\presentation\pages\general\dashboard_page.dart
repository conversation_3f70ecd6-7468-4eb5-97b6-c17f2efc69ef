import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';
import 'home_page.dart';
import 'general_page.dart';
import '../sales/sales_page.dart';
import '../purchases/purchases_page.dart';
import '../inventory_reports/inventory_page.dart';
import '../account_reports/account_reports_page.dart';
import '../cards/cards_page.dart';
import '../vouchers/vouchers_page.dart';
import '../statistical_reports/statistical_reports_page.dart';
import '../system_management/system_management_page.dart';
import '../tools/tools_page.dart';
import '../help/help_page.dart';

/// صفحة لوحة التحكم الرئيسية
/// تعرض جميع الوحدات المتاحة في التطبيق على شكل بطاقات
class DashboardPage extends StatelessWidget {
  const DashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان لوحة التحكم
            Text(
              '${localizations.appTitle} ${localizations.dashboard}',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            // شبكة الوحدات
            Expanded(
              child: GridView.count(
                crossAxisCount: 3,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  _buildModuleCard(
                    context,
                    localizations.general,
                    Icons.settings,
                    Colors.blue,
                    () => _navigateToModulePages(context, 'general'),
                  ),
                  _buildModuleCard(
                    context,
                    localizations.cards,
                    Icons.credit_card,
                    Colors.green,
                    () => _navigateToModulePages(context, 'cards'),
                  ),
                  _buildModuleCard(
                    context,
                    localizations.purchases,
                    Icons.shopping_cart,
                    Colors.orange,
                    () => _navigateToModulePages(context, 'purchases'),
                  ),
                  _buildModuleCard(
                    context,
                    localizations.sales,
                    Icons.point_of_sale,
                    Colors.red,
                    () => _navigateToModulePages(context, 'sales'),
                  ),
                  _buildModuleCard(
                    context,
                    localizations.vouchers,
                    Icons.receipt_long,
                    Colors.purple,
                    () => _navigateToModulePages(context, 'vouchers'),
                  ),
                  _buildModuleCard(
                    context,
                    localizations.inventoryReports,
                    Icons.inventory_2,
                    Colors.teal,
                    () => _navigateToModulePages(context, 'inventory_reports'),
                  ),
                  _buildModuleCard(
                    context,
                    localizations.accountReports,
                    Icons.account_balance,
                    Colors.indigo,
                    () => _navigateToModulePages(context, 'account_reports'),
                  ),
                  _buildModuleCard(
                    context,
                    localizations.statisticalReports,
                    Icons.bar_chart,
                    Colors.amber.shade800,
                    () =>
                        _navigateToModulePages(context, 'statistical_reports'),
                  ),
                  _buildModuleCard(
                    context,
                    localizations.systemManagement,
                    Icons.admin_panel_settings,
                    Colors.brown,
                    () => _navigateToModulePages(context, 'system_management'),
                  ),
                  _buildModuleCard(
                    context,
                    localizations.tools,
                    Icons.build,
                    Colors.grey.shade700,
                    () => _navigateToModulePages(context, 'tools'),
                  ),
                  _buildModuleCard(
                    context,
                    localizations.help,
                    Icons.help,
                    Colors.cyan,
                    () => _navigateToModulePages(context, 'help'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة وحدة
  ///
  /// تقوم هذه الدالة ببناء بطاقة تعرض معلومات الوحدة وتتيح للمستخدم الوصول إليها
  Widget _buildModuleCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 40,
                color: color,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// التنقل إلى صفحات الوحدة
  void _navigateToModulePages(BuildContext context, String module) {
    Widget? targetPage;

    switch (module) {
      case 'sales':
        targetPage = const SalesPage();
        break;
      case 'purchases':
        targetPage = const PurchasesPage();
        break;
      case 'inventory_reports':
        targetPage = const InventoryPage();
        break;
      case 'account_reports':
        targetPage = const AccountReportsPage();
        break;
      case 'general':
        targetPage = const GeneralPage();
        break;
      case 'cards':
        targetPage = const CardsPage();
        break;
      case 'vouchers':
        targetPage = const VouchersPage();
        break;
      case 'statistical_reports':
        targetPage = const StatisticalReportsPage();
        break;
      case 'system_management':
        targetPage = const SystemManagementPage();
        break;
      case 'tools':
        targetPage = const ToolsPage();
        break;
      case 'help':
        targetPage = const HelpPage();
        break;
      default:
        // للوحدات الأخرى، استخدم ModulePagesScreen
        targetPage = ModulePagesScreen(module: module);
        break;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => targetPage!,
      ),
    );
  }
}
