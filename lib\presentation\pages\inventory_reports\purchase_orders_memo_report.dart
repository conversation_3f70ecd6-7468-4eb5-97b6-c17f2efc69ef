import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة مذكرة الطلبات
/// تعرض ملخص أوامر الشراء والطلبات المعلقة والمكتملة
class PurchaseOrdersMemoReportPage extends StatefulWidget {
  const PurchaseOrdersMemoReportPage({super.key});

  @override
  State<PurchaseOrdersMemoReportPage> createState() => _PurchaseOrdersMemoReportPageState();
}

class _PurchaseOrdersMemoReportPageState extends State<PurchaseOrdersMemoReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedSupplier;
  String? _orderStatus = 'all';
  final String _priority = 'all';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.purchaseOrdersMemo),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.add_shopping_cart),
            onPressed: _createNewOrder,
            tooltip: 'إنشاء طلب جديد',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.indigo[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'المورد',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedSupplier,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الموردين')),
                          DropdownMenuItem(value: 'supplier1', child: Text('شركة التقنية المتقدمة')),
                          DropdownMenuItem(value: 'supplier2', child: Text('مؤسسة الإلكترونيات')),
                          DropdownMenuItem(value: 'supplier3', child: Text('شركة المكتبية الحديثة')),
                        ],
                        onChanged: (value) => setState(() => _selectedSupplier = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'حالة الطلب',
                          border: OutlineInputBorder(),
                        ),
                        value: _orderStatus,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الحالات')),
                          DropdownMenuItem(value: 'pending', child: Text('معلق')),
                          DropdownMenuItem(value: 'approved', child: Text('معتمد')),
                          DropdownMenuItem(value: 'shipped', child: Text('تم الشحن')),
                          DropdownMenuItem(value: 'delivered', child: Text('تم التسليم')),
                          DropdownMenuItem(value: 'cancelled', child: Text('ملغي')),
                        ],
                        onChanged: (value) => setState(() => _orderStatus = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.fromDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, true),
                        controller: TextEditingController(
                          text: _startDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.toDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, false),
                        controller: TextEditingController(
                          text: _endDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.search),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.indigo,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص الطلبات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'ملخص أوامر الشراء',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSummaryCard('إجمالي الطلبات', '45', Colors.blue, Icons.shopping_cart),
                              _buildSummaryCard('معلقة', '12', Colors.orange, Icons.pending),
                              _buildSummaryCard('معتمدة', '18', Colors.green, Icons.check_circle),
                              _buildSummaryCard('ملغية', '3', Colors.red, Icons.cancel),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول الطلبات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل أوامر الشراء',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: [
                                const DataColumn(label: Text('رقم الطلب')),
                                const DataColumn(label: Text('تاريخ الطلب')),
                                const DataColumn(label: Text('المورد')),
                                const DataColumn(label: Text('عدد الأصناف')),
                                const DataColumn(label: Text('إجمالي القيمة')),
                                const DataColumn(label: Text('تاريخ التسليم المتوقع')),
                                const DataColumn(label: Text('الحالة')),
                                const DataColumn(label: Text('الأولوية')),
                                const DataColumn(label: Text('الإجراءات')),
                              ],
                              rows: _buildOrderRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // تحليل الموردين
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تحليل أداء الموردين',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('المورد')),
                                DataColumn(label: Text('عدد الطلبات')),
                                DataColumn(label: Text('إجمالي القيمة')),
                                DataColumn(label: Text('معدل التسليم')),
                                DataColumn(label: Text('التقييم')),
                              ],
                              rows: _buildSupplierAnalysisRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _createNewOrder,
                                  icon: const Icon(Icons.add),
                                  label: const Text('طلب جديد'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _approveOrders,
                                  icon: const Icon(Icons.check),
                                  label: const Text('اعتماد الطلبات'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _trackOrders,
                                  icon: const Icon(Icons.track_changes),
                                  label: const Text('تتبع الطلبات'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _sendReminders,
                                  icon: const Icon(Icons.notifications),
                                  label: const Text('إرسال تذكيرات'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<DataRow> _buildOrderRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('PO-2024-001')),
        const DataCell(Text('2024-01-15')),
        const DataCell(Text('شركة التقنية المتقدمة')),
        const DataCell(Text('5')),
        const DataCell(Text('125,000 ر.س')),
        const DataCell(Text('2024-01-25')),
        DataCell(_buildStatusBadge('معتمد', Colors.green)),
        DataCell(_buildPriorityBadge('عالي', Colors.red)),
        DataCell(Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.visibility, color: Colors.blue),
              onPressed: () => _viewOrder('PO-2024-001'),
            ),
            IconButton(
              icon: const Icon(Icons.edit, color: Colors.orange),
              onPressed: () => _editOrder('PO-2024-001'),
            ),
          ],
        )),
      ]),
      DataRow(cells: [
        const DataCell(Text('PO-2024-002')),
        const DataCell(Text('2024-01-16')),
        const DataCell(Text('مؤسسة الإلكترونيات')),
        const DataCell(Text('8')),
        const DataCell(Text('85,000 ر.س')),
        const DataCell(Text('2024-01-28')),
        DataCell(_buildStatusBadge('معلق', Colors.orange)),
        DataCell(_buildPriorityBadge('متوسط', Colors.amber)),
        DataCell(Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.visibility, color: Colors.blue),
              onPressed: () => _viewOrder('PO-2024-002'),
            ),
            IconButton(
              icon: const Icon(Icons.edit, color: Colors.orange),
              onPressed: () => _editOrder('PO-2024-002'),
            ),
          ],
        )),
      ]),
    ];
  }

  List<DataRow> _buildSupplierAnalysisRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('شركة التقنية المتقدمة')),
        const DataCell(Text('15')),
        const DataCell(Text('450,000 ر.س')),
        const DataCell(Text('95%')),
        DataCell(_buildRatingStars(5)),
      ]),
      DataRow(cells: [
        const DataCell(Text('مؤسسة الإلكترونيات')),
        const DataCell(Text('12')),
        const DataCell(Text('320,000 ر.س')),
        const DataCell(Text('88%')),
        DataCell(_buildRatingStars(4)),
      ]),
      DataRow(cells: [
        const DataCell(Text('شركة المكتبية الحديثة')),
        const DataCell(Text('8')),
        const DataCell(Text('180,000 ر.س')),
        const DataCell(Text('92%')),
        DataCell(_buildRatingStars(4)),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(status, style: const TextStyle(color: Colors.white, fontSize: 12)),
    );
  }

  Widget _buildPriorityBadge(String priority, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(priority, style: const TextStyle(color: Colors.white, fontSize: 12)),
    );
  }

  Widget _buildRatingStars(int rating) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        return Icon(
          index < rating ? Icons.star : Icons.star_border,
          color: Colors.amber,
          size: 16,
        );
      }),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء مذكرة الطلبات بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة مذكرة الطلبات...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير مذكرة الطلبات...')),
    );
  }

  void _createNewOrder() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('فتح صفحة إنشاء طلب جديد')),
    );
  }

  void _approveOrders() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم اعتماد الطلبات المحددة')),
    );
  }

  void _trackOrders() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('فتح صفحة تتبع الطلبات')),
    );
  }

  void _sendReminders() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إرسال التذكيرات للموردين')),
    );
  }

  void _viewOrder(String orderId) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض تفاصيل الطلب $orderId')),
    );
  }

  void _editOrder(String orderId) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تعديل الطلب $orderId')),
    );
  }
}
