import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة إعادة إحتساب متوسط التكلفة
/// تتيح إعادة حساب متوسط تكلفة الأصناف في المخزون
class RecalculateAverageCostPage extends StatefulWidget {
  const RecalculateAverageCostPage({super.key});

  @override
  State<RecalculateAverageCostPage> createState() =>
      _RecalculateAverageCostPageState();
}

class _RecalculateAverageCostPageState
    extends State<RecalculateAverageCostPage> {
  final _formKey = GlobalKey<FormState>();

  String? _selectedCategory;
  String? _selectedWarehouse;
  String? _calculationMethod;
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 90));
  DateTime _endDate = DateTime.now();
  final Set<String> _selectedItems = {};
  bool _isProcessing = false;
  bool _includeZeroQuantity = false;
  bool _updatePrices = true;
  bool _createBackup = true;

  final List<Map<String, String>> _categories = [
    {'id': 'all', 'name': 'جميع الفئات'},
    {'id': 'electronics', 'name': 'الإلكترونيات'},
    {'id': 'furniture', 'name': 'الأثاث المكتبي'},
    {'id': 'stationery', 'name': 'القرطاسية'},
    {'id': 'equipment', 'name': 'المعدات'},
  ];

  final List<Map<String, String>> _warehouses = [
    {'id': 'all', 'name': 'جميع المخازن'},
    {'id': 'main', 'name': 'المخزن الرئيسي'},
    {'id': 'jeddah', 'name': 'مخزن جدة'},
    {'id': 'dammam', 'name': 'مخزن الدمام'},
    {'id': 'returns', 'name': 'مخزن المرتجعات'},
  ];

  final List<Map<String, String>> _calculationMethods = [
    {
      'id': 'weighted',
      'name': 'المتوسط المرجح',
      'description': 'حساب المتوسط بناءً على الكميات'
    },
    {
      'id': 'fifo',
      'name': 'الوارد أولاً صادر أولاً',
      'description': 'FIFO - First In First Out'
    },
    {
      'id': 'lifo',
      'name': 'الوارد أخيراً صادر أولاً',
      'description': 'LIFO - Last In First Out'
    },
    {
      'id': 'simple',
      'name': 'المتوسط البسيط',
      'description': 'متوسط الأسعار بدون ترجيح'
    },
  ];

  final List<Map<String, dynamic>> _items = [
    {
      'id': '1',
      'name': 'لابتوب ديل XPS',
      'category': 'electronics',
      'currentCost': '2500.00',
      'quantity': 15,
      'lastUpdate': '2024/01/15'
    },
    {
      'id': '2',
      'name': 'مكتب خشبي فاخر',
      'category': 'furniture',
      'currentCost': '800.00',
      'quantity': 8,
      'lastUpdate': '2024/01/10'
    },
    {
      'id': '3',
      'name': 'أقلام حبر جاف',
      'category': 'stationery',
      'currentCost': '25.00',
      'quantity': 200,
      'lastUpdate': '2024/01/20'
    },
    {
      'id': '4',
      'name': 'طابعة ليزر ملونة',
      'category': 'electronics',
      'currentCost': '1200.00',
      'quantity': 5,
      'lastUpdate': '2024/01/12'
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.recalculateAverageCost),
        backgroundColor: Colors.lightGreen,
        foregroundColor: Colors.black,
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: _showCalculationHistory,
            tooltip: 'سجل الحسابات',
          ),
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: _showHelpDialog,
            tooltip: 'المساعدة',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // بطاقة معايير الاختيار
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'معايير الاختيار',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.lightGreen,
                      ),
                    ),
                    const SizedBox(height: 16),

                    Row(
                      children: [
                        // الفئة
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedCategory,
                            decoration: const InputDecoration(
                              labelText: 'فئة الأصناف',
                              prefixIcon: Icon(Icons.category),
                              border: OutlineInputBorder(),
                            ),
                            items: _categories
                                .map<DropdownMenuItem<String>>((category) {
                              return DropdownMenuItem<String>(
                                value: category['id'],
                                child: Text(category['name']!),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedCategory = value;
                                _selectedItems.clear();
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'يرجى اختيار الفئة';
                              }
                              return null;
                            },
                          ),
                        ),

                        const SizedBox(width: 16),

                        // المخزن
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedWarehouse,
                            decoration: const InputDecoration(
                              labelText: 'المخزن',
                              prefixIcon: Icon(Icons.warehouse),
                              border: OutlineInputBorder(),
                            ),
                            items: _warehouses
                                .map<DropdownMenuItem<String>>((warehouse) {
                              return DropdownMenuItem<String>(
                                value: warehouse['id'],
                                child: Text(warehouse['name']!),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedWarehouse = value;
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'يرجى اختيار المخزن';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // طريقة الحساب
                    DropdownButtonFormField<String>(
                      value: _calculationMethod,
                      decoration: const InputDecoration(
                        labelText: 'طريقة الحساب',
                        prefixIcon: Icon(Icons.calculate),
                        border: OutlineInputBorder(),
                      ),
                      items: _calculationMethods
                          .map<DropdownMenuItem<String>>((method) {
                        return DropdownMenuItem<String>(
                          value: method['id'],
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(method['name']!),
                              Text(
                                method['description']!,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _calculationMethod = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار طريقة الحساب';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة الفترة الزمنية
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'الفترة الزمنية للحساب',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.lightGreen,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        // تاريخ البداية
                        Expanded(
                          child: ListTile(
                            leading: const Icon(Icons.calendar_today),
                            title: const Text('من تاريخ'),
                            subtitle: Text(
                                '${_startDate.day}/${_startDate.month}/${_startDate.year}'),
                            onTap: () => _selectDate(true),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                              side: BorderSide(color: Colors.grey.shade300),
                            ),
                          ),
                        ),

                        const SizedBox(width: 16),

                        // تاريخ النهاية
                        Expanded(
                          child: ListTile(
                            leading: const Icon(Icons.event),
                            title: const Text('إلى تاريخ'),
                            subtitle: Text(
                                '${_endDate.day}/${_endDate.month}/${_endDate.year}'),
                            onTap: () => _selectDate(false),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                              side: BorderSide(color: Colors.grey.shade300),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة الأصناف
            if (_selectedCategory != null && _selectedCategory != 'all')
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Text(
                            'اختيار الأصناف',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.lightGreen,
                            ),
                          ),
                          const Spacer(),
                          TextButton(
                            onPressed: _selectAllItems,
                            child: const Text('تحديد الكل'),
                          ),
                          TextButton(
                            onPressed: _clearAllItems,
                            child: const Text('إلغاء الكل'),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      ...(_getFilteredItems().map((item) => CheckboxListTile(
                            value: _selectedItems.contains(item['id']),
                            onChanged: (bool? value) {
                              setState(() {
                                if (value == true) {
                                  _selectedItems.add(item['id']);
                                } else {
                                  _selectedItems.remove(item['id']);
                                }
                              });
                            },
                            title: Text(item['name']),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                    'التكلفة الحالية: ${item['currentCost']} ر.س'),
                                Text(
                                    'الكمية: ${item['quantity']} | آخر تحديث: ${item['lastUpdate']}'),
                              ],
                            ),
                            controlAffinity: ListTileControlAffinity.leading,
                          ))),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 16),

            // بطاقة خيارات إضافية
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'خيارات إضافية',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.lightGreen,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SwitchListTile(
                      title: const Text('تضمين الأصناف ذات الكمية صفر'),
                      subtitle: const Text('حساب التكلفة للأصناف غير المتوفرة'),
                      value: _includeZeroQuantity,
                      onChanged: (value) {
                        setState(() {
                          _includeZeroQuantity = value;
                        });
                      },
                    ),
                    SwitchListTile(
                      title: const Text('تحديث أسعار البيع تلقائياً'),
                      subtitle: const Text(
                          'تحديث أسعار البيع بناءً على التكلفة الجديدة'),
                      value: _updatePrices,
                      onChanged: (value) {
                        setState(() {
                          _updatePrices = value;
                        });
                      },
                    ),
                    SwitchListTile(
                      title: const Text('إنشاء نسخة احتياطية'),
                      subtitle: const Text('حفظ البيانات الحالية قبل التحديث'),
                      value: _createBackup,
                      onChanged: (value) {
                        setState(() {
                          _createBackup = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // معاينة العملية
            if (_selectedCategory != null &&
                _selectedWarehouse != null &&
                _calculationMethod != null)
              Card(
                color: Colors.lightGreen.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'معاينة عملية الحساب',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildPreviewRow('الفئة:', _getCategoryName()),
                      _buildPreviewRow('المخزن:', _getWarehouseName()),
                      _buildPreviewRow(
                          'طريقة الحساب:', _getCalculationMethodName()),
                      _buildPreviewRow('الفترة:',
                          '${_startDate.day}/${_startDate.month}/${_startDate.year} - ${_endDate.day}/${_endDate.month}/${_endDate.year}'),
                      _buildPreviewRow('عدد الأصناف:', _getItemsCount()),
                      _buildPreviewRow('تضمين الكمية صفر:',
                          _includeZeroQuantity ? 'نعم' : 'لا'),
                      _buildPreviewRow(
                          'تحديث الأسعار:', _updatePrices ? 'نعم' : 'لا'),
                      _buildPreviewRow(
                          'نسخة احتياطية:', _createBackup ? 'نعم' : 'لا'),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 24),

            // أزرار العمليات
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isProcessing ? null : _startCalculation,
                    icon: _isProcessing
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.calculate),
                    label: Text(
                        _isProcessing ? 'جاري الحساب...' : 'بدء إعادة الحساب'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.lightGreen,
                      foregroundColor: Colors.black,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _resetForm,
                    icon: const Icon(Icons.refresh),
                    label: const Text('إعادة تعيين'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getFilteredItems() {
    if (_selectedCategory == null || _selectedCategory == 'all') return _items;
    return _items
        .where((item) => item['category'] == _selectedCategory)
        .toList();
  }

  String _getCategoryName() {
    if (_selectedCategory == null) return 'غير محدد';
    final category =
        _categories.firstWhere((c) => c['id'] == _selectedCategory);
    return category['name']!;
  }

  String _getWarehouseName() {
    if (_selectedWarehouse == null) return 'غير محدد';
    final warehouse =
        _warehouses.firstWhere((w) => w['id'] == _selectedWarehouse);
    return warehouse['name']!;
  }

  String _getCalculationMethodName() {
    if (_calculationMethod == null) return 'غير محدد';
    final method =
        _calculationMethods.firstWhere((m) => m['id'] == _calculationMethod);
    return method['name']!;
  }

  String _getItemsCount() {
    if (_selectedCategory == 'all') {
      return '${_items.length} صنف';
    } else if (_selectedItems.isEmpty) {
      return '${_getFilteredItems().length} صنف';
    } else {
      return '${_selectedItems.length} صنف محدد';
    }
  }

  Future<void> _selectDate(bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : _endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _selectAllItems() {
    setState(() {
      _selectedItems.clear();
      _selectedItems
          .addAll(_getFilteredItems().map((item) => item['id'] as String));
    });
  }

  void _clearAllItems() {
    setState(() {
      _selectedItems.clear();
    });
  }

  Future<void> _startCalculation() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isProcessing = true;
      });

      // محاكاة عملية الحساب
      await Future.delayed(const Duration(seconds: 4));

      setState(() {
        _isProcessing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'تم إعادة حساب متوسط التكلفة لـ ${_getItemsCount()} بنجاح'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 4),
          ),
        );
        _resetForm();
      }
    }
  }

  void _resetForm() {
    setState(() {
      _selectedCategory = null;
      _selectedWarehouse = null;
      _calculationMethod = null;
      _selectedItems.clear();
      _includeZeroQuantity = false;
      _updatePrices = true;
      _createBackup = true;
      _startDate = DateTime.now().subtract(const Duration(days: 90));
      _endDate = DateTime.now();
    });
  }

  void _showCalculationHistory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض سجل عمليات الحساب')),
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مساعدة - إعادة حساب متوسط التكلفة'),
        content: const Text(
          'هذه الأداة تتيح إعادة حساب متوسط تكلفة الأصناف بناءً على:\n\n'
          '• المتوسط المرجح: يعتمد على الكميات\n'
          '• FIFO: الوارد أولاً صادر أولاً\n'
          '• LIFO: الوارد أخيراً صادر أولاً\n'
          '• المتوسط البسيط: متوسط الأسعار\n\n'
          'يُنصح بإنشاء نسخة احتياطية قبل التحديث.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('فهمت'),
          ),
        ],
      ),
    );
  }
}
