import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير أصناف بيعت أقل من التكلفة
/// تعرض الأصناف التي تم بيعها بأسعار أقل من تكلفتها
class ItemsSoldBelowCostReportPage extends StatefulWidget {
  const ItemsSoldBelowCostReportPage({super.key});

  @override
  State<ItemsSoldBelowCostReportPage> createState() => _ItemsSoldBelowCostReportPageState();
}

class _ItemsSoldBelowCostReportPageState extends State<ItemsSoldBelowCostReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedCategory;
  String? _selectedWarehouse;
  double _lossThreshold = 0.0;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.itemsSoldBelowCost),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.red[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'حد الخسارة المقبول (%)',
                          border: OutlineInputBorder(),
                          suffixIcon: Icon(Icons.percent),
                        ),
                        keyboardType: TextInputType.number,
                        onChanged: (value) => setState(() => _lossThreshold = double.tryParse(value) ?? 0.0),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.category,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedCategory,
                        items: [
                          DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                          DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                          DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                          DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                        ],
                        onChanged: (value) => setState(() => _selectedCategory = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.fromDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, true),
                        controller: TextEditingController(
                          text: _startDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.toDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, false),
                        controller: TextEditingController(
                          text: _endDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.search),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // تحذير الخسائر
                  Card(
                    color: Colors.red[50],
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        children: [
                          const Icon(Icons.warning, color: Colors.red, size: 32),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'تحذير: خسائر في المبيعات',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.red,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                const Text(
                                  'تم العثور على أصناف بيعت بأسعار أقل من التكلفة. يرجى مراجعة استراتيجية التسعير.',
                                  style: TextStyle(color: Colors.red),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إحصائيات الخسائر
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إحصائيات الخسائر',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildLossCard('عدد الأصناف الخاسرة', '8', Colors.red),
                              _buildLossCard('إجمالي الخسائر', '12,450 ر.س', Colors.orange),
                              _buildLossCard('متوسط نسبة الخسارة', '15.2%', Colors.deepOrange),
                              _buildLossCard('أكبر خسارة', '3,200 ر.س', Colors.redAccent),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول الأصناف الخاسرة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل الأصناف المباعة بخسارة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: [
                                DataColumn(label: Text(localizations.itemCode)),
                                DataColumn(label: Text(localizations.itemName)),
                                DataColumn(label: Text('الكمية المباعة')),
                                DataColumn(label: Text('سعر التكلفة')),
                                DataColumn(label: Text('سعر البيع')),
                                DataColumn(label: Text('الخسارة للوحدة')),
                                DataColumn(label: Text('إجمالي الخسارة')),
                                DataColumn(label: Text('نسبة الخسارة %')),
                                DataColumn(label: Text('مستوى الخطر')),
                              ],
                              rows: _buildLossRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // توصيات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'التوصيات',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          _buildRecommendationItem(
                            'مراجعة أسعار البيع',
                            'يُنصح بمراجعة أسعار البيع للأصناف الخاسرة وتعديلها',
                            Icons.price_change,
                            Colors.blue,
                          ),
                          _buildRecommendationItem(
                            'تحليل التكاليف',
                            'مراجعة تكاليف الشراء والعمليات لتقليل التكلفة',
                            Icons.analytics,
                            Colors.green,
                          ),
                          _buildRecommendationItem(
                            'استراتيجية التخلص',
                            'النظر في استراتيجيات التخلص من المخزون الراكد',
                            Icons.inventory,
                            Colors.orange,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<DataRow> _buildLossRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('001')),
        const DataCell(Text('جهاز كمبيوتر قديم')),
        const DataCell(Text('5')),
        const DataCell(Text('2,500')),
        const DataCell(Text('2,000')),
        const DataCell(Text('500')),
        const DataCell(Text('2,500')),
        const DataCell(Text('20%')),
        DataCell(_buildRiskLevel('عالي', Colors.red)),
      ]),
      DataRow(cells: [
        const DataCell(Text('002')),
        const DataCell(Text('طابعة قديمة')),
        const DataCell(Text('8')),
        const DataCell(Text('800')),
        const DataCell(Text('650')),
        const DataCell(Text('150')),
        const DataCell(Text('1,200')),
        const DataCell(Text('18.75%')),
        DataCell(_buildRiskLevel('عالي', Colors.red)),
      ]),
      DataRow(cells: [
        const DataCell(Text('003')),
        const DataCell(Text('كيبورد تالف')),
        const DataCell(Text('15')),
        const DataCell(Text('120')),
        const DataCell(Text('100')),
        const DataCell(Text('20')),
        const DataCell(Text('300')),
        const DataCell(Text('16.67%')),
        DataCell(_buildRiskLevel('متوسط', Colors.orange)),
      ]),
    ];
  }

  Widget _buildLossCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRiskLevel(String level, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(level, style: const TextStyle(color: Colors.white, fontSize: 12)),
    );
  }

  Widget _buildRecommendationItem(String title, String description, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(
                  description,
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء التقرير بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }
}
