import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير حركة تفصيلية للأصناف بسندات الإخراج
/// يعرض تفاصيل حركة الأصناف في سندات الإخراج
class ItemsExitVouchersDetailedReportPage extends StatefulWidget {
  const ItemsExitVouchersDetailedReportPage({super.key});

  @override
  State<ItemsExitVouchersDetailedReportPage> createState() => _ItemsExitVouchersDetailedReportPageState();
}

class _ItemsExitVouchersDetailedReportPageState extends State<ItemsExitVouchersDetailedReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedCategory;
  String? _exitReason = 'all';
  final String _sortBy = 'exit_date';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('حركة الأصناف بسندات الإخراج'),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.receipt),
            onPressed: _showVoucherDetails,
            tooltip: 'تفاصيل السندات',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilterSection(localizations),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildExitReasonsSection(),
                  const SizedBox(height: 16),
                  _buildExitVouchersTableSection(),
                  const SizedBox(height: 16),
                  _buildMostExitedItemsSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.purple[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.fromDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, true),
                  controller: TextEditingController(
                    text: _startDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.toDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, false),
                  controller: TextEditingController(
                    text: _endDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: localizations.category,
                    border: const OutlineInputBorder(),
                  ),
                  value: _selectedCategory,
                  items: [
                    DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                    DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                    DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                    DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                  ],
                  onChanged: (value) => setState(() => _selectedCategory = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'سبب الإخراج',
                    border: OutlineInputBorder(),
                  ),
                  value: _exitReason,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الأسباب')),
                    DropdownMenuItem(value: 'sale', child: Text('بيع')),
                    DropdownMenuItem(value: 'damage', child: Text('تلف')),
                    DropdownMenuItem(value: 'expiry', child: Text('انتهاء صلاحية')),
                    DropdownMenuItem(value: 'transfer', child: Text('نقل')),
                    DropdownMenuItem(value: 'return', child: Text('إرجاع')),
                  ],
                  onChanged: (value) => setState(() => _exitReason = value),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.exit_to_app, color: Colors.purple, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص سندات الإخراج',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي السندات', '285', Colors.purple, Icons.receipt),
                _buildSummaryCard('إجمالي الكمية', '1,485', Colors.blue, Icons.inventory),
                _buildSummaryCard('قيمة الإخراج', '485,000 ر.س', Colors.green, Icons.monetization_on),
                _buildSummaryCard('متوسط السند', '1,702 ر.س', Colors.orange, Icons.calculate),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExitReasonsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.pie_chart, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'توزيع أسباب الإخراج',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildReasonCard('بيع', '185 سند', Colors.green),
                _buildReasonCard('تلف', '45 سند', Colors.red),
                _buildReasonCard('نقل', '35 سند', Colors.blue),
                _buildReasonCard('أخرى', '20 سند', Colors.orange),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExitVouchersTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل سندات الإخراج',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('رقم السند')),
                  DataColumn(label: Text('التاريخ')),
                  DataColumn(label: Text('اسم الصنف')),
                  DataColumn(label: Text('الكمية')),
                  DataColumn(label: Text('القيمة')),
                  DataColumn(label: Text('سبب الإخراج')),
                  DataColumn(label: Text('المستودع')),
                  DataColumn(label: Text('الموظف')),
                  DataColumn(label: Text('ملاحظات')),
                ],
                rows: _buildExitVouchersRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMostExitedItemsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.trending_up, color: Colors.amber, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'الأصناف الأكثر إخراجاً',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildMostExitedItemsList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _createExitVoucher,
                    icon: const Icon(Icons.add),
                    label: const Text('إنشاء سند إخراج'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _auditExitVouchers,
                    icon: const Icon(Icons.fact_check),
                    label: const Text('مراجعة السندات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildMostExitedItemsList() {
    final mostExitedItems = [
      {'name': 'لابتوب ديل XPS 13', 'quantity': '125 قطعة', 'value': '562,500 ر.س', 'vouchers': '45 سند'},
      {'name': 'هاتف آيفون 15', 'quantity': '95 قطعة', 'value': '361,000 ر.س', 'vouchers': '35 سند'},
      {'name': 'طابعة HP LaserJet', 'quantity': '75 قطعة', 'value': '225,000 ر.س', 'vouchers': '25 سند'},
    ];

    return mostExitedItems.map((item) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.amber.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.amber.withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.trending_up, color: Colors.amber, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item['name']!,
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  'كمية: ${item['quantity']} • قيمة: ${item['value']} • سندات: ${item['vouchers']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildExitVouchersRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('EX-001')),
        const DataCell(Text('2024-01-15')),
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('5')),
        const DataCell(Text('22,500 ر.س')),
        DataCell(_buildReasonBadge('بيع', Colors.green)),
        const DataCell(Text('المستودع الرئيسي')),
        const DataCell(Text('أحمد محمد')),
        const DataCell(Text('بيع عادي')),
      ]),
      DataRow(cells: [
        const DataCell(Text('EX-002')),
        const DataCell(Text('2024-01-18')),
        const DataCell(Text('هاتف آيفون 15')),
        const DataCell(Text('3')),
        const DataCell(Text('11,400 ر.س')),
        DataCell(_buildReasonBadge('بيع', Colors.green)),
        const DataCell(Text('مستودع الفرع الأول')),
        const DataCell(Text('فاطمة علي')),
        const DataCell(Text('بيع مع خصم')),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildReasonCard(String reason, String count, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(reason, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildReasonBadge(String reason, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(reason, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showVoucherDetails() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تفاصيل السندات')),
    );
  }

  void _createExitVoucher() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء سند إخراج جديد')),
    );
  }

  void _auditExitVouchers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مراجعة سندات الإخراج')),
    );
  }
}
