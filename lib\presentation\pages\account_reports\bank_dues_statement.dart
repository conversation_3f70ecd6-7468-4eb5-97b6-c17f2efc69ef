import 'package:flutter/material.dart';

/// صفحة كشف مستحقات البنوك
/// تعرض المستحقات والالتزامات البنكية
class BankDuesStatementPage extends StatefulWidget {
  const BankDuesStatementPage({super.key});

  @override
  State<BankDuesStatementPage> createState() => _BankDuesStatementPageState();
}

class _BankDuesStatementPageState extends State<BankDuesStatementPage> {
  String _selectedBank = 'all';
  String _selectedType = 'all';

  // بيانات تجريبية للبنوك
  final Map<String, String> _banks = {
    'all': 'جميع البنوك',
    'alahli': 'البنك الأهلي',
    'alrajhi': 'بنك الراجحي',
    'riyad': 'بنك الرياض',
    'samba': 'بنك سامبا',
    'alinma': 'بنك الإنماء',
  };

  // بيانات تجريبية لمستحقات البنوك
  final List<Map<String, dynamic>> _bankDuesData = [
    {
      'bankName': 'البنك الأهلي',
      'bankCode': 'alahli',
      'accountNumber': '*********',
      'dueType': 'قرض تجاري',
      'originalAmount': 500000.0,
      'remainingAmount': 350000.0,
      'paidAmount': 150000.0,
      'monthlyInstallment': 15000.0,
      'interestRate': 5.5,
      'startDate': '2023-01-15',
      'endDate': '2026-01-15',
      'nextPaymentDate': '2024-02-15',
      'status': 'نشط',
      'overdueAmount': 0.0,
    },
    {
      'bankName': 'بنك الراجحي',
      'bankCode': 'alrajhi',
      'accountNumber': '*********',
      'dueType': 'تمويل عقاري',
      'originalAmount': 800000.0,
      'remainingAmount': 650000.0,
      'paidAmount': 150000.0,
      'monthlyInstallment': 8500.0,
      'interestRate': 4.2,
      'startDate': '2022-06-01',
      'endDate': '2032-06-01',
      'nextPaymentDate': '2024-02-01',
      'status': 'نشط',
      'overdueAmount': 0.0,
    },
    {
      'bankName': 'بنك الرياض',
      'bankCode': 'riyad',
      'accountNumber': '*********',
      'dueType': 'بطاقة ائتمان',
      'originalAmount': 50000.0,
      'remainingAmount': 25000.0,
      'paidAmount': 25000.0,
      'monthlyInstallment': 2500.0,
      'interestRate': 18.0,
      'startDate': '2023-08-01',
      'endDate': '2024-08-01',
      'nextPaymentDate': '2024-02-10',
      'status': 'نشط',
      'overdueAmount': 2500.0,
    },
    {
      'bankName': 'بنك سامبا',
      'bankCode': 'samba',
      'accountNumber': '*********',
      'dueType': 'تمويل سيارة',
      'originalAmount': 120000.0,
      'remainingAmount': 45000.0,
      'paidAmount': 75000.0,
      'monthlyInstallment': 3500.0,
      'interestRate': 6.8,
      'startDate': '2022-03-01',
      'endDate': '2025-03-01',
      'nextPaymentDate': '2024-02-01',
      'status': 'نشط',
      'overdueAmount': 0.0,
    },
    {
      'bankName': 'بنك الإنماء',
      'bankCode': 'alinma',
      'accountNumber': '*********',
      'dueType': 'قرض شخصي',
      'originalAmount': 80000.0,
      'remainingAmount': 0.0,
      'paidAmount': 80000.0,
      'monthlyInstallment': 0.0,
      'interestRate': 7.5,
      'startDate': '2021-01-01',
      'endDate': '2023-12-31',
      'nextPaymentDate': '-',
      'status': 'مسدد',
      'overdueAmount': 0.0,
    },
  ];

  @override
  Widget build(BuildContext context) {
    final filteredData = _getFilteredData();
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('كشف مستحقات البنوك'),
        backgroundColor: Colors.blueGrey,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedBank,
                        decoration: const InputDecoration(
                          labelText: 'البنك',
                          border: OutlineInputBorder(),
                        ),
                        items: _banks.entries.map((entry) {
                          return DropdownMenuItem(
                            value: entry.key,
                            child: Text(entry.value),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedBank = value!;
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedType,
                        decoration: const InputDecoration(
                          labelText: 'نوع المستحق',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الأنواع')),
                          DropdownMenuItem(value: 'قرض تجاري', child: Text('قرض تجاري')),
                          DropdownMenuItem(value: 'تمويل عقاري', child: Text('تمويل عقاري')),
                          DropdownMenuItem(value: 'بطاقة ائتمان', child: Text('بطاقة ائتمان')),
                          DropdownMenuItem(value: 'تمويل سيارة', child: Text('تمويل سيارة')),
                          DropdownMenuItem(value: 'قرض شخصي', child: Text('قرض شخصي')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedType = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // ملخص المستحقات
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              color: Colors.blueGrey[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    const Text(
                      'ملخص مستحقات البنوك',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Column(
                          children: [
                            const Text('إجمالي المستحقات'),
                            Text(
                              '${_getTotalRemaining(filteredData)} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.red,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('إجمالي المدفوع'),
                            Text(
                              '${_getTotalPaid(filteredData)} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('المتأخرات'),
                            Text(
                              '${_getTotalOverdue(filteredData)} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.orange,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                _buildStatCard('عدد المستحقات', filteredData.length.toString(), Colors.blue),
                _buildStatCard('المستحقات النشطة', _getActiveCount(filteredData).toString(), Colors.green),
                _buildStatCard('الأقساط الشهرية', '${_getTotalMonthlyInstallments(filteredData)} ر.س', Colors.orange),
                _buildStatCard('متوسط الفائدة', '${_getAverageInterestRate(filteredData)}%', Colors.purple),
              ],
            ),
          ),

          // قائمة المستحقات
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: filteredData.length,
              itemBuilder: (context, index) {
                final due = filteredData[index];
                double completionPercentage = (due['paidAmount'] / due['originalAmount']) * 100;
                
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: _getStatusColor(due['status']),
                      child: Text(
                        due['bankName'].substring(0, 2),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    title: Text(
                      '${due['bankName']} - ${due['dueType']}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('المتبقي: ${due['remainingAmount'].toStringAsFixed(2)} ر.س'),
                        Text('القسط الشهري: ${due['monthlyInstallment'].toStringAsFixed(2)} ر.س'),
                        const SizedBox(height: 4),
                        LinearProgressIndicator(
                          value: completionPercentage / 100,
                          backgroundColor: Colors.grey[300],
                          valueColor: AlwaysStoppedAnimation<Color>(
                            _getStatusColor(due['status']),
                          ),
                        ),
                        Text('مكتمل: ${completionPercentage.toStringAsFixed(1)}%'),
                      ],
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('المبلغ الأصلي', '${due['originalAmount'].toStringAsFixed(2)} ر.س', Icons.account_balance),
                                ),
                                Expanded(
                                  child: _buildDetailCard('المبلغ المتبقي', '${due['remainingAmount'].toStringAsFixed(2)} ر.س', Icons.money_off),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('المبلغ المدفوع', '${due['paidAmount'].toStringAsFixed(2)} ر.س', Icons.payment),
                                ),
                                Expanded(
                                  child: _buildDetailCard('معدل الفائدة', '${due['interestRate']}%', Icons.percent),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('رقم الحساب', due['accountNumber'], Icons.credit_card),
                                ),
                                Expanded(
                                  child: _buildDetailCard('الدفعة التالية', due['nextPaymentDate'], Icons.schedule),
                                ),
                              ],
                            ),
                            if (due['overdueAmount'] > 0) ...[
                              const SizedBox(height: 8),
                              Container(
                                padding: const EdgeInsets.all(8.0),
                                decoration: BoxDecoration(
                                  color: Colors.orange[100],
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                  children: [
                                    const Icon(Icons.warning, color: Colors.orange),
                                    const SizedBox(width: 8),
                                    Text(
                                      'مبلغ متأخر: ${due['overdueAmount'].toStringAsFixed(2)} ر.س',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: Colors.orange,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshData,
        backgroundColor: Colors.blueGrey,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث البيانات'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailCard(String title, String value, IconData icon) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Icon(icon, size: 20, color: Colors.grey[600]),
            const SizedBox(height: 4),
            Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
            Text(
              title,
              style: const TextStyle(fontSize: 10),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'نشط':
        return Colors.green;
      case 'مسدد':
        return Colors.blue;
      case 'متأخر':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  List<Map<String, dynamic>> _getFilteredData() {
    return _bankDuesData.where((due) {
      bool bankMatch = _selectedBank == 'all' || due['bankCode'] == _selectedBank;
      bool typeMatch = _selectedType == 'all' || due['dueType'] == _selectedType;
      return bankMatch && typeMatch;
    }).toList();
  }

  double _getTotalRemaining(List<Map<String, dynamic>> data) {
    return data.fold(0.0, (sum, due) => sum + due['remainingAmount']);
  }

  double _getTotalPaid(List<Map<String, dynamic>> data) {
    return data.fold(0.0, (sum, due) => sum + due['paidAmount']);
  }

  double _getTotalOverdue(List<Map<String, dynamic>> data) {
    return data.fold(0.0, (sum, due) => sum + due['overdueAmount']);
  }

  int _getActiveCount(List<Map<String, dynamic>> data) {
    return data.where((due) => due['status'] == 'نشط').length;
  }

  double _getTotalMonthlyInstallments(List<Map<String, dynamic>> data) {
    return data.where((due) => due['status'] == 'نشط').fold(0.0, (sum, due) => sum + due['monthlyInstallment']);
  }

  String _getAverageInterestRate(List<Map<String, dynamic>> data) {
    if (data.isEmpty) return '0.0';
    double total = data.fold(0.0, (sum, due) => sum + due['interestRate']);
    return (total / data.length).toStringAsFixed(1);
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة كشف مستحقات البنوك')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير كشف مستحقات البنوك')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث بيانات مستحقات البنوك'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
