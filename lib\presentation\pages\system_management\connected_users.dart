import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة المستخدمين المتصلين بالنظام
/// تعرض قائمة بجميع المستخدمين المتصلين حالياً مع تفاصيل الجلسة
class ConnectedUsersPage extends StatefulWidget {
  const ConnectedUsersPage({super.key});

  @override
  State<ConnectedUsersPage> createState() => _ConnectedUsersPageState();
}

class _ConnectedUsersPageState extends State<ConnectedUsersPage> {
  final _searchController = TextEditingController();

  String _searchQuery = '';
  String _selectedFilter = 'all';
  bool _autoRefresh = true;

  final List<Map<String, dynamic>> _connectedUsers = [
    {
      'id': 'user1',
      'name': 'أحمد محمد',
      'role': 'مدير',
      'department': 'المبيعات',
      'loginTime': '2024/01/25 08:30',
      'lastActivity': '2024/01/25 14:25',
      'ipAddress': '*************',
      'device': 'Windows 11 - Chrome',
      'location': 'الفرع الرئيسي',
      'status': 'نشط',
      'sessionDuration': '5 ساعات 55 دقيقة',
      'actions': 125,
    },
    {
      'id': 'user2',
      'name': 'فاطمة علي',
      'role': 'محاسب',
      'department': 'المحاسبة',
      'loginTime': '2024/01/25 09:15',
      'lastActivity': '2024/01/25 14:20',
      'ipAddress': '*************',
      'device': 'Windows 10 - Firefox',
      'location': 'الفرع الرئيسي',
      'status': 'نشط',
      'sessionDuration': '5 ساعات 5 دقائق',
      'actions': 89,
    },
    {
      'id': 'user3',
      'name': 'محمد سالم',
      'role': 'موظف',
      'department': 'المخازن',
      'loginTime': '2024/01/25 10:00',
      'lastActivity': '2024/01/25 13:45',
      'ipAddress': '*************',
      'device': 'Android - Chrome Mobile',
      'location': 'مستودع الشمال',
      'status': 'خامل',
      'sessionDuration': '4 ساعات 25 دقيقة',
      'actions': 45,
    },
    {
      'id': 'user4',
      'name': 'نورا أحمد',
      'role': 'مشرف',
      'department': 'المشتريات',
      'loginTime': '2024/01/25 07:45',
      'lastActivity': '2024/01/25 14:22',
      'ipAddress': '*************',
      'device': 'MacOS - Safari',
      'location': 'فرع الشرق',
      'status': 'نشط',
      'sessionDuration': '6 ساعات 40 دقيقة',
      'actions': 156,
    },
    {
      'id': 'user5',
      'name': 'خالد عبدالله',
      'role': 'موظف',
      'department': 'المبيعات',
      'loginTime': '2024/01/25 11:30',
      'lastActivity': '2024/01/25 12:15',
      'ipAddress': '*************',
      'device': 'iOS - Safari Mobile',
      'location': 'فرع الغرب',
      'status': 'منقطع',
      'sessionDuration': '2 ساعة 55 دقيقة',
      'actions': 23,
    },
  ];

  List<Map<String, dynamic>> get _filteredUsers {
    List<Map<String, dynamic>> filtered = _connectedUsers;

    // تطبيق فلتر الحالة
    if (_selectedFilter == 'active') {
      filtered = filtered.where((user) => user['status'] == 'نشط').toList();
    } else if (_selectedFilter == 'idle') {
      filtered = filtered.where((user) => user['status'] == 'خامل').toList();
    } else if (_selectedFilter == 'disconnected') {
      filtered = filtered.where((user) => user['status'] == 'منقطع').toList();
    }

    // تطبيق البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered
          .where((user) =>
              user['name']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              user['role']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              user['department']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              user['location']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()))
          .toList();
    }

    return filtered;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.connectedUsers),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(_autoRefresh ? Icons.refresh : Icons.refresh_outlined),
            onPressed: _toggleAutoRefresh,
            tooltip: _autoRefresh
                ? localizations.stopAutoRefresh
                : localizations.enableAutoRefresh,
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showSessionAnalytics,
            tooltip: localizations.sessionAnalytics,
          ),
        ],
      ),
      body: Column(
        children: [
          // بطاقة البحث والفلترة
          Card(
            margin: const EdgeInsets.all(16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // شريط البحث
                  TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      labelText: localizations.searchUser,
                      prefixIcon: const Icon(Icons.search),
                      border: const OutlineInputBorder(),
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 8),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),

                  const SizedBox(height: 16),

                  // فلاتر الحالة
                  Row(
                    children: [
                      const Text('الفلتر:',
                          style: TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(width: 16),
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedFilter,
                          decoration: const InputDecoration(
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 8),
                          ),
                          items: const [
                            DropdownMenuItem(
                                value: 'all', child: Text('جميع المستخدمين')),
                            DropdownMenuItem(
                                value: 'active', child: Text('النشطين')),
                            DropdownMenuItem(
                                value: 'idle', child: Text('الخاملين')),
                            DropdownMenuItem(
                                value: 'disconnected',
                                child: Text('المنقطعين')),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedFilter = value!;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // إحصائيات سريعة
          Card(
            margin: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatCard('المجموع', _connectedUsers.length.toString(),
                      Colors.blue),
                  _buildStatCard(
                      'النشطين',
                      _connectedUsers
                          .where((u) => u['status'] == 'نشط')
                          .length
                          .toString(),
                      Colors.green),
                  _buildStatCard(
                      'الخاملين',
                      _connectedUsers
                          .where((u) => u['status'] == 'خامل')
                          .length
                          .toString(),
                      Colors.orange),
                  _buildStatCard(
                      'المنقطعين',
                      _connectedUsers
                          .where((u) => u['status'] == 'منقطع')
                          .length
                          .toString(),
                      Colors.red),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // قائمة المستخدمين المتصلين
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _filteredUsers.length,
              itemBuilder: (context, index) {
                final user = _filteredUsers[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: _getStatusColor(user['status']),
                      child: Icon(
                        _getStatusIcon(user['status']),
                        color: Colors.white,
                      ),
                    ),
                    title: Text(
                      user['name'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('${user['role']} - ${user['department']}'),
                        Text('${user['location']} | ${user['sessionDuration']}',
                            style: const TextStyle(fontSize: 12)),
                      ],
                    ),
                    trailing: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getStatusColor(user['status']).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        user['status'],
                        style: TextStyle(
                          color: _getStatusColor(user['status']),
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            _buildDetailRow('وقت الدخول:', user['loginTime']),
                            _buildDetailRow('آخر نشاط:', user['lastActivity']),
                            _buildDetailRow('عنوان IP:', user['ipAddress']),
                            _buildDetailRow('الجهاز:', user['device']),
                            _buildDetailRow(
                                'عدد العمليات:', '${user['actions']} عملية'),

                            const SizedBox(height: 16),

                            // أزرار العمليات
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                ElevatedButton.icon(
                                  onPressed: () => _sendMessage(user),
                                  icon: const Icon(Icons.message, size: 16),
                                  label: const Text('رسالة'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 8),
                                  ),
                                ),
                                ElevatedButton.icon(
                                  onPressed: () => _viewUserActivity(user),
                                  icon: const Icon(Icons.visibility, size: 16),
                                  label: const Text('النشاط'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 8),
                                  ),
                                ),
                                ElevatedButton.icon(
                                  onPressed: () => _disconnectUser(user),
                                  icon: const Icon(Icons.logout, size: 16),
                                  label: const Text('قطع الاتصال'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 8),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshUsersList,
        backgroundColor: Colors.teal,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Column(
      children: [
        CircleAvatar(
          backgroundColor: color,
          child: Text(
            value,
            style: const TextStyle(
                color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'نشط':
        return Colors.green;
      case 'خامل':
        return Colors.orange;
      case 'منقطع':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'نشط':
        return Icons.circle;
      case 'خامل':
        return Icons.pause_circle;
      case 'منقطع':
        return Icons.cancel;
      default:
        return Icons.help;
    }
  }

  void _toggleAutoRefresh() {
    setState(() {
      _autoRefresh = !_autoRefresh;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(_autoRefresh
            ? 'تم تفعيل التحديث التلقائي'
            : 'تم إيقاف التحديث التلقائي'),
        backgroundColor: _autoRefresh ? Colors.green : Colors.orange,
      ),
    );
  }

  void _refreshUsersList() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث قائمة المستخدمين'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _sendMessage(Map<String, dynamic> user) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('إرسال رسالة إلى ${user['name']}')),
    );
  }

  void _viewUserActivity(Map<String, dynamic> user) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض نشاط المستخدم ${user['name']}')),
    );
  }

  void _disconnectUser(Map<String, dynamic> user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد قطع الاتصال'),
        content: Text('هل أنت متأكد من قطع اتصال المستخدم ${user['name']}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم قطع اتصال المستخدم ${user['name']}'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('قطع الاتصال',
                style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showSessionAnalytics() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تحليلات الجلسات')),
    );
  }
}
