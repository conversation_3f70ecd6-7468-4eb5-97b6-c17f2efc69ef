import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير الأصناف المكررة للعميل
/// يعرض الأصناف التي اشتراها العميل أكثر من مرة
class CustomerDuplicateItemsReportPage extends StatefulWidget {
  const CustomerDuplicateItemsReportPage({super.key});

  @override
  State<CustomerDuplicateItemsReportPage> createState() => _CustomerDuplicateItemsReportPageState();
}

class _CustomerDuplicateItemsReportPageState extends State<CustomerDuplicateItemsReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedCustomer;
  String? _duplicateThreshold = '2';
  final String _sortBy = 'purchase_count';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الأصناف المكررة للعميل'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showDuplicateAnalysis,
            tooltip: 'تحليل التكرار',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilterSection(localizations),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildDuplicateStatsSection(),
                  const SizedBox(height: 16),
                  _buildDuplicateItemsTableSection(),
                  const SizedBox(height: 16),
                  _buildTopDuplicateItemsSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.deepPurple[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.fromDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, true),
                  controller: TextEditingController(
                    text: _startDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.toDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, false),
                  controller: TextEditingController(
                    text: _endDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'العميل',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedCustomer,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع العملاء')),
                    DropdownMenuItem(value: 'customer1', child: Text('أحمد محمد')),
                    DropdownMenuItem(value: 'customer2', child: Text('فاطمة علي')),
                    DropdownMenuItem(value: 'customer3', child: Text('محمد سالم')),
                    DropdownMenuItem(value: 'customer4', child: Text('نورا أحمد')),
                  ],
                  onChanged: (value) => setState(() => _selectedCustomer = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'حد التكرار',
                    border: OutlineInputBorder(),
                  ),
                  value: _duplicateThreshold,
                  items: const [
                    DropdownMenuItem(value: '2', child: Text('مرتين أو أكثر')),
                    DropdownMenuItem(value: '3', child: Text('3 مرات أو أكثر')),
                    DropdownMenuItem(value: '5', child: Text('5 مرات أو أكثر')),
                    DropdownMenuItem(value: '10', child: Text('10 مرات أو أكثر')),
                  ],
                  onChanged: (value) => setState(() => _duplicateThreshold = value),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.repeat, color: Colors.deepPurple, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص الأصناف المكررة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي العملاء', '125', Colors.deepPurple, Icons.people),
                _buildSummaryCard('أصناف مكررة', '485', Colors.blue, Icons.repeat),
                _buildSummaryCard('متوسط التكرار', '3.2', Colors.green, Icons.calculate),
                _buildSummaryCard('قيمة المكررات', '285,000 ر.س', Colors.orange, Icons.monetization_on),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDuplicateStatsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.bar_chart, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'إحصائيات التكرار',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildStatsCard('مرتين', '185 صنف', Colors.green),
                _buildStatsCard('3-5 مرات', '125 صنف', Colors.blue),
                _buildStatsCard('6-10 مرات', '85 صنف', Colors.orange),
                _buildStatsCard('أكثر من 10', '90 صنف', Colors.red),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDuplicateItemsTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل الأصناف المكررة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('اسم العميل')),
                  DataColumn(label: Text('اسم الصنف')),
                  DataColumn(label: Text('عدد مرات الشراء')),
                  DataColumn(label: Text('إجمالي الكمية')),
                  DataColumn(label: Text('إجمالي القيمة')),
                  DataColumn(label: Text('متوسط السعر')),
                  DataColumn(label: Text('أول شراء')),
                  DataColumn(label: Text('آخر شراء')),
                  DataColumn(label: Text('التصنيف')),
                ],
                rows: _buildDuplicateItemsRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopDuplicateItemsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.star, color: Colors.amber, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'الأصناف الأكثر تكراراً',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildTopDuplicateItemsList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _createLoyaltyProgram,
                    icon: const Icon(Icons.card_giftcard),
                    label: const Text('برنامج ولاء'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _createBundleOffers,
                    icon: const Icon(Icons.local_offer),
                    label: const Text('عروض مجمعة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _sendPersonalizedOffers,
                    icon: const Icon(Icons.email),
                    label: const Text('عروض شخصية'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _analyzeCustomerBehavior,
                    icon: const Icon(Icons.psychology),
                    label: const Text('تحليل السلوك'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildTopDuplicateItemsList() {
    final topItems = [
      {'item': 'لابتوب ديل XPS 13', 'customer': 'أحمد محمد', 'count': '15 مرة', 'value': '67,500 ر.س'},
      {'item': 'هاتف آيفون 15', 'customer': 'فاطمة علي', 'count': '12 مرة', 'value': '45,600 ر.س'},
      {'item': 'طابعة HP LaserJet', 'customer': 'محمد سالم', 'count': '8 مرات', 'value': '24,000 ر.س'},
      {'item': 'شاشة سامسونج 27"', 'customer': 'نورا أحمد', 'count': '6 مرات', 'value': '18,000 ر.س'},
    ];

    return topItems.map((item) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.amber.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.amber.withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.star, color: Colors.amber, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${item['item']} - ${item['customer']}',
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  'تكرار: ${item['count']} • قيمة: ${item['value']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildDuplicateItemsRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('أحمد محمد')),
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('15')),
        const DataCell(Text('15')),
        const DataCell(Text('67,500 ر.س')),
        const DataCell(Text('4,500 ر.س')),
        const DataCell(Text('2023-01-15')),
        const DataCell(Text('2024-01-20')),
        DataCell(_buildFrequencyBadge('عالي', Colors.red)),
      ]),
      DataRow(cells: [
        const DataCell(Text('فاطمة علي')),
        const DataCell(Text('هاتف آيفون 15')),
        const DataCell(Text('12')),
        const DataCell(Text('12')),
        const DataCell(Text('45,600 ر.س')),
        const DataCell(Text('3,800 ر.س')),
        const DataCell(Text('2023-02-10')),
        const DataCell(Text('2024-01-18')),
        DataCell(_buildFrequencyBadge('عالي', Colors.red)),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatsCard(String range, String count, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(range, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFrequencyBadge(String frequency, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(frequency, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showDuplicateAnalysis() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تحليل التكرار المتقدم')),
    );
  }

  void _createLoyaltyProgram() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء برنامج ولاء للعملاء المتكررين')),
    );
  }

  void _createBundleOffers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء عروض مجمعة للأصناف المكررة')),
    );
  }

  void _sendPersonalizedOffers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إرسال عروض شخصية للعملاء')),
    );
  }

  void _analyzeCustomerBehavior() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحليل سلوك العملاء الشرائي')),
    );
  }
}
