import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة طباعة بيانات أصناف معينة
/// تتيح اختيار أصناف محددة وطباعة بياناتها
class PrintSpecificItemsDataPage extends StatefulWidget {
  const PrintSpecificItemsDataPage({super.key});

  @override
  State<PrintSpecificItemsDataPage> createState() =>
      _PrintSpecificItemsDataPageState();
}

class _PrintSpecificItemsDataPageState
    extends State<PrintSpecificItemsDataPage> {
  final _searchController = TextEditingController();

  String _searchQuery = '';
  String? _selectedCategory;
  String? _selectedPrintFormat;
  bool _includeImages = false;
  bool _includePrices = true;
  bool _includeStock = true;
  bool _includeBarcode = false;

  final List<String> _categories = [
    'إلكترونيات',
    'أثاث',
    'ملابس',
    'أدوات منزلية',
    'كتب ومجلات',
    'رياضة وترفيه',
  ];

  final List<Map<String, String>> _printFormats = [
    {
      'id': 'detailed',
      'name': 'تقرير مفصل',
      'description': 'جميع التفاصيل مع الصور'
    },
    {'id': 'summary', 'name': 'ملخص', 'description': 'البيانات الأساسية فقط'},
    {'id': 'catalog', 'name': 'كتالوج', 'description': 'تنسيق كتالوج للعرض'},
    {'id': 'inventory', 'name': 'جرد', 'description': 'تقرير جرد مع الكميات'},
    {
      'id': 'price_list',
      'name': 'قائمة أسعار',
      'description': 'الأسعار والأكواد فقط'
    },
  ];

  final List<Map<String, dynamic>> _items = [
    {
      'id': 'item1',
      'code': 'A001',
      'name': 'لابتوب ديل XPS 13',
      'category': 'إلكترونيات',
      'price': 2500.0,
      'cost': 2000.0,
      'stock': 25,
      'minStock': 5,
      'supplier': 'شركة التقنية المتقدمة',
      'barcode': '1234567890123',
      'description': 'لابتوب عالي الأداء مع معالج Intel Core i7',
      'isSelected': false,
    },
    {
      'id': 'item2',
      'code': 'B002',
      'name': 'طابعة HP LaserJet Pro',
      'category': 'إلكترونيات',
      'price': 450.0,
      'cost': 350.0,
      'stock': 12,
      'minStock': 3,
      'supplier': 'مؤسسة الطباعة الحديثة',
      'barcode': '2345678901234',
      'description': 'طابعة ليزر سريعة وموفرة للحبر',
      'isSelected': false,
    },
    {
      'id': 'item3',
      'code': 'C003',
      'name': 'كرسي مكتب جلد طبيعي',
      'category': 'أثاث',
      'price': 350.0,
      'cost': 250.0,
      'stock': 8,
      'minStock': 2,
      'supplier': 'شركة الأثاث الفاخر',
      'barcode': '3456789012345',
      'description': 'كرسي مكتب مريح من الجلد الطبيعي',
      'isSelected': false,
    },
    {
      'id': 'item4',
      'code': 'D004',
      'name': 'شاشة سامسونج 27 بوصة',
      'category': 'إلكترونيات',
      'price': 800.0,
      'cost': 650.0,
      'stock': 15,
      'minStock': 4,
      'supplier': 'وكيل سامسونج المعتمد',
      'barcode': '4567890123456',
      'description': 'شاشة عالية الدقة 4K مع تقنية HDR',
      'isSelected': false,
    },
    {
      'id': 'item5',
      'code': 'E005',
      'name': 'مكتب خشبي كلاسيكي',
      'category': 'أثاث',
      'price': 1200.0,
      'cost': 900.0,
      'stock': 6,
      'minStock': 1,
      'supplier': 'مصنع الأثاث الملكي',
      'barcode': '5678901234567',
      'description': 'مكتب خشبي فاخر بتصميم كلاسيكي',
      'isSelected': false,
    },
  ];

  List<Map<String, dynamic>> get _filteredItems {
    List<Map<String, dynamic>> filtered = _items;

    // فلتر الفئة
    if (_selectedCategory != null) {
      filtered = filtered
          .where((item) => item['category'] == _selectedCategory)
          .toList();
    }

    // البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered
          .where((item) =>
              item['name']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              item['code']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              item['description']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()))
          .toList();
    }

    return filtered;
  }

  List<Map<String, dynamic>> get _selectedItems {
    return _items.where((item) => item['isSelected'] == true).toList();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.printSpecificItemsData),
        backgroundColor: Colors.deepOrange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.select_all),
            onPressed: _selectAllItems,
            tooltip: 'تحديد الكل',
          ),
          IconButton(
            icon: const Icon(Icons.clear_all),
            onPressed: _clearAllSelections,
            tooltip: 'إلغاء التحديد',
          ),
        ],
      ),
      body: Column(
        children: [
          // بطاقة البحث والفلترة
          Card(
            margin: const EdgeInsets.all(16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // شريط البحث
                  TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      labelText: 'البحث في الأصناف',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),

                  const SizedBox(height: 16),

                  // فلتر الفئة
                  DropdownButtonFormField<String>(
                    value: _selectedCategory,
                    decoration: const InputDecoration(
                      labelText: 'فلتر حسب الفئة',
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    ),
                    items: [
                      const DropdownMenuItem(
                          value: null, child: Text('جميع الفئات')),
                      ..._categories.map((category) => DropdownMenuItem(
                            value: category,
                            child: Text(category),
                          )),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedCategory = value;
                      });
                    },
                  ),
                ],
              ),
            ),
          ),

          // إحصائيات التحديد
          Card(
            margin: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatCard(
                      'المجموع', _filteredItems.length.toString(), Colors.blue),
                  _buildStatCard(
                      'المحدد', _selectedItems.length.toString(), Colors.green),
                  _buildStatCard(
                      'المتبقي',
                      (_filteredItems.length - _selectedItems.length)
                          .toString(),
                      Colors.orange),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // قائمة الأصناف
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _filteredItems.length,
              itemBuilder: (context, index) {
                final item = _filteredItems[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: CheckboxListTile(
                    value: item['isSelected'],
                    onChanged: (value) {
                      setState(() {
                        item['isSelected'] = value ?? false;
                      });
                    },
                    secondary: CircleAvatar(
                      backgroundColor: Colors.deepOrange,
                      child: Text(
                        item['code'].substring(0, 1),
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                    title: Text(
                      '${item['code']} - ${item['name']}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                            'الفئة: ${item['category']} | المورد: ${item['supplier']}'),
                        Text(
                            'السعر: ${item['price']} ر.س | المخزون: ${item['stock']} قطعة'),
                        Text(
                          item['description'],
                          style: const TextStyle(
                              fontSize: 12, fontStyle: FontStyle.italic),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                    isThreeLine: true,
                  ),
                );
              },
            ),
          ),
        ],
      ),
      bottomNavigationBar: _selectedItems.isNotEmpty
          ? Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.3),
                    spreadRadius: 1,
                    blurRadius: 5,
                    offset: const Offset(0, -3),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // خيارات الطباعة
                  Row(
                    children: [
                      // تنسيق الطباعة
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedPrintFormat,
                          decoration: const InputDecoration(
                            labelText: 'تنسيق الطباعة',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 8),
                          ),
                          items: _printFormats
                              .map<DropdownMenuItem<String>>((format) {
                            return DropdownMenuItem<String>(
                              value: format['id'],
                              child: Text(
                                format['name']!,
                                overflow: TextOverflow.ellipsis,
                              ),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedPrintFormat = value;
                            });
                          },
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // خيارات إضافية
                  Wrap(
                    spacing: 8,
                    children: [
                      FilterChip(
                        label: const Text('تضمين الصور'),
                        selected: _includeImages,
                        onSelected: (value) {
                          setState(() {
                            _includeImages = value;
                          });
                        },
                      ),
                      FilterChip(
                        label: const Text('تضمين الأسعار'),
                        selected: _includePrices,
                        onSelected: (value) {
                          setState(() {
                            _includePrices = value;
                          });
                        },
                      ),
                      FilterChip(
                        label: const Text('تضمين المخزون'),
                        selected: _includeStock,
                        onSelected: (value) {
                          setState(() {
                            _includeStock = value;
                          });
                        },
                      ),
                      FilterChip(
                        label: const Text('تضمين الباركود'),
                        selected: _includeBarcode,
                        onSelected: (value) {
                          setState(() {
                            _includeBarcode = value;
                          });
                        },
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // أزرار العمليات
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _previewPrint,
                          icon: const Icon(Icons.preview),
                          label: const Text('معاينة'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _printItems,
                          icon: const Icon(Icons.print),
                          label: Text('طباعة (${_selectedItems.length})'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.deepOrange,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            )
          : null,
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Column(
      children: [
        CircleAvatar(
          backgroundColor: color,
          child: Text(
            value,
            style: const TextStyle(
                color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  void _selectAllItems() {
    setState(() {
      for (var item in _filteredItems) {
        item['isSelected'] = true;
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم تحديد ${_filteredItems.length} صنف')),
    );
  }

  void _clearAllSelections() {
    setState(() {
      for (var item in _items) {
        item['isSelected'] = false;
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إلغاء جميع التحديدات')),
    );
  }

  void _previewPrint() {
    if (_selectedPrintFormat == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار تنسيق الطباعة'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
            'معاينة طباعة ${_selectedItems.length} صنف بتنسيق $_selectedPrintFormat'),
      ),
    );
  }

  void _printItems() {
    if (_selectedPrintFormat == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار تنسيق الطباعة'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // إنشاء ملخص الطباعة
    String summary = 'طباعة ${_selectedItems.length} صنف\n';
    summary +=
        'التنسيق: ${_printFormats.firstWhere((f) => f['id'] == _selectedPrintFormat)['name']}\n';
    summary += 'الخيارات: ';

    List<String> options = [];
    if (_includeImages) options.add('الصور');
    if (_includePrices) options.add('الأسعار');
    if (_includeStock) options.add('المخزون');
    if (_includeBarcode) options.add('الباركود');

    summary += options.join(', ');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الطباعة'),
        content: Text(summary),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إرسال الطباعة بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
              _clearAllSelections();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.deepOrange),
            child: const Text('طباعة', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
