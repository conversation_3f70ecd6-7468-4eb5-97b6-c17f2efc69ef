import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير تغير مواقع الأصناف
/// يعرض تغييرات مواقع الأصناف في المستودعات
class ItemLocationChangesReportPage extends StatefulWidget {
  const ItemLocationChangesReportPage({super.key});

  @override
  State<ItemLocationChangesReportPage> createState() => _ItemLocationChangesReportPageState();
}

class _ItemLocationChangesReportPageState extends State<ItemLocationChangesReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedWarehouse;
  String? _movementType = 'all';
  String? _selectedUser;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('تغير مواقع الأصناف'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.map),
            onPressed: _showLocationMap,
            tooltip: 'خريطة المواقع',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.teal[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.fromDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, true),
                        controller: TextEditingController(
                          text: _startDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.toDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, false),
                        controller: TextEditingController(
                          text: _endDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.warehouse,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedWarehouse,
                        items: [
                          DropdownMenuItem(value: 'all', child: Text(localizations.allWarehouses)),
                          DropdownMenuItem(value: 'main', child: Text(localizations.mainWarehouse)),
                          DropdownMenuItem(value: 'branch1', child: Text('${localizations.branchWarehouse} الأول')),
                          DropdownMenuItem(value: 'branch2', child: Text('${localizations.branchWarehouse} الثاني')),
                        ],
                        onChanged: (value) => setState(() => _selectedWarehouse = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'نوع الحركة',
                          border: OutlineInputBorder(),
                        ),
                        value: _movementType,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الحركات')),
                          DropdownMenuItem(value: 'transfer', child: Text('نقل بين مستودعات')),
                          DropdownMenuItem(value: 'relocation', child: Text('إعادة ترتيب داخلي')),
                          DropdownMenuItem(value: 'adjustment', child: Text('تعديل موقع')),
                        ],
                        onChanged: (value) => setState(() => _movementType = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'المستخدم',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedUser,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع المستخدمين')),
                          DropdownMenuItem(value: 'admin', child: Text('المدير العام')),
                          DropdownMenuItem(value: 'warehouse', child: Text('أمين المستودع')),
                          DropdownMenuItem(value: 'employee', child: Text('موظف المخزون')),
                        ],
                        onChanged: (value) => setState(() => _selectedUser = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _generateReport,
                        icon: const Icon(Icons.search),
                        label: Text(localizations.generateReport),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.teal,
                          foregroundColor: Colors.white,
                          minimumSize: const Size(double.infinity, 48),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص حركات المواقع
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.location_on, color: Colors.teal, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'ملخص حركات المواقع',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSummaryCard('إجمالي الحركات', '234', Colors.blue, Icons.swap_horiz),
                              _buildSummaryCard('نقل بين مستودعات', '89', Colors.green, Icons.transfer_within_a_station),
                              _buildSummaryCard('إعادة ترتيب', '145', Colors.orange, Icons.shuffle),
                              _buildSummaryCard('أصناف منقولة', '156', Colors.purple, Icons.inventory),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // توزيع الحركات حسب النوع
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'توزيع الحركات حسب النوع',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Container(
                            height: 200,
                            width: double.infinity,
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey[300]!),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  flex: 38,
                                  child: Container(
                                    margin: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.green.withOpacity(0.7),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: const Center(
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Text('نقل بين مستودعات', style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
                                          Text('89 حركة', style: TextStyle(color: Colors.white, fontSize: 12)),
                                          Text('38%', style: TextStyle(color: Colors.white, fontSize: 12)),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                Expanded(
                                  flex: 62,
                                  child: Container(
                                    margin: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.orange.withOpacity(0.7),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: const Center(
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Text('إعادة ترتيب داخلي', style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
                                          Text('145 حركة', style: TextStyle(color: Colors.white, fontSize: 12)),
                                          Text('62%', style: TextStyle(color: Colors.white, fontSize: 12)),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول تفاصيل حركات المواقع
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل حركات المواقع',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('كود الصنف')),
                                DataColumn(label: Text('اسم الصنف')),
                                DataColumn(label: Text('الكمية')),
                                DataColumn(label: Text('الموقع السابق')),
                                DataColumn(label: Text('الموقع الجديد')),
                                DataColumn(label: Text('نوع الحركة')),
                                DataColumn(label: Text('تاريخ النقل')),
                                DataColumn(label: Text('المستخدم')),
                                DataColumn(label: Text('السبب')),
                              ],
                              rows: _buildLocationChangeRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // خريطة المستودعات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.map, color: Colors.blue, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'خريطة توزيع المستودعات',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildWarehouseCard('المستودع الرئيسي', '1,245 صنف', '85%', Colors.blue),
                              _buildWarehouseCard('الفرع الأول', '856 صنف', '92%', Colors.green),
                              _buildWarehouseCard('الفرع الثاني', '634 صنف', '78%', Colors.orange),
                              _buildWarehouseCard('مستودع التبريد', '234 صنف', '95%', Colors.purple),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // أكثر الأصناف نقلاً
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.trending_up, color: Colors.green, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'أكثر الأصناف نقلاً',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          ..._buildMostMovedItemsList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _bulkTransfer,
                                  icon: const Icon(Icons.transfer_within_a_station),
                                  label: const Text('نقل جماعي'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _optimizeLocations,
                                  icon: const Icon(Icons.auto_fix_high),
                                  label: const Text('تحسين المواقع'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _generateTransferOrder,
                                  icon: const Icon(Icons.assignment),
                                  label: const Text('أمر نقل'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _trackMovements,
                                  icon: const Icon(Icons.track_changes),
                                  label: const Text('تتبع الحركات'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<DataRow> _buildLocationChangeRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('ITEM-001')),
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('25')),
        const DataCell(Text('المستودع الرئيسي - A1')),
        const DataCell(Text('الفرع الأول - B2')),
        DataCell(_buildMovementTypeBadge('نقل', Colors.green)),
        const DataCell(Text('2024-02-15')),
        const DataCell(Text('أحمد محمد')),
        const DataCell(Text('إعادة توزيع المخزون')),
      ]),
      DataRow(cells: [
        const DataCell(Text('ITEM-002')),
        const DataCell(Text('قميص قطني')),
        const DataCell(Text('100')),
        const DataCell(Text('الفرع الأول - C1')),
        const DataCell(Text('الفرع الأول - C3')),
        DataCell(_buildMovementTypeBadge('ترتيب', Colors.orange)),
        const DataCell(Text('2024-02-14')),
        const DataCell(Text('فاطمة أحمد')),
        const DataCell(Text('تحسين الوصول')),
      ]),
      DataRow(cells: [
        const DataCell(Text('ITEM-003')),
        const DataCell(Text('عصير برتقال')),
        const DataCell(Text('200')),
        const DataCell(Text('الفرع الثاني - D1')),
        const DataCell(Text('مستودع التبريد - R1')),
        DataCell(_buildMovementTypeBadge('نقل', Colors.green)),
        const DataCell(Text('2024-02-13')),
        const DataCell(Text('محمد علي')),
        const DataCell(Text('متطلبات التبريد')),
      ]),
    ];
  }

  List<Widget> _buildMostMovedItemsList() {
    final items = [
      {'name': 'لابتوب ديل XPS 13', 'moves': '8 حركات', 'lastMove': 'الفرع الأول'},
      {'name': 'قميص قطني', 'moves': '6 حركات', 'lastMove': 'المستودع الرئيسي'},
      {'name': 'هاتف آيفون 15', 'moves': '5 حركات', 'lastMove': 'الفرع الثاني'},
      {'name': 'ساعة ذكية', 'moves': '4 حركات', 'lastMove': 'مستودع التبريد'},
    ];

    return items.map((item) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.teal.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.teal.withOpacity(0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.swap_horiz, color: Colors.teal, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item['name']!,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'آخر موقع: ${item['lastMove']}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.teal,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              item['moves']!,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWarehouseCard(String name, String items, String capacity, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            children: [
              Text(
                capacity,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                items,
                style: TextStyle(
                  fontSize: 10,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                name,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMovementTypeBadge(String type, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        type,
        style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير تغير مواقع الأصناف بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showLocationMap() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض خريطة تفاعلية للمواقع')),
    );
  }

  void _bulkTransfer() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('فتح أداة النقل الجماعي')),
    );
  }

  void _optimizeLocations() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحسين توزيع المواقع تلقائياً')),
    );
  }

  void _generateTransferOrder() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء أمر نقل جديد')),
    );
  }

  void _trackMovements() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تتبع حركات الأصناف في الوقت الفعلي')),
    );
  }
}
