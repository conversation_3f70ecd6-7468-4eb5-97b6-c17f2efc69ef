import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير متابعة الكميات المتبقية لطلبات الشراء
/// يعرض الكميات المتبقية من طلبات الشراء المعلقة
class PurchaseOrdersRemainingQuantitiesReportPage extends StatefulWidget {
  const PurchaseOrdersRemainingQuantitiesReportPage({super.key});

  @override
  State<PurchaseOrdersRemainingQuantitiesReportPage> createState() => _PurchaseOrdersRemainingQuantitiesReportPageState();
}

class _PurchaseOrdersRemainingQuantitiesReportPageState extends State<PurchaseOrdersRemainingQuantitiesReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedSupplier;
  String? _orderStatus = 'all';
  String? _urgencyLevel = 'all';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('متابعة الكميات المتبقية لطلبات الشراء'),
        backgroundColor: Colors.deepOrange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: _sendReminders,
            tooltip: 'إرسال تذكيرات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          _buildFilterSection(localizations),
          
          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildStatusSection(),
                  const SizedBox(height: 16),
                  _buildOrdersTableSection(),
                  const SizedBox(height: 16),
                  _buildUrgentOrdersSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.deepOrange[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.fromDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, true),
                  controller: TextEditingController(
                    text: _startDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.toDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, false),
                  controller: TextEditingController(
                    text: _endDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'المورد',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedSupplier,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الموردين')),
                    DropdownMenuItem(value: 'supplier1', child: Text('شركة التقنية المتقدمة')),
                    DropdownMenuItem(value: 'supplier2', child: Text('مؤسسة الإلكترونيات')),
                    DropdownMenuItem(value: 'supplier3', child: Text('شركة الأجهزة الذكية')),
                  ],
                  onChanged: (value) => setState(() => _selectedSupplier = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'حالة الطلب',
                    border: OutlineInputBorder(),
                  ),
                  value: _orderStatus,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الحالات')),
                    DropdownMenuItem(value: 'pending', child: Text('في الانتظار')),
                    DropdownMenuItem(value: 'partial', child: Text('تسليم جزئي')),
                    DropdownMenuItem(value: 'delayed', child: Text('متأخر')),
                    DropdownMenuItem(value: 'confirmed', child: Text('مؤكد')),
                  ],
                  onChanged: (value) => setState(() => _orderStatus = value),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'مستوى الأولوية',
                    border: OutlineInputBorder(),
                  ),
                  value: _urgencyLevel,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع المستويات')),
                    DropdownMenuItem(value: 'urgent', child: Text('عاجل')),
                    DropdownMenuItem(value: 'high', child: Text('عالي')),
                    DropdownMenuItem(value: 'medium', child: Text('متوسط')),
                    DropdownMenuItem(value: 'low', child: Text('منخفض')),
                  ],
                  onChanged: (value) => setState(() => _urgencyLevel = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.analytics),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.deepOrange,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.shopping_cart, color: Colors.deepOrange, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص طلبات الشراء المعلقة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي الطلبات', '125', Colors.deepOrange, Icons.receipt),
                _buildSummaryCard('كمية متبقية', '2,850', Colors.blue, Icons.inventory),
                _buildSummaryCard('قيمة متبقية', '485,000 ر.س', Colors.green, Icons.monetization_on),
                _buildSummaryCard('طلبات متأخرة', '25', Colors.red, Icons.warning),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.pie_chart, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'توزيع حالات الطلبات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildStatusCard('في الانتظار', '65 طلب', Colors.blue),
                _buildStatusCard('تسليم جزئي', '35 طلب', Colors.orange),
                _buildStatusCard('متأخر', '25 طلب', Colors.red),
                _buildStatusCard('مؤكد', '45 طلب', Colors.green),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrdersTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل طلبات الشراء المعلقة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('رقم الطلب')),
                  DataColumn(label: Text('اسم الصنف')),
                  DataColumn(label: Text('المورد')),
                  DataColumn(label: Text('الكمية المطلوبة')),
                  DataColumn(label: Text('الكمية المستلمة')),
                  DataColumn(label: Text('الكمية المتبقية')),
                  DataColumn(label: Text('تاريخ الطلب')),
                  DataColumn(label: Text('تاريخ التسليم المتوقع')),
                  DataColumn(label: Text('الحالة')),
                  DataColumn(label: Text('الأولوية')),
                ],
                rows: _buildOrdersRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUrgentOrdersSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.priority_high, color: Colors.red, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'الطلبات العاجلة والمتأخرة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildUrgentOrdersList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _followUpWithSuppliers,
                    icon: const Icon(Icons.phone),
                    label: const Text('متابعة الموردين'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _updateDeliveryDates,
                    icon: const Icon(Icons.update),
                    label: const Text('تحديث مواعيد التسليم'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _cancelDelayedOrders,
                    icon: const Icon(Icons.cancel),
                    label: const Text('إلغاء الطلبات المتأخرة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _findAlternativeSuppliers,
                    icon: const Icon(Icons.search),
                    label: const Text('البحث عن موردين بديلين'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildUrgentOrdersList() {
    final urgentOrders = [
      {'order': 'PO-001', 'item': 'لابتوب ديل XPS 13', 'remaining': '15 قطعة', 'delay': '5 أيام', 'priority': 'عاجل'},
      {'order': 'PO-005', 'item': 'هاتف آيفون 15', 'remaining': '25 قطعة', 'delay': '3 أيام', 'priority': 'عالي'},
      {'order': 'PO-012', 'item': 'طابعة HP LaserJet', 'remaining': '8 قطع', 'delay': '7 أيام', 'priority': 'عاجل'},
      {'order': 'PO-018', 'item': 'شاشة سامسونج 27 بوصة', 'remaining': '12 قطعة', 'delay': '2 أيام', 'priority': 'عالي'},
    ];

    return urgentOrders.map((order) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: _getPriorityColor(order['priority']!).withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: _getPriorityColor(order['priority']!).withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.priority_high, color: Colors.red, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${order['order']} - ${order['item']}',
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  'متبقي: ${order['remaining']} • تأخير: ${order['delay']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getPriorityColor(order['priority']!).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              order['priority']!,
              style: TextStyle(
                color: _getPriorityColor(order['priority']!),
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildOrdersRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('PO-001')),
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('شركة التقنية المتقدمة')),
        const DataCell(Text('50')),
        const DataCell(Text('35')),
        const DataCell(Text('15')),
        const DataCell(Text('2024-01-10')),
        const DataCell(Text('2024-01-25')),
        DataCell(_buildStatusBadge('تسليم جزئي', Colors.orange)),
        DataCell(_buildPriorityBadge('عاجل', Colors.red)),
      ]),
      DataRow(cells: [
        const DataCell(Text('PO-002')),
        const DataCell(Text('هاتف آيفون 15')),
        const DataCell(Text('مؤسسة الإلكترونيات')),
        const DataCell(Text('30')),
        const DataCell(Text('5')),
        const DataCell(Text('25')),
        const DataCell(Text('2024-01-12')),
        const DataCell(Text('2024-01-28')),
        DataCell(_buildStatusBadge('في الانتظار', Colors.blue)),
        DataCell(_buildPriorityBadge('عالي', Colors.orange)),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusCard(String status, String count, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(status, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(status, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Widget _buildPriorityBadge(String priority, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(priority, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Color _getPriorityColor(String priority) {
    switch (priority) {
      case 'عاجل':
        return Colors.red;
      case 'عالي':
        return Colors.orange;
      case 'متوسط':
        return Colors.blue;
      case 'منخفض':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير متابعة طلبات الشراء بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _sendReminders() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إرسال تذكيرات للموردين')),
    );
  }

  void _followUpWithSuppliers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('متابعة الموردين للطلبات المعلقة')),
    );
  }

  void _updateDeliveryDates() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحديث مواعيد التسليم المتوقعة')),
    );
  }

  void _cancelDelayedOrders() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إلغاء الطلبات المتأخرة')),
    );
  }

  void _findAlternativeSuppliers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('البحث عن موردين بديلين')),
    );
  }
}
