import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير أعمار المخزون
/// يعرض تحليل أعمار الأصناف في المخزون
class InventoryAgingReportPage extends StatefulWidget {
  const InventoryAgingReportPage({super.key});

  @override
  State<InventoryAgingReportPage> createState() => _InventoryAgingReportPageState();
}

class _InventoryAgingReportPageState extends State<InventoryAgingReportPage> {
  String? _selectedCategory;
  String? _agingPeriod = 'all';
  String? _sortBy = 'aging_days';
  String? _selectedWarehouse;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('أعمار المخزون'),
        backgroundColor: Colors.blueGrey,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.schedule),
            onPressed: _showAgingAnalysis,
            tooltip: 'تحليل الأعمار',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilterSection(localizations),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildAgingPeriodsSection(),
                  const SizedBox(height: 16),
                  _buildAgingTableSection(),
                  const SizedBox(height: 16),
                  _buildSlowMovingItemsSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.blueGrey[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: localizations.category,
                    border: const OutlineInputBorder(),
                  ),
                  value: _selectedCategory,
                  items: [
                    DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                    DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                    DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                    DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                  ],
                  onChanged: (value) => setState(() => _selectedCategory = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'فترة العمر',
                    border: OutlineInputBorder(),
                  ),
                  value: _agingPeriod,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الفترات')),
                    DropdownMenuItem(value: '0-30', child: Text('0-30 يوم')),
                    DropdownMenuItem(value: '31-60', child: Text('31-60 يوم')),
                    DropdownMenuItem(value: '61-90', child: Text('61-90 يوم')),
                    DropdownMenuItem(value: '91-180', child: Text('91-180 يوم')),
                    DropdownMenuItem(value: '180+', child: Text('أكثر من 180 يوم')),
                  ],
                  onChanged: (value) => setState(() => _agingPeriod = value),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'المستودع',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedWarehouse,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع المستودعات')),
                    DropdownMenuItem(value: 'main', child: Text('المستودع الرئيسي')),
                    DropdownMenuItem(value: 'branch1', child: Text('مستودع الفرع الأول')),
                    DropdownMenuItem(value: 'branch2', child: Text('مستودع الفرع الثاني')),
                  ],
                  onChanged: (value) => setState(() => _selectedWarehouse = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'ترتيب حسب',
                    border: OutlineInputBorder(),
                  ),
                  value: _sortBy,
                  items: const [
                    DropdownMenuItem(value: 'aging_days', child: Text('عدد الأيام')),
                    DropdownMenuItem(value: 'item_name', child: Text('اسم الصنف')),
                    DropdownMenuItem(value: 'quantity', child: Text('الكمية')),
                    DropdownMenuItem(value: 'value', child: Text('القيمة')),
                  ],
                  onChanged: (value) => setState(() => _sortBy = value),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.schedule, color: Colors.blueGrey, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص أعمار المخزون',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي الأصناف', '1,285', Colors.blueGrey, Icons.inventory),
                _buildSummaryCard('متوسط العمر', '85 يوم', Colors.blue, Icons.schedule),
                _buildSummaryCard('بطيئة الحركة', '185', Colors.orange, Icons.trending_down),
                _buildSummaryCard('قيمة بطيئة الحركة', '285,000 ر.س', Colors.red, Icons.monetization_on),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAgingPeriodsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.pie_chart, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'توزيع فترات الأعمار',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Column(
              children: [
                Row(
                  children: [
                    _buildAgingCard('0-30 يوم', '485 صنف', Colors.green),
                    _buildAgingCard('31-60 يوم', '325 صنف', Colors.blue),
                    _buildAgingCard('61-90 يوم', '285 صنف', Colors.orange),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    _buildAgingCard('91-180 يوم', '125 صنف', Colors.red),
                    _buildAgingCard('أكثر من 180 يوم', '65 صنف', Colors.purple),
                    const Expanded(child: SizedBox()),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAgingTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل أعمار المخزون',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('اسم الصنف')),
                  DataColumn(label: Text('الفئة')),
                  DataColumn(label: Text('تاريخ الإدخال')),
                  DataColumn(label: Text('عدد الأيام')),
                  DataColumn(label: Text('الكمية')),
                  DataColumn(label: Text('القيمة')),
                  DataColumn(label: Text('المستودع')),
                  DataColumn(label: Text('التصنيف')),
                  DataColumn(label: Text('الحالة')),
                ],
                rows: _buildAgingRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSlowMovingItemsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.trending_down, color: Colors.red, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'الأصناف بطيئة الحركة (أكثر من 90 يوم)',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildSlowMovingItemsList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _createPromotionCampaign,
                    icon: const Icon(Icons.local_offer),
                    label: const Text('حملة ترويجية'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _liquidateSlowItems,
                    icon: const Icon(Icons.sell),
                    label: const Text('تصفية بطيئة الحركة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _transferToOtherBranches,
                    icon: const Icon(Icons.swap_horiz),
                    label: const Text('نقل لفروع أخرى'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _setAgingAlerts,
                    icon: const Icon(Icons.notifications),
                    label: const Text('تنبيهات الأعمار'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildSlowMovingItemsList() {
    final slowMovingItems = [
      {'name': 'طابعة HP LaserJet Pro', 'days': '125 يوم', 'quantity': '15 قطعة', 'value': '45,000 ر.س'},
      {'name': 'شاشة سامسونج 32 بوصة', 'days': '98 يوم', 'quantity': '8 قطع', 'value': '24,000 ر.س'},
      {'name': 'كيبورد ميكانيكي', 'days': '156 يوم', 'quantity': '25 قطعة', 'value': '12,500 ر.س'},
      {'name': 'ماوس لاسلكي', 'days': '189 يوم', 'quantity': '35 قطعة', 'value': '8,750 ر.س'},
    ];

    return slowMovingItems.map((item) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.red.withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.trending_down, color: Colors.red, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item['name']!,
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  'عمر: ${item['days']} • كمية: ${item['quantity']} • قيمة: ${item['value']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Text(
              'بطيء',
              style: TextStyle(
                color: Colors.red,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildAgingRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('طابعة HP LaserJet Pro')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('2023-08-15')),
        const DataCell(Text('125')),
        const DataCell(Text('15')),
        const DataCell(Text('45,000 ر.س')),
        const DataCell(Text('المستودع الرئيسي')),
        DataCell(_buildAgingBadge('بطيء', Colors.red)),
        DataCell(_buildStatusBadge('يحتاج إجراء', Colors.orange)),
      ]),
      DataRow(cells: [
        const DataCell(Text('شاشة سامسونج 32"')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('2023-09-20')),
        const DataCell(Text('98')),
        const DataCell(Text('8')),
        const DataCell(Text('24,000 ر.س')),
        const DataCell(Text('مستودع الفرع الأول')),
        DataCell(_buildAgingBadge('بطيء', Colors.red)),
        DataCell(_buildStatusBadge('يحتاج إجراء', Colors.orange)),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAgingCard(String period, String count, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(period, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAgingBadge(String aging, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(aging, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(status, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showAgingAnalysis() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تحليل الأعمار المتقدم')),
    );
  }

  void _createPromotionCampaign() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء حملة ترويجية للأصناف بطيئة الحركة')),
    );
  }

  void _liquidateSlowItems() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصفية الأصناف بطيئة الحركة')),
    );
  }

  void _transferToOtherBranches() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('نقل الأصناف لفروع أخرى')),
    );
  }

  void _setAgingAlerts() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تعيين تنبيهات أعمار المخزون')),
    );
  }
}
