import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';
import 'fiscal_year/fiscal_year_page.dart';
import 'change_language.dart';
import 'change_theme.dart';
import 'create_edit_fiscal_year.dart';

/// صفحة الإعدادات العامة
/// تحتوي على جميع الإعدادات والوظائف العامة للنظام
class GeneralPage extends StatelessWidget {
  const GeneralPage({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.general),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      backgroundColor: Colors.grey[50],
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: GridView.count(
          crossAxisCount: 3,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          children: [
            // زر السنة المالية
            _buildGeneralCard(
              context,
              localizations.fiscalYear,
              Icons.calendar_today,
              Colors.blue,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const FiscalYearPage(),
                ),
              ),
            ),

            // زر إنشاء/تعديل السنة المالية
            _buildGeneralCard(
              context,
              localizations.createEditFiscalYearFull,
              Icons.edit_calendar,
              Colors.green,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const CreateEditFiscalYear(),
                ),
              ),
            ),

            // زر إعدادات الصفحة
            _buildGeneralCard(
              context,
              localizations.pageSettings,
              Icons.settings_applications,
              Colors.purple,
              () => _showComingSoon(context, localizations.pageSettings),
            ),

            // زر إعدادات الطابعة
            _buildGeneralCard(
              context,
              localizations.printerSettings,
              Icons.print,
              Colors.red,
              () => _showComingSoon(context, localizations.printerSettings),
            ),

            // زر تغيير اللغة
            _buildGeneralCard(
              context,
              localizations.changeLanguage,
              Icons.language,
              Colors.teal,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ChangeLanguage(),
                ),
              ),
            ),

            // زر تغيير المظهر
            _buildGeneralCard(
              context,
              localizations.changeTheme,
              Icons.color_lens,
              Colors.indigo,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ChangeTheme(),
                ),
              ),
            ),

            // زر النسخ الاحتياطي والاستعادة
            _buildGeneralCard(
              context,
              localizations.backupRestore,
              Icons.backup,
              Colors.grey[700]!,
              () => _showComingSoon(context, localizations.backupRestore),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة إعداد عام
  Widget _buildGeneralCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                color.withValues(alpha: 0.1),
                color.withValues(alpha: 0.05),
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 48,
                color: color,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// عرض رسالة "قريباً"
  void _showComingSoon(BuildContext context, String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature - قريباً'),
        backgroundColor: Colors.blue,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
