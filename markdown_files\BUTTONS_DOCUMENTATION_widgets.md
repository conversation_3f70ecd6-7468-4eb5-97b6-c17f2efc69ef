# توثيق الأزرار العامة في التطبيق

هذا الملف يحتوي على توثيق لجميع الأزرار العامة المتوفرة في التطبيق. يرجى الرجوع إلى هذا الملف قبل إنشاء أي زر جديد للتأكد من عدم وجود تكرار، ويجب تحديث هذا الملف عند إضافة أي زر جديد.

## كيفية استخدام الأزرار

جميع الأزرار متاحة من خلال الفئة المركزية `GlobalButtons` في الملف `global_buttons.dart`. يمكن استخدام هذه الأزرار في أي مكان في التطبيق كما يلي:

```dart
import 'package:your_app/presentation/widgets/global_buttons/global_buttons.dart';

// استخدام زر جديد
GlobalButtons.newButton(
  onPressed: () {
    // الإجراء المطلوب عند النقر على الزر
  },
  tooltip: 'إنشاء جديد', // اختياري
  isLoading: false, // اختياري
  isDisabled: false, // اختياري
);
```

## قائمة الأزرار المتوفرة

| # | اسم الزر بالعربية | اسم الملف | اسم الدالة في GlobalButtons |
|---|-----------------|----------|---------------------------|
| 1 | زر_جديد | new_button.dart | newButton |
| 2 | زر_إستعلام | query_button.dart | queryButton |
| 3 | زر_تراجع | undo_button.dart | undoButton |
| 4 | زر_حفظ | save_button.dart | saveButton |
| 5 | زر_تحديد_الكمية_الإفتراضية | set_default_quantity_button.dart | setDefaultQuantityButton |
| 6 | زر_ارسال_رسالة_sms | send_sms_button.dart | sendSmsButton |
| 7 | زر_تصدير_الى_ملف_إكسل | export_excel_button.dart | exportExcelButton |
| 8 | زر_فواتير_المبيعات_العميل | client_sales_invoices_button.dart | clientSalesInvoicesButton |
| 9 | زر_إعتماد_الفاتورة | approve_invoice_button.dart | approveInvoiceButton |
| 10 | زر_ارسال_عبر_الواتساب | send_whatsapp_button.dart | sendWhatsappButton |
| 11 | زر_نسخ_من | copy_from_button.dart | copyFromButton |
| 12 | زر_طباعة_الفسح | print_clearance_button.dart | printClearanceButton |
| 13 | زر_طباعة | print_button.dart | printButton |
| 14 | زر_التقارير | reports_button.dart | reportsButton |
| 15 | زر_طباعة_الفاتورة_من_المستودع | print_from_warehouse_button.dart | printFromWarehouseButton |
| 16 | زر_تعديل_الأسعار | edit_prices_button.dart | editPricesButton |
| 17 | زر_تحويل_الى_عرض_سعر | convert_to_quote_button.dart | convertToQuoteButton |
| 18 | زر_تغير_طريقة_الدفع | change_payment_method_button.dart | changePaymentMethodButton |
| 19 | زر_نقاط_الخصم | discount_points_button.dart | discountPointsButton |
| 20 | زر_مرتجعات_قديمة | old_returns_button.dart | oldReturnsButton |
| 21 | زر_إظهار_و_إخفاء_الحقول_حسب_الرغبة | toggle_fields_button.dart | toggleFieldsButton |
| 22 | زر_فرز_الأعمدة | sort_columns_button.dart | sortColumnsButton |
| 23 | زر_تغير_طريقة_العرض | change_view_button.dart | changeViewButton |
| 24 | زر_استيراد_من_طلب_عرض_سعر | import_from_quote_button.dart | importFromQuoteButton |
| 25 | زر_حذف | delete_button.dart | deleteButton |
| 26 | زر_تعميد_الطلب | approve_order_button.dart | approveOrderButton |
| 27 | زر_الغاء_تعميد_الطلب | cancel_approval_button.dart | cancelApprovalButton |
| 28 | زر_إقفال_طلب_الشراء | close_purchase_request_button.dart | closePurchaseRequestButton |
| 29 | زر_التأكد_من_الأصناف | verify_items_button.dart | verifyItemsButton |
| 30 | زر_تعديل_طلب_الشراء | edit_purchase_request_button.dart | editPurchaseRequestButton |
| 31 | زر_تعديل_طلب_الشراء_المستلمة | edit_received_request_button.dart | editReceivedRequestButton |
| 32 | زر_مقارنة_الأصناف | compare_items_button.dart | compareItemsButton |
| 33 | زر_قائمة_سندات_الإستلام | receipt_list_button.dart | receiptListButton |
| 34 | زر_حركة_أصناف_شحنة_معينة | shipment_item_movement_button.dart | shipmentItemMovementButton |
| 35 | زر_الأصناف_المنتهية_لشحنة_معينة | expired_items_button.dart | expiredItemsButton |
| 36 | زر_تقرير_مستحقات_البنك | bank_dues_report_button.dart | bankDuesReportButton |
| 37 | زر_سندات_الإستلام_بالتصنيفات | categorized_receipts_button.dart | categorizedReceiptsButton |
| 38 | زر_أصناف_المشتريات | purchase_items_button.dart | purchaseItemsButton |
| 39 | زر_محددات_فاتورة_الشراء | purchase_invoice_filters_button.dart | purchaseInvoiceFiltersButton |
| 40 | زر_الخصم | discount_button.dart | discountButton |
| 41 | زر_سحب_الأصناف | fetch_items_button.dart | fetchItemsButton |
| 42 | زر_تعديل_أسعار_الفاتورة | edit_invoice_prices_button.dart | editInvoicePricesButton |
| 43 | زر_تكبير_العرض | zoom_in_button.dart | zoomInButton |
| 44 | زر_تصغير_العرض | zoom_out_button.dart | zoomOutButton |
| 45 | زر_معاينة_الطباعة | print_preview_button.dart | printPreviewButton |
| 46 | زر_تصدير_الى_PDF | export_pdf_button.dart | exportPdfButton |
| 47 | زر_تغيير_طريقة_عرض_التقرير | report_view_button.dart | reportViewButton |
| 48 | زر_البحث_عن_قيمة_محدد_في_التقرير | report_search_button.dart | reportSearchButton |
| 49 | زر_توريد_من_قيد_سابق | import_previous_entry_button.dart | importPreviousEntryButton |
| 50 | زر_تعديل_على_الملاحظات | edit_notes_button.dart | editNotesButton |
| 51 | زر_سحب_من_ملف | import_from_file_button.dart | importFromFileButton |
| 52 | زر_سحب_فواتير_هذا_الحساب_آليا | fetch_account_invoices_button.dart | fetchAccountInvoicesButton |
| 53 | زر_طباعة_شيك | print_cheque_button.dart | printChequeButton |
| 54 | زر_حركة_سداد_عهد_الفواتير | invoice_settlement_movement_button.dart | invoiceSettlementMovementButton |
| 55 | زر_العهد_المسددة_في_فترة_معينة | settled_advances_button.dart | settledAdvancesButton |
| 56 | زر_توريد_من_سندات_الإخراج | supply_from_outbound_button.dart | supplyFromOutboundButton |
| 57 | زر_إضافة_الأصناف_تبعا_لشروط_معينة | auto_item_add_button.dart | autoItemAddButton |
| 58 | زر_تعديل_الفواتير_المطبوعة | edit_printed_invoices_button.dart | editPrintedInvoicesButton |
| 59 | زر_تعديل_المرتجعات_المطبوعة | edit_printed_returns_button.dart | editPrintedReturnsButton |
| 60 | زر_تعميد_سندات_التحويل_الغير_معمدة | approve_unapproved_transfer_vouchers_button.dart | approveUnapprovedTransferVouchersButton |
| 61 | زر_مشاركة_فواتير_المتجر | share_store_invoices_button.dart | shareStoreInvoicesButton |

## خصائص الأزرار

جميع الأزرار تقبل الخصائص التالية:

- `onPressed`: دالة تُنفذ عند النقر على الزر (إلزامي)
- `tooltip`: تلميح يظهر عند تمرير المؤشر فوق الزر (اختياري)
- `isLoading`: لعرض مؤشر التحميل بدلاً من الأيقونة (اختياري، القيمة الافتراضية هي `false`)
- `isDisabled`: لتعطيل الزر (اختياري، القيمة الافتراضية هي `false`)

## كيفية إضافة زر جديد

لإضافة زر جديد، يجب اتباع الخطوات التالية:

1. إنشاء ملف جديد في المجلد `lib/presentation/widgets/global_buttons` باسم مناسب (مثال: `new_feature_button.dart`)
2. تنفيذ فئة الزر باستخدام نفس النمط المستخدم في الأزرار الأخرى
3. إضافة دالة جديدة في الفئة `GlobalButtons` في الملف `global_buttons.dart`
4. تحديث هذا الملف التوثيقي بإضافة الزر الجديد إلى الجدول أعلاه

## ملاحظات هامة

- يجب الحفاظ على اتساق مظهر وسلوك الأزرار في جميع أنحاء التطبيق
- يجب استخدام أيقونات مناسبة وواضحة لكل زر
- يجب استخدام ألوان متناسقة مع نظام الألوان العام للتطبيق
- يجب أن تكون أسماء الأزرار واضحة ومعبرة عن وظيفتها

## آخر تحديث

تم تحديث هذا الملف بتاريخ: [التاريخ الحالي]
