import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة مراقبة أصناف المشتريات لسنة سابقة
/// يعرض تحليل ومقارنة أصناف المشتريات للسنة السابقة
class PurchaseItemsMonitoringPreviousYearReportPage extends StatefulWidget {
  const PurchaseItemsMonitoringPreviousYearReportPage({super.key});

  @override
  State<PurchaseItemsMonitoringPreviousYearReportPage> createState() =>
      _PurchaseItemsMonitoringPreviousYearReportPageState();
}

class _PurchaseItemsMonitoringPreviousYearReportPageState
    extends State<PurchaseItemsMonitoringPreviousYearReportPage> {
  String? _selectedYear = '2023';
  String? _comparisonYear = '2022';
  String? _selectedSupplier;
  String? _selectedCategory;
  String? _analysisType = 'comparison';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('مراقبة أصناف المشتريات لسنة سابقة'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.compare),
            onPressed: _compareYears,
            tooltip: 'مقارنة السنوات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.deepPurple[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'السنة المراد تحليلها',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedYear,
                        items: const [
                          DropdownMenuItem(value: '2023', child: Text('2023')),
                          DropdownMenuItem(value: '2022', child: Text('2022')),
                          DropdownMenuItem(value: '2021', child: Text('2021')),
                          DropdownMenuItem(value: '2020', child: Text('2020')),
                        ],
                        onChanged: (value) =>
                            setState(() => _selectedYear = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'سنة المقارنة',
                          border: OutlineInputBorder(),
                        ),
                        value: _comparisonYear,
                        items: const [
                          DropdownMenuItem(value: '2022', child: Text('2022')),
                          DropdownMenuItem(value: '2021', child: Text('2021')),
                          DropdownMenuItem(value: '2020', child: Text('2020')),
                          DropdownMenuItem(value: '2019', child: Text('2019')),
                        ],
                        onChanged: (value) =>
                            setState(() => _comparisonYear = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'المورد',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedSupplier,
                        items: const [
                          DropdownMenuItem(
                              value: 'all', child: Text('جميع الموردين')),
                          DropdownMenuItem(
                              value: 'supplier1',
                              child: Text('شركة التقنية المتقدمة')),
                          DropdownMenuItem(
                              value: 'supplier2',
                              child: Text('مؤسسة الجودة العالمية')),
                          DropdownMenuItem(
                              value: 'supplier3',
                              child: Text('شركة الإمداد الشامل')),
                        ],
                        onChanged: (value) =>
                            setState(() => _selectedSupplier = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'نوع التحليل',
                          border: OutlineInputBorder(),
                        ),
                        value: _analysisType,
                        items: const [
                          DropdownMenuItem(
                              value: 'comparison', child: Text('مقارنة سنوية')),
                          DropdownMenuItem(
                              value: 'trend', child: Text('تحليل الاتجاه')),
                          DropdownMenuItem(
                              value: 'seasonal', child: Text('تحليل موسمي')),
                          DropdownMenuItem(
                              value: 'growth', child: Text('تحليل النمو')),
                        ],
                        onChanged: (value) =>
                            setState(() => _analysisType = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.analytics),
                  label: const Text('تحليل البيانات'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.deepPurple,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص المقارنة السنوية
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.compare_arrows,
                                  color: Colors.deepPurple, size: 24),
                              const SizedBox(width: 8),
                              Text(
                                'مقارنة المشتريات $_selectedYear مقابل $_comparisonYear',
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildComparisonCard(
                                  'إجمالي المشتريات',
                                  '2,850,000 ر.س',
                                  '2,450,000 ر.س',
                                  '+16.3%',
                                  Colors.green),
                              _buildComparisonCard('عدد الأصناف', '1,245',
                                  '1,180', '+5.5%', Colors.blue),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              _buildComparisonCard(
                                  'متوسط سعر الصنف',
                                  '2,290 ر.س',
                                  '2,076 ر.س',
                                  '+10.3%',
                                  Colors.orange),
                              _buildComparisonCard('عدد الموردين', '45', '42',
                                  '+7.1%', Colors.purple),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // تحليل الاتجاهات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تحليل اتجاهات المشتريات',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildTrendCard('نمو قوي', '285 صنف', '+25%',
                                  Colors.green, Icons.trending_up),
                              _buildTrendCard('نمو متوسط', '450 صنف', '+10%',
                                  Colors.blue, Icons.trending_up),
                              _buildTrendCard('ثابت', '310 صنف', '0%',
                                  Colors.grey, Icons.trending_flat),
                              _buildTrendCard('تراجع', '200 صنف', '-15%',
                                  Colors.red, Icons.trending_down),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // أفضل الأصناف نمواً
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'أفضل الأصناف نمواً',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ..._buildTopGrowthItemsList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول المقارنة التفصيلية
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'مقارنة تفصيلية للأصناف',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: [
                                const DataColumn(label: Text('اسم الصنف')),
                                DataColumn(label: Text('$_selectedYear')),
                                DataColumn(label: Text('$_comparisonYear')),
                                const DataColumn(label: Text('التغيير')),
                                const DataColumn(label: Text('النسبة')),
                                const DataColumn(label: Text('الاتجاه')),
                                const DataColumn(label: Text('التقييم')),
                              ],
                              rows: _buildComparisonRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // تحليل الموسمية
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.calendar_month,
                                  color: Colors.indigo, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'تحليل الموسمية',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSeasonCard('الربع الأول', '650,000 ر.س',
                                  '+12%', Colors.green),
                              _buildSeasonCard('الربع الثاني', '720,000 ر.س',
                                  '+18%', Colors.blue),
                              _buildSeasonCard('الربع الثالث', '580,000 ر.س',
                                  '+8%', Colors.orange),
                              _buildSeasonCard('الربع الرابع', '900,000 ر.س',
                                  '+25%', Colors.purple),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _generateForecast,
                                  icon: const Icon(Icons.trending_up),
                                  label: const Text('توقعات السنة القادمة'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _optimizePurchases,
                                  icon: const Icon(Icons.tune),
                                  label: const Text('تحسين المشتريات'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _identifyOpportunities,
                                  icon: const Icon(Icons.lightbulb),
                                  label: const Text('تحديد الفرص'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _createStrategy,
                                  icon: const Icon(Icons.business_center),
                                  label: const Text('وضع استراتيجية'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildTopGrowthItemsList() {
    final items = [
      {
        'item': 'لابتوب ديل XPS 13',
        'growth': '+45%',
        'current': '312,500 ر.س',
        'previous': '215,000 ر.س'
      },
      {
        'item': 'طابعة HP LaserJet',
        'growth': '+32%',
        'current': '255,000 ر.س',
        'previous': '193,000 ر.س'
      },
      {
        'item': 'هاتف آيفون 15',
        'growth': '+28%',
        'current': '195,000 ر.س',
        'previous': '152,000 ر.س'
      },
      {
        'item': 'ساعة ذكية',
        'growth': '+25%',
        'current': '142,500 ر.س',
        'previous': '114,000 ر.س'
      },
    ];

    return items
        .map((item) => Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.green.withOpacity(0.3)),
                borderRadius: BorderRadius.circular(8),
                color: Colors.green.withOpacity(0.05),
              ),
              child: Row(
                children: [
                  const Icon(Icons.trending_up, color: Colors.green, size: 24),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item['item']!,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '${item['current']} (${item['previous']})',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      item['growth']!,
                      style: const TextStyle(
                        color: Colors.green,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ))
        .toList();
  }

  List<DataRow> _buildComparisonRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('312,500 ر.س')),
        const DataCell(Text('215,000 ر.س')),
        const DataCell(Text('+97,500 ر.س')),
        const DataCell(Text('+45.3%')),
        DataCell(_buildTrendIcon(Colors.green, Icons.trending_up)),
        DataCell(_buildRatingBadge('ممتاز', Colors.green)),
      ]),
      DataRow(cells: [
        const DataCell(Text('طابعة HP LaserJet')),
        const DataCell(Text('255,000 ر.س')),
        const DataCell(Text('193,000 ر.س')),
        const DataCell(Text('+62,000 ر.س')),
        const DataCell(Text('+32.1%')),
        DataCell(_buildTrendIcon(Colors.green, Icons.trending_up)),
        DataCell(_buildRatingBadge('جيد جداً', Colors.blue)),
      ]),
      DataRow(cells: [
        const DataCell(Text('هاتف قديم')),
        const DataCell(Text('85,000 ر.س')),
        const DataCell(Text('125,000 ر.س')),
        const DataCell(Text('-40,000 ر.س')),
        const DataCell(Text('-32.0%')),
        DataCell(_buildTrendIcon(Colors.red, Icons.trending_down)),
        DataCell(_buildRatingBadge('ضعيف', Colors.red)),
      ]),
    ];
  }

  Widget _buildComparisonCard(String title, String current, String previous,
      String change, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style:
                    const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                current,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                'السابق: $previous',
                style: TextStyle(fontSize: 10, color: Colors.grey[600]),
              ),
              const SizedBox(height: 4),
              Text(
                change,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTrendCard(String title, String count, String percentage,
      Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                count,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                percentage,
                style: TextStyle(
                  fontSize: 12,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSeasonCard(
      String season, String amount, String growth, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                amount,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                growth,
                style: TextStyle(
                  fontSize: 12,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                season,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTrendIcon(Color color, IconData icon) {
    return Icon(icon, color: color, size: 20);
  }

  Widget _buildRatingBadge(String rating, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        rating,
        style:
            TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold),
      ),
    );
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
          content: Text('تم إنشاء تقرير مراقبة المشتريات للسنة السابقة بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _compareYears() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مقارنة تفصيلية بين السنوات')),
    );
  }

  void _generateForecast() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء توقعات للسنة القادمة')),
    );
  }

  void _optimizePurchases() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحسين استراتيجية المشتريات')),
    );
  }

  void _identifyOpportunities() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحديد فرص التحسين')),
    );
  }

  void _createStrategy() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('وضع استراتيجية المشتريات')),
    );
  }
}
