import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة أنواع المصروفات
/// تتيح إدارة تصنيفات وأنواع المصروفات المختلفة
class ExpenseTypesPage extends StatefulWidget {
  const ExpenseTypesPage({super.key});

  @override
  State<ExpenseTypesPage> createState() => _ExpenseTypesPageState();
}

class _ExpenseTypesPageState extends State<ExpenseTypesPage> {
  String _searchQuery = '';
  String _selectedCategory = 'all';

  // بيانات تجريبية لأنواع المصروفات
  final List<Map<String, dynamic>> _expenseTypes = [
    {
      'id': 'EXP001',
      'name': 'رواتب الموظفين',
      'code': 'SAL',
      'category': 'مصروفات إدارية',
      'description': 'رواتب ومكافآت الموظفين',
      'isActive': true,
      'requiresApproval': true,
      'maxAmount': 100000.0,
      'accountCode': '5101',
      'usageCount': 45,
      'totalAmount': 450000.0,
      'icon': Icons.people,
      'color': Colors.blue,
    },
    {
      'id': 'EXP002',
      'name': 'إيجار المكاتب',
      'code': 'RENT',
      'category': 'مصروفات تشغيلية',
      'description': 'إيجار المكاتب والمرافق',
      'isActive': true,
      'requiresApproval': true,
      'maxAmount': 50000.0,
      'accountCode': '5201',
      'usageCount': 12,
      'totalAmount': 180000.0,
      'icon': Icons.business,
      'color': Colors.orange,
    },
    {
      'id': 'EXP003',
      'name': 'مصروفات السفر',
      'code': 'TRAVEL',
      'category': 'مصروفات إدارية',
      'description': 'تذاكر طيران وإقامة ومواصلات',
      'isActive': true,
      'requiresApproval': true,
      'maxAmount': 15000.0,
      'accountCode': '5102',
      'usageCount': 28,
      'totalAmount': 85000.0,
      'icon': Icons.flight,
      'color': Colors.green,
    },
    {
      'id': 'EXP004',
      'name': 'صيانة المعدات',
      'code': 'MAINT',
      'category': 'مصروفات تشغيلية',
      'description': 'صيانة الأجهزة والمعدات',
      'isActive': true,
      'requiresApproval': false,
      'maxAmount': 10000.0,
      'accountCode': '5202',
      'usageCount': 35,
      'totalAmount': 125000.0,
      'icon': Icons.build,
      'color': Colors.purple,
    },
    {
      'id': 'EXP005',
      'name': 'مصروفات التسويق',
      'code': 'MARKET',
      'category': 'مصروفات تسويقية',
      'description': 'إعلانات وحملات تسويقية',
      'isActive': false,
      'requiresApproval': true,
      'maxAmount': 25000.0,
      'accountCode': '5301',
      'usageCount': 8,
      'totalAmount': 45000.0,
      'icon': Icons.campaign,
      'color': Colors.red,
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('أنواع المصروفات'),
        backgroundColor: Colors.grey,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addExpenseType,
            tooltip: 'إضافة نوع مصروف جديد',
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showExpenseAnalytics,
            tooltip: 'تحليل المصروفات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                TextField(
                  decoration: const InputDecoration(
                    hintText: 'البحث في أنواع المصروفات...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedCategory,
                        decoration: const InputDecoration(
                          labelText: 'فئة المصروف',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الفئات')),
                          DropdownMenuItem(value: 'مصروفات إدارية', child: Text('مصروفات إدارية')),
                          DropdownMenuItem(value: 'مصروفات تشغيلية', child: Text('مصروفات تشغيلية')),
                          DropdownMenuItem(value: 'مصروفات تسويقية', child: Text('مصروفات تسويقية')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedCategory = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                _buildStatCard('المجموع', _expenseTypes.length.toString(), Colors.blue),
                _buildStatCard('النشطة', _expenseTypes.where((e) => e['isActive']).length.toString(), Colors.green),
                _buildStatCard('معطلة', _expenseTypes.where((e) => !e['isActive']).length.toString(), Colors.red),
                _buildStatCard('الاستخدام', _getTotalUsage(), Colors.orange),
              ],
            ),
          ),

          // قائمة أنواع المصروفات
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _expenseTypes.length,
              itemBuilder: (context, index) {
                final expenseType = _expenseTypes[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: expenseType['isActive'] ? expenseType['color'] : Colors.grey,
                      child: Icon(
                        expenseType['icon'],
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    title: Row(
                      children: [
                        Expanded(
                          child: Text(
                            expenseType['name'],
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                        if (expenseType['requiresApproval'])
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.amber,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Text(
                              'يتطلب موافقة',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('الكود: ${expenseType['code']} | الفئة: ${expenseType['category']}'),
                        Text('رقم الحساب: ${expenseType['accountCode']}'),
                        Text('الحد الأقصى: ${expenseType['maxAmount']} ر.س'),
                        Text('الاستخدام: ${expenseType['usageCount']} مرة | المجموع: ${expenseType['totalAmount']} ر.س'),
                        Text(expenseType['description']),
                      ],
                    ),
                    trailing: PopupMenuButton<String>(
                      onSelected: (value) => _handleAction(value, expenseType),
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'edit',
                          child: ListTile(
                            leading: Icon(Icons.edit),
                            title: Text('تعديل'),
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'toggle',
                          child: ListTile(
                            leading: Icon(Icons.toggle_on),
                            title: Text('تغيير الحالة'),
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'usage_report',
                          child: ListTile(
                            leading: Icon(Icons.analytics),
                            title: Text('تقرير الاستخدام'),
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'approval_settings',
                          child: ListTile(
                            leading: Icon(Icons.approval),
                            title: Text('إعدادات الموافقة'),
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: ListTile(
                            leading: Icon(Icons.delete, color: Colors.red),
                            title: Text('حذف', style: TextStyle(color: Colors.red)),
                          ),
                        ),
                      ],
                    ),
                    isThreeLine: true,
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addExpenseType,
        backgroundColor: Colors.grey,
        icon: const Icon(Icons.add),
        label: const Text('إضافة نوع'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getTotalUsage() {
    int total = _expenseTypes.fold(0, (sum, type) => sum + (type['usageCount'] as int));
    return total.toString();
  }

  void _addExpenseType() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إضافة نوع مصروف جديد')),
    );
  }

  void _showExpenseAnalytics() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تحليل المصروفات')),
    );
  }

  void _handleAction(String action, Map<String, dynamic> expenseType) {
    switch (action) {
      case 'edit':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تعديل نوع المصروف ${expenseType['name']}')),
        );
        break;
      case 'toggle':
        setState(() {
          expenseType['isActive'] = !expenseType['isActive'];
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم ${expenseType['isActive'] ? 'تفعيل' : 'تعطيل'} نوع المصروف'),
          ),
        );
        break;
      case 'usage_report':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تقرير استخدام ${expenseType['name']}')),
        );
        break;
      case 'approval_settings':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('إعدادات موافقة ${expenseType['name']}')),
        );
        break;
      case 'delete':
        _showDeleteConfirmation(expenseType);
        break;
    }
  }

  void _showDeleteConfirmation(Map<String, dynamic> expenseType) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف نوع المصروف ${expenseType['name']}؟\nسيتم حذف جميع المصروفات المرتبطة بهذا النوع.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _expenseTypes.removeWhere((e) => e['id'] == expenseType['id']);
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف نوع المصروف ${expenseType['name']}'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
