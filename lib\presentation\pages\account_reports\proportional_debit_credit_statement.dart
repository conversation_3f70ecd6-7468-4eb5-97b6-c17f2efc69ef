import 'package:flutter/material.dart';

/// صفحة كشف حساب نسبي مدين دائن
/// تعرض النسب المئوية للمدين والدائن في الحسابات
class ProportionalDebitCreditStatementPage extends StatefulWidget {
  const ProportionalDebitCreditStatementPage({super.key});

  @override
  State<ProportionalDebitCreditStatementPage> createState() => _ProportionalDebitCreditStatementPageState();
}

class _ProportionalDebitCreditStatementPageState extends State<ProportionalDebitCreditStatementPage> {
  String _selectedPeriod = 'current_month';

  // بيانات تجريبية للكشف النسبي
  final List<Map<String, dynamic>> _proportionalData = [
    {
      'accountCode': '1101',
      'accountName': 'النقدية في الصندوق',
      'accountType': 'أصول',
      'totalDebit': 85000.0,
      'totalCredit': 15000.0,
      'netBalance': 70000.0,
      'debitPercentage': 85.0,
      'creditPercentage': 15.0,
      'movementsCount': 45,
    },
    {
      'accountCode': '1102',
      'accountName': 'البنك الأهلي',
      'accountType': 'أصول',
      'totalDebit': 120000.0,
      'totalCredit': 35000.0,
      'netBalance': 85000.0,
      'debitPercentage': 77.4,
      'creditPercentage': 22.6,
      'movementsCount': 62,
    },
    {
      'accountCode': '2101',
      'accountName': 'الموردين',
      'accountType': 'خصوم',
      'totalDebit': 25000.0,
      'totalCredit': 95000.0,
      'netBalance': -70000.0,
      'debitPercentage': 20.8,
      'creditPercentage': 79.2,
      'movementsCount': 38,
    },
    {
      'accountCode': '4101',
      'accountName': 'إيرادات المبيعات',
      'accountType': 'إيرادات',
      'totalDebit': 5000.0,
      'totalCredit': 180000.0,
      'netBalance': -175000.0,
      'debitPercentage': 2.7,
      'creditPercentage': 97.3,
      'movementsCount': 125,
    },
    {
      'accountCode': '5101',
      'accountName': 'مصروفات الرواتب',
      'accountType': 'مصروفات',
      'totalDebit': 65000.0,
      'totalCredit': 8000.0,
      'netBalance': 57000.0,
      'debitPercentage': 89.0,
      'creditPercentage': 11.0,
      'movementsCount': 24,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('كشف حساب نسبي مدين دائن'),
        backgroundColor: Colors.amber,
        foregroundColor: Colors.black,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedPeriod,
                    decoration: const InputDecoration(
                      labelText: 'الفترة',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'current_month', child: Text('الشهر الحالي')),
                      DropdownMenuItem(value: 'current_quarter', child: Text('الربع الحالي')),
                      DropdownMenuItem(value: 'current_year', child: Text('السنة الحالية')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedPeriod = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
          ),

          // ملخص النسب
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              color: Colors.amber[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    const Text(
                      'ملخص النسب المئوية',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Column(
                          children: [
                            const Text('إجمالي المدين'),
                            Text(
                              '${_getTotalDebit()} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('إجمالي الدائن'),
                            Text(
                              '${_getTotalCredit()} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.red,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('عدد الحسابات'),
                            Text(
                              _proportionalData.length.toString(),
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                _buildStatCard('متوسط المدين', '${_getAverageDebitPercentage()}%', Colors.green),
                _buildStatCard('متوسط الدائن', '${_getAverageCreditPercentage()}%', Colors.red),
                _buildStatCard('أعلى نسبة مدين', '${_getHighestDebitPercentage()}%', Colors.blue),
                _buildStatCard('أعلى نسبة دائن', '${_getHighestCreditPercentage()}%', Colors.orange),
              ],
            ),
          ),

          // قائمة الحسابات
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: _proportionalData.length,
              itemBuilder: (context, index) {
                final account = _proportionalData[index];
                
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: _getAccountTypeColor(account['accountType']),
                      child: Text(
                        account['accountCode'].substring(0, 2),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    title: Text(
                      '${account['accountCode']} - ${account['accountName']}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('صافي الرصيد: ${account['netBalance'].toStringAsFixed(2)} ر.س'),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('مدين: ${account['debitPercentage']}%'),
                                  LinearProgressIndicator(
                                    value: account['debitPercentage'] / 100,
                                    backgroundColor: Colors.grey[300],
                                    valueColor: const AlwaysStoppedAnimation<Color>(Colors.green),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('دائن: ${account['creditPercentage']}%'),
                                  LinearProgressIndicator(
                                    value: account['creditPercentage'] / 100,
                                    backgroundColor: Colors.grey[300],
                                    valueColor: const AlwaysStoppedAnimation<Color>(Colors.red),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('إجمالي المدين', '${account['totalDebit'].toStringAsFixed(2)} ر.س', Icons.add_circle, Colors.green),
                                ),
                                Expanded(
                                  child: _buildDetailCard('إجمالي الدائن', '${account['totalCredit'].toStringAsFixed(2)} ر.س', Icons.remove_circle, Colors.red),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('نسبة المدين', '${account['debitPercentage']}%', Icons.trending_up, Colors.green),
                                ),
                                Expanded(
                                  child: _buildDetailCard('نسبة الدائن', '${account['creditPercentage']}%', Icons.trending_down, Colors.red),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('عدد الحركات', account['movementsCount'].toString(), Icons.swap_horiz, Colors.blue),
                                ),
                                Expanded(
                                  child: _buildDetailCard('نوع الحساب', account['accountType'], Icons.category, Colors.orange),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            // مخطط دائري للنسب
                            SizedBox(
                              height: 100,
                              child: Row(
                                children: [
                                  Expanded(
                                    flex: account['debitPercentage'].round(),
                                    child: Container(
                                      height: 20,
                                      decoration: const BoxDecoration(
                                        color: Colors.green,
                                        borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(10),
                                          bottomLeft: Radius.circular(10),
                                        ),
                                      ),
                                      child: Center(
                                        child: Text(
                                          'مدين ${account['debitPercentage']}%',
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    flex: account['creditPercentage'].round(),
                                    child: Container(
                                      height: 20,
                                      decoration: const BoxDecoration(
                                        color: Colors.red,
                                        borderRadius: BorderRadius.only(
                                          topRight: Radius.circular(10),
                                          bottomRight: Radius.circular(10),
                                        ),
                                      ),
                                      child: Center(
                                        child: Text(
                                          'دائن ${account['creditPercentage']}%',
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshData,
        backgroundColor: Colors.amber,
        foregroundColor: Colors.black,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث البيانات'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Icon(icon, size: 20, color: color),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                color: color,
              ),
            ),
            Text(
              title,
              style: const TextStyle(fontSize: 10),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Color _getAccountTypeColor(String accountType) {
    switch (accountType) {
      case 'أصول':
        return Colors.green;
      case 'خصوم':
        return Colors.red;
      case 'حقوق ملكية':
        return Colors.blue;
      case 'إيرادات':
        return Colors.purple;
      case 'مصروفات':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  double _getTotalDebit() {
    return _proportionalData.fold(0.0, (sum, account) => sum + account['totalDebit']);
  }

  double _getTotalCredit() {
    return _proportionalData.fold(0.0, (sum, account) => sum + account['totalCredit']);
  }

  String _getAverageDebitPercentage() {
    if (_proportionalData.isEmpty) return '0.0';
    double total = _proportionalData.fold(0.0, (sum, account) => sum + account['debitPercentage']);
    return (total / _proportionalData.length).toStringAsFixed(1);
  }

  String _getAverageCreditPercentage() {
    if (_proportionalData.isEmpty) return '0.0';
    double total = _proportionalData.fold(0.0, (sum, account) => sum + account['creditPercentage']);
    return (total / _proportionalData.length).toStringAsFixed(1);
  }

  String _getHighestDebitPercentage() {
    if (_proportionalData.isEmpty) return '0.0';
    double max = _proportionalData.map((account) => account['debitPercentage'] as double).reduce((a, b) => a > b ? a : b);
    return max.toStringAsFixed(1);
  }

  String _getHighestCreditPercentage() {
    if (_proportionalData.isEmpty) return '0.0';
    double max = _proportionalData.map((account) => account['creditPercentage'] as double).reduce((a, b) => a > b ? a : b);
    return max.toStringAsFixed(1);
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة كشف الحساب النسبي')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير كشف الحساب النسبي')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث بيانات كشف الحساب النسبي'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
