import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';
import 'vat_tax_settings.dart';
import 'whatsapp_service_connection_settings.dart';
import 'tabby_tamara_payment_services_settings.dart';
import 'system_monitoring.dart';
import 'backup_restore.dart';
import 'barcode_printing.dart';
import 'transfer_account_balances.dart';
import 'transfer_inventory_balances.dart';
import 'transfer_stock_balances.dart';
import 'copy_previous_year_documents.dart';
import 'hijri_dates_entry.dart';
import 'prepare_statistics_files.dart';
import 'recalculate_average_cost.dart';
import 'maintain_inventory_quantities.dart';
import 'maintain_fixed_orders.dart';
import 'maintain_account_balances.dart';
import 'maintain_supplier_items.dart';
import 'maintain_reservations.dart';
import 'user_permissions.dart';
import 'change_user_password.dart';
import 'change_user_branch_warehouse.dart';
import 'activate_users.dart';
import 'server_main_file.dart';
import 'system_comparisons.dart';
import 'change_item_number.dart';
import 'connected_users.dart';
import 'sudden_shutdown_monitoring.dart';
import 'settle_previous_sales_invoices.dart';
import 'settle_previous_purchase_invoices.dart';
import 'item_sales_purchases_movement.dart';
import 'print_specific_items_data.dart';
import 'print_barcode.dart';
import 'print_barcode_specific_items.dart';
import 'currency_receipt.dart';
import 'terminals.dart';
import 'barcode_definition.dart';
import 'label_definition.dart';
import 'suspend_application.dart';
import 'edit_printed_invoices.dart';
import 'edit_printed_returns.dart';
import 'approve_transfer_vouchers.dart';
import 'share_store_invoices.dart';
import 'sign_and_share_invoices.dart';
import 'transfer_account_movement.dart';
import 'user_activity_report.dart';
import 'sign_and_share_discount_notifications.dart';

/// صفحة إدارة النظام الرئيسية
/// تعرض ملخص النظام وأدوات الإدارة
class SystemManagementPage extends StatelessWidget {
  const SystemManagementPage({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.systemManagement),
        backgroundColor: Colors.brown,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقات الإحصائيات السريعة
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    title: localizations.activeUsers,
                    value: '12',
                    icon: Icons.people,
                    color: Colors.blue,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    title: localizations.definedRoles,
                    value: '5',
                    icon: Icons.admin_panel_settings,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    title: localizations.lastBackup,
                    value: localizations.today,
                    icon: Icons.backup,
                    color: Colors.orange,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    title: localizations.systemStatus,
                    value: localizations.excellent,
                    icon: Icons.health_and_safety,
                    color: Colors.teal,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // قائمة أدوات الإدارة
            Text(
              localizations.systemManagementTools,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            Expanded(
              child: ListView(
                children: [
                  // الصفحات حسب الترتيب في القائمة المطلوبة مع أرقام تسلسلية
                  _buildNumberedManagementCard(
                    number: 1,
                    title: localizations.vatSettings,
                    icon: Icons.receipt,
                    color: Colors.green,
                    isImplemented: true,
                    onTap: () =>
                        _navigateToManagement(context, 'vat_tax_settings'),
                  ),
                  _buildNumberedManagementCard(
                    number: 2,
                    title: localizations.whatsappConnectionSettings,
                    icon: Icons.chat,
                    color: Colors.green,
                    isImplemented: true,
                    onTap: () => _navigateToManagement(
                        context, 'whatsapp_service_connection'),
                  ),
                  _buildNumberedManagementCard(
                    number: 3,
                    title: localizations.paymentServicesSettings,
                    icon: Icons.payment,
                    color: Colors.purple,
                    isImplemented: true,
                    onTap: () => _navigateToManagement(
                        context, 'tabby_tamara_payment_services'),
                  ),
                  _buildNumberedManagementCard(
                    number: 4,
                    title: localizations.systemMonitoring,
                    icon: Icons.monitor,
                    color: Colors.indigo,
                    isImplemented: true,
                    onTap: () =>
                        _navigateToManagement(context, 'system_monitoring'),
                  ),
                  _buildNumberedManagementCard(
                    number: 5,
                    title: localizations.userPermissions,
                    icon: Icons.security,
                    color: Colors.deepPurple,
                    isImplemented: true,
                    onTap: () =>
                        _navigateToManagement(context, 'user_permissions'),
                  ),
                  _buildNumberedManagementCard(
                    number: 6,
                    title: localizations.changeUserPassword,
                    icon: Icons.lock_reset,
                    color: Colors.orange,
                    isImplemented: true,
                    onTap: () =>
                        _navigateToManagement(context, 'change_user_password'),
                  ),
                  _buildNumberedManagementCard(
                    number: 7,
                    title: localizations.changeUserBranchWarehouse,
                    icon: Icons.swap_horiz,
                    color: Colors.teal,
                    isImplemented: true,
                    onTap: () => _navigateToManagement(
                        context, 'change_user_branch_warehouse'),
                  ),
                  _buildNumberedManagementCard(
                    number: 8,
                    title: localizations.activateUsers,
                    icon: Icons.people,
                    color: Colors.indigo,
                    isImplemented: true,
                    onTap: () =>
                        _navigateToManagement(context, 'activate_users'),
                  ),
                  _buildNumberedManagementCard(
                    number: 9,
                    title: localizations.serverMainFile,
                    icon: Icons.computer,
                    color: Colors.deepOrange,
                    isImplemented: true,
                    onTap: () =>
                        _navigateToManagement(context, 'server_main_file'),
                  ),
                  _buildNumberedManagementCard(
                    number: 10,
                    title: localizations.systemBackupRestore,
                    icon: Icons.backup,
                    color: Colors.orange,
                    isImplemented: true,
                    onTap: () =>
                        _navigateToManagement(context, 'backup_restore'),
                  ),
                  _buildNumberedManagementCard(
                    number: 11,
                    title: localizations.transferAccountBalances,
                    icon: Icons.account_balance_wallet,
                    color: Colors.cyan,
                    isImplemented: true,
                    onTap: () => _navigateToManagement(
                        context, 'transfer_account_balances'),
                  ),
                  _buildNumberedManagementCard(
                    number: 12,
                    title: localizations.transferInventoryBalances,
                    icon: Icons.inventory_2,
                    color: Colors.lime,
                    isImplemented: true,
                    onTap: () => _navigateToManagement(
                        context, 'transfer_inventory_balances'),
                  ),
                  _buildNumberedManagementCard(
                    number: 13,
                    title: localizations.transferStockBalances,
                    icon: Icons.warehouse,
                    color: Colors.amber,
                    isImplemented: true,
                    onTap: () => _navigateToManagement(
                        context, 'transfer_stock_balances'),
                  ),
                  _buildNumberedManagementCard(
                    number: 14,
                    title: localizations.copyPreviousYearDocuments,
                    icon: Icons.copy_all,
                    color: Colors.pink,
                    isImplemented: true,
                    onTap: () => _navigateToManagement(
                        context, 'copy_previous_year_documents'),
                  ),
                  _buildNumberedManagementCard(
                    number: 15,
                    title: localizations.hijriDatesEntry,
                    icon: Icons.calendar_month,
                    color: Colors.deepOrange,
                    isImplemented: true,
                    onTap: () =>
                        _navigateToManagement(context, 'hijri_dates_entry'),
                  ),
                  _buildNumberedManagementCard(
                    number: 16,
                    title: localizations.prepareStatisticsFiles,
                    icon: Icons.analytics,
                    color: Colors.lightBlue,
                    isImplemented: true,
                    onTap: () => _navigateToManagement(
                        context, 'prepare_statistics_files'),
                  ),
                  _buildNumberedManagementCard(
                    number: 17,
                    title: localizations.recalculateAverageCost,
                    icon: Icons.calculate,
                    color: Colors.lightGreen,
                    isImplemented: true,
                    onTap: () => _navigateToManagement(
                        context, 'recalculate_average_cost'),
                  ),
                  _buildNumberedManagementCard(
                    number: 18,
                    title: localizations.maintainInventoryQuantities,
                    icon: Icons.build,
                    color: Colors.grey,
                    isImplemented: true,
                    onTap: () => _navigateToManagement(
                        context, 'maintain_inventory_quantities'),
                  ),
                  _buildNumberedManagementCard(
                    number: 19,
                    title: localizations.maintainFixedOrders,
                    icon: Icons.construction,
                    color: Colors.blueGrey,
                    isImplemented: true,
                    onTap: () =>
                        _navigateToManagement(context, 'maintain_fixed_orders'),
                  ),
                  _buildNumberedManagementCard(
                    number: 20,
                    title: localizations.maintainAccountBalances,
                    icon: Icons.account_balance,
                    color: Colors.indigo,
                    isImplemented: true,
                    onTap: () => _navigateToManagement(
                        context, 'maintain_account_balances'),
                  ),
                  _buildNumberedManagementCard(
                    number: 21,
                    title: localizations.maintainSupplierItems,
                    icon: Icons.link,
                    color: Colors.teal,
                    isImplemented: true,
                    onTap: () => _navigateToManagement(
                        context, 'maintain_supplier_items'),
                  ),
                  _buildNumberedManagementCard(
                    number: 22,
                    title: localizations.maintainReservations,
                    icon: Icons.book_online,
                    color: Colors.purple,
                    isImplemented: true,
                    onTap: () =>
                        _navigateToManagement(context, 'maintain_reservations'),
                  ),
                  _buildNumberedManagementCard(
                    number: 23,
                    title: localizations.systemComparisons,
                    icon: Icons.compare_arrows,
                    color: Colors.purple,
                    isImplemented: true,
                    onTap: () =>
                        _navigateToManagement(context, 'system_comparisons'),
                  ),
                  _buildNumberedManagementCard(
                    number: 24,
                    title: localizations.changeItemNumber,
                    icon: Icons.edit,
                    color: Colors.indigo,
                    isImplemented: true,
                    onTap: () =>
                        _navigateToManagement(context, 'change_item_number'),
                  ),
                  _buildNumberedManagementCard(
                    number: 25,
                    title: localizations.connectedUsers,
                    icon: Icons.people_outline,
                    color: Colors.teal,
                    isImplemented: true,
                    onTap: () =>
                        _navigateToManagement(context, 'connected_users'),
                  ),
                  _buildNumberedManagementCard(
                    number: 26,
                    title: localizations.suddenShutdownMonitoring,
                    icon: Icons.warning,
                    color: Colors.red,
                    isImplemented: true,
                    onTap: () => _navigateToManagement(
                        context, 'sudden_shutdown_monitoring'),
                  ),
                  _buildNumberedManagementCard(
                    number: 27,
                    title: localizations.settlePreviousSalesInvoices,
                    icon: Icons.payment,
                    color: Colors.green,
                    isImplemented: true,
                    onTap: () => _navigateToManagement(
                        context, 'settle_previous_sales_invoices'),
                  ),
                  _buildNumberedManagementCard(
                    number: 28,
                    title: localizations.settlePreviousPurchaseInvoices,
                    icon: Icons.account_balance_wallet,
                    color: Colors.brown,
                    isImplemented: true,
                    onTap: () => _navigateToManagement(
                        context, 'settle_previous_purchase_invoices'),
                  ),
                  _buildNumberedManagementCard(
                    number: 29,
                    title: localizations.itemSalesPurchasesMovement,
                    icon: Icons.trending_up,
                    color: Colors.cyan,
                    isImplemented: true,
                    onTap: () => _navigateToManagement(
                        context, 'item_sales_purchases_movement'),
                  ),
                  _buildNumberedManagementCard(
                    number: 30,
                    title: localizations.printSpecificItemsData,
                    icon: Icons.print,
                    color: Colors.deepOrange,
                    isImplemented: true,
                    onTap: () => _navigateToManagement(
                        context, 'print_specific_items_data'),
                  ),
                  _buildNumberedManagementCard(
                    number: 31,
                    title: localizations.printBarcode,
                    icon: Icons.qr_code,
                    color: Colors.indigo,
                    isImplemented: true,
                    onTap: () =>
                        _navigateToManagement(context, 'print_barcode'),
                  ),
                  _buildNumberedManagementCard(
                    number: 32,
                    title: localizations.printBarcodeSpecificItems,
                    icon: Icons.qr_code_scanner,
                    color: Colors.purple,
                    isImplemented: true,
                    onTap: () => _navigateToManagement(
                        context, 'print_barcode_specific_items'),
                  ),
                  _buildNumberedManagementCard(
                    number: 33,
                    title: localizations.currencyReceipt,
                    icon: Icons.currency_exchange,
                    color: Colors.green,
                    isImplemented: true,
                    onTap: () =>
                        _navigateToManagement(context, 'currency_receipt'),
                  ),
                  _buildNumberedManagementCard(
                    number: 34,
                    title: localizations.terminals,
                    icon: Icons.computer,
                    color: Colors.blue,
                    isImplemented: true,
                    onTap: () => _navigateToManagement(context, 'terminals'),
                  ),
                  _buildNumberedManagementCard(
                    number: 35,
                    title: localizations.barcodeDefinition,
                    icon: Icons.qr_code,
                    color: Colors.indigo,
                    isImplemented: true,
                    onTap: () =>
                        _navigateToManagement(context, 'barcode_definition'),
                  ),
                  _buildNumberedManagementCard(
                    number: 36,
                    title: localizations.labelDefinition,
                    icon: Icons.label,
                    color: Colors.deepPurple,
                    isImplemented: true,
                    onTap: () =>
                        _navigateToManagement(context, 'label_definition'),
                  ),
                  _buildNumberedManagementCard(
                    number: 37,
                    title: localizations.suspendApplication,
                    icon: Icons.pause_circle,
                    color: Colors.red,
                    isImplemented: true,
                    onTap: () =>
                        _navigateToManagement(context, 'suspend_application'),
                  ),
                  _buildNumberedManagementCard(
                    number: 38,
                    title: localizations.editPrintedInvoices,
                    icon: Icons.edit_document,
                    color: Colors.orange,
                    isImplemented: true,
                    onTap: () =>
                        _navigateToManagement(context, 'edit_printed_invoices'),
                  ),
                  _buildNumberedManagementCard(
                    number: 39,
                    title: localizations.editPrintedReturns,
                    icon: Icons.edit_note,
                    color: Colors.deepOrange,
                    isImplemented: true,
                    onTap: () =>
                        _navigateToManagement(context, 'edit_printed_returns'),
                  ),
                  _buildNumberedManagementCard(
                    number: 40,
                    title: localizations.approveTransferVouchers,
                    icon: Icons.approval,
                    color: Colors.teal,
                    isImplemented: true,
                    onTap: () => _navigateToManagement(
                        context, 'approve_transfer_vouchers'),
                  ),
                  _buildNumberedManagementCard(
                    number: 41,
                    title: localizations.shareStoreInvoices,
                    icon: Icons.share,
                    color: Colors.green,
                    isImplemented: true,
                    onTap: () =>
                        _navigateToManagement(context, 'share_store_invoices'),
                  ),
                  _buildNumberedManagementCard(
                    number: 42,
                    title: localizations.signAndShareInvoices,
                    icon: Icons.verified,
                    color: Colors.indigo,
                    isImplemented: true,
                    onTap: () => _navigateToManagement(
                        context, 'sign_and_share_invoices'),
                  ),
                  _buildNumberedManagementCard(
                    number: 43,
                    title: localizations.transferAccountMovement,
                    icon: Icons.swap_horiz,
                    color: Colors.purple,
                    isImplemented: true,
                    onTap: () => _navigateToManagement(
                        context, 'transfer_account_movement'),
                  ),
                  _buildNumberedManagementCard(
                    number: 44,
                    title: localizations.userActivityReport,
                    icon: Icons.analytics,
                    color: Colors.cyan,
                    isImplemented: true,
                    onTap: () =>
                        _navigateToManagement(context, 'user_activity_report'),
                  ),
                  _buildNumberedManagementCard(
                    number: 45,
                    title: localizations.signAndShareDiscountNotifications,
                    icon: Icons.discount,
                    color: Colors.amber,
                    isImplemented: true,
                    onTap: () => _navigateToManagement(
                        context, 'sign_and_share_discount_notifications'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const Spacer(),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة إدارة مرقمة
  Widget _buildNumberedManagementCard({
    required int number,
    required String title,
    required IconData icon,
    required Color color,
    required bool isImplemented,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              // رقم تسلسلي
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: color, width: 2),
                ),
                child: Center(
                  child: Text(
                    number.toString(),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),

              // أيقونة
              Icon(
                icon,
                size: 32,
                color: color,
              ),
              const SizedBox(width: 16),

              // العنوان
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),

              // مؤشر الحالة
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: isImplemented ? Colors.green : Colors.grey,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  isImplemented ? 'مكتمل' : 'قريباً',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

              const SizedBox(width: 8),

              // سهم
              Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey[400],
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// التنقل إلى أداة إدارة معينة
  void _navigateToManagement(BuildContext context, String management) {
    Widget? targetPage;

    switch (management) {
      case 'vat_tax_settings':
        targetPage = const VatTaxSettingsPage();
        break;
      case 'whatsapp_service_connection':
        targetPage = const WhatsappServiceConnectionSettings();
        break;
      case 'tabby_tamara_payment_services':
        targetPage = const TabbyTamaraPaymentServicesSettingsPage();
        break;
      case 'system_monitoring':
        targetPage = const SystemMonitoringPage();
        break;
      case 'backup_restore':
        targetPage = const BackupRestorePage();
        break;
      case 'barcode_printing':
        targetPage = const BarcodePrintingPage();
        break;
      case 'transfer_account_balances':
        targetPage = const TransferAccountBalancesPage();
        break;
      case 'transfer_inventory_balances':
        targetPage = const TransferInventoryBalancesPage();
        break;
      case 'transfer_stock_balances':
        targetPage = const TransferStockBalancesPage();
        break;
      case 'copy_previous_year_documents':
        targetPage = const CopyPreviousYearDocumentsPage();
        break;
      case 'hijri_dates_entry':
        targetPage = const HijriDatesEntryPage();
        break;
      case 'prepare_statistics_files':
        targetPage = const PrepareStatisticsFilesPage();
        break;
      case 'recalculate_average_cost':
        targetPage = const RecalculateAverageCostPage();
        break;
      case 'maintain_inventory_quantities':
        targetPage = const MaintainInventoryQuantitiesPage();
        break;
      case 'maintain_fixed_orders':
        targetPage = const MaintainFixedOrdersPage();
        break;
      case 'maintain_account_balances':
        targetPage = const MaintainAccountBalancesPage();
        break;
      case 'maintain_supplier_items':
        targetPage = const MaintainSupplierItemsPage();
        break;
      case 'maintain_reservations':
        targetPage = const MaintainReservationsPage();
        break;
      case 'user_permissions':
        targetPage = const UserPermissionsPage();
        break;
      case 'change_user_password':
        targetPage = const ChangeUserPasswordPage();
        break;
      case 'change_user_branch_warehouse':
        targetPage = const ChangeUserBranchWarehousePage();
        break;
      case 'activate_users':
        targetPage = const ActivateUsersPage();
        break;
      case 'server_main_file':
        targetPage = const ServerMainFilePage();
        break;
      case 'system_comparisons':
        targetPage = const SystemComparisonsPage();
        break;
      case 'change_item_number':
        targetPage = const ChangeItemNumberPage();
        break;
      case 'connected_users':
        targetPage = const ConnectedUsersPage();
        break;
      case 'sudden_shutdown_monitoring':
        targetPage = const SuddenShutdownMonitoringPage();
        break;
      case 'settle_previous_sales_invoices':
        targetPage = const SettlePreviousSalesInvoicesPage();
        break;
      case 'settle_previous_purchase_invoices':
        targetPage = const SettlePreviousPurchaseInvoicesPage();
        break;
      case 'item_sales_purchases_movement':
        targetPage = const ItemSalesPurchasesMovementPage();
        break;
      case 'print_specific_items_data':
        targetPage = const PrintSpecificItemsDataPage();
        break;
      case 'print_barcode':
        targetPage = const PrintBarcodePage();
        break;
      case 'print_barcode_specific_items':
        targetPage = const PrintBarcodeSpecificItemsPage();
        break;
      case 'currency_receipt':
        targetPage = const CurrencyReceiptPage();
        break;
      case 'terminals':
        targetPage = const TerminalsPage();
        break;
      case 'barcode_definition':
        targetPage = const BarcodeDefinitionPage();
        break;
      case 'label_definition':
        targetPage = const LabelDefinitionPage();
        break;
      case 'suspend_application':
        targetPage = const SuspendApplicationPage();
        break;
      case 'edit_printed_invoices':
        targetPage = const EditPrintedInvoicesPage();
        break;
      case 'edit_printed_returns':
        targetPage = const EditPrintedReturnsPage();
        break;
      case 'approve_transfer_vouchers':
        targetPage = const ApproveTransferVouchersPage();
        break;
      case 'share_store_invoices':
        targetPage = const ShareStoreInvoicesPage();
        break;
      case 'sign_and_share_invoices':
        targetPage = const SignAndShareInvoicesPage();
        break;
      case 'transfer_account_movement':
        targetPage = const TransferAccountMovementPage();
        break;
      case 'user_activity_report':
        targetPage = const UserActivityReportPage();
        break;
      case 'sign_and_share_discount_notifications':
        targetPage = const SignAndShareDiscountNotificationsPage();
        break;
      default:
        final localizations = AppLocalizations.of(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${localizations.navigateTo} $management')),
        );
        return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => targetPage!,
      ),
    );
  }
}
