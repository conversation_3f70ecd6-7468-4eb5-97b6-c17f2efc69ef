import 'package:flutter/material.dart';
import '../../../../data/fiscal_year_data.dart';

class FiscalYearPage extends StatefulWidget {
  const FiscalYearPage({super.key});

  @override
  State<FiscalYearPage> createState() => _FiscalYearPageState();
}

class _FiscalYearPageState extends State<FiscalYearPage>
    with TickerProviderStateMixin {
  int? _selectedRowIndex;
  String _searchQuery = '';
  String _filterStatus = 'الكل';

  // متحكمات الرسوم المتحركة
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // مرجع للبيانات المشتركة
  final FiscalYearData _fiscalYearData = FiscalYearData();

  // قائمة السنوات المالية المفلترة والمرتبة
  List<Map<String, dynamic>> get _filteredFiscalYears {
    return _getFilteredAndSortedData();
  }

  // دالة منفصلة للحصول على البيانات المفلترة
  List<Map<String, dynamic>> _getFilteredAndSortedData() {
    // الحصول على البيانات الأساسية
    var allData = _fiscalYearData.searchFiscalYears(_searchQuery);

    // إنشاء نسخة قابلة للتعديل
    var filtered = <Map<String, dynamic>>[];
    for (var item in allData) {
      filtered.add(Map<String, dynamic>.from(item));
    }

    // تطبيق الفلتر حسب الحالة
    if (_filterStatus != 'الكل') {
      filtered = filtered.where((year) {
        final startDate = DateTime.parse(year['startDate']);
        final endDate = DateTime.parse(year['endDate']);
        final now = DateTime.now();

        switch (_filterStatus) {
          case 'نشطة':
            return now.isAfter(startDate) && now.isBefore(endDate);
          case 'منتهية':
            return now.isAfter(endDate);
          case 'مستقبلية':
            return now.isBefore(startDate);
          default:
            return true;
        }
      }).toList();
    }

    return filtered;
  }

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    // بدء الرسوم المتحركة
    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'إدارة السنوات المالية',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.blue[700],
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Column(
        children: [
          // شريط الأدوات المطور
          FadeTransition(
            opacity: _fadeAnimation,
            child: _buildAdvancedToolbar(),
          ),

          // محتوى الصفحة
          Expanded(
            child: SlideTransition(
              position: _slideAnimation,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: _buildAdvancedTable(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء شريط الأدوات المطور
  Widget _buildAdvancedToolbar() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // الصف الأول: المعلومات
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.calendar_today,
                  color: Colors.blue[600],
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'إدارة السنوات المالية',
                      style: TextStyle(
                        color: Colors.grey[800],
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'المجموع: ${_fiscalYearData.fiscalYears.length} | المعروضة: ${_filteredFiscalYears.length} | النشطة: ${_getActiveYearsCount()}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // الصف الثاني: أدوات التحكم
          Column(
            children: [
              // حقل البحث
              TextField(
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                  });
                },
                decoration: InputDecoration(
                  hintText: 'ابحث بالاسم أو الرقم أو التاريخ...',
                  hintStyle: TextStyle(color: Colors.grey[500], fontSize: 14),
                  prefixIcon: Icon(Icons.search, color: Colors.blue[600]),
                  suffixIcon: _searchQuery.isNotEmpty
                      ? IconButton(
                          icon: Icon(Icons.clear, color: Colors.grey[600]),
                          onPressed: () {
                            setState(() {
                              _searchQuery = '';
                            });
                          },
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: Colors.blue[600]!, width: 2),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
              ),

              const SizedBox(height: 12),

              // فلتر الحالة وأزرار التحكم
              Row(
                children: [
                  // فلتر الحالة
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[300]!),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: DropdownButton<String>(
                        value: _filterStatus,
                        underline: const SizedBox(),
                        icon: Icon(Icons.filter_list, color: Colors.blue[600]),
                        isExpanded: true,
                        items: ['الكل', 'نشطة', 'منتهية', 'مستقبلية']
                            .map((String value) {
                          return DropdownMenuItem<String>(
                            value: value,
                            child: Text(value),
                          );
                        }).toList(),
                        onChanged: (String? newValue) {
                          setState(() {
                            _filterStatus = newValue!;
                          });
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// حساب عدد السنوات النشطة
  int _getActiveYearsCount() {
    final now = DateTime.now();
    return _fiscalYearData.fiscalYears.where((year) {
      final startDate = DateTime.parse(year['startDate']);
      final endDate = DateTime.parse(year['endDate']);
      return now.isAfter(startDate) && now.isBefore(endDate);
    }).length;
  }

  /// بناء الجدول المطور
  Widget _buildAdvancedTable() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        children: [
          // رأس الجدول مع إمكانية الترتيب
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 1,
                  child: Text(
                    'رقم الشركة',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[700],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'رقم السنة',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[700],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Text(
                    'الاسم العربي',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[700],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'بداية السنة',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[700],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'نهاية السنة',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[700],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'الحالة',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[700],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),

          // محتوى الجدول
          Expanded(
            child: _filteredFiscalYears.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    itemCount: _filteredFiscalYears.length,
                    itemBuilder: (context, index) {
                      final fiscalYear = _filteredFiscalYears[index];
                      return _buildAdvancedDataRow(fiscalYear, index);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد سنوات مالية تطابق البحث',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'جرب تغيير كلمات البحث أو الفلتر',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء صف البيانات المطور
  Widget _buildAdvancedDataRow(Map<String, dynamic> fiscalYear, int index) {
    final ValueNotifier<bool> isHovered = ValueNotifier<bool>(false);
    final status = _getFiscalYearStatus(fiscalYear);

    return ValueListenableBuilder<bool>(
      valueListenable: isHovered,
      builder: (context, hovered, child) {
        return MouseRegion(
          cursor: SystemMouseCursors.click,
          onEnter: (_) => isHovered.value = true,
          onExit: (_) => isHovered.value = false,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: hovered
                  ? Colors.blue.withValues(alpha: 0.1)
                  : (_selectedRowIndex == index
                      ? Colors.blue.withValues(alpha: 0.15)
                      : Colors.white),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: hovered ? Colors.blue[300]! : Colors.grey[200]!,
                width: hovered ? 2 : 1,
              ),
              boxShadow: hovered
                  ? [
                      BoxShadow(
                        color: Colors.blue.withValues(alpha: 0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ]
                  : null,
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  setState(() {
                    _selectedRowIndex =
                        _selectedRowIndex == index ? null : index;
                  });
                },
                borderRadius: BorderRadius.circular(8),
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 1,
                        child: Text(
                          fiscalYear['companyNumber'],
                          textAlign: TextAlign.center,
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Text(
                          fiscalYear['fiscalYearNumber'],
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                              fontSize: 14, fontWeight: FontWeight.w600),
                        ),
                      ),
                      Expanded(
                        flex: 3,
                        child: Text(
                          fiscalYear['arabicName'],
                          textAlign: TextAlign.center,
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Text(
                          fiscalYear['startDate'],
                          textAlign: TextAlign.center,
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Text(
                          fiscalYear['endDate'],
                          textAlign: TextAlign.center,
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: _buildStatusChip(status),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء رقاقة الحالة
  Widget _buildStatusChip(Map<String, dynamic> status) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: status['color'].withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: status['color'].withValues(alpha: 0.3)),
      ),
      child: Text(
        status['text'],
        style: TextStyle(
          color: status['color'],
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// الحصول على حالة السنة المالية
  Map<String, dynamic> _getFiscalYearStatus(Map<String, dynamic> fiscalYear) {
    final startDate = DateTime.parse(fiscalYear['startDate']);
    final endDate = DateTime.parse(fiscalYear['endDate']);
    final now = DateTime.now();

    if (now.isAfter(startDate) && now.isBefore(endDate)) {
      return {'text': 'نشطة', 'color': Colors.green};
    } else if (now.isAfter(endDate)) {
      return {'text': 'منتهية', 'color': Colors.red};
    } else {
      return {'text': 'مستقبلية', 'color': Colors.orange};
    }
  }
}
