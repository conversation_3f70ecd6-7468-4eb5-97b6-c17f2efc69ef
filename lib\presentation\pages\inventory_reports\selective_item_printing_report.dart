import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة طباعة إختيارية للأصناف
/// يعرض ويطبع قوائم مخصصة للأصناف
class SelectiveItemPrintingReportPage extends StatefulWidget {
  const SelectiveItemPrintingReportPage({super.key});

  @override
  State<SelectiveItemPrintingReportPage> createState() =>
      _SelectiveItemPrintingReportPageState();
}

class _SelectiveItemPrintingReportPageState
    extends State<SelectiveItemPrintingReportPage> {
  String? _selectedCategory;
  String? _selectedWarehouse;
  String? _printFormat = 'list';
  String? _sortBy = 'name';
  bool _includeImages = false;
  bool _includePrices = true;
  bool _includeQuantities = true;
  bool _includeBarcode = false;

  final List<Map<String, dynamic>> _selectedItems = [];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('طباعة إختيارية للأصناف'),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printSelectedItems,
            tooltip: 'طباعة الأصناف المحددة',
          ),
          IconButton(
            icon: const Icon(Icons.preview),
            onPressed: _previewPrint,
            tooltip: 'معاينة الطباعة',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _printSettings,
            tooltip: 'إعدادات الطباعة',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة والإعدادات
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.indigo[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.category,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedCategory,
                        items: [
                          DropdownMenuItem(
                              value: 'all',
                              child: Text(localizations.allCategories)),
                          DropdownMenuItem(
                              value: 'electronics',
                              child: Text(localizations.electronics)),
                          DropdownMenuItem(
                              value: 'clothing',
                              child: Text(localizations.clothing)),
                          DropdownMenuItem(
                              value: 'food', child: Text(localizations.food)),
                        ],
                        onChanged: (value) =>
                            setState(() => _selectedCategory = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.warehouse,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedWarehouse,
                        items: [
                          DropdownMenuItem(
                              value: 'all',
                              child: Text(localizations.allWarehouses)),
                          DropdownMenuItem(
                              value: 'main',
                              child: Text(localizations.mainWarehouse)),
                          DropdownMenuItem(
                              value: 'branch1',
                              child: Text(
                                  '${localizations.branchWarehouse} الأول')),
                          DropdownMenuItem(
                              value: 'branch2',
                              child: Text(
                                  '${localizations.branchWarehouse} الثاني')),
                        ],
                        onChanged: (value) =>
                            setState(() => _selectedWarehouse = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'تنسيق الطباعة',
                          border: OutlineInputBorder(),
                        ),
                        value: _printFormat,
                        items: const [
                          DropdownMenuItem(value: 'list', child: Text('قائمة')),
                          DropdownMenuItem(value: 'table', child: Text('جدول')),
                          DropdownMenuItem(
                              value: 'cards', child: Text('بطاقات')),
                          DropdownMenuItem(
                              value: 'labels', child: Text('ملصقات')),
                        ],
                        onChanged: (value) =>
                            setState(() => _printFormat = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'ترتيب حسب',
                          border: OutlineInputBorder(),
                        ),
                        value: _sortBy,
                        items: const [
                          DropdownMenuItem(value: 'name', child: Text('الاسم')),
                          DropdownMenuItem(value: 'code', child: Text('الكود')),
                          DropdownMenuItem(
                              value: 'price', child: Text('السعر')),
                          DropdownMenuItem(
                              value: 'quantity', child: Text('الكمية')),
                        ],
                        onChanged: (value) => setState(() => _sortBy = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _loadItems,
                  icon: const Icon(Icons.search),
                  label: const Text('تحميل الأصناف'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.indigo,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ],
            ),
          ),

          // محتوى الأصناف
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // إعدادات الطباعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.print_outlined,
                                  color: Colors.indigo, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'إعدادات الطباعة',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: CheckboxListTile(
                                  title: const Text('تضمين الصور'),
                                  value: _includeImages,
                                  onChanged: (value) =>
                                      setState(() => _includeImages = value!),
                                ),
                              ),
                              Expanded(
                                child: CheckboxListTile(
                                  title: const Text('تضمين الأسعار'),
                                  value: _includePrices,
                                  onChanged: (value) =>
                                      setState(() => _includePrices = value!),
                                ),
                              ),
                            ],
                          ),
                          Row(
                            children: [
                              Expanded(
                                child: CheckboxListTile(
                                  title: const Text('تضمين الكميات'),
                                  value: _includeQuantities,
                                  onChanged: (value) => setState(
                                      () => _includeQuantities = value!),
                                ),
                              ),
                              Expanded(
                                child: CheckboxListTile(
                                  title: const Text('تضمين الباركود'),
                                  value: _includeBarcode,
                                  onChanged: (value) =>
                                      setState(() => _includeBarcode = value!),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // قائمة الأصناف للاختيار
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Text(
                                'اختيار الأصناف للطباعة',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const Spacer(),
                              Text('المحدد: ${_selectedItems.length}'),
                              const SizedBox(width: 16),
                              ElevatedButton(
                                onPressed: _selectAllItems,
                                child: const Text('تحديد الكل'),
                              ),
                              const SizedBox(width: 8),
                              ElevatedButton(
                                onPressed: _clearSelection,
                                child: const Text('إلغاء التحديد'),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          ..._buildItemsList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // معاينة تنسيق الطباعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'معاينة تنسيق الطباعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          _buildFormatPreview(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // قوالب الطباعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.description,
                                  color: Colors.green, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'قوالب الطباعة',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildTemplateCard(
                                  'قائمة أسعار',
                                  'قالب لطباعة قائمة الأسعار',
                                  Icons.price_check,
                                  Colors.blue),
                              _buildTemplateCard(
                                  'كتالوج منتجات',
                                  'قالب لطباعة كتالوج المنتجات',
                                  Icons.book,
                                  Colors.green),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              _buildTemplateCard(
                                  'ملصقات أصناف',
                                  'قالب لطباعة ملصقات الأصناف',
                                  Icons.label,
                                  Colors.orange),
                              _buildTemplateCard(
                                  'جرد مخزون',
                                  'قالب لطباعة قوائم الجرد',
                                  Icons.inventory,
                                  Colors.purple),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _printSelectedItems,
                                  icon: const Icon(Icons.print),
                                  label: const Text('طباعة المحدد'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _exportToPDF,
                                  icon: const Icon(Icons.picture_as_pdf),
                                  label: const Text('تصدير PDF'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _saveTemplate,
                                  icon: const Icon(Icons.save),
                                  label: const Text('حفظ كقالب'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _emailList,
                                  icon: const Icon(Icons.email),
                                  label: const Text('إرسال بالإيميل'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildItemsList() {
    final items = [
      {
        'code': 'ITM-001',
        'name': 'لابتوب ديل XPS 13',
        'price': '2,500 ر.س',
        'quantity': '25',
        'category': 'إلكترونيات'
      },
      {
        'code': 'ITM-002',
        'name': 'طابعة HP LaserJet',
        'price': '1,200 ر.س',
        'quantity': '15',
        'category': 'إلكترونيات'
      },
      {
        'code': 'ITM-003',
        'name': 'هاتف آيفون 15',
        'price': '3,000 ر.س',
        'quantity': '30',
        'category': 'إلكترونيات'
      },
      {
        'code': 'ITM-004',
        'name': 'ساعة ذكية',
        'price': '1,500 ر.س',
        'quantity': '20',
        'category': 'إكسسوارات'
      },
    ];

    return items
        .map((item) => Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.indigo.withOpacity(0.3)),
                borderRadius: BorderRadius.circular(8),
                color: Colors.indigo.withOpacity(0.05),
              ),
              child: Row(
                children: [
                  Checkbox(
                    value: _selectedItems
                        .any((selected) => selected['code'] == item['code']),
                    onChanged: (value) => _toggleItemSelection(item, value!),
                  ),
                  const SizedBox(width: 12),
                  const Icon(Icons.inventory_2, color: Colors.indigo, size: 24),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${item['code']} - ${item['name']}',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'السعر: ${item['price']} • الكمية: ${item['quantity']} • ${item['category']}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.info, color: Colors.blue),
                    onPressed: () => _showItemDetails(item),
                    tooltip: 'تفاصيل الصنف',
                  ),
                ],
              ),
            ))
        .toList();
  }

  Widget _buildFormatPreview() {
    switch (_printFormat) {
      case 'list':
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('1. لابتوب ديل XPS 13 - 2,500 ر.س'),
              Text('2. طابعة HP LaserJet - 1,200 ر.س'),
              Text('3. هاتف آيفون 15 - 3,000 ر.س'),
            ],
          ),
        );
      case 'table':
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Column(
            children: [
              Row(
                children: [
                  Expanded(
                      child: Text('الكود',
                          style: TextStyle(fontWeight: FontWeight.bold))),
                  Expanded(
                      child: Text('الاسم',
                          style: TextStyle(fontWeight: FontWeight.bold))),
                  Expanded(
                      child: Text('السعر',
                          style: TextStyle(fontWeight: FontWeight.bold))),
                ],
              ),
              Divider(),
              Row(
                children: [
                  Expanded(child: Text('ITM-001')),
                  Expanded(child: Text('لابتوب ديل')),
                  Expanded(child: Text('2,500 ر.س')),
                ],
              ),
            ],
          ),
        );
      case 'cards':
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Row(
            children: [
              Expanded(
                child: Card(
                  child: Padding(
                    padding: EdgeInsets.all(8),
                    child: Column(
                      children: [
                        Text('لابتوب ديل XPS 13',
                            style: TextStyle(fontWeight: FontWeight.bold)),
                        Text('2,500 ر.س'),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      case 'labels':
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    border: Border.all(),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Column(
                    children: [
                      Text('ITM-001'),
                      Text('لابتوب ديل'),
                      Text('||||| ||||'),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      default:
        return const SizedBox();
    }
  }

  Widget _buildTemplateCard(
      String title, String description, IconData icon, Color color) {
    return Expanded(
      child: Card(
        child: InkWell(
          onTap: () => _selectTemplate(title),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Icon(icon, color: color, size: 32),
                const SizedBox(height: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(fontSize: 10),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _toggleItemSelection(Map<String, dynamic> item, bool selected) {
    setState(() {
      if (selected) {
        _selectedItems.add(item);
      } else {
        _selectedItems
            .removeWhere((selected) => selected['code'] == item['code']);
      }
    });
  }

  void _loadItems() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تحميل الأصناف بنجاح')),
    );
  }

  void _selectAllItems() {
    setState(() {
      _selectedItems.clear();
      // إضافة جميع الأصناف للقائمة المحددة
    });
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تحديد جميع الأصناف')),
    );
  }

  void _clearSelection() {
    setState(() {
      _selectedItems.clear();
    });
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إلغاء تحديد جميع الأصناف')),
    );
  }

  void _printSelectedItems() {
    if (_selectedItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى تحديد أصناف للطباعة')),
      );
      return;
    }
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('طباعة ${_selectedItems.length} صنف')),
    );
  }

  void _previewPrint() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('معاينة الطباعة')),
    );
  }

  void _printSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('فتح إعدادات الطباعة')),
    );
  }

  void _exportToPDF() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير القائمة إلى PDF')),
    );
  }

  void _saveTemplate() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('حفظ القالب بنجاح')),
    );
  }

  void _emailList() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إرسال القائمة بالإيميل')),
    );
  }

  void _showItemDetails(Map<String, dynamic> item) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تفاصيل الصنف: ${item['name']}')),
    );
  }

  void _selectTemplate(String template) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم اختيار قالب: $template')),
    );
  }
}
