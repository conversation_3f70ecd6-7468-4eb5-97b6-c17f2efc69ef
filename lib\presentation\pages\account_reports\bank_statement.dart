import 'package:flutter/material.dart';

/// صفحة كشف البنك
/// تعرض كشف حساب البنك مع جميع المعاملات
class BankStatementPage extends StatefulWidget {
  const BankStatementPage({super.key});

  @override
  State<BankStatementPage> createState() => _BankStatementPageState();
}

class _BankStatementPageState extends State<BankStatementPage> {
  String _selectedBank = 'alahli';
  String _selectedPeriod = 'current_month';

  // بيانات تجريبية لكشف البنك
  final Map<String, List<Map<String, dynamic>>> _bankStatements = {
    'alahli': [
      {
        'date': '2024-01-15',
        'description': 'إيداع نقدي',
        'reference': 'DEP001',
        'debit': 25000.0,
        'credit': 0.0,
        'balance': 125000.0,
        'type': 'إيداع',
      },
      {
        'date': '2024-01-16',
        'description': 'تحويل للموردين',
        'reference': 'TRF001',
        'debit': 0.0,
        'credit': 15000.0,
        'balance': 110000.0,
        'type': 'تحويل',
      },
      {
        'date': '2024-01-18',
        'description': 'رسوم بنكية',
        'reference': 'FEE001',
        'debit': 0.0,
        'credit': 150.0,
        'balance': 109850.0,
        'type': 'رسوم',
      },
    ],
  };

  @override
  Widget build(BuildContext context) {
    final currentStatements = _bankStatements[_selectedBank] ?? [];
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('كشف البنك'),
        backgroundColor: Colors.lightGreen,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedBank,
                    decoration: const InputDecoration(
                      labelText: 'البنك',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'alahli', child: Text('البنك الأهلي')),
                      DropdownMenuItem(value: 'alrajhi', child: Text('بنك الراجحي')),
                      DropdownMenuItem(value: 'riyad', child: Text('بنك الرياض')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedBank = value!;
                      });
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedPeriod,
                    decoration: const InputDecoration(
                      labelText: 'الفترة',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'current_month', child: Text('الشهر الحالي')),
                      DropdownMenuItem(value: 'last_month', child: Text('الشهر الماضي')),
                      DropdownMenuItem(value: 'current_quarter', child: Text('الربع الحالي')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedPeriod = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
          ),

          // معلومات الحساب
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              color: Colors.lightGreen[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      _getBankName(_selectedBank),
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Column(
                          children: [
                            const Text('الرصيد الحالي'),
                            Text(
                              '${_getCurrentBalance(currentStatements)} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('عدد المعاملات'),
                            Text(
                              currentStatements.length.toString(),
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('إجمالي الإيداعات'),
                            Text(
                              '${_getTotalDeposits(currentStatements)} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.orange,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

          // قائمة المعاملات
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: currentStatements.length,
              itemBuilder: (context, index) {
                final transaction = currentStatements[index];
                
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: _getTransactionColor(transaction['type']),
                      child: Icon(
                        _getTransactionIcon(transaction['type']),
                        color: Colors.white,
                      ),
                    ),
                    title: Text(
                      transaction['description'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('التاريخ: ${transaction['date']}'),
                        Text('المرجع: ${transaction['reference']}'),
                        if (transaction['debit'] > 0)
                          Text(
                            'مدين: ${transaction['debit'].toStringAsFixed(2)} ر.س',
                            style: const TextStyle(
                              color: Colors.green,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        if (transaction['credit'] > 0)
                          Text(
                            'دائن: ${transaction['credit'].toStringAsFixed(2)} ر.س',
                            style: const TextStyle(
                              color: Colors.red,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                      ],
                    ),
                    trailing: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        const Text(
                          'الرصيد',
                          style: TextStyle(fontSize: 10),
                        ),
                        Text(
                          '${transaction['balance'].toStringAsFixed(2)} ر.س',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                    isThreeLine: true,
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshData,
        backgroundColor: Colors.lightGreen,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث البيانات'),
      ),
    );
  }

  String _getBankName(String bankCode) {
    switch (bankCode) {
      case 'alahli':
        return 'البنك الأهلي';
      case 'alrajhi':
        return 'بنك الراجحي';
      case 'riyad':
        return 'بنك الرياض';
      default:
        return 'بنك غير معروف';
    }
  }

  Color _getTransactionColor(String type) {
    switch (type) {
      case 'إيداع':
        return Colors.green;
      case 'تحويل':
        return Colors.blue;
      case 'رسوم':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getTransactionIcon(String type) {
    switch (type) {
      case 'إيداع':
        return Icons.add;
      case 'تحويل':
        return Icons.swap_horiz;
      case 'رسوم':
        return Icons.remove;
      default:
        return Icons.account_balance;
    }
  }

  double _getCurrentBalance(List<Map<String, dynamic>> statements) {
    if (statements.isEmpty) return 0.0;
    return statements.last['balance'];
  }

  double _getTotalDeposits(List<Map<String, dynamic>> statements) {
    return statements.fold(0.0, (sum, transaction) => sum + transaction['debit']);
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة كشف البنك')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير كشف البنك')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث بيانات كشف البنك'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
