import 'package:flutter/material.dart';

/// صفحة تقرير الدخل اليومي
/// تعرض تقرير الدخل والإيرادات اليومية
class DailyIncomeReportPage extends StatefulWidget {
  const DailyIncomeReportPage({super.key});

  @override
  State<DailyIncomeReportPage> createState() => _DailyIncomeReportPageState();
}

class _DailyIncomeReportPageState extends State<DailyIncomeReportPage> {
  String _selectedPeriod = 'last_7_days';

  // بيانات تجريبية للدخل اليومي
  final Map<String, List<Map<String, dynamic>>> _dailyIncomeData = {
    'last_7_days': [
      {
        'date': '2024-01-15',
        'dayName': 'الاثنين',
        'totalRevenue': 25000.0,
        'totalExpenses': 18000.0,
        'netIncome': 7000.0,
        'ordersCount': 45,
        'avgOrderValue': 555.56,
        'details': [
          {'category': 'مبيعات نقدية', 'amount': 15000.0, 'type': 'revenue'},
          {'category': 'مبيعات آجلة', 'amount': 10000.0, 'type': 'revenue'},
          {'category': 'تكلفة البضاعة', 'amount': 12000.0, 'type': 'expense'},
          {'category': 'مصروفات تشغيلية', 'amount': 6000.0, 'type': 'expense'},
        ],
      },
      {
        'date': '2024-01-16',
        'dayName': 'الثلاثاء',
        'totalRevenue': 32000.0,
        'totalExpenses': 22000.0,
        'netIncome': 10000.0,
        'ordersCount': 58,
        'avgOrderValue': 551.72,
        'details': [
          {'category': 'مبيعات نقدية', 'amount': 20000.0, 'type': 'revenue'},
          {'category': 'مبيعات آجلة', 'amount': 12000.0, 'type': 'revenue'},
          {'category': 'تكلفة البضاعة', 'amount': 15000.0, 'type': 'expense'},
          {'category': 'مصروفات تشغيلية', 'amount': 7000.0, 'type': 'expense'},
        ],
      },
      {
        'date': '2024-01-17',
        'dayName': 'الأربعاء',
        'totalRevenue': 28000.0,
        'totalExpenses': 20000.0,
        'netIncome': 8000.0,
        'ordersCount': 52,
        'avgOrderValue': 538.46,
        'details': [
          {'category': 'مبيعات نقدية', 'amount': 18000.0, 'type': 'revenue'},
          {'category': 'مبيعات آجلة', 'amount': 10000.0, 'type': 'revenue'},
          {'category': 'تكلفة البضاعة', 'amount': 14000.0, 'type': 'expense'},
          {'category': 'مصروفات تشغيلية', 'amount': 6000.0, 'type': 'expense'},
        ],
      },
      {
        'date': '2024-01-18',
        'dayName': 'الخميس',
        'totalRevenue': 35000.0,
        'totalExpenses': 24000.0,
        'netIncome': 11000.0,
        'ordersCount': 62,
        'avgOrderValue': 564.52,
        'details': [
          {'category': 'مبيعات نقدية', 'amount': 22000.0, 'type': 'revenue'},
          {'category': 'مبيعات آجلة', 'amount': 13000.0, 'type': 'revenue'},
          {'category': 'تكلفة البضاعة', 'amount': 16000.0, 'type': 'expense'},
          {'category': 'مصروفات تشغيلية', 'amount': 8000.0, 'type': 'expense'},
        ],
      },
      {
        'date': '2024-01-19',
        'dayName': 'الجمعة',
        'totalRevenue': 18000.0,
        'totalExpenses': 13000.0,
        'netIncome': 5000.0,
        'ordersCount': 32,
        'avgOrderValue': 562.50,
        'details': [
          {'category': 'مبيعات نقدية', 'amount': 12000.0, 'type': 'revenue'},
          {'category': 'مبيعات آجلة', 'amount': 6000.0, 'type': 'revenue'},
          {'category': 'تكلفة البضاعة', 'amount': 9000.0, 'type': 'expense'},
          {'category': 'مصروفات تشغيلية', 'amount': 4000.0, 'type': 'expense'},
        ],
      },
      {
        'date': '2024-01-20',
        'dayName': 'السبت',
        'totalRevenue': 42000.0,
        'totalExpenses': 28000.0,
        'netIncome': 14000.0,
        'ordersCount': 75,
        'avgOrderValue': 560.00,
        'details': [
          {'category': 'مبيعات نقدية', 'amount': 28000.0, 'type': 'revenue'},
          {'category': 'مبيعات آجلة', 'amount': 14000.0, 'type': 'revenue'},
          {'category': 'تكلفة البضاعة', 'amount': 20000.0, 'type': 'expense'},
          {'category': 'مصروفات تشغيلية', 'amount': 8000.0, 'type': 'expense'},
        ],
      },
      {
        'date': '2024-01-21',
        'dayName': 'الأحد',
        'totalRevenue': 30000.0,
        'totalExpenses': 21000.0,
        'netIncome': 9000.0,
        'ordersCount': 55,
        'avgOrderValue': 545.45,
        'details': [
          {'category': 'مبيعات نقدية', 'amount': 19000.0, 'type': 'revenue'},
          {'category': 'مبيعات آجلة', 'amount': 11000.0, 'type': 'revenue'},
          {'category': 'تكلفة البضاعة', 'amount': 15000.0, 'type': 'expense'},
          {'category': 'مصروفات تشغيلية', 'amount': 6000.0, 'type': 'expense'},
        ],
      },
    ],
  };

  @override
  Widget build(BuildContext context) {
    final currentData = _dailyIncomeData[_selectedPeriod] ?? [];
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير الدخل اليومي'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedPeriod,
                    decoration: const InputDecoration(
                      labelText: 'الفترة',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'last_7_days', child: Text('آخر 7 أيام')),
                      DropdownMenuItem(value: 'last_30_days', child: Text('آخر 30 يوم')),
                      DropdownMenuItem(value: 'current_month', child: Text('الشهر الحالي')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedPeriod = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
          ),

          // ملخص الفترة
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              color: Colors.green[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    const Text(
                      'ملخص الدخل اليومي',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Column(
                          children: [
                            const Text('إجمالي الإيرادات'),
                            Text(
                              '${_getTotalRevenue(currentData)} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('إجمالي المصروفات'),
                            Text(
                              '${_getTotalExpenses(currentData)} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.red,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('صافي الدخل'),
                            Text(
                              '${_getTotalNetIncome(currentData)} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                _buildStatCard('متوسط الدخل اليومي', '${_getAverageDailyIncome(currentData)} ر.س', Colors.green),
                _buildStatCard('أعلى دخل يومي', '${_getHighestDailyIncome(currentData)} ر.س', Colors.blue),
                _buildStatCard('إجمالي الطلبات', _getTotalOrders(currentData).toString(), Colors.orange),
                _buildStatCard('متوسط قيمة الطلب', '${_getAverageOrderValue(currentData)} ر.س', Colors.purple),
              ],
            ),
          ),

          // قائمة الأيام
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: currentData.length,
              itemBuilder: (context, index) {
                final day = currentData[index];
                double profitMargin = (day['netIncome'] / day['totalRevenue']) * 100;
                
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: _getProfitColor(day['netIncome']),
                      child: Text(
                        day['dayName'].substring(0, 2),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    title: Text(
                      '${day['dayName']} - ${day['date']}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('صافي الدخل: ${day['netIncome'].toStringAsFixed(2)} ر.س'),
                        Text('هامش الربح: ${profitMargin.toStringAsFixed(1)}%'),
                        const SizedBox(height: 4),
                        LinearProgressIndicator(
                          value: profitMargin / 100,
                          backgroundColor: Colors.grey[300],
                          valueColor: AlwaysStoppedAnimation<Color>(
                            _getProfitColor(day['netIncome']),
                          ),
                        ),
                      ],
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('إجمالي الإيرادات', '${day['totalRevenue'].toStringAsFixed(2)} ر.س', Icons.trending_up, Colors.green),
                                ),
                                Expanded(
                                  child: _buildDetailCard('إجمالي المصروفات', '${day['totalExpenses'].toStringAsFixed(2)} ر.س', Icons.trending_down, Colors.red),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('عدد الطلبات', day['ordersCount'].toString(), Icons.shopping_cart, Colors.blue),
                                ),
                                Expanded(
                                  child: _buildDetailCard('متوسط الطلب', '${day['avgOrderValue'].toStringAsFixed(2)} ر.س', Icons.calculate, Colors.orange),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            const Text(
                              'تفاصيل اليوم:',
                              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                            ),
                            const SizedBox(height: 8),
                            ...day['details'].map<Widget>((detail) {
                              return ListTile(
                                leading: Icon(
                                  detail['type'] == 'revenue' ? Icons.add_circle : Icons.remove_circle,
                                  color: detail['type'] == 'revenue' ? Colors.green : Colors.red,
                                ),
                                title: Text(detail['category']),
                                trailing: Text(
                                  '${detail['amount'].toStringAsFixed(2)} ر.س',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: detail['type'] == 'revenue' ? Colors.green : Colors.red,
                                  ),
                                ),
                              );
                            }).toList(),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshData,
        backgroundColor: Colors.green,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث البيانات'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Icon(icon, size: 20, color: color),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                color: color,
              ),
            ),
            Text(
              title,
              style: const TextStyle(fontSize: 10),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Color _getProfitColor(double netIncome) {
    if (netIncome >= 10000) return Colors.green;
    if (netIncome >= 5000) return Colors.orange;
    return Colors.red;
  }

  double _getTotalRevenue(List<Map<String, dynamic>> data) {
    return data.fold(0.0, (sum, day) => sum + day['totalRevenue']);
  }

  double _getTotalExpenses(List<Map<String, dynamic>> data) {
    return data.fold(0.0, (sum, day) => sum + day['totalExpenses']);
  }

  double _getTotalNetIncome(List<Map<String, dynamic>> data) {
    return data.fold(0.0, (sum, day) => sum + day['netIncome']);
  }

  String _getAverageDailyIncome(List<Map<String, dynamic>> data) {
    if (data.isEmpty) return '0.00';
    return (_getTotalNetIncome(data) / data.length).toStringAsFixed(2);
  }

  String _getHighestDailyIncome(List<Map<String, dynamic>> data) {
    if (data.isEmpty) return '0.00';
    double max = data.map((day) => day['netIncome'] as double).reduce((a, b) => a > b ? a : b);
    return max.toStringAsFixed(2);
  }

  int _getTotalOrders(List<Map<String, dynamic>> data) {
    return data.fold(0, (sum, day) => sum + day['ordersCount'] as int);
  }

  String _getAverageOrderValue(List<Map<String, dynamic>> data) {
    if (data.isEmpty) return '0.00';
    double totalRevenue = _getTotalRevenue(data);
    int totalOrders = _getTotalOrders(data);
    if (totalOrders == 0) return '0.00';
    return (totalRevenue / totalOrders).toStringAsFixed(2);
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة تقرير الدخل اليومي')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير تقرير الدخل اليومي')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث بيانات الدخل اليومي'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
