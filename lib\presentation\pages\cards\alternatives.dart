import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة البدائل
/// تتيح إدارة الأصناف البديلة للمنتجات
class AlternativesPage extends StatefulWidget {
  const AlternativesPage({super.key});

  @override
  State<AlternativesPage> createState() => _AlternativesPageState();
}

class _AlternativesPageState extends State<AlternativesPage> {
  String _searchQuery = '';

  // بيانات تجريبية للبدائل
  final List<Map<String, dynamic>> _alternatives = [
    {
      'id': 'ALT001',
      'mainItem': 'لابتوب ديل XPS 13',
      'mainItemCode': 'DELL-XPS13',
      'alternatives': [
        {'name': 'لابتوب HP Spectre', 'code': 'HP-SPEC', 'price': 4500.0, 'availability': true},
        {'name': 'لابتوب Lenovo ThinkPad', 'code': 'LEN-TP', 'price': 4200.0, 'availability': true},
        {'name': 'لابتوب ASUS ZenBook', 'code': 'ASUS-ZB', 'price': 4800.0, 'availability': false},
      ],
      'category': 'إلكترونيات',
      'isActive': true,
    },
    {
      'id': 'ALT002',
      'mainItem': 'هاتف iPhone 14',
      'mainItemCode': 'IPHONE-14',
      'alternatives': [
        {'name': 'هاتف Samsung Galaxy S23', 'code': 'SAM-S23', 'price': 3200.0, 'availability': true},
        {'name': 'هاتف Google Pixel 7', 'code': 'GOO-PIX7', 'price': 2800.0, 'availability': true},
      ],
      'category': 'إلكترونيات',
      'isActive': true,
    },
    {
      'id': 'ALT003',
      'mainItem': 'قميص قطني أزرق',
      'mainItemCode': 'SHIRT-BLUE',
      'alternatives': [
        {'name': 'قميص قطني أبيض', 'code': 'SHIRT-WHITE', 'price': 120.0, 'availability': true},
        {'name': 'قميص قطني أسود', 'code': 'SHIRT-BLACK', 'price': 125.0, 'availability': true},
        {'name': 'قميص قطني رمادي', 'code': 'SHIRT-GRAY', 'price': 115.0, 'availability': false},
      ],
      'category': 'ملابس',
      'isActive': false,
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.alternatives),
        backgroundColor: Colors.cyan,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addAlternative,
            tooltip: 'إضافة بديل جديد',
          ),
          IconButton(
            icon: const Icon(Icons.sync),
            onPressed: _syncAlternatives,
            tooltip: 'مزامنة البدائل',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: TextField(
              decoration: const InputDecoration(
                hintText: 'البحث في البدائل...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                _buildStatCard('المجموع', _alternatives.length.toString(), Colors.blue),
                _buildStatCard('النشطة', _alternatives.where((a) => a['isActive']).length.toString(), Colors.green),
                _buildStatCard('معطلة', _alternatives.where((a) => !a['isActive']).length.toString(), Colors.red),
                _buildStatCard('البدائل', _getTotalAlternatives(), Colors.orange),
              ],
            ),
          ),

          // قائمة البدائل
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _alternatives.length,
              itemBuilder: (context, index) {
                final alternative = _alternatives[index];
                
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: alternative['isActive'] ? Colors.green : Colors.red,
                      child: Icon(
                        Icons.swap_horiz,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    title: Text(
                      alternative['mainItem'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('الكود: ${alternative['mainItemCode']}'),
                        Text('الفئة: ${alternative['category']}'),
                        Text('عدد البدائل: ${alternative['alternatives'].length}'),
                      ],
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          alternative['isActive'] ? Icons.check_circle : Icons.cancel,
                          color: alternative['isActive'] ? Colors.green : Colors.red,
                        ),
                        PopupMenuButton<String>(
                          onSelected: (value) => _handleAction(value, alternative),
                          itemBuilder: (context) => [
                            const PopupMenuItem(
                              value: 'edit',
                              child: ListTile(
                                leading: Icon(Icons.edit),
                                title: Text('تعديل'),
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'toggle',
                              child: ListTile(
                                leading: Icon(Icons.toggle_on),
                                title: Text('تغيير الحالة'),
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'add_alternative',
                              child: ListTile(
                                leading: Icon(Icons.add),
                                title: Text('إضافة بديل'),
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'delete',
                              child: ListTile(
                                leading: Icon(Icons.delete, color: Colors.red),
                                title: Text('حذف', style: TextStyle(color: Colors.red)),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'البدائل المتاحة:',
                              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                            ),
                            const SizedBox(height: 8),
                            ...alternative['alternatives'].map<Widget>((alt) {
                              return Card(
                                color: alt['availability'] ? Colors.green[50] : Colors.red[50],
                                child: ListTile(
                                  leading: Icon(
                                    alt['availability'] ? Icons.check_circle : Icons.cancel,
                                    color: alt['availability'] ? Colors.green : Colors.red,
                                  ),
                                  title: Text(alt['name']),
                                  subtitle: Text('الكود: ${alt['code']}'),
                                  trailing: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        '${alt['price']} ر.س',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 14,
                                        ),
                                      ),
                                      Text(
                                        alt['availability'] ? 'متوفر' : 'غير متوفر',
                                        style: TextStyle(
                                          color: alt['availability'] ? Colors.green : Colors.red,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            }).toList(),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () => _addAlternativeToItem(alternative),
                                    icon: const Icon(Icons.add),
                                    label: const Text('إضافة بديل'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.blue,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () => _checkAvailability(alternative),
                                    icon: const Icon(Icons.refresh),
                                    label: const Text('تحديث التوفر'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.orange,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addAlternative,
        backgroundColor: Colors.cyan,
        icon: const Icon(Icons.add),
        label: const Text('إضافة بديل'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getTotalAlternatives() {
    int total = _alternatives.fold(0, (sum, alt) => sum + (alt['alternatives'] as List).length);
    return total.toString();
  }

  void _addAlternative() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إضافة مجموعة بدائل جديدة')),
    );
  }

  void _syncAlternatives() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم مزامنة البدائل مع المخزون'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _addAlternativeToItem(Map<String, dynamic> alternative) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('إضافة بديل جديد لـ ${alternative['mainItem']}')),
    );
  }

  void _checkAvailability(Map<String, dynamic> alternative) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تحديث توفر بدائل ${alternative['mainItem']}'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _handleAction(String action, Map<String, dynamic> alternative) {
    switch (action) {
      case 'edit':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تعديل بدائل ${alternative['mainItem']}')),
        );
        break;
      case 'toggle':
        setState(() {
          alternative['isActive'] = !alternative['isActive'];
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم ${alternative['isActive'] ? 'تفعيل' : 'تعطيل'} البدائل'),
          ),
        );
        break;
      case 'add_alternative':
        _addAlternativeToItem(alternative);
        break;
      case 'delete':
        _showDeleteConfirmation(alternative);
        break;
    }
  }

  void _showDeleteConfirmation(Map<String, dynamic> alternative) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف بدائل ${alternative['mainItem']}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _alternatives.removeWhere((a) => a['id'] == alternative['id']);
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف بدائل ${alternative['mainItem']}'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
