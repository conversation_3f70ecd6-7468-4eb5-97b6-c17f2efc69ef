import 'package:flutter/material.dart';

/// صفحة تقرير مراقبة الإيرادات
/// تعرض مراقبة ومتابعة الإيرادات مقابل الأهداف المحددة
class RevenueMonitoringReportPage extends StatefulWidget {
  const RevenueMonitoringReportPage({super.key});

  @override
  State<RevenueMonitoringReportPage> createState() => _RevenueMonitoringReportPageState();
}

class _RevenueMonitoringReportPageState extends State<RevenueMonitoringReportPage> {
  String _selectedPeriod = 'current_month';
  String _selectedCategory = 'all';

  // بيانات تجريبية لمراقبة الإيرادات
  final List<Map<String, dynamic>> _revenueData = [
    {
      'category': 'مبيعات المنتجات',
      'targetAmount': 200000.0,
      'actualAmount': 185000.0,
      'variance': -15000.0,
      'variancePercentage': -7.5,
      'achievement': 92.5,
      'lastMonthAmount': 175000.0,
      'growth': 5.7,
      'status': 'أقل من المستهدف',
      'details': [
        {'item': 'منتجات إلكترونية', 'target': 120000.0, 'actual': 115000.0},
        {'item': 'منتجات منزلية', 'target': 50000.0, 'actual': 45000.0},
        {'item': 'منتجات رياضية', 'target': 30000.0, 'actual': 25000.0},
      ],
    },
    {
      'category': 'الخدمات الاستشارية',
      'targetAmount': 80000.0,
      'actualAmount': 95000.0,
      'variance': 15000.0,
      'variancePercentage': 18.8,
      'achievement': 118.8,
      'lastMonthAmount': 85000.0,
      'growth': 11.8,
      'status': 'تجاوز المستهدف',
      'details': [
        {'item': 'استشارات تقنية', 'target': 50000.0, 'actual': 60000.0},
        {'item': 'استشارات إدارية', 'target': 20000.0, 'actual': 25000.0},
        {'item': 'استشارات مالية', 'target': 10000.0, 'actual': 10000.0},
      ],
    },
    {
      'category': 'خدمات الصيانة',
      'targetAmount': 45000.0,
      'actualAmount': 42000.0,
      'variance': -3000.0,
      'variancePercentage': -6.7,
      'achievement': 93.3,
      'lastMonthAmount': 40000.0,
      'growth': 5.0,
      'status': 'أقل من المستهدف',
      'details': [
        {'item': 'صيانة أجهزة', 'target': 25000.0, 'actual': 23000.0},
        {'item': 'صيانة برامج', 'target': 15000.0, 'actual': 14000.0},
        {'item': 'صيانة شبكات', 'target': 5000.0, 'actual': 5000.0},
      ],
    },
    {
      'category': 'التدريب والتطوير',
      'targetAmount': 35000.0,
      'actualAmount': 38000.0,
      'variance': 3000.0,
      'variancePercentage': 8.6,
      'achievement': 108.6,
      'lastMonthAmount': 32000.0,
      'growth': 18.8,
      'status': 'تجاوز المستهدف',
      'details': [
        {'item': 'دورات تقنية', 'target': 20000.0, 'actual': 22000.0},
        {'item': 'دورات إدارية', 'target': 10000.0, 'actual': 11000.0},
        {'item': 'ورش عمل', 'target': 5000.0, 'actual': 5000.0},
      ],
    },
  ];

  @override
  Widget build(BuildContext context) {
    final filteredData = _getFilteredData();
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير مراقبة الإيرادات'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedPeriod,
                        decoration: const InputDecoration(
                          labelText: 'الفترة',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'current_month', child: Text('الشهر الحالي')),
                          DropdownMenuItem(value: 'current_quarter', child: Text('الربع الحالي')),
                          DropdownMenuItem(value: 'current_year', child: Text('السنة الحالية')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedPeriod = value!;
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedCategory,
                        decoration: const InputDecoration(
                          labelText: 'فئة الإيرادات',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الفئات')),
                          DropdownMenuItem(value: 'مبيعات المنتجات', child: Text('مبيعات المنتجات')),
                          DropdownMenuItem(value: 'الخدمات الاستشارية', child: Text('الخدمات الاستشارية')),
                          DropdownMenuItem(value: 'خدمات الصيانة', child: Text('خدمات الصيانة')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedCategory = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // ملخص الإيرادات
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              color: Colors.green[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    const Text(
                      'ملخص مراقبة الإيرادات',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Column(
                          children: [
                            const Text('الهدف المحدد'),
                            Text(
                              '${_getTotalTarget(filteredData)} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('الإيرادات الفعلية'),
                            Text(
                              '${_getTotalActual(filteredData)} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('نسبة التحقيق'),
                            Text(
                              '${_getOverallAchievement(filteredData)}%',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: _getOverallAchievement(filteredData) >= 100 ? Colors.green : Colors.orange,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                _buildStatCard('فئات الإيرادات', filteredData.length.toString(), Colors.blue),
                _buildStatCard('تجاوز الهدف', _getAboveTargetCount(filteredData).toString(), Colors.green),
                _buildStatCard('أقل من الهدف', _getBelowTargetCount(filteredData).toString(), Colors.red),
                _buildStatCard('متوسط النمو', '${_getAverageGrowth(filteredData)}%', Colors.orange),
              ],
            ),
          ),

          // قائمة الإيرادات
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: filteredData.length,
              itemBuilder: (context, index) {
                final revenue = filteredData[index];
                
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: _getAchievementColor(revenue['achievement']),
                      child: Text(
                        '${revenue['achievement'].toStringAsFixed(0)}%',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        ),
                      ),
                    ),
                    title: Text(
                      revenue['category'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('الهدف: ${revenue['targetAmount'].toStringAsFixed(2)} ر.س'),
                        Text('الفعلي: ${revenue['actualAmount'].toStringAsFixed(2)} ر.س'),
                        Text('التحقيق: ${revenue['achievement'].toStringAsFixed(1)}%'),
                        const SizedBox(height: 4),
                        LinearProgressIndicator(
                          value: (revenue['achievement'] / 100).clamp(0.0, 1.0),
                          backgroundColor: Colors.grey[300],
                          valueColor: AlwaysStoppedAnimation<Color>(
                            _getAchievementColor(revenue['achievement']),
                          ),
                        ),
                        Text('الحالة: ${revenue['status']}'),
                      ],
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('الهدف المحدد', '${revenue['targetAmount'].toStringAsFixed(2)} ر.س', Icons.flag, Colors.blue),
                                ),
                                Expanded(
                                  child: _buildDetailCard('الإيرادات الفعلية', '${revenue['actualAmount'].toStringAsFixed(2)} ر.س', Icons.attach_money, Colors.green),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('الانحراف', '${revenue['variance'].toStringAsFixed(2)} ر.س', Icons.trending_up, revenue['variance'] >= 0 ? Colors.green : Colors.red),
                                ),
                                Expanded(
                                  child: _buildDetailCard('نسبة التحقيق', '${revenue['achievement'].toStringAsFixed(1)}%', Icons.percent, _getAchievementColor(revenue['achievement'])),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('الشهر الماضي', '${revenue['lastMonthAmount'].toStringAsFixed(2)} ر.س', Icons.history, Colors.grey),
                                ),
                                Expanded(
                                  child: _buildDetailCard('النمو', '${revenue['growth']}%', Icons.show_chart, revenue['growth'] >= 0 ? Colors.green : Colors.red),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            const Text(
                              'تفاصيل الإيرادات:',
                              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                            ),
                            const SizedBox(height: 8),
                            ...revenue['details'].map<Widget>((detail) {
                              double achievement = (detail['actual'] / detail['target']) * 100;
                              return ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: _getAchievementColor(achievement),
                                  radius: 15,
                                  child: Text(
                                    '${achievement.toStringAsFixed(0)}%',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                title: Text(detail['item']),
                                subtitle: Text('الهدف: ${detail['target'].toStringAsFixed(2)} ر.س'),
                                trailing: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Text(
                                      '${detail['actual'].toStringAsFixed(2)} ر.س',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: _getAchievementColor(achievement),
                                      ),
                                    ),
                                    Text(
                                      '${achievement.toStringAsFixed(1)}%',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: _getAchievementColor(achievement),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }).toList(),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshData,
        backgroundColor: Colors.green,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث البيانات'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Icon(icon, size: 20, color: color),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                color: color,
              ),
            ),
            Text(
              title,
              style: const TextStyle(fontSize: 10),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Color _getAchievementColor(double achievement) {
    if (achievement >= 100) return Colors.green;
    if (achievement >= 80) return Colors.orange;
    return Colors.red;
  }

  List<Map<String, dynamic>> _getFilteredData() {
    if (_selectedCategory == 'all') {
      return _revenueData;
    }
    return _revenueData.where((revenue) => revenue['category'] == _selectedCategory).toList();
  }

  double _getTotalTarget(List<Map<String, dynamic>> data) {
    return data.fold(0.0, (sum, revenue) => sum + revenue['targetAmount']);
  }

  double _getTotalActual(List<Map<String, dynamic>> data) {
    return data.fold(0.0, (sum, revenue) => sum + revenue['actualAmount']);
  }

  double _getOverallAchievement(List<Map<String, dynamic>> data) {
    double totalTarget = _getTotalTarget(data);
    double totalActual = _getTotalActual(data);
    if (totalTarget == 0) return 0.0;
    return (totalActual / totalTarget) * 100;
  }

  int _getAboveTargetCount(List<Map<String, dynamic>> data) {
    return data.where((revenue) => revenue['achievement'] >= 100).length;
  }

  int _getBelowTargetCount(List<Map<String, dynamic>> data) {
    return data.where((revenue) => revenue['achievement'] < 100).length;
  }

  String _getAverageGrowth(List<Map<String, dynamic>> data) {
    if (data.isEmpty) return '0.0';
    double total = data.fold(0.0, (sum, revenue) => sum + revenue['growth']);
    return (total / data.length).toStringAsFixed(1);
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة تقرير مراقبة الإيرادات')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير تقرير مراقبة الإيرادات')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث بيانات مراقبة الإيرادات'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
