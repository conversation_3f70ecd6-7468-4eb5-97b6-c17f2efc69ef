import 'package:flutter/material.dart';

class CategorizedReceiptsButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String? tooltip;
  final bool isLoading;
  final bool isDisabled;

  const CategorizedReceiptsButton({
    super.key,
    this.onPressed,
    this.tooltip = 'Categorized Receipts',
    this.isLoading = false,
    this.isDisabled = false,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip ?? 'Categorized Receipts',
      child: ElevatedButton.icon(
        onPressed: isDisabled || isLoading ? null : onPressed,
        icon: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              )
            : const Icon(Icons.category),
        label: const Text('Categorized Receipts'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.purple.shade800,
          foregroundColor: Colors.white,
        ),
      ),
    );
  }
}
