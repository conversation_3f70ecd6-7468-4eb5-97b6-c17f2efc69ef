import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة التقارير الإحصائية الرئيسية
/// تعرض ملخص الإحصائيات والتقارير المتاحة
class StatisticalReportsPage extends StatelessWidget {
  const StatisticalReportsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.statisticalReports),
        backgroundColor: Colors.amber.shade800,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقات الإحصائيات السريعة
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    title: 'نمو المبيعات',
                    value: '+15.5%',
                    icon: Icons.trending_up,
                    color: Colors.green,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    title: 'نمو المشتريات',
                    value: '+8.2%',
                    icon: Icons.trending_down,
                    color: Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    title: 'أفضل فرع',
                    value: 'الفرع الرئيسي',
                    icon: Icons.store,
                    color: Colors.blue,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    title: 'أفضل منتج',
                    value: 'منتج A',
                    icon: Icons.star,
                    color: Colors.purple,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            
            // قائمة التقارير الإحصائية
            Text(
              'التقارير الإحصائية',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  _buildReportCard(
                    title: localizations.salesDetails,
                    icon: Icons.bar_chart,
                    color: Colors.blue,
                    onTap: () => _navigateToReport(context, 'sales_details'),
                  ),
                  _buildReportCard(
                    title: localizations.purchasesDetails,
                    icon: Icons.shopping_cart,
                    color: Colors.orange,
                    onTap: () => _navigateToReport(context, 'purchases_details'),
                  ),
                  _buildReportCard(
                    title: localizations.monthlyBranchSalesMovement,
                    icon: Icons.trending_up,
                    color: Colors.green,
                    onTap: () => _navigateToReport(context, 'monthly_branch_sales'),
                  ),
                  _buildReportCard(
                    title: localizations.monthlyBranchPurchasesMovement,
                    icon: Icons.trending_down,
                    color: Colors.red,
                    onTap: () => _navigateToReport(context, 'monthly_branch_purchases'),
                  ),
                  _buildReportCard(
                    title: 'تحليل الأرباح',
                    icon: Icons.attach_money,
                    color: Colors.teal,
                    onTap: () => _navigateToReport(context, 'profit_analysis'),
                  ),
                  _buildReportCard(
                    title: 'تقرير الأداء',
                    icon: Icons.assessment,
                    color: Colors.indigo,
                    onTap: () => _navigateToReport(context, 'performance_report'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const Spacer(),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة تقرير
  Widget _buildReportCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 40,
                color: color,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// التنقل إلى تقرير معين
  void _navigateToReport(BuildContext context, String report) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('الانتقال إلى تقرير $report')),
    );
  }
}
