import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة طلب الأصناف خلال فترة
/// يعرض تقرير طلبات الأصناف خلال فترة محددة
class ItemRequestsDuringPeriodReportPage extends StatefulWidget {
  const ItemRequestsDuringPeriodReportPage({super.key});

  @override
  State<ItemRequestsDuringPeriodReportPage> createState() => _ItemRequestsDuringPeriodReportPageState();
}

class _ItemRequestsDuringPeriodReportPageState extends State<ItemRequestsDuringPeriodReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedDepartment;
  String? _requestStatus = 'all';
  String? _priority = 'all';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('طلب الأصناف خلال فترة'),
        backgroundColor: Colors.amber,
        foregroundColor: Colors.black,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.approval),
            onPressed: _bulkApproval,
            tooltip: 'موافقة مجمعة',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.amber[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.fromDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, true),
                        controller: TextEditingController(
                          text: _startDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.toDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, false),
                        controller: TextEditingController(
                          text: _endDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'القسم',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedDepartment,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الأقسام')),
                          DropdownMenuItem(value: 'sales', child: Text('قسم المبيعات')),
                          DropdownMenuItem(value: 'warehouse', child: Text('قسم المستودع')),
                          DropdownMenuItem(value: 'it', child: Text('قسم تقنية المعلومات')),
                          DropdownMenuItem(value: 'admin', child: Text('القسم الإداري')),
                        ],
                        onChanged: (value) => setState(() => _selectedDepartment = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'حالة الطلب',
                          border: OutlineInputBorder(),
                        ),
                        value: _requestStatus,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الحالات')),
                          DropdownMenuItem(value: 'pending', child: Text('قيد الانتظار')),
                          DropdownMenuItem(value: 'approved', child: Text('موافق عليه')),
                          DropdownMenuItem(value: 'rejected', child: Text('مرفوض')),
                          DropdownMenuItem(value: 'completed', child: Text('مكتمل')),
                        ],
                        onChanged: (value) => setState(() => _requestStatus = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'الأولوية',
                          border: OutlineInputBorder(),
                        ),
                        value: _priority,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الأولويات')),
                          DropdownMenuItem(value: 'urgent', child: Text('عاجل')),
                          DropdownMenuItem(value: 'high', child: Text('عالي')),
                          DropdownMenuItem(value: 'medium', child: Text('متوسط')),
                          DropdownMenuItem(value: 'low', child: Text('منخفض')),
                        ],
                        onChanged: (value) => setState(() => _priority = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _generateReport,
                        icon: const Icon(Icons.search),
                        label: Text(localizations.generateReport),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.amber,
                          foregroundColor: Colors.black,
                          minimumSize: const Size(double.infinity, 48),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص طلبات الأصناف
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.request_page, color: Colors.amber, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'ملخص طلبات الأصناف',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSummaryCard('إجمالي الطلبات', '485', Colors.blue, Icons.request_page),
                              _buildSummaryCard('قيد الانتظار', '125', Colors.orange, Icons.hourglass_empty),
                              _buildSummaryCard('موافق عليها', '285', Colors.green, Icons.check_circle),
                              _buildSummaryCard('مرفوضة', '75', Colors.red, Icons.cancel),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // الطلبات العاجلة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.priority_high, color: Colors.red, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'الطلبات العاجلة',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          ..._buildUrgentRequestsList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول تفاصيل الطلبات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل طلبات الأصناف',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('رقم الطلب')),
                                DataColumn(label: Text('تاريخ الطلب')),
                                DataColumn(label: Text('القسم')),
                                DataColumn(label: Text('الصنف المطلوب')),
                                DataColumn(label: Text('الكمية')),
                                DataColumn(label: Text('الأولوية')),
                                DataColumn(label: Text('الحالة')),
                                DataColumn(label: Text('مقدم الطلب')),
                                DataColumn(label: Text('إجراءات')),
                              ],
                              rows: _buildRequestRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إحصائيات الأقسام
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.pie_chart, color: Colors.indigo, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'إحصائيات الطلبات حسب القسم',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildDepartmentCard('المبيعات', '185 طلب', '38%', Colors.blue),
                              _buildDepartmentCard('المستودع', '125 طلب', '26%', Colors.green),
                              _buildDepartmentCard('تقنية المعلومات', '95 طلب', '20%', Colors.orange),
                              _buildDepartmentCard('الإداري', '80 طلب', '16%', Colors.purple),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // الأصناف الأكثر طلباً
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'الأصناف الأكثر طلباً',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ..._buildMostRequestedItemsList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _approveSelected,
                                  icon: const Icon(Icons.check),
                                  label: const Text('موافقة مجمعة'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _rejectSelected,
                                  icon: const Icon(Icons.close),
                                  label: const Text('رفض مجمع'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _createPurchaseOrder,
                                  icon: const Icon(Icons.shopping_cart),
                                  label: const Text('إنشاء أمر شراء'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _sendNotifications,
                                  icon: const Icon(Icons.notifications),
                                  label: const Text('إرسال تنبيهات'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildUrgentRequestsList() {
    final urgentRequests = [
      {'id': 'REQ-2024-001', 'item': 'لابتوب ديل XPS 13', 'department': 'تقنية المعلومات', 'quantity': '5'},
      {'id': 'REQ-2024-002', 'item': 'طابعة HP LaserJet', 'department': 'المبيعات', 'quantity': '2'},
      {'id': 'REQ-2024-003', 'item': 'هاتف آيفون 15', 'department': 'الإداري', 'quantity': '3'},
      {'id': 'REQ-2024-004', 'item': 'ساعة ذكية', 'department': 'المستودع', 'quantity': '1'},
    ];

    return urgentRequests.map((request) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.red.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.red.withOpacity(0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.priority_high, color: Colors.red, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${request['id']} - ${request['item']}',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${request['department']} • الكمية: ${request['quantity']}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          ElevatedButton(
            onPressed: () => _approveRequest(request['id']!),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              minimumSize: const Size(80, 32),
            ),
            child: const Text('موافقة'),
          ),
        ],
      ),
    )).toList();
  }

  List<Widget> _buildMostRequestedItemsList() {
    final items = [
      {'item': 'لابتوب ديل XPS 13', 'requests': '45', 'departments': '3 أقسام'},
      {'item': 'طابعة HP LaserJet', 'requests': '32', 'departments': '4 أقسام'},
      {'item': 'هاتف آيفون 15', 'requests': '28', 'departments': '2 أقسام'},
      {'item': 'ساعة ذكية', 'requests': '25', 'departments': '3 أقسام'},
    ];

    return items.map((item) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.amber.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.amber.withOpacity(0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.trending_up, color: Colors.amber, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item['item']!,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${item['requests']} طلب من ${item['departments']}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.amber.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${item['requests']} طلب',
              style: const TextStyle(
                color: Colors.amber,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildRequestRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('REQ-2024-001')),
        const DataCell(Text('2024-02-15')),
        const DataCell(Text('تقنية المعلومات')),
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('5')),
        DataCell(_buildPriorityBadge('عاجل', Colors.red)),
        DataCell(_buildStatusBadge('قيد الانتظار', Colors.orange)),
        const DataCell(Text('أحمد محمد')),
        DataCell(Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.check, color: Colors.green, size: 16),
              onPressed: () => _approveRequest('REQ-2024-001'),
            ),
            IconButton(
              icon: const Icon(Icons.close, color: Colors.red, size: 16),
              onPressed: () => _rejectRequest('REQ-2024-001'),
            ),
          ],
        )),
      ]),
      DataRow(cells: [
        const DataCell(Text('REQ-2024-002')),
        const DataCell(Text('2024-02-14')),
        const DataCell(Text('المبيعات')),
        const DataCell(Text('طابعة HP LaserJet')),
        const DataCell(Text('2')),
        DataCell(_buildPriorityBadge('عالي', Colors.orange)),
        DataCell(_buildStatusBadge('موافق عليه', Colors.green)),
        const DataCell(Text('فاطمة أحمد')),
        DataCell(Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.visibility, color: Colors.blue, size: 16),
              onPressed: () => _viewRequest('REQ-2024-002'),
            ),
          ],
        )),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDepartmentCard(String department, String requests, String percentage, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                requests,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                percentage,
                style: TextStyle(
                  fontSize: 12,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                department,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPriorityBadge(String priority, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        priority,
        style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        status,
        style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير طلبات الأصناف بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _bulkApproval() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('موافقة مجمعة على الطلبات')),
    );
  }

  void _approveSelected() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم الموافقة على الطلبات المحددة')),
    );
  }

  void _rejectSelected() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم رفض الطلبات المحددة')),
    );
  }

  void _createPurchaseOrder() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء أمر شراء للأصناف المطلوبة')),
    );
  }

  void _sendNotifications() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إرسال تنبيهات للأقسام المعنية')),
    );
  }

  void _approveRequest(String requestId) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم الموافقة على الطلب $requestId')),
    );
  }

  void _rejectRequest(String requestId) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم رفض الطلب $requestId')),
    );
  }

  void _viewRequest(String requestId) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض تفاصيل الطلب $requestId')),
    );
  }
}
