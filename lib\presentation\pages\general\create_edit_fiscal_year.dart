import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/localization/app_localizations.dart';
import '../../../data/fiscal_year_data.dart';
import '../../widgets/global_buttons/global_buttons.dart';
import '../../widgets/global_buttons/query_button.dart';

/// صفحة إنشاء وتعديل السنة المالية
/// تتيح للمستخدم إنشاء سنة مالية جديدة أو تعديل سنة مالية موجودة
class CreateEditFiscalYear extends StatefulWidget {
  const CreateEditFiscalYear({super.key});

  @override
  State<CreateEditFiscalYear> createState() => _CreateEditFiscalYearState();
}

class _CreateEditFiscalYearState extends State<CreateEditFiscalYear> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;

  // متحكمات النصوص
  final _companyNumberController = TextEditingController();
  final _fiscalYearNumberController = TextEditingController();
  final _arabicNameController = TextEditingController();
  final _englishNameController = TextEditingController();
  final _startDateController = TextEditingController();
  final _endDateController = TextEditingController();
  final _skipNumberController = TextEditingController();

  // متغيرات الاختيار
  String _continueSequence = 'نعم';

  // مرجع للبيانات المشتركة
  final FiscalYearData _fiscalYearData = FiscalYearData();

  @override
  void dispose() {
    _companyNumberController.dispose();
    _fiscalYearNumberController.dispose();
    _arabicNameController.dispose();
    _englishNameController.dispose();
    _startDateController.dispose();
    _endDateController.dispose();
    _skipNumberController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          localizations.createEditFiscalYear,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.green[700],
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Column(
        children: [
          // شريط الأزرار
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  // زر حفظ
                  GlobalButtons.saveButton(
                    onPressed: _saveFiscalYear,
                    tooltip: 'حفظ السنة المالية',
                    isLoading: _isLoading,
                  ),
                  const SizedBox(width: 16),

                  // زر استعلام
                  QueryButton(
                    onPressed: _showQueryDialog,
                    tooltip: 'استعلام عن السنوات المالية',
                  ),
                  const SizedBox(width: 16),

                  // زر إلغاء
                  GlobalButtons.cancelButton(
                    onPressed: () => Navigator.pop(context),
                    tooltip: 'إلغاء العملية',
                  ),
                ],
              ),
            ),
          ),

          // محتوى الصفحة
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // كرت بيانات السنة المالية
                  Expanded(
                    flex: 2,
                    child: _buildFormCard(),
                  ),
                  const SizedBox(height: 16),

                  // كرت الملاحظة
                  Expanded(
                    flex: 1,
                    child: _buildNoteCard(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء كرت النموذج
  Widget _buildFormCard() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.08),
            spreadRadius: 0,
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.05),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان الكرت المحسن
              Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.blue[600]!, Colors.blue[700]!],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withValues(alpha: 0.3),
                      spreadRadius: 0,
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.edit_calendar,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Text(
                      'بيانات السنة المالية',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),

              // محتوى النموذج
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // الصف الأول: رقم الشركة ورقم السنة المالية
                      Row(
                        children: [
                          Expanded(
                            child: _buildEnhancedTextField(
                              label: 'رقم الشركة',
                              controller: _companyNumberController,
                              hint: 'ادخل رقم الشركة',
                              icon: Icons.business_outlined,
                              isRequired: true,
                              isNumber: true,
                            ),
                          ),
                          const SizedBox(width: 24),
                          Expanded(
                            child: _buildEnhancedTextField(
                              label: 'رقم السنة المالية',
                              controller: _fiscalYearNumberController,
                              hint: 'ادخل رقم السنة المالية',
                              icon: Icons.numbers_outlined,
                              isRequired: true,
                              isNumber: true,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),

                      // الصف الثاني: الاسم العربي والاسم الإنجليزي
                      Row(
                        children: [
                          Expanded(
                            child: _buildEnhancedTextField(
                              label: 'الاسم العربي',
                              controller: _arabicNameController,
                              hint: 'ادخل اسم السنة المالية بالعربية',
                              icon: Icons.text_fields_outlined,
                              isRequired: true,
                            ),
                          ),
                          const SizedBox(width: 24),
                          Expanded(
                            child: _buildEnhancedTextField(
                              label: 'الاسم الإنجليزي',
                              controller: _englishNameController,
                              hint: 'ادخل اسم السنة المالية بالإنجليزية',
                              icon: Icons.translate_outlined,
                              isRequired: true,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),

                      // الصف الثالث: بداية السنة ونهاية السنة
                      Row(
                        children: [
                          Expanded(
                            child: _buildEnhancedDateField(
                              label: 'بداية السنة',
                              controller: _startDateController,
                              hint: 'YYYY-MM-DD',
                              icon: Icons.calendar_today_outlined,
                              isRequired: true,
                            ),
                          ),
                          const SizedBox(width: 24),
                          Expanded(
                            child: _buildEnhancedDateField(
                              label: 'نهاية السنة',
                              controller: _endDateController,
                              hint: 'YYYY-MM-DD',
                              icon: Icons.event_outlined,
                              isRequired: true,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),

                      // الصف الرابع: استمرار التسلسل ورقم التخطي
                      Row(
                        children: [
                          Expanded(
                            child: _buildEnhancedDropdownField(
                              label: 'استمرار الرقم التسلسلي',
                              value: _continueSequence,
                              items: ['نعم', 'لا'],
                              onChanged: (value) {
                                setState(() {
                                  _continueSequence = value!;
                                });
                              },
                              icon: Icons.format_list_numbered_outlined,
                            ),
                          ),
                          const SizedBox(width: 24),
                          Expanded(
                            child: AnimatedContainer(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                              child: _continueSequence == 'لا'
                                  ? _buildEnhancedTextField(
                                      label: 'رقم التخطي',
                                      controller: _skipNumberController,
                                      hint: 'ادخل رقم التخطي',
                                      icon: Icons.skip_next_outlined,
                                      isNumber: true,
                                      isRequired: true,
                                    )
                                  : SizedBox(
                                      height:
                                          80, // ارتفاع ثابت للحفاظ على التخطيط
                                      child: Center(
                                        child: Text(
                                          'يظهر عند اختيار "لا"',
                                          style: TextStyle(
                                            color: Colors.grey[400],
                                            fontSize: 14,
                                            fontStyle: FontStyle.italic,
                                          ),
                                        ),
                                      ),
                                    ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء كرت الملاحظة المحسن
  Widget _buildNoteCard() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withValues(alpha: 0.08),
            spreadRadius: 0,
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.05),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(28.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان الكرت المحسن
            Container(
              padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 18),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.orange[500]!, Colors.orange[600]!],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.orange.withValues(alpha: 0.3),
                    spreadRadius: 0,
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.info_outline,
                      color: Colors.white,
                      size: 22,
                    ),
                  ),
                  const SizedBox(width: 14),
                  Text(
                    'ملاحظة مهمة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      letterSpacing: 0.5,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // محتوى الملاحظة
            Expanded(
              child: SingleChildScrollView(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // تحديد حجم الخط والمسافات بناءً على عرض الشاشة
                    double screenWidth = MediaQuery.of(context).size.width;

                    // تحديد نقاط التوقف للشاشات المختلفة
                    bool isLargeScreen = screenWidth > 1200; // شاشات كبيرة
                    bool isMediumScreen = screenWidth > 768; // شاشات متوسطة
                    bool isSmallScreen = screenWidth > 480; // شاشات صغيرة
                    // أصغر من 480 = شاشات صغيرة جداً (هواتف)

                    // تحديد أحجام الخط
                    double titleFontSize = isLargeScreen
                        ? 18
                        : isMediumScreen
                            ? 16
                            : isSmallScreen
                                ? 14
                                : 12;
                    double bodyFontSize = isLargeScreen
                        ? 16
                        : isMediumScreen
                            ? 14
                            : isSmallScreen
                                ? 12
                                : 11;
                    double exampleFontSize = isLargeScreen
                        ? 14
                        : isMediumScreen
                            ? 13
                            : isSmallScreen
                                ? 11
                                : 10;

                    // تحديد المسافات والحشو
                    double containerPadding = isLargeScreen
                        ? 20
                        : isMediumScreen
                            ? 16
                            : isSmallScreen
                                ? 12
                                : 8;
                    double examplePadding = isLargeScreen
                        ? 16
                        : isMediumScreen
                            ? 12
                            : isSmallScreen
                                ? 8
                                : 6;

                    return Column(
                      children: [
                        // الإطار الأول: النص الرئيسي والتفاصيل
                        Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(containerPadding),
                          decoration: BoxDecoration(
                            color: Colors.orange.shade50,
                            borderRadius: BorderRadius.circular(isLargeScreen
                                ? 12
                                : isMediumScreen
                                    ? 10
                                    : isSmallScreen
                                        ? 8
                                        : 6),
                            border: Border.all(
                              color: Colors.orange.shade200,
                              width: isSmallScreen ? 1 : 0.8,
                            ),
                            boxShadow: isLargeScreen
                                ? [
                                    BoxShadow(
                                      color:
                                          Colors.orange.withValues(alpha: 0.08),
                                      blurRadius: 6,
                                      offset: const Offset(0, 2),
                                    ),
                                  ]
                                : null,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // النص الرئيسي
                              Text(
                                'في حال اختيار "لا" في خيار استمرار الرقم التسلسلي:',
                                style: TextStyle(
                                  fontSize: titleFontSize,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.orange.shade800,
                                ),
                              ),
                              SizedBox(
                                  height: isLargeScreen
                                      ? 16
                                      : isMediumScreen
                                          ? 12
                                          : isSmallScreen
                                              ? 8
                                              : 6),

                              // التفاصيل
                              Text(
                                'سوف يتم تخطي جميع المستندات بعد الرقم الذي يتم كتابته',
                                style: TextStyle(
                                  fontSize: bodyFontSize,
                                  color: Colors.orange.shade700,
                                  height: isSmallScreen ? 1.4 : 1.5,
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                            height: isLargeScreen
                                ? 16
                                : isMediumScreen
                                    ? 12
                                    : isSmallScreen
                                        ? 8
                                        : 6),

                        // الإطار الثاني: المثال
                        Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(examplePadding),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(isLargeScreen
                                ? 12
                                : isMediumScreen
                                    ? 10
                                    : isSmallScreen
                                        ? 8
                                        : 6),
                            border: Border.all(
                              color: Colors.orange.shade300,
                              width: isSmallScreen ? 1 : 0.8,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.orange.withValues(
                                    alpha: isLargeScreen
                                        ? 0.12
                                        : isMediumScreen
                                            ? 0.1
                                            : 0.08),
                                blurRadius: isLargeScreen
                                    ? 6
                                    : isMediumScreen
                                        ? 4
                                        : 3,
                                offset: Offset(0, isSmallScreen ? 2 : 1),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // عنوان المثال
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: isLargeScreen
                                      ? 10
                                      : isMediumScreen
                                          ? 8
                                          : isSmallScreen
                                              ? 6
                                              : 4,
                                  vertical: isLargeScreen
                                      ? 6
                                      : isMediumScreen
                                          ? 4
                                          : isSmallScreen
                                              ? 3
                                              : 2,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.orange.shade100,
                                  borderRadius: BorderRadius.circular(
                                      isSmallScreen ? 6 : 4),
                                ),
                                child: Text(
                                  'مثال توضيحي:',
                                  style: TextStyle(
                                    fontSize: bodyFontSize,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.orange.shade800,
                                  ),
                                ),
                              ),
                              SizedBox(
                                  height: isLargeScreen
                                      ? 16
                                      : isMediumScreen
                                          ? 12
                                          : isSmallScreen
                                              ? 8
                                              : 6),

                              // النقاط التوضيحية
                              ...[
                                'إذا كان رقم التخطي = 50',
                                'فاتورة المبيعات تبدأ بآخر تسلسل موجود',
                                'ثم يتم تخطي 50 رقم',
                                'ثم يكمل التسلسل العادي',
                                'وهكذا مع بقية المستندات'
                              ].map(
                                (text) => Padding(
                                  padding: EdgeInsets.only(
                                    bottom: isLargeScreen
                                        ? 8
                                        : isMediumScreen
                                            ? 6
                                            : isSmallScreen
                                                ? 4
                                                : 3,
                                  ),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        margin: EdgeInsets.only(
                                          top: isLargeScreen
                                              ? 8
                                              : isMediumScreen
                                                  ? 6
                                                  : isSmallScreen
                                                      ? 4
                                                      : 3,
                                          left: isSmallScreen ? 4 : 2,
                                        ),
                                        width: isLargeScreen
                                            ? 8
                                            : isMediumScreen
                                                ? 6
                                                : isSmallScreen
                                                    ? 5
                                                    : 4,
                                        height: isLargeScreen
                                            ? 8
                                            : isMediumScreen
                                                ? 6
                                                : isSmallScreen
                                                    ? 5
                                                    : 4,
                                        decoration: BoxDecoration(
                                          color: Colors.orange.shade600,
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                      SizedBox(
                                          width: isLargeScreen
                                              ? 12
                                              : isMediumScreen
                                                  ? 10
                                                  : isSmallScreen
                                                      ? 8
                                                      : 6),
                                      Expanded(
                                        child: Text(
                                          text,
                                          style: TextStyle(
                                            fontSize: exampleFontSize,
                                            color: Colors.orange.shade700,
                                            height: isSmallScreen ? 1.3 : 1.4,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض نافذة الاستعلام
  void _showQueryDialog() {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // تحديد أحجام النافذة بناءً على حجم الشاشة
    double dialogWidth = screenWidth > 1200
        ? screenWidth * 0.7
        : screenWidth > 768
            ? screenWidth * 0.85
            : screenWidth * 0.95;
    double dialogHeight = screenHeight > 800
        ? screenHeight * 0.8
        : screenHeight > 600
            ? screenHeight * 0.85
            : screenHeight * 0.9;

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            width: dialogWidth,
            height: dialogHeight,
            padding: EdgeInsets.all(screenWidth > 768 ? 24 : 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // عنوان النافذة
                Row(
                  children: [
                    Icon(
                      Icons.search,
                      color: Colors.blue[700],
                      size: screenWidth > 768 ? 28 : 24,
                    ),
                    SizedBox(width: screenWidth > 768 ? 12 : 8),
                    Expanded(
                      child: Text(
                        'استعلام السنوات المالية',
                        style: TextStyle(
                          fontSize: screenWidth > 768 ? 20 : 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue[700],
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: Icon(
                        Icons.close,
                        size: screenWidth > 768 ? 24 : 20,
                      ),
                      tooltip: 'إغلاق',
                    ),
                  ],
                ),
                SizedBox(height: screenWidth > 768 ? 20 : 16),

                // جدول البيانات
                Expanded(
                  child: _buildDataTable(),
                ),

                const SizedBox(height: 20),

                // أزرار التحكم
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('إغلاق'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء جدول البيانات
  Widget _buildDataTable() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 768;

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // رأس الجدول
          Container(
            padding: EdgeInsets.all(isSmallScreen ? 8 : 12),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: isSmallScreen ? _buildMobileHeader() : _buildDesktopHeader(),
          ),

          // محتوى الجدول
          Expanded(
            child: ListView.builder(
              itemCount: _fiscalYearData.fiscalYears.length,
              itemBuilder: (context, index) {
                final fiscalYear = _fiscalYearData.fiscalYears[index];
                return _buildDataRow(fiscalYear, index);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// بناء رأس الجدول للشاشات الكبيرة
  Widget _buildDesktopHeader() {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: Text(
            'رقم الشركة',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.blue[700],
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        Expanded(
          flex: 1,
          child: Text(
            'رقم السنة',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.blue[700],
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        Expanded(
          flex: 2,
          child: Text(
            'الاسم العربي',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.blue[700],
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        Expanded(
          flex: 2,
          child: Text(
            'الاسم الإنجليزي',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.blue[700],
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        Expanded(
          flex: 1,
          child: Text(
            'بداية السنة',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.blue[700],
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        Expanded(
          flex: 1,
          child: Text(
            'نهاية السنة',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.blue[700],
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  /// بناء رأس الجدول للشاشات الصغيرة
  Widget _buildMobileHeader() {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: Text(
            'رقم الشركة',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.blue[700],
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        Expanded(
          flex: 1,
          child: Text(
            'رقم السنة',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.blue[700],
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        Expanded(
          flex: 3,
          child: Text(
            'الاسم العربي',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.blue[700],
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        Expanded(
          flex: 2,
          child: Text(
            'السنة',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.blue[700],
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  /// بناء صف البيانات
  Widget _buildDataRow(Map<String, dynamic> fiscalYear, int index) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 768;
    final ValueNotifier<bool> isHovered = ValueNotifier<bool>(false);

    return ValueListenableBuilder<bool>(
      valueListenable: isHovered,
      builder: (context, hovered, child) {
        return MouseRegion(
          cursor: SystemMouseCursors.click,
          onEnter: (_) => isHovered.value = true,
          onExit: (_) => isHovered.value = false,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            decoration: BoxDecoration(
              color: hovered
                  ? Colors.blue.withValues(alpha: 0.1)
                  : (index % 2 == 0 ? Colors.white : Colors.grey[50]),
              border: Border(
                bottom: BorderSide(color: Colors.grey[200]!),
              ),
              boxShadow: hovered
                  ? [
                      BoxShadow(
                        color: Colors.blue.withValues(alpha: 0.2),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : null,
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => _selectFiscalYear(fiscalYear),
                onDoubleTap: () => _selectFiscalYear(fiscalYear),
                splashColor: Colors.blue.withValues(alpha: 0.3),
                highlightColor: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4),
                child: Padding(
                  padding: EdgeInsets.all(isSmallScreen ? 8 : 12),
                  child: Row(
                    children: [
                      // أيقونة صغيرة للإشارة إلى إمكانية النقر
                      AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        width: 4,
                        height: isSmallScreen ? 20 : 24,
                        decoration: BoxDecoration(
                          color: hovered ? Colors.blue[600] : Colors.blue[300],
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                      SizedBox(width: isSmallScreen ? 8 : 12),

                      // محتوى الصف
                      Expanded(
                        child: isSmallScreen
                            ? _buildMobileDataRow(fiscalYear)
                            : _buildDesktopDataRow(fiscalYear),
                      ),

                      // أيقونة السهم للإشارة إلى إمكانية التحديد
                      AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        child: Icon(
                          Icons.arrow_forward_ios,
                          size: isSmallScreen ? 14 : 16,
                          color: hovered ? Colors.blue[600] : Colors.grey[400],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء صف البيانات للشاشات الكبيرة
  Widget _buildDesktopDataRow(Map<String, dynamic> fiscalYear) {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: Text(
            fiscalYear['companyNumber'],
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 14),
          ),
        ),
        Expanded(
          flex: 1,
          child: Text(
            fiscalYear['fiscalYearNumber'],
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 14),
          ),
        ),
        Expanded(
          flex: 2,
          child: Text(
            fiscalYear['arabicName'],
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 14),
          ),
        ),
        Expanded(
          flex: 2,
          child: Text(
            fiscalYear['englishName'],
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 14),
          ),
        ),
        Expanded(
          flex: 1,
          child: Text(
            fiscalYear['startDate'],
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 14),
          ),
        ),
        Expanded(
          flex: 1,
          child: Text(
            fiscalYear['endDate'],
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 14),
          ),
        ),
      ],
    );
  }

  /// بناء صف البيانات للشاشات الصغيرة
  Widget _buildMobileDataRow(Map<String, dynamic> fiscalYear) {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: Text(
            fiscalYear['companyNumber'],
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 12),
          ),
        ),
        Expanded(
          flex: 1,
          child: Text(
            fiscalYear['fiscalYearNumber'],
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 12),
          ),
        ),
        Expanded(
          flex: 3,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                fiscalYear['arabicName'],
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                fiscalYear['englishName'],
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        Expanded(
          flex: 2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                fiscalYear['startDate'],
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 11),
              ),
              const SizedBox(height: 2),
              Text(
                fiscalYear['endDate'],
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// اختيار سنة مالية من الجدول
  void _selectFiscalYear(Map<String, dynamic> fiscalYear) {
    setState(() {
      _companyNumberController.text = fiscalYear['companyNumber'];
      _fiscalYearNumberController.text = fiscalYear['fiscalYearNumber'];
      _arabicNameController.text = fiscalYear['arabicName'];
      _englishNameController.text = fiscalYear['englishName'];
      _startDateController.text = fiscalYear['startDate'];
      _endDateController.text = fiscalYear['endDate'];
      _continueSequence = fiscalYear['continueSequence'];
      _skipNumberController.text = fiscalYear['skipNumber'];
    });

    // إغلاق النافذة
    Navigator.pop(context);

    // إظهار رسالة تأكيد
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تحميل بيانات: ${fiscalYear['arabicName']}'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// حفظ السنة المالية
  void _saveFiscalYear() {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      // محاكاة عملية الحفظ
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حفظ السنة المالية بنجاح'),
              backgroundColor: Colors.green,
            ),
          );

          // العودة للصفحة السابقة
          Navigator.pop(context);
        }
      });
    }
  }

  /// بناء حقل نصي
  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    required String hint,
    required IconData icon,
    bool isRequired = false,
    bool isNumber = false,
    bool enabled = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label + (isRequired ? ' *' : ''),
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.green[700],
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          enabled: enabled,
          keyboardType: isNumber ? TextInputType.number : TextInputType.text,
          validator: isRequired
              ? (value) {
                  if (value == null || value.isEmpty) {
                    return 'هذا الحقل مطلوب';
                  }
                  return null;
                }
              : null,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: Icon(icon, color: Colors.green[600]),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.green[600]!, width: 2),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
        ),
      ],
    );
  }

  /// بناء حقل التاريخ
  Widget _buildDateField({
    required String label,
    required TextEditingController controller,
    required String hint,
    required IconData icon,
    bool isRequired = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label + (isRequired ? ' *' : ''),
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.green[700],
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          readOnly: true,
          validator: isRequired
              ? (value) {
                  if (value == null || value.isEmpty) {
                    return 'هذا الحقل مطلوب';
                  }
                  return null;
                }
              : null,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: Icon(icon, color: Colors.green[600]),
            suffixIcon: IconButton(
              icon: Icon(Icons.calendar_today, color: Colors.green[600]),
              onPressed: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime(2100),
                );
                if (date != null) {
                  controller.text =
                      '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
                }
              },
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.green[600]!, width: 2),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
        ),
      ],
    );
  }

  /// بناء حقل القائمة المنسدلة
  Widget _buildDropdownField({
    required String label,
    required String value,
    required List<String> items,
    required Function(String?) onChanged,
    required IconData icon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.green[700],
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: value,
          items: items.map((String item) {
            return DropdownMenuItem<String>(
              value: item,
              child: Text(item),
            );
          }).toList(),
          onChanged: onChanged,
          decoration: InputDecoration(
            prefixIcon: Icon(icon, color: Colors.green[600]),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.green[600]!, width: 2),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
        ),
      ],
    );
  }

  /// بناء حقل نصي محسن
  Widget _buildEnhancedTextField({
    required String label,
    required TextEditingController controller,
    required String hint,
    required IconData icon,
    bool isRequired = false,
    bool isNumber = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // تسمية الحقل المحسنة
        Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  size: 20,
                  color: Colors.blue[600],
                ),
              ),
              const SizedBox(width: 12),
              Text(
                label,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                  letterSpacing: 0.3,
                ),
              ),
              if (isRequired)
                Container(
                  margin: const EdgeInsets.only(right: 4),
                  child: Text(
                    '*',
                    style: TextStyle(
                      color: Colors.red[500],
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
        ),

        // حقل الإدخال المحسن
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                spreadRadius: 0,
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextFormField(
            controller: controller,
            keyboardType: isNumber ? TextInputType.number : TextInputType.text,
            inputFormatters:
                isNumber ? [FilteringTextInputFormatter.digitsOnly] : null,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[800],
              fontWeight: FontWeight.w500,
            ),
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: TextStyle(
                color: Colors.grey[400],
                fontSize: 15,
                fontWeight: FontWeight.w400,
              ),
              filled: true,
              fillColor: Colors.white,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[200]!, width: 1.5),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[200]!, width: 1.5),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.blue[500]!, width: 2.5),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.red[400]!, width: 2),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.red[500]!, width: 2.5),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 16,
              ),
              prefixIcon: Container(
                margin: const EdgeInsets.all(8),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  size: 20,
                  color: Colors.blue[600],
                ),
              ),
            ),
            validator: isRequired
                ? (value) {
                    if (value == null || value.isEmpty) {
                      return 'هذا الحقل مطلوب';
                    }
                    return null;
                  }
                : null,
          ),
        ),
      ],
    );
  }

  /// بناء حقل التاريخ المحسن
  Widget _buildEnhancedDateField({
    required String label,
    required TextEditingController controller,
    required String hint,
    required IconData icon,
    bool isRequired = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // تسمية الحقل المحسنة
        Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  size: 20,
                  color: Colors.blue[600],
                ),
              ),
              const SizedBox(width: 12),
              Text(
                label,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                  letterSpacing: 0.3,
                ),
              ),
              if (isRequired)
                Container(
                  margin: const EdgeInsets.only(right: 4),
                  child: Text(
                    '*',
                    style: TextStyle(
                      color: Colors.red[500],
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
        ),

        // حقل التاريخ المحسن
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                spreadRadius: 0,
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextFormField(
            controller: controller,
            readOnly: true,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[800],
              fontWeight: FontWeight.w500,
            ),
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: TextStyle(
                color: Colors.grey[400],
                fontSize: 15,
                fontWeight: FontWeight.w400,
              ),
              filled: true,
              fillColor: Colors.white,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[200]!, width: 1.5),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[200]!, width: 1.5),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.blue[500]!, width: 2.5),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 16,
              ),
              prefixIcon: Container(
                margin: const EdgeInsets.all(8),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  size: 20,
                  color: Colors.blue[600],
                ),
              ),
              suffixIcon: Container(
                margin: const EdgeInsets.all(8),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.calendar_month,
                  size: 20,
                  color: Colors.blue[600],
                ),
              ),
            ),
            onTap: () async {
              DateTime? pickedDate = await showDatePicker(
                context: context,
                initialDate: DateTime.now(),
                firstDate: DateTime(2000),
                lastDate: DateTime(2100),
                builder: (context, child) {
                  return Theme(
                    data: Theme.of(context).copyWith(
                      colorScheme: ColorScheme.light(
                        primary: Colors.blue[600]!,
                        onPrimary: Colors.white,
                        surface: Colors.white,
                        onSurface: Colors.grey[800]!,
                      ),
                    ),
                    child: child!,
                  );
                },
              );
              if (pickedDate != null) {
                controller.text =
                    "${pickedDate.year}-${pickedDate.month.toString().padLeft(2, '0')}-${pickedDate.day.toString().padLeft(2, '0')}";
              }
            },
            validator: isRequired
                ? (value) {
                    if (value == null || value.isEmpty) {
                      return 'هذا الحقل مطلوب';
                    }
                    return null;
                  }
                : null,
          ),
        ),
      ],
    );
  }

  /// بناء حقل القائمة المنسدلة المحسن
  Widget _buildEnhancedDropdownField({
    required String label,
    required String value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
    required IconData icon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // تسمية الحقل المحسنة
        Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  size: 20,
                  color: Colors.blue[600],
                ),
              ),
              const SizedBox(width: 12),
              Text(
                label,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                  letterSpacing: 0.3,
                ),
              ),
            ],
          ),
        ),

        // القائمة المنسدلة المحسنة
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                spreadRadius: 0,
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: DropdownButtonFormField<String>(
            value: value,
            items: items.map((String item) {
              return DropdownMenuItem<String>(
                value: item,
                child: Text(
                  item,
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[800],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              );
            }).toList(),
            onChanged: onChanged,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[800],
              fontWeight: FontWeight.w500,
            ),
            decoration: InputDecoration(
              filled: true,
              fillColor: Colors.white,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[200]!, width: 1.5),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[200]!, width: 1.5),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.blue[500]!, width: 2.5),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 16,
              ),
              prefixIcon: Container(
                margin: const EdgeInsets.all(8),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  size: 20,
                  color: Colors.blue[600],
                ),
              ),
            ),
            dropdownColor: Colors.white,
            elevation: 8,
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ],
    );
  }
}
