import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير تاريخ صلاحية الأصناف
/// يعرض الأصناف مع تواريخ انتهاء صلاحيتها
class ItemsExpiryDatesReportPage extends StatefulWidget {
  const ItemsExpiryDatesReportPage({super.key});

  @override
  State<ItemsExpiryDatesReportPage> createState() => _ItemsExpiryDatesReportPageState();
}

class _ItemsExpiryDatesReportPageState extends State<ItemsExpiryDatesReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedCategory;
  String? _expiryStatus = 'all';
  String? _sortBy = 'expiry_date';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('تاريخ صلاحية الأصناف'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.warning),
            onPressed: _showExpiryAlerts,
            tooltip: 'تنبيهات انتهاء الصلاحية',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          _buildFilterSection(localizations),
          
          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildExpiryStatusSection(),
                  const SizedBox(height: 16),
                  _buildItemsTableSection(),
                  const SizedBox(height: 16),
                  _buildCriticalItemsSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.deepPurple[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.fromDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, true),
                  controller: TextEditingController(
                    text: _startDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.toDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, false),
                  controller: TextEditingController(
                    text: _endDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: localizations.category,
                    border: const OutlineInputBorder(),
                  ),
                  value: _selectedCategory,
                  items: [
                    DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                    DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                    DropdownMenuItem(value: 'medicine', child: const Text('أدوية')),
                    DropdownMenuItem(value: 'cosmetics', child: const Text('مستحضرات تجميل')),
                    DropdownMenuItem(value: 'chemicals', child: const Text('مواد كيميائية')),
                  ],
                  onChanged: (value) => setState(() => _selectedCategory = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'حالة الصلاحية',
                    border: OutlineInputBorder(),
                  ),
                  value: _expiryStatus,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الحالات')),
                    DropdownMenuItem(value: 'expired', child: Text('منتهية الصلاحية')),
                    DropdownMenuItem(value: 'expiring_soon', child: Text('تنتهي قريباً')),
                    DropdownMenuItem(value: 'valid', child: Text('صالحة')),
                    DropdownMenuItem(value: 'critical', child: Text('حرجة')),
                  ],
                  onChanged: (value) => setState(() => _expiryStatus = value),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'ترتيب حسب',
                    border: OutlineInputBorder(),
                  ),
                  value: _sortBy,
                  items: const [
                    DropdownMenuItem(value: 'expiry_date', child: Text('تاريخ انتهاء الصلاحية')),
                    DropdownMenuItem(value: 'item_name', child: Text('اسم الصنف')),
                    DropdownMenuItem(value: 'quantity', child: Text('الكمية')),
                    DropdownMenuItem(value: 'value', child: Text('القيمة')),
                  ],
                  onChanged: (value) => setState(() => _sortBy = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.analytics),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.deepPurple,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.schedule, color: Colors.deepPurple, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص صلاحية الأصناف',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي الأصناف', '1,285', Colors.deepPurple, Icons.inventory),
                _buildSummaryCard('منتهية الصلاحية', '45', Colors.red, Icons.dangerous),
                _buildSummaryCard('تنتهي خلال شهر', '125', Colors.orange, Icons.warning),
                _buildSummaryCard('قيمة المنتهية', '85,000 ر.س', Colors.blue, Icons.monetization_on),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExpiryStatusSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.pie_chart, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'توزيع حالات الصلاحية',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildStatusCard('صالحة', '985 صنف', Colors.green),
                _buildStatusCard('تنتهي قريباً', '125 صنف', Colors.orange),
                _buildStatusCard('منتهية', '45 صنف', Colors.red),
                _buildStatusCard('حرجة', '130 صنف', Colors.purple),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل صلاحية الأصناف',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('اسم الصنف')),
                  DataColumn(label: Text('الفئة')),
                  DataColumn(label: Text('تاريخ الإنتاج')),
                  DataColumn(label: Text('تاريخ انتهاء الصلاحية')),
                  DataColumn(label: Text('الأيام المتبقية')),
                  DataColumn(label: Text('الكمية')),
                  DataColumn(label: Text('القيمة')),
                  DataColumn(label: Text('الحالة')),
                  DataColumn(label: Text('إجراءات')),
                ],
                rows: _buildItemsRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCriticalItemsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.priority_high, color: Colors.red, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'الأصناف الحرجة (تنتهي خلال أسبوع)',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildCriticalItemsList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _createDiscountCampaign,
                    icon: const Icon(Icons.local_offer),
                    label: const Text('حملة تخفيضات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _disposeExpiredItems,
                    icon: const Icon(Icons.delete),
                    label: const Text('التخلص من المنتهية'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _setExpiryAlerts,
                    icon: const Icon(Icons.notifications),
                    label: const Text('تعيين تنبيهات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _generateExpiryReport,
                    icon: const Icon(Icons.report),
                    label: const Text('تقرير شامل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildCriticalItemsList() {
    final criticalItems = [
      {'name': 'دواء الضغط - أملوديبين', 'expiry': '3 أيام', 'quantity': '25 علبة', 'value': '1,250 ر.س'},
      {'name': 'كريم مرطب للوجه', 'expiry': '5 أيام', 'quantity': '15 قطعة', 'value': '450 ر.س'},
      {'name': 'عصير برتقال طبيعي', 'expiry': '2 أيام', 'quantity': '50 زجاجة', 'value': '200 ر.س'},
      {'name': 'مضاد حيوي - أموكسيسيلين', 'expiry': '7 أيام', 'quantity': '30 علبة', 'value': '900 ر.س'},
    ];

    return criticalItems.map((item) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.red.withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.priority_high, color: Colors.red, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item['name']!,
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  'ينتهي خلال: ${item['expiry']} • ${item['quantity']} • ${item['value']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Text(
              'حرج',
              style: TextStyle(
                color: Colors.red,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildItemsRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('دواء الضغط - أملوديبين')),
        const DataCell(Text('أدوية')),
        const DataCell(Text('2023-06-15')),
        const DataCell(Text('2024-06-15')),
        const DataCell(Text('3 أيام')),
        const DataCell(Text('25')),
        const DataCell(Text('1,250 ر.س')),
        DataCell(_buildStatusBadge('حرج', Colors.red)),
        DataCell(_buildActionButton('تخفيض', Colors.orange)),
      ]),
      DataRow(cells: [
        const DataCell(Text('كريم مرطب للوجه')),
        const DataCell(Text('مستحضرات تجميل')),
        const DataCell(Text('2023-01-20')),
        const DataCell(Text('2024-01-20')),
        const DataCell(Text('5 أيام')),
        const DataCell(Text('15')),
        const DataCell(Text('450 ر.س')),
        DataCell(_buildStatusBadge('حرج', Colors.red)),
        DataCell(_buildActionButton('تخفيض', Colors.orange)),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusCard(String status, String count, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(status, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(status, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Widget _buildActionButton(String label, Color color) {
    return ElevatedButton(
      onPressed: () => _performAction(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        minimumSize: const Size(80, 32),
      ),
      child: Text(label, style: const TextStyle(fontSize: 10)),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير صلاحية الأصناف بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showExpiryAlerts() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تنبيهات انتهاء الصلاحية')),
    );
  }

  void _createDiscountCampaign() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء حملة تخفيضات للأصناف قريبة الانتهاء')),
    );
  }

  void _disposeExpiredItems() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('التخلص من الأصناف منتهية الصلاحية')),
    );
  }

  void _setExpiryAlerts() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تعيين تنبيهات انتهاء الصلاحية')),
    );
  }

  void _generateExpiryReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء تقرير شامل لانتهاء الصلاحية')),
    );
  }

  void _performAction(String action) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تنفيذ إجراء: $action')),
    );
  }
}
