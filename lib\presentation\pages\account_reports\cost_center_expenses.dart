import 'package:flutter/material.dart';

/// صفحة تقرير نفقات مراكز التكلفة
/// تعرض نفقات ومصروفات مراكز التكلفة المختلفة
class CostCenterExpensesPage extends StatefulWidget {
  const CostCenterExpensesPage({super.key});

  @override
  State<CostCenterExpensesPage> createState() => _CostCenterExpensesPageState();
}

class _CostCenterExpensesPageState extends State<CostCenterExpensesPage> {
  String _selectedCostCenter = 'all';
  String _selectedPeriod = 'current_month';

  // بيانات تجريبية لنفقات مراكز التكلفة
  final List<Map<String, dynamic>> _costCenterExpenses = [
    {
      'costCenterCode': 'CC001',
      'costCenterName': 'قسم المبيعات',
      'manager': 'أحمد محمد',
      'totalExpenses': 85000.0,
      'budgetAmount': 90000.0,
      'variance': -5000.0,
      'variancePercentage': -5.6,
      'lastMonthExpenses': 78000.0,
      'growth': 9.0,
      'expenseCategories': [
        {'category': 'الرواتب والأجور', 'amount': 45000.0, 'percentage': 52.9},
        {'category': 'عمولات المبيعات', 'amount': 20000.0, 'percentage': 23.5},
        {'category': 'مصروفات التسويق', 'amount': 12000.0, 'percentage': 14.1},
        {'category': 'مصروفات أخرى', 'amount': 8000.0, 'percentage': 9.4},
      ],
    },
    {
      'costCenterCode': 'CC002',
      'costCenterName': 'قسم الإنتاج',
      'manager': 'فاطمة أحمد',
      'totalExpenses': 125000.0,
      'budgetAmount': 120000.0,
      'variance': 5000.0,
      'variancePercentage': 4.2,
      'lastMonthExpenses': 118000.0,
      'growth': 5.9,
      'expenseCategories': [
        {'category': 'المواد الخام', 'amount': 65000.0, 'percentage': 52.0},
        {'category': 'أجور العمال', 'amount': 35000.0, 'percentage': 28.0},
        {'category': 'صيانة المعدات', 'amount': 15000.0, 'percentage': 12.0},
        {'category': 'مصروفات أخرى', 'amount': 10000.0, 'percentage': 8.0},
      ],
    },
    {
      'costCenterCode': 'CC003',
      'costCenterName': 'قسم الإدارة',
      'manager': 'محمد علي',
      'totalExpenses': 65000.0,
      'budgetAmount': 70000.0,
      'variance': -5000.0,
      'variancePercentage': -7.1,
      'lastMonthExpenses': 62000.0,
      'growth': 4.8,
      'expenseCategories': [
        {'category': 'رواتب الإدارة', 'amount': 40000.0, 'percentage': 61.5},
        {'category': 'مصروفات مكتبية', 'amount': 10000.0, 'percentage': 15.4},
        {'category': 'اتصالات وإنترنت', 'amount': 8000.0, 'percentage': 12.3},
        {'category': 'مصروفات أخرى', 'amount': 7000.0, 'percentage': 10.8},
      ],
    },
    {
      'costCenterCode': 'CC004',
      'costCenterName': 'قسم تقنية المعلومات',
      'manager': 'سارة سعد',
      'totalExpenses': 45000.0,
      'budgetAmount': 50000.0,
      'variance': -5000.0,
      'variancePercentage': -10.0,
      'lastMonthExpenses': 42000.0,
      'growth': 7.1,
      'expenseCategories': [
        {'category': 'رواتب المطورين', 'amount': 25000.0, 'percentage': 55.6},
        {'category': 'تراخيص البرامج', 'amount': 10000.0, 'percentage': 22.2},
        {'category': 'أجهزة ومعدات', 'amount': 7000.0, 'percentage': 15.6},
        {'category': 'مصروفات أخرى', 'amount': 3000.0, 'percentage': 6.7},
      ],
    },
  ];

  @override
  Widget build(BuildContext context) {
    final filteredData = _getFilteredData();

    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير نفقات مراكز التكلفة'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedCostCenter,
                        decoration: const InputDecoration(
                          labelText: 'مركز التكلفة',
                          border: OutlineInputBorder(),
                        ),
                        items: [
                          const DropdownMenuItem(
                              value: 'all', child: Text('جميع المراكز')),
                          ..._costCenterExpenses.map((center) {
                            return DropdownMenuItem<String>(
                              value: center['costCenterCode'],
                              child: Text(center['costCenterName']),
                            );
                          }),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedCostCenter = value!;
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedPeriod,
                        decoration: const InputDecoration(
                          labelText: 'الفترة',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(
                              value: 'current_month',
                              child: Text('الشهر الحالي')),
                          DropdownMenuItem(
                              value: 'current_quarter',
                              child: Text('الربع الحالي')),
                          DropdownMenuItem(
                              value: 'current_year',
                              child: Text('السنة الحالية')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedPeriod = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // ملخص نفقات مراكز التكلفة
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              color: Colors.teal[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    const Text(
                      'ملخص نفقات مراكز التكلفة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Column(
                          children: [
                            const Text('إجمالي النفقات'),
                            Text(
                              '${_getTotalExpenses(filteredData)} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.red,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('إجمالي الميزانية'),
                            Text(
                              '${_getTotalBudget(filteredData)} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('الانحراف'),
                            Text(
                              '${_getTotalVariance(filteredData)} ر.س',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: _getTotalVariance(filteredData) >= 0
                                    ? Colors.red
                                    : Colors.green,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                _buildStatCard('مراكز التكلفة', filteredData.length.toString(),
                    Colors.blue),
                _buildStatCard(
                    'ضمن الميزانية',
                    _getWithinBudgetCount(filteredData).toString(),
                    Colors.green),
                _buildStatCard('تجاوز الميزانية',
                    _getOverBudgetCount(filteredData).toString(), Colors.red),
                _buildStatCard('متوسط النمو',
                    '${_getAverageGrowth(filteredData)}%', Colors.orange),
              ],
            ),
          ),

          // قائمة مراكز التكلفة
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: filteredData.length,
              itemBuilder: (context, index) {
                final costCenter = filteredData[index];

                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor:
                          _getVarianceColor(costCenter['variance']),
                      child: Text(
                        costCenter['costCenterCode'].substring(2),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    title: Text(
                      '${costCenter['costCenterCode']} - ${costCenter['costCenterName']}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('المدير: ${costCenter['manager']}'),
                        Text(
                            'النفقات: ${costCenter['totalExpenses'].toStringAsFixed(2)} ر.س'),
                        Text(
                            'الميزانية: ${costCenter['budgetAmount'].toStringAsFixed(2)} ر.س'),
                        Text(
                            'الانحراف: ${costCenter['variance'].toStringAsFixed(2)} ر.س (${costCenter['variancePercentage']}%)'),
                        const SizedBox(height: 4),
                        LinearProgressIndicator(
                          value: (costCenter['totalExpenses'] /
                                  costCenter['budgetAmount'])
                              .clamp(0.0, 1.0),
                          backgroundColor: Colors.grey[300],
                          valueColor: AlwaysStoppedAnimation<Color>(
                            _getVarianceColor(costCenter['variance']),
                          ),
                        ),
                      ],
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard(
                                      'إجمالي النفقات',
                                      '${costCenter['totalExpenses'].toStringAsFixed(2)} ر.س',
                                      Icons.money_off,
                                      Colors.red),
                                ),
                                Expanded(
                                  child: _buildDetailCard(
                                      'الميزانية',
                                      '${costCenter['budgetAmount'].toStringAsFixed(2)} ر.س',
                                      Icons.account_balance,
                                      Colors.blue),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard(
                                      'الشهر الماضي',
                                      '${costCenter['lastMonthExpenses'].toStringAsFixed(2)} ر.س',
                                      Icons.history,
                                      Colors.grey),
                                ),
                                Expanded(
                                  child: _buildDetailCard(
                                      'النمو',
                                      '${costCenter['growth']}%',
                                      Icons.trending_up,
                                      costCenter['growth'] >= 0
                                          ? Colors.green
                                          : Colors.red),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            const Text(
                              'تفاصيل النفقات:',
                              style: TextStyle(
                                  fontWeight: FontWeight.bold, fontSize: 16),
                            ),
                            const SizedBox(height: 8),
                            ...costCenter['expenseCategories']
                                .map<Widget>((category) {
                              return ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: Colors.teal,
                                  radius: 15,
                                  child: Text(
                                    '${category['percentage'].toStringAsFixed(0)}%',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                title: Text(category['category']),
                                trailing: Text(
                                  '${category['amount'].toStringAsFixed(2)} ر.س',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                subtitle: LinearProgressIndicator(
                                  value: category['percentage'] / 100,
                                  backgroundColor: Colors.grey[300],
                                  valueColor:
                                      const AlwaysStoppedAnimation<Color>(
                                          Colors.teal),
                                ),
                              );
                            }).toList(),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshData,
        backgroundColor: Colors.teal,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث البيانات'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailCard(
      String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Icon(icon, size: 20, color: color),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                color: color,
              ),
            ),
            Text(
              title,
              style: const TextStyle(fontSize: 10),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Color _getVarianceColor(double variance) {
    if (variance > 0) return Colors.red;
    if (variance < 0) return Colors.green;
    return Colors.grey;
  }

  List<Map<String, dynamic>> _getFilteredData() {
    if (_selectedCostCenter == 'all') {
      return _costCenterExpenses;
    }
    return _costCenterExpenses
        .where((center) => center['costCenterCode'] == _selectedCostCenter)
        .toList();
  }

  double _getTotalExpenses(List<Map<String, dynamic>> data) {
    return data.fold(0.0, (sum, center) => sum + center['totalExpenses']);
  }

  double _getTotalBudget(List<Map<String, dynamic>> data) {
    return data.fold(0.0, (sum, center) => sum + center['budgetAmount']);
  }

  double _getTotalVariance(List<Map<String, dynamic>> data) {
    return data.fold(0.0, (sum, center) => sum + center['variance']);
  }

  int _getWithinBudgetCount(List<Map<String, dynamic>> data) {
    return data.where((center) => center['variance'] <= 0).length;
  }

  int _getOverBudgetCount(List<Map<String, dynamic>> data) {
    return data.where((center) => center['variance'] > 0).length;
  }

  String _getAverageGrowth(List<Map<String, dynamic>> data) {
    if (data.isEmpty) return '0.0';
    double total = data.fold(0.0, (sum, center) => sum + center['growth']);
    return (total / data.length).toStringAsFixed(1);
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة تقرير نفقات مراكز التكلفة')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير تقرير نفقات مراكز التكلفة')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث بيانات نفقات مراكز التكلفة'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
