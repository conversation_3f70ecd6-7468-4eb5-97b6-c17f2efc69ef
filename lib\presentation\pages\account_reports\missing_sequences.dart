import 'package:flutter/material.dart';

/// صفحة التسلسلات المفقودة
/// تعرض التسلسلات المفقودة في الفواتير والمستندات
class MissingSequencesPage extends StatefulWidget {
  const MissingSequencesPage({super.key});

  @override
  State<MissingSequencesPage> createState() => _MissingSequencesPageState();
}

class _MissingSequencesPageState extends State<MissingSequencesPage> {
  String _selectedDocumentType = 'invoices';
  String _selectedPeriod = 'current_month';

  // بيانات تجريبية للتسلسلات المفقودة
  final Map<String, List<Map<String, dynamic>>> _missingSequencesData = {
    'invoices': [
      {
        'sequenceType': 'فواتير المبيعات',
        'prefix': 'INV',
        'missingNumbers': [1005, 1008, 1012, 1015, 1018],
        'lastNumber': 1025,
        'totalMissing': 5,
        'dateRange': '2024-01-01 إلى 2024-01-31',
        'severity': 'متوسط',
      },
      {
        'sequenceType': 'فواتير المشتريات',
        'prefix': 'PUR',
        'missingNumbers': [2003, 2007],
        'lastNumber': 2015,
        'totalMissing': 2,
        'dateRange': '2024-01-01 إلى 2024-01-31',
        'severity': 'منخفض',
      },
    ],
    'vouchers': [
      {
        'sequenceType': 'سندات القبض',
        'prefix': 'REC',
        'missingNumbers': [3002, 3006, 3009],
        'lastNumber': 3020,
        'totalMissing': 3,
        'dateRange': '2024-01-01 إلى 2024-01-31',
        'severity': 'متوسط',
      },
      {
        'sequenceType': 'سندات الصرف',
        'prefix': 'PAY',
        'missingNumbers': [4001, 4004, 4008, 4011, 4014, 4017],
        'lastNumber': 4025,
        'totalMissing': 6,
        'dateRange': '2024-01-01 إلى 2024-01-31',
        'severity': 'عالي',
      },
    ],
    'orders': [
      {
        'sequenceType': 'أوامر البيع',
        'prefix': 'SO',
        'missingNumbers': [5003, 5007, 5012],
        'lastNumber': 5018,
        'totalMissing': 3,
        'dateRange': '2024-01-01 إلى 2024-01-31',
        'severity': 'متوسط',
      },
    ],
  };

  @override
  Widget build(BuildContext context) {
    final currentData = _missingSequencesData[_selectedDocumentType] ?? [];
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('التسلسلات المفقودة'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedDocumentType,
                        decoration: const InputDecoration(
                          labelText: 'نوع المستند',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'invoices', child: Text('الفواتير')),
                          DropdownMenuItem(value: 'vouchers', child: Text('السندات')),
                          DropdownMenuItem(value: 'orders', child: Text('الأوامر')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedDocumentType = value!;
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedPeriod,
                        decoration: const InputDecoration(
                          labelText: 'الفترة',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'current_month', child: Text('الشهر الحالي')),
                          DropdownMenuItem(value: 'last_month', child: Text('الشهر الماضي')),
                          DropdownMenuItem(value: 'current_quarter', child: Text('الربع الحالي')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedPeriod = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // ملخص التسلسلات المفقودة
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              color: Colors.orange[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    const Text(
                      'ملخص التسلسلات المفقودة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Column(
                          children: [
                            const Text('أنواع المستندات'),
                            Text(
                              currentData.length.toString(),
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('إجمالي المفقود'),
                            Text(
                              _getTotalMissing(currentData).toString(),
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.red,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('حالات عالية الخطورة'),
                            Text(
                              _getHighSeverityCount(currentData).toString(),
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.orange,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                _buildStatCard('إجمالي المفقود', _getTotalMissing(currentData).toString(), Colors.red),
                _buildStatCard('خطورة عالية', _getHighSeverityCount(currentData).toString(), Colors.orange),
                _buildStatCard('خطورة متوسطة', _getMediumSeverityCount(currentData).toString(), Colors.yellow[700]!),
                _buildStatCard('خطورة منخفضة', _getLowSeverityCount(currentData).toString(), Colors.green),
              ],
            ),
          ),

          // قائمة التسلسلات المفقودة
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: currentData.length,
              itemBuilder: (context, index) {
                final sequence = currentData[index];
                
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: _getSeverityColor(sequence['severity']),
                      child: Text(
                        sequence['totalMissing'].toString(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    title: Text(
                      sequence['sequenceType'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('البادئة: ${sequence['prefix']}'),
                        Text('عدد المفقود: ${sequence['totalMissing']}'),
                        Text('آخر رقم: ${sequence['lastNumber']}'),
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: _getSeverityColor(sequence['severity']),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'خطورة ${sequence['severity']}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('نوع التسلسل', sequence['sequenceType'], Icons.description),
                                ),
                                Expanded(
                                  child: _buildDetailCard('البادئة', sequence['prefix'], Icons.label),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('آخر رقم', sequence['lastNumber'].toString(), Icons.last_page),
                                ),
                                Expanded(
                                  child: _buildDetailCard('الفترة', sequence['dateRange'], Icons.date_range),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            const Text(
                              'الأرقام المفقودة:',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Wrap(
                              spacing: 8.0,
                              runSpacing: 4.0,
                              children: sequence['missingNumbers'].map<Widget>((number) {
                                return Chip(
                                  label: Text(
                                    '${sequence['prefix']}-${number.toString().padLeft(4, '0')}',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                  backgroundColor: _getSeverityColor(sequence['severity']),
                                );
                              }).toList(),
                            ),
                            const SizedBox(height: 12),
                            Row(
                              children: [
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () => _investigateSequence(sequence),
                                    icon: const Icon(Icons.search),
                                    label: const Text('تحقق'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.blue,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () => _fixSequence(sequence),
                                    icon: const Icon(Icons.build),
                                    label: const Text('إصلاح'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.green,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () => _generateReport(sequence),
                                    icon: const Icon(Icons.report),
                                    label: const Text('تقرير'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.orange,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshData,
        backgroundColor: Colors.orange,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث البيانات'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailCard(String title, String value, IconData icon) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Icon(icon, size: 20, color: Colors.grey[600]),
            const SizedBox(height: 4),
            Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
            Text(
              title,
              style: const TextStyle(fontSize: 10),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Color _getSeverityColor(String severity) {
    switch (severity) {
      case 'عالي':
        return Colors.red;
      case 'متوسط':
        return Colors.orange;
      case 'منخفض':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  int _getTotalMissing(List<Map<String, dynamic>> data) {
    return data.fold(0, (sum, sequence) => sum + sequence['totalMissing'] as int);
  }

  int _getHighSeverityCount(List<Map<String, dynamic>> data) {
    return data.where((sequence) => sequence['severity'] == 'عالي').length;
  }

  int _getMediumSeverityCount(List<Map<String, dynamic>> data) {
    return data.where((sequence) => sequence['severity'] == 'متوسط').length;
  }

  int _getLowSeverityCount(List<Map<String, dynamic>> data) {
    return data.where((sequence) => sequence['severity'] == 'منخفض').length;
  }

  void _investigateSequence(Map<String, dynamic> sequence) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تحقق من تسلسل ${sequence['sequenceType']}')),
    );
  }

  void _fixSequence(Map<String, dynamic> sequence) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('إصلاح تسلسل ${sequence['sequenceType']}')),
    );
  }

  void _generateReport(Map<String, dynamic> sequence) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('إنشاء تقرير لـ ${sequence['sequenceType']}')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة تقرير التسلسلات المفقودة')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير تقرير التسلسلات المفقودة')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث بيانات التسلسلات المفقودة'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
