import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير أصناف منتهية الكميات
/// تعرض الأصناف التي انتهت كمياتها من المخزون
class OutOfStockItemsReportPage extends StatefulWidget {
  const OutOfStockItemsReportPage({super.key});

  @override
  State<OutOfStockItemsReportPage> createState() => _OutOfStockItemsReportPageState();
}

class _OutOfStockItemsReportPageState extends State<OutOfStockItemsReportPage> {
  String? _selectedCategory;
  String? _selectedWarehouse;
  String? _selectedPriority = 'all';
  bool _includeInactive = false;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.outOfStockItems),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.shopping_cart),
            onPressed: _generatePurchaseOrder,
            tooltip: 'إنشاء أمر شراء',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.red[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.category,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedCategory,
                        items: [
                          DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                          DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                          DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                          DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                        ],
                        onChanged: (value) => setState(() => _selectedCategory = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.warehouse,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedWarehouse,
                        items: [
                          DropdownMenuItem(value: 'all', child: Text(localizations.allWarehouses)),
                          DropdownMenuItem(value: 'main', child: Text(localizations.mainWarehouse)),
                          DropdownMenuItem(value: 'branch1', child: Text('${localizations.branchWarehouse} الأول')),
                        ],
                        onChanged: (value) => setState(() => _selectedWarehouse = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'أولوية الطلب',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedPriority,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الأولويات')),
                          DropdownMenuItem(value: 'urgent', child: Text('عاجل')),
                          DropdownMenuItem(value: 'high', child: Text('عالي')),
                          DropdownMenuItem(value: 'medium', child: Text('متوسط')),
                          DropdownMenuItem(value: 'low', child: Text('منخفض')),
                        ],
                        onChanged: (value) => setState(() => _selectedPriority = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: CheckboxListTile(
                        title: const Text('تضمين الأصناف غير النشطة'),
                        value: _includeInactive,
                        onChanged: (value) => setState(() => _includeInactive = value!),
                        controlAffinity: ListTileControlAffinity.leading,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.search),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // تحذير نفاد المخزون
                  Card(
                    color: Colors.red[50],
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        children: [
                          const Icon(Icons.warning, color: Colors.red, size: 32),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'تحذير: نفاد المخزون',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.red,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                const Text(
                                  'يوجد أصناف منتهية الكميات تحتاج إلى إعادة تموين فوري.',
                                  style: TextStyle(color: Colors.red),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إحصائيات نفاد المخزون
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إحصائيات نفاد المخزون',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildStatCard('أصناف منتهية', '18', Colors.red, Icons.inventory_2),
                              _buildStatCard('عاجل', '5', Colors.deepOrange, Icons.priority_high),
                              _buildStatCard('عالي الأولوية', '8', Colors.orange, Icons.arrow_upward),
                              _buildStatCard('متوسط الأولوية', '5', Colors.amber, Icons.remove),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول الأصناف المنتهية
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل الأصناف المنتهية',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: [
                                DataColumn(label: Text(localizations.itemCode)),
                                DataColumn(label: Text(localizations.itemName)),
                                DataColumn(label: Text(localizations.category)),
                                DataColumn(label: Text('الكمية الحالية')),
                                DataColumn(label: Text('آخر بيع')),
                                DataColumn(label: Text('متوسط الاستهلاك')),
                                DataColumn(label: Text('الكمية المقترحة')),
                                DataColumn(label: Text('الأولوية')),
                                DataColumn(label: Text('الإجراء')),
                              ],
                              rows: _buildOutOfStockRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // تحليل حسب التصنيف
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تحليل نفاد المخزون حسب التصنيف',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('التصنيف')),
                                DataColumn(label: Text('عدد الأصناف المنتهية')),
                                DataColumn(label: Text('النسبة من الإجمالي')),
                                DataColumn(label: Text('القيمة المقدرة')),
                                DataColumn(label: Text('الحالة')),
                              ],
                              rows: _buildCategoryAnalysisRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // توصيات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'توصيات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _generatePurchaseOrder,
                                  icon: const Icon(Icons.shopping_cart),
                                  label: const Text('إنشاء أمر شراء'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _sendAlert,
                                  icon: const Icon(Icons.notification_important),
                                  label: const Text('إرسال تنبيه'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<DataRow> _buildOutOfStockRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('001')),
        const DataCell(Text('جهاز كمبيوتر محمول')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('0')),
        const DataCell(Text('2024-01-10')),
        const DataCell(Text('3 قطع/أسبوع')),
        const DataCell(Text('15')),
        DataCell(_buildPriorityBadge('عاجل', Colors.red)),
        DataCell(IconButton(
          icon: const Icon(Icons.add_shopping_cart, color: Colors.green),
          onPressed: () => _addToPurchaseOrder('001'),
        )),
      ]),
      DataRow(cells: [
        const DataCell(Text('002')),
        const DataCell(Text('طابعة ليزر')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('0')),
        const DataCell(Text('2024-01-12')),
        const DataCell(Text('2 قطع/أسبوع')),
        const DataCell(Text('10')),
        DataCell(_buildPriorityBadge('عالي', Colors.orange)),
        DataCell(IconButton(
          icon: const Icon(Icons.add_shopping_cart, color: Colors.green),
          onPressed: () => _addToPurchaseOrder('002'),
        )),
      ]),
      DataRow(cells: [
        const DataCell(Text('003')),
        const DataCell(Text('قميص قطني')),
        const DataCell(Text('ملابس')),
        const DataCell(Text('0')),
        const DataCell(Text('2024-01-08')),
        const DataCell(Text('5 قطع/أسبوع')),
        const DataCell(Text('25')),
        DataCell(_buildPriorityBadge('متوسط', Colors.amber)),
        DataCell(IconButton(
          icon: const Icon(Icons.add_shopping_cart, color: Colors.green),
          onPressed: () => _addToPurchaseOrder('003'),
        )),
      ]),
    ];
  }

  List<DataRow> _buildCategoryAnalysisRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('8')),
        const DataCell(Text('44.4%')),
        const DataCell(Text('125,000 ر.س')),
        DataCell(_buildStatusBadge('حرج', Colors.red)),
      ]),
      DataRow(cells: [
        const DataCell(Text('ملابس')),
        const DataCell(Text('6')),
        const DataCell(Text('33.3%')),
        const DataCell(Text('45,000 ر.س')),
        DataCell(_buildStatusBadge('متوسط', Colors.orange)),
      ]),
      DataRow(cells: [
        const DataCell(Text('مواد غذائية')),
        const DataCell(Text('4')),
        const DataCell(Text('22.2%')),
        const DataCell(Text('15,000 ر.س')),
        DataCell(_buildStatusBadge('منخفض', Colors.amber)),
      ]),
    ];
  }

  Widget _buildStatCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPriorityBadge(String priority, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(priority, style: const TextStyle(color: Colors.white, fontSize: 12)),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(status, style: const TextStyle(color: Colors.white, fontSize: 12)),
    );
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء التقرير بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _generatePurchaseOrder() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء أمر الشراء بنجاح')),
    );
  }

  void _sendAlert() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إرسال التنبيه للمسؤولين')),
    );
  }

  void _addToPurchaseOrder(String itemCode) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم إضافة الصنف $itemCode لأمر الشراء')),
    );
  }
}
