import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير سندات الإخراج لصنف تالف
/// يعرض سندات إخراج الأصناف التالفة من المخزون
class DamagedItemExitVouchersReportPage extends StatefulWidget {
  const DamagedItemExitVouchersReportPage({super.key});

  @override
  State<DamagedItemExitVouchersReportPage> createState() => _DamagedItemExitVouchersReportPageState();
}

class _DamagedItemExitVouchersReportPageState extends State<DamagedItemExitVouchersReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedCategory;
  String? _damageReason = 'all';
  String? _selectedWarehouse;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('سندات الإخراج لصنف تالف'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.warning),
            onPressed: _showDamageAnalysis,
            tooltip: 'تحليل الأضرار',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          _buildFilterSection(localizations),
          
          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildDamageReasonsSection(),
                  const SizedBox(height: 16),
                  _buildExitVouchersTableSection(),
                  const SizedBox(height: 16),
                  _buildCostAnalysisSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.red[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.fromDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, true),
                  controller: TextEditingController(
                    text: _startDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.toDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, false),
                  controller: TextEditingController(
                    text: _endDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: localizations.category,
                    border: const OutlineInputBorder(),
                  ),
                  value: _selectedCategory,
                  items: [
                    DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                    DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                    DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                    DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                    const DropdownMenuItem(value: 'home', child: Text('أدوات منزلية')),
                  ],
                  onChanged: (value) => setState(() => _selectedCategory = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'سبب التلف',
                    border: OutlineInputBorder(),
                  ),
                  value: _damageReason,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الأسباب')),
                    DropdownMenuItem(value: 'expired', child: Text('انتهاء صلاحية')),
                    DropdownMenuItem(value: 'broken', child: Text('كسر أو تلف')),
                    DropdownMenuItem(value: 'defective', child: Text('عيب تصنيع')),
                    DropdownMenuItem(value: 'water_damage', child: Text('تلف بالماء')),
                    DropdownMenuItem(value: 'other', child: Text('أسباب أخرى')),
                  ],
                  onChanged: (value) => setState(() => _damageReason = value),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'المستودع',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedWarehouse,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع المستودعات')),
                    DropdownMenuItem(value: 'main', child: Text('المستودع الرئيسي')),
                    DropdownMenuItem(value: 'branch1', child: Text('مستودع الفرع الأول')),
                    DropdownMenuItem(value: 'branch2', child: Text('مستودع الفرع الثاني')),
                  ],
                  onChanged: (value) => setState(() => _selectedWarehouse = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.analytics),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.warning, color: Colors.red, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص سندات الإخراج للأصناف التالفة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي السندات', '125', Colors.red, Icons.receipt_long),
                _buildSummaryCard('إجمالي الكمية', '850', Colors.orange, Icons.inventory),
                _buildSummaryCard('قيمة الخسائر', '185,000 ر.س', Colors.purple, Icons.money_off),
                _buildSummaryCard('متوسط الخسارة', '1,480 ر.س', Colors.blue, Icons.calculate),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDamageReasonsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.pie_chart, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'توزيع أسباب التلف',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildReasonCard('انتهاء صلاحية', '45 سند', Colors.orange),
                _buildReasonCard('كسر أو تلف', '35 سند', Colors.red),
                _buildReasonCard('عيب تصنيع', '25 سند', Colors.purple),
                _buildReasonCard('أسباب أخرى', '20 سند', Colors.grey),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExitVouchersTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل سندات الإخراج',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('رقم السند')),
                  DataColumn(label: Text('اسم الصنف')),
                  DataColumn(label: Text('الكمية')),
                  DataColumn(label: Text('قيمة الخسارة')),
                  DataColumn(label: Text('سبب التلف')),
                  DataColumn(label: Text('المستودع')),
                  DataColumn(label: Text('تاريخ الإخراج')),
                  DataColumn(label: Text('الموظف المسؤول')),
                  DataColumn(label: Text('ملاحظات')),
                ],
                rows: _buildExitVouchersRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCostAnalysisSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.calculate, color: Colors.green, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'تحليل التكاليف والخسائر',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildCostAnalysisList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _createExitVoucher,
                    icon: const Icon(Icons.add),
                    label: const Text('إنشاء سند إخراج'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _preventiveMeasures,
                    icon: const Icon(Icons.security),
                    label: const Text('إجراءات وقائية'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _insuranceClaim,
                    icon: const Icon(Icons.shield),
                    label: const Text('مطالبة تأمين'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _supplierClaim,
                    icon: const Icon(Icons.business),
                    label: const Text('مطالبة مورد'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildCostAnalysisList() {
    final costs = [
      {'category': 'إلكترونيات', 'quantity': '125', 'value': '85,000 ر.س', 'percentage': '46%'},
      {'category': 'ملابس', 'quantity': '285', 'value': '45,000 ر.س', 'percentage': '24%'},
      {'category': 'أغذية', 'quantity': '385', 'value': '35,000 ر.س', 'percentage': '19%'},
      {'category': 'أدوات منزلية', 'quantity': '55', 'value': '20,000 ر.س', 'percentage': '11%'},
    ];

    return costs.map((cost) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.green.withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.calculate, color: Colors.green, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  cost['category']!,
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  'الكمية: ${cost['quantity']} • القيمة: ${cost['value']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              cost['percentage']!,
              style: const TextStyle(
                color: Colors.green,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildExitVouchersRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('EX001')),
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('3')),
        const DataCell(Text('13,500 ر.س')),
        DataCell(_buildReasonBadge('تلف بالماء', Colors.blue)),
        const DataCell(Text('المستودع الرئيسي')),
        const DataCell(Text('2024-01-15')),
        const DataCell(Text('أحمد محمد')),
        const DataCell(Text('تسرب في السقف')),
      ]),
      DataRow(cells: [
        const DataCell(Text('EX002')),
        const DataCell(Text('هاتف آيفون 15')),
        const DataCell(Text('2')),
        const DataCell(Text('7,600 ر.س')),
        DataCell(_buildReasonBadge('كسر أو تلف', Colors.red)),
        const DataCell(Text('مستودع الفرع الأول')),
        const DataCell(Text('2024-01-18')),
        const DataCell(Text('فاطمة علي')),
        const DataCell(Text('سقوط أثناء النقل')),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildReasonCard(String reason, String count, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(reason, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildReasonBadge(String reason, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(reason, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير سندات الإخراج للأصناف التالفة بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showDamageAnalysis() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تحليل مفصل للأضرار')),
    );
  }

  void _createExitVoucher() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء سند إخراج جديد للأصناف التالفة')),
    );
  }

  void _preventiveMeasures() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تطبيق إجراءات وقائية لمنع التلف')),
    );
  }

  void _insuranceClaim() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تقديم مطالبة تأمين للأصناف التالفة')),
    );
  }

  void _supplierClaim() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تقديم مطالبة للمورد للأصناف المعيبة')),
    );
  }
}
