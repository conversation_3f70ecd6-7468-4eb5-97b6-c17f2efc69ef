import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تغيير كلمة المرور للمستخدم
/// تتيح للمدير تغيير كلمة مرور أي مستخدم في النظام
class ChangeUserPasswordPage extends StatefulWidget {
  const ChangeUserPasswordPage({super.key});

  @override
  State<ChangeUserPasswordPage> createState() => _ChangeUserPasswordPageState();
}

class _ChangeUserPasswordPageState extends State<ChangeUserPasswordPage> {
  final _formKey = GlobalKey<FormState>();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  String? _selectedUser;
  bool _isProcessing = false;
  bool _showNewPassword = false;
  bool _showConfirmPassword = false;
  bool _forceChangeOnLogin = true;
  bool _sendNotification = true;

  final List<Map<String, String>> _users = [
    {
      'id': 'user1',
      'name': 'أحمد محمد',
      'role': 'مدير',
      'department': 'المبيعات',
      'lastLogin': '2024/01/25'
    },
    {
      'id': 'user2',
      'name': 'فاطمة علي',
      'role': 'محاسب',
      'department': 'المحاسبة',
      'lastLogin': '2024/01/24'
    },
    {
      'id': 'user3',
      'name': 'محمد سالم',
      'role': 'موظف',
      'department': 'المخازن',
      'lastLogin': '2024/01/23'
    },
    {
      'id': 'user4',
      'name': 'نورا أحمد',
      'role': 'مشرف',
      'department': 'المشتريات',
      'lastLogin': '2024/01/22'
    },
    {
      'id': 'user5',
      'name': 'خالد عبدالله',
      'role': 'موظف',
      'department': 'المبيعات',
      'lastLogin': '2024/01/21'
    },
  ];

  @override
  void dispose() {
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.changeUserPassword),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: _showPasswordHistory,
            tooltip: 'سجل تغيير كلمات المرور',
          ),
          IconButton(
            icon: const Icon(Icons.security),
            onPressed: _showPasswordPolicy,
            tooltip: 'سياسة كلمات المرور',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // بطاقة اختيار المستخدم
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'اختيار المستخدم',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: _selectedUser,
                      decoration: const InputDecoration(
                        labelText: 'المستخدم',
                        prefixIcon: Icon(Icons.person, size: 20),
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                      ),
                      items: _users.map<DropdownMenuItem<String>>((user) {
                        return DropdownMenuItem<String>(
                          value: user['id'],
                          child: Text(
                            '${user['name']} - ${user['role']}',
                            style: const TextStyle(fontSize: 14),
                            overflow: TextOverflow.ellipsis,
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedUser = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار المستخدم';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة كلمة المرور الجديدة
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'كلمة المرور الجديدة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // كلمة المرور الجديدة
                    TextFormField(
                      controller: _newPasswordController,
                      obscureText: !_showNewPassword,
                      decoration: InputDecoration(
                        labelText: 'كلمة المرور الجديدة',
                        prefixIcon: const Icon(Icons.lock, size: 20),
                        suffixIcon: IconButton(
                          icon: Icon(
                            _showNewPassword
                                ? Icons.visibility
                                : Icons.visibility_off,
                            size: 20,
                          ),
                          onPressed: () {
                            setState(() {
                              _showNewPassword = !_showNewPassword;
                            });
                          },
                        ),
                        border: const OutlineInputBorder(),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 8),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال كلمة المرور الجديدة';
                        }
                        if (value.length < 8) {
                          return 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
                        }
                        if (!_isPasswordStrong(value)) {
                          return 'كلمة المرور ضعيفة. يجب أن تحتوي على أحرف وأرقام ورموز';
                        }
                        return null;
                      },
                      onChanged: (value) {
                        setState(() {}); // لتحديث مؤشر قوة كلمة المرور
                      },
                    ),

                    const SizedBox(height: 16),

                    // تأكيد كلمة المرور
                    TextFormField(
                      controller: _confirmPasswordController,
                      obscureText: !_showConfirmPassword,
                      decoration: InputDecoration(
                        labelText: 'تأكيد كلمة المرور',
                        prefixIcon: const Icon(Icons.lock_outline, size: 20),
                        suffixIcon: IconButton(
                          icon: Icon(
                            _showConfirmPassword
                                ? Icons.visibility
                                : Icons.visibility_off,
                            size: 20,
                          ),
                          onPressed: () {
                            setState(() {
                              _showConfirmPassword = !_showConfirmPassword;
                            });
                          },
                        ),
                        border: const OutlineInputBorder(),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 8),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى تأكيد كلمة المرور';
                        }
                        if (value != _newPasswordController.text) {
                          return 'كلمة المرور غير متطابقة';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // مؤشر قوة كلمة المرور
                    if (_newPasswordController.text.isNotEmpty)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'قوة كلمة المرور:',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 8),
                          LinearProgressIndicator(
                            value: _getPasswordStrength(
                                _newPasswordController.text),
                            backgroundColor: Colors.grey[300],
                            valueColor: AlwaysStoppedAnimation<Color>(
                              _getPasswordStrengthColor(
                                  _newPasswordController.text),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _getPasswordStrengthText(
                                _newPasswordController.text),
                            style: TextStyle(
                              color: _getPasswordStrengthColor(
                                  _newPasswordController.text),
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),

                    const SizedBox(height: 16),

                    // زر توليد كلمة مرور قوية
                    Center(
                      child: OutlinedButton.icon(
                        onPressed: _generateStrongPassword,
                        icon: const Icon(Icons.auto_fix_high),
                        label: const Text('توليد كلمة مرور قوية'),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة خيارات إضافية
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'خيارات إضافية',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SwitchListTile(
                      title: const Text(
                          'إجبار المستخدم على تغيير كلمة المرور في الدخول التالي'),
                      subtitle: const Text(
                          'سيطلب من المستخدم تغيير كلمة المرور عند الدخول'),
                      value: _forceChangeOnLogin,
                      onChanged: (value) {
                        setState(() {
                          _forceChangeOnLogin = value;
                        });
                      },
                    ),
                    SwitchListTile(
                      title: const Text('إرسال إشعار للمستخدم'),
                      subtitle:
                          const Text('إرسال إشعار بريد إلكتروني أو رسالة نصية'),
                      value: _sendNotification,
                      onChanged: (value) {
                        setState(() {
                          _sendNotification = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // معاينة العملية
            if (_selectedUser != null)
              Card(
                color: Colors.orange.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'معاينة العملية',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.orange,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildPreviewRow('المستخدم:', _getUserName()),
                      _buildPreviewRow('الدور:', _getUserRole()),
                      _buildPreviewRow('القسم:', _getUserDepartment()),
                      _buildPreviewRow(
                          'إجبار التغيير:', _forceChangeOnLogin ? 'نعم' : 'لا'),
                      _buildPreviewRow(
                          'إرسال إشعار:', _sendNotification ? 'نعم' : 'لا'),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 24),

            // أزرار العمليات
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isProcessing ? null : _changePassword,
                    icon: _isProcessing
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.save),
                    label: Text(_isProcessing
                        ? 'جاري التغيير...'
                        : 'تغيير كلمة المرور'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _resetForm,
                    icon: const Icon(Icons.refresh),
                    label: const Text('إعادة تعيين'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  bool _isPasswordStrong(String password) {
    // يجب أن تحتوي على أحرف كبيرة وصغيرة وأرقام ورموز
    final hasUppercase = password.contains(RegExp(r'[A-Z]'));
    final hasLowercase = password.contains(RegExp(r'[a-z]'));
    final hasDigits = password.contains(RegExp(r'[0-9]'));
    final hasSpecialCharacters =
        password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));

    return hasUppercase && hasLowercase && hasDigits && hasSpecialCharacters;
  }

  double _getPasswordStrength(String password) {
    if (password.isEmpty) return 0.0;

    double strength = 0.0;

    // طول كلمة المرور
    if (password.length >= 8) strength += 0.2;
    if (password.length >= 12) strength += 0.1;

    // أحرف كبيرة
    if (password.contains(RegExp(r'[A-Z]'))) strength += 0.2;

    // أحرف صغيرة
    if (password.contains(RegExp(r'[a-z]'))) strength += 0.2;

    // أرقام
    if (password.contains(RegExp(r'[0-9]'))) strength += 0.2;

    // رموز خاصة
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) strength += 0.1;

    return strength.clamp(0.0, 1.0);
  }

  Color _getPasswordStrengthColor(String password) {
    final strength = _getPasswordStrength(password);
    if (strength < 0.3) return Colors.red;
    if (strength < 0.6) return Colors.orange;
    if (strength < 0.8) return Colors.yellow;
    return Colors.green;
  }

  String _getPasswordStrengthText(String password) {
    final strength = _getPasswordStrength(password);
    if (strength < 0.3) return 'ضعيفة';
    if (strength < 0.6) return 'متوسطة';
    if (strength < 0.8) return 'جيدة';
    return 'قوية';
  }

  void _generateStrongPassword() {
    const chars =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#\$%^&*()';
    final random = DateTime.now().millisecondsSinceEpoch;
    String password = '';

    // ضمان وجود كل نوع من الأحرف
    password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[(random % 26)];
    password += 'abcdefghijklmnopqrstuvwxyz'[(random % 26)];
    password += '0123456789'[(random % 10)];
    password += '!@#\$%^&*()'[(random % 10)];

    // إضافة باقي الأحرف
    for (int i = 4; i < 12; i++) {
      password += chars[(random + i) % chars.length];
    }

    // خلط الأحرف
    final shuffled = password.split('')..shuffle();
    password = shuffled.join('');

    setState(() {
      _newPasswordController.text = password;
      _confirmPasswordController.text = password;
    });
  }

  String _getUserName() {
    if (_selectedUser == null) return 'غير محدد';
    final user = _users.firstWhere((u) => u['id'] == _selectedUser);
    return user['name']!;
  }

  String _getUserRole() {
    if (_selectedUser == null) return 'غير محدد';
    final user = _users.firstWhere((u) => u['id'] == _selectedUser);
    return user['role']!;
  }

  String _getUserDepartment() {
    if (_selectedUser == null) return 'غير محدد';
    final user = _users.firstWhere((u) => u['id'] == _selectedUser);
    return user['department']!;
  }

  Future<void> _changePassword() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isProcessing = true;
      });

      // محاكاة عملية تغيير كلمة المرور
      await Future.delayed(const Duration(seconds: 2));

      setState(() {
        _isProcessing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('تم تغيير كلمة مرور المستخدم ${_getUserName()} بنجاح'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
        _resetForm();
      }
    }
  }

  void _resetForm() {
    setState(() {
      _selectedUser = null;
      _newPasswordController.clear();
      _confirmPasswordController.clear();
      _forceChangeOnLogin = true;
      _sendNotification = true;
      _showNewPassword = false;
      _showConfirmPassword = false;
    });
  }

  void _showPasswordHistory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض سجل تغيير كلمات المرور')),
    );
  }

  void _showPasswordPolicy() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('سياسة كلمات المرور'),
        content: const Text(
          'متطلبات كلمة المرور:\n\n'
          '• 8 أحرف على الأقل\n'
          '• أحرف كبيرة وصغيرة\n'
          '• أرقام\n'
          '• رموز خاصة (!@#\$%^&*)\n'
          '• عدم استخدام كلمات مرور سابقة\n'
          '• تغيير كلمة المرور كل 90 يوم',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('فهمت'),
          ),
        ],
      ),
    );
  }
}
