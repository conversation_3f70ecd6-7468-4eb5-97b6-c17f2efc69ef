import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة مقارنات على النظام
/// تتيح إجراء مقارنات شاملة بين بيانات النظام المختلفة
class SystemComparisonsPage extends StatefulWidget {
  const SystemComparisonsPage({super.key});

  @override
  State<SystemComparisonsPage> createState() => _SystemComparisonsPageState();
}

class _SystemComparisonsPageState extends State<SystemComparisonsPage> {
  String? _selectedComparisonType;
  String? _selectedPeriod1;
  String? _selectedPeriod2;
  bool _isProcessing = false;
  bool _showDetails = true;
  bool _includeGraphs = true;

  final List<Map<String, String>> _comparisonTypes = [
    {
      'id': 'sales',
      'name': 'مقارنة المبيعات',
      'description': 'مقارنة أرقام المبيعات بين فترات'
    },
    {
      'id': 'purchases',
      'name': 'مقارنة المشتريات',
      'description': 'مقارنة أرقام المشتريات بين فترات'
    },
    {
      'id': 'inventory',
      'name': 'مقارنة المخزون',
      'description': 'مقارنة كميات وقيم المخزون'
    },
    {
      'id': 'customers',
      'name': 'مقارنة العملاء',
      'description': 'مقارنة أداء العملاء'
    },
    {
      'id': 'products',
      'name': 'مقارنة الأصناف',
      'description': 'مقارنة أداء الأصناف'
    },
    {
      'id': 'branches',
      'name': 'مقارنة الفروع',
      'description': 'مقارنة أداء الفروع'
    },
    {
      'id': 'users',
      'name': 'مقارنة المستخدمين',
      'description': 'مقارنة أداء المستخدمين'
    },
    {
      'id': 'financial',
      'name': 'مقارنة مالية',
      'description': 'مقارنة الأرباح والخسائر'
    },
  ];

  final List<Map<String, String>> _periods = [
    {'id': 'today', 'name': 'اليوم', 'description': 'البيانات اليومية'},
    {'id': 'yesterday', 'name': 'أمس', 'description': 'بيانات أمس'},
    {'id': 'this_week', 'name': 'هذا الأسبوع', 'description': 'الأسبوع الحالي'},
    {
      'id': 'last_week',
      'name': 'الأسبوع الماضي',
      'description': 'الأسبوع السابق'
    },
    {'id': 'this_month', 'name': 'هذا الشهر', 'description': 'الشهر الحالي'},
    {'id': 'last_month', 'name': 'الشهر الماضي', 'description': 'الشهر السابق'},
    {'id': 'this_quarter', 'name': 'هذا الربع', 'description': 'الربع الحالي'},
    {
      'id': 'last_quarter',
      'name': 'الربع الماضي',
      'description': 'الربع السابق'
    },
    {'id': 'this_year', 'name': 'هذا العام', 'description': 'العام الحالي'},
    {'id': 'last_year', 'name': 'العام الماضي', 'description': 'العام السابق'},
  ];

  final List<Map<String, dynamic>> _comparisonResults = [
    {
      'title': 'إجمالي المبيعات',
      'period1': '125,450 ر.س',
      'period2': '98,320 ر.س',
      'difference': '+27,130 ر.س',
      'percentage': '+27.6%',
      'trend': 'up',
      'color': Colors.green,
    },
    {
      'title': 'عدد الفواتير',
      'period1': '245',
      'period2': '198',
      'difference': '+47',
      'percentage': '+23.7%',
      'trend': 'up',
      'color': Colors.blue,
    },
    {
      'title': 'متوسط قيمة الفاتورة',
      'period1': '512 ر.س',
      'period2': '497 ر.س',
      'difference': '+15 ر.س',
      'percentage': '+3.0%',
      'trend': 'up',
      'color': Colors.orange,
    },
    {
      'title': 'إجمالي المشتريات',
      'period1': '78,900 ر.س',
      'period2': '85,200 ر.س',
      'difference': '-6,300 ر.س',
      'percentage': '-7.4%',
      'trend': 'down',
      'color': Colors.red,
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.systemComparisons),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showAdvancedAnalytics,
            tooltip: 'تحليلات متقدمة',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportComparison,
            tooltip: 'تصدير المقارنة',
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          // بطاقة إعدادات المقارنة
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'إعدادات المقارنة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.purple,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // نوع المقارنة
                  DropdownButtonFormField<String>(
                    value: _selectedComparisonType,
                    decoration: const InputDecoration(
                      labelText: 'نوع المقارنة',
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    ),
                    items:
                        _comparisonTypes.map<DropdownMenuItem<String>>((type) {
                      return DropdownMenuItem<String>(
                        value: type['id'],
                        child: Text(
                          type['name']!,
                          style: const TextStyle(fontSize: 14),
                          overflow: TextOverflow.ellipsis,
                        ),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedComparisonType = value;
                      });
                    },
                  ),

                  const SizedBox(height: 16),

                  Row(
                    children: [
                      // الفترة الأولى
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedPeriod1,
                          decoration: const InputDecoration(
                            labelText: 'الفترة الأولى',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 8),
                          ),
                          items:
                              _periods.map<DropdownMenuItem<String>>((period) {
                            return DropdownMenuItem<String>(
                              value: period['id'],
                              child: Text(
                                period['name']!,
                                style: const TextStyle(fontSize: 14),
                                overflow: TextOverflow.ellipsis,
                              ),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedPeriod1 = value;
                            });
                          },
                        ),
                      ),

                      const SizedBox(width: 16),

                      // الفترة الثانية
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedPeriod2,
                          decoration: const InputDecoration(
                            labelText: 'الفترة الثانية',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 8),
                          ),
                          items:
                              _periods.map<DropdownMenuItem<String>>((period) {
                            return DropdownMenuItem<String>(
                              value: period['id'],
                              child: Text(
                                period['name']!,
                                style: const TextStyle(fontSize: 14),
                                overflow: TextOverflow.ellipsis,
                              ),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedPeriod2 = value;
                            });
                          },
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // خيارات إضافية
                  Row(
                    children: [
                      Expanded(
                        child: CheckboxListTile(
                          title: const Text('عرض التفاصيل'),
                          value: _showDetails,
                          onChanged: (value) {
                            setState(() {
                              _showDetails = value ?? true;
                            });
                          },
                          dense: true,
                        ),
                      ),
                      Expanded(
                        child: CheckboxListTile(
                          title: const Text('تضمين الرسوم البيانية'),
                          value: _includeGraphs,
                          onChanged: (value) {
                            setState(() {
                              _includeGraphs = value ?? true;
                            });
                          },
                          dense: true,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // زر إجراء المقارنة
          ElevatedButton.icon(
            onPressed: _isProcessing ? null : _performComparison,
            icon: _isProcessing
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.compare),
            label: Text(_isProcessing ? 'جاري المقارنة...' : 'إجراء المقارنة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.purple,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),

          const SizedBox(height: 16),

          // نتائج المقارنة
          if (_selectedComparisonType != null &&
              _selectedPeriod1 != null &&
              _selectedPeriod2 != null)
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.analytics, color: Colors.purple),
                        const SizedBox(width: 8),
                        const Text(
                          'نتائج المقارنة',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.purple,
                          ),
                        ),
                        const Spacer(),
                        Text(
                          '${_getPeriodName(_selectedPeriod1!)} مقابل ${_getPeriodName(_selectedPeriod2!)}',
                          style:
                              const TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // جدول النتائج
                    ...(_comparisonResults.map((result) => Card(
                          margin: const EdgeInsets.symmetric(vertical: 4),
                          child: ListTile(
                            leading: CircleAvatar(
                              backgroundColor: result['color'],
                              child: Icon(
                                result['trend'] == 'up'
                                    ? Icons.trending_up
                                    : Icons.trending_down,
                                color: Colors.white,
                                size: 20,
                              ),
                            ),
                            title: Text(result['title']),
                            subtitle: Row(
                              children: [
                                Text(
                                    '${result['period1']} ← ${result['period2']}'),
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 6, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: result['color'].withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Text(
                                    result['percentage'],
                                    style: TextStyle(
                                      color: result['color'],
                                      fontWeight: FontWeight.bold,
                                      fontSize: 12,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            trailing: Text(
                              result['difference'],
                              style: TextStyle(
                                color: result['color'],
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ))),
                  ],
                ),
              ),
            ),

          const SizedBox(height: 16),

          // ملخص المقارنة
          if (_selectedComparisonType != null)
            Card(
              color: Colors.purple.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'ملخص المقارنة',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.purple,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildSummaryRow('إجمالي التحسن:', '+15.2%', Colors.green),
                    _buildSummaryRow('أفضل أداء:', 'المبيعات', Colors.blue),
                    _buildSummaryRow(
                        'يحتاج تحسين:', 'المشتريات', Colors.orange),
                    _buildSummaryRow('التوصية:', 'زيادة التركيز على المبيعات',
                        Colors.purple),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(color: color, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  String _getPeriodName(String periodId) {
    final period = _periods.firstWhere((p) => p['id'] == periodId);
    return period['name']!;
  }

  Future<void> _performComparison() async {
    setState(() {
      _isProcessing = true;
    });

    // محاكاة عملية المقارنة
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isProcessing = false;
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إجراء المقارنة بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _showAdvancedAnalytics() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض التحليلات المتقدمة')),
    );
  }

  void _exportComparison() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير نتائج المقارنة')),
    );
  }
}
