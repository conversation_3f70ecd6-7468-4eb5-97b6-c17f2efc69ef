import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة ربط المستودعات
/// تتيح إدارة ربط المستودعات بالفروع والمستخدمين
class WarehouseLinkingPage extends StatefulWidget {
  const WarehouseLinkingPage({super.key});

  @override
  State<WarehouseLinkingPage> createState() => _WarehouseLinkingPageState();
}

class _WarehouseLinkingPageState extends State<WarehouseLinkingPage> {
  String _searchQuery = '';
  String _selectedType = 'all';

  // بيانات تجريبية لربط المستودعات
  final List<Map<String, dynamic>> _warehouseLinks = [
    {
      'id': 'LINK001',
      'warehouseName': 'المستودع الرئيسي',
      'warehouseCode': 'MAIN-WH',
      'branchName': 'الفرع الرئيسي',
      'branchCode': 'MAIN-BR',
      'linkedUsers': ['أحمد محمد', 'فاطمة أحمد'],
      'permissions': ['قراءة', 'كتابة', 'حذف'],
      'isActive': true,
      'linkType': 'فرع',
      'createdDate': '2024-01-10',
      'lastAccess': '2024-01-15 14:30:00',
    },
    {
      'id': 'LINK002',
      'warehouseName': 'مستودع التبريد',
      'warehouseCode': 'COLD-WH',
      'branchName': 'فرع جدة',
      'branchCode': 'JED-BR',
      'linkedUsers': ['محمد علي'],
      'permissions': ['قراءة', 'كتابة'],
      'isActive': true,
      'linkType': 'فرع',
      'createdDate': '2024-01-12',
      'lastAccess': '2024-01-15 12:15:00',
    },
    {
      'id': 'LINK003',
      'warehouseName': 'مستودع الفرع الشرقي',
      'warehouseCode': 'EAST-WH',
      'branchName': null,
      'branchCode': null,
      'linkedUsers': ['سارة خالد', 'نورا سعد'],
      'permissions': ['قراءة'],
      'isActive': false,
      'linkType': 'مستخدم',
      'createdDate': '2024-01-08',
      'lastAccess': '2024-01-13 09:45:00',
    },
    {
      'id': 'LINK004',
      'warehouseName': 'المستودع الرئيسي',
      'warehouseCode': 'MAIN-WH',
      'branchName': null,
      'branchCode': null,
      'linkedUsers': ['علي أحمد'],
      'permissions': ['قراءة', 'كتابة'],
      'isActive': true,
      'linkType': 'مستخدم',
      'createdDate': '2024-01-14',
      'lastAccess': '2024-01-15 16:20:00',
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('ربط المستودعات'),
        backgroundColor: Colors.lightBlue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addWarehouseLink,
            tooltip: 'إضافة ربط جديد',
          ),
          IconButton(
            icon: const Icon(Icons.account_tree),
            onPressed: _showLinkingTree,
            tooltip: 'شجرة الربط',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                TextField(
                  decoration: const InputDecoration(
                    hintText: 'البحث في ربط المستودعات...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedType,
                        decoration: const InputDecoration(
                          labelText: 'نوع الربط',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الأنواع')),
                          DropdownMenuItem(value: 'فرع', child: Text('ربط بالفروع')),
                          DropdownMenuItem(value: 'مستخدم', child: Text('ربط بالمستخدمين')),
                          DropdownMenuItem(value: 'active', child: Text('النشطة فقط')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedType = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                _buildStatCard('المجموع', _warehouseLinks.length.toString(), Colors.blue),
                _buildStatCard('النشطة', _warehouseLinks.where((l) => l['isActive']).length.toString(), Colors.green),
                _buildStatCard('معطلة', _warehouseLinks.where((l) => !l['isActive']).length.toString(), Colors.red),
                _buildStatCard('المستخدمين', _getTotalUsers(), Colors.orange),
              ],
            ),
          ),

          // قائمة ربط المستودعات
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _warehouseLinks.length,
              itemBuilder: (context, index) {
                final link = _warehouseLinks[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: link['isActive'] ? Colors.lightBlue : Colors.grey,
                      child: Icon(
                        link['linkType'] == 'فرع' ? Icons.store : Icons.person,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    title: Text(
                      link['warehouseName'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('كود المستودع: ${link['warehouseCode']}'),
                        if (link['linkType'] == 'فرع' && link['branchName'] != null)
                          Text('مربوط بالفرع: ${link['branchName']} (${link['branchCode']})')
                        else
                          Text('مربوط بالمستخدمين: ${link['linkedUsers'].join(', ')}'),
                        Text('الصلاحيات: ${link['permissions'].join(', ')}'),
                        Text('آخر وصول: ${link['lastAccess']}'),
                      ],
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: link['linkType'] == 'فرع' ? Colors.blue : Colors.purple,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            link['linkType'],
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        PopupMenuButton<String>(
                          onSelected: (value) => _handleAction(value, link),
                          itemBuilder: (context) => [
                            const PopupMenuItem(
                              value: 'edit',
                              child: ListTile(
                                leading: Icon(Icons.edit),
                                title: Text('تعديل'),
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'toggle',
                              child: ListTile(
                                leading: Icon(Icons.toggle_on),
                                title: Text('تغيير الحالة'),
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'permissions',
                              child: ListTile(
                                leading: Icon(Icons.security),
                                title: Text('إدارة الصلاحيات'),
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'access_log',
                              child: ListTile(
                                leading: Icon(Icons.history),
                                title: Text('سجل الوصول'),
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'delete',
                              child: ListTile(
                                leading: Icon(Icons.delete, color: Colors.red),
                                title: Text('حذف', style: TextStyle(color: Colors.red)),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    isThreeLine: true,
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addWarehouseLink,
        backgroundColor: Colors.lightBlue,
        icon: const Icon(Icons.add),
        label: const Text('إضافة ربط'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getTotalUsers() {
    Set<String> uniqueUsers = {};
    for (var link in _warehouseLinks) {
      uniqueUsers.addAll(List<String>.from(link['linkedUsers']));
    }
    return uniqueUsers.length.toString();
  }

  void _addWarehouseLink() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إضافة ربط مستودع جديد')),
    );
  }

  void _showLinkingTree() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض شجرة ربط المستودعات')),
    );
  }

  void _handleAction(String action, Map<String, dynamic> link) {
    switch (action) {
      case 'edit':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تعديل ربط ${link['warehouseName']}')),
        );
        break;
      case 'toggle':
        setState(() {
          link['isActive'] = !link['isActive'];
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم ${link['isActive'] ? 'تفعيل' : 'تعطيل'} الربط'),
          ),
        );
        break;
      case 'permissions':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('إدارة صلاحيات ${link['warehouseName']}')),
        );
        break;
      case 'access_log':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('سجل وصول ${link['warehouseName']}')),
        );
        break;
      case 'delete':
        _showDeleteConfirmation(link);
        break;
    }
  }

  void _showDeleteConfirmation(Map<String, dynamic> link) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف ربط ${link['warehouseName']}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _warehouseLinks.removeWhere((l) => l['id'] == link['id']);
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف ربط ${link['warehouseName']}'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
