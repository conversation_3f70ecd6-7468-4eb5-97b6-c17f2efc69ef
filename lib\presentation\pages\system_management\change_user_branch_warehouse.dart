import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تغيير الفرع والمستودع الإفتراضي للمستخدم
/// تتيح للمدير تغيير الفرع والمستودع الافتراضي لأي مستخدم
class ChangeUserBranchWarehousePage extends StatefulWidget {
  const ChangeUserBranchWarehousePage({super.key});

  @override
  State<ChangeUserBranchWarehousePage> createState() =>
      _ChangeUserBranchWarehousePageState();
}

class _ChangeUserBranchWarehousePageState
    extends State<ChangeUserBranchWarehousePage> {
  final _formKey = GlobalKey<FormState>();

  String? _selectedUser;
  String? _selectedBranch;
  String? _selectedWarehouse;
  bool _isProcessing = false;
  bool _applyToAllUsers = false;
  bool _sendNotification = true;

  final List<Map<String, String>> _users = [
    {
      'id': 'user1',
      'name': 'أحمد محمد',
      'role': 'مدير',
      'currentBranch': 'الفرع الرئيسي',
      'currentWarehouse': 'المستودع الرئيسي'
    },
    {
      'id': 'user2',
      'name': 'فاطمة علي',
      'role': 'محاسب',
      'currentBranch': 'فرع الشمال',
      'currentWarehouse': 'مستودع الشمال'
    },
    {
      'id': 'user3',
      'name': 'محمد سالم',
      'role': 'موظف',
      'currentBranch': 'فرع الجنوب',
      'currentWarehouse': 'مستودع الجنوب'
    },
    {
      'id': 'user4',
      'name': 'نورا أحمد',
      'role': 'مشرف',
      'currentBranch': 'الفرع الرئيسي',
      'currentWarehouse': 'المستودع الرئيسي'
    },
    {
      'id': 'user5',
      'name': 'خالد عبدالله',
      'role': 'موظف',
      'currentBranch': 'فرع الشرق',
      'currentWarehouse': 'مستودع الشرق'
    },
  ];

  final List<Map<String, String>> _branches = [
    {
      'id': 'main',
      'name': 'الفرع الرئيسي',
      'location': 'الرياض',
      'manager': 'أحمد محمد'
    },
    {
      'id': 'north',
      'name': 'فرع الشمال',
      'location': 'الدمام',
      'manager': 'فاطمة علي'
    },
    {
      'id': 'south',
      'name': 'فرع الجنوب',
      'location': 'جدة',
      'manager': 'محمد سالم'
    },
    {
      'id': 'east',
      'name': 'فرع الشرق',
      'location': 'الخبر',
      'manager': 'نورا أحمد'
    },
    {
      'id': 'west',
      'name': 'فرع الغرب',
      'location': 'مكة',
      'manager': 'خالد عبدالله'
    },
  ];

  final List<Map<String, String>> _warehouses = [
    {
      'id': 'main_wh',
      'name': 'المستودع الرئيسي',
      'branch': 'main',
      'capacity': '10000 متر مربع'
    },
    {
      'id': 'north_wh',
      'name': 'مستودع الشمال',
      'branch': 'north',
      'capacity': '5000 متر مربع'
    },
    {
      'id': 'south_wh',
      'name': 'مستودع الجنوب',
      'branch': 'south',
      'capacity': '7000 متر مربع'
    },
    {
      'id': 'east_wh',
      'name': 'مستودع الشرق',
      'branch': 'east',
      'capacity': '6000 متر مربع'
    },
    {
      'id': 'west_wh',
      'name': 'مستودع الغرب',
      'branch': 'west',
      'capacity': '4000 متر مربع'
    },
    {
      'id': 'temp_wh',
      'name': 'المستودع المؤقت',
      'branch': 'main',
      'capacity': '2000 متر مربع'
    },
  ];

  List<Map<String, String>> get _filteredWarehouses {
    if (_selectedBranch == null) return _warehouses;
    return _warehouses.where((wh) => wh['branch'] == _selectedBranch).toList();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.changeUserBranchWarehouse),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: _showChangeHistory,
            tooltip: 'سجل التغييرات',
          ),
          IconButton(
            icon: const Icon(Icons.business),
            onPressed: _showBranchesReport,
            tooltip: 'تقرير الفروع',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // بطاقة اختيار المستخدم
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'اختيار المستخدم',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.teal,
                      ),
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: _selectedUser,
                      decoration: const InputDecoration(
                        labelText: 'المستخدم',
                        prefixIcon: Icon(Icons.person, size: 20),
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                      ),
                      items: _users.map<DropdownMenuItem<String>>((user) {
                        return DropdownMenuItem<String>(
                          value: user['id'],
                          child: Text(
                            '${user['name']} - ${user['role']}',
                            style: const TextStyle(fontSize: 14),
                            overflow: TextOverflow.ellipsis,
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedUser = value;
                          // تعيين القيم الحالية
                          if (value != null) {
                            final user =
                                _users.firstWhere((u) => u['id'] == value);
                            _selectedBranch = _branches.firstWhere((b) =>
                                b['name'] == user['currentBranch'])['id'];
                            _selectedWarehouse = _warehouses.firstWhere((w) =>
                                w['name'] == user['currentWarehouse'])['id'];
                          }
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار المستخدم';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة الإعدادات الحالية
            if (_selectedUser != null)
              Card(
                color: Colors.grey.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'الإعدادات الحالية',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildInfoRow('الفرع الحالي:', _getCurrentBranchName()),
                      _buildInfoRow(
                          'المستودع الحالي:', _getCurrentWarehouseName()),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 16),

            // بطاقة الإعدادات الجديدة
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'الإعدادات الجديدة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.teal,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // الفرع الجديد
                    DropdownButtonFormField<String>(
                      value: _selectedBranch,
                      decoration: const InputDecoration(
                        labelText: 'الفرع الجديد',
                        prefixIcon: Icon(Icons.business, size: 20),
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                      ),
                      items: _branches.map<DropdownMenuItem<String>>((branch) {
                        return DropdownMenuItem<String>(
                          value: branch['id'],
                          child: Text(
                            '${branch['name']} - ${branch['location']}',
                            style: const TextStyle(fontSize: 14),
                            overflow: TextOverflow.ellipsis,
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedBranch = value;
                          _selectedWarehouse = null; // إعادة تعيين المستودع
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار الفرع';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // المستودع الجديد
                    DropdownButtonFormField<String>(
                      value: _selectedWarehouse,
                      decoration: const InputDecoration(
                        labelText: 'المستودع الجديد',
                        prefixIcon: Icon(Icons.warehouse, size: 20),
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                      ),
                      items: _filteredWarehouses
                          .map<DropdownMenuItem<String>>((warehouse) {
                        return DropdownMenuItem<String>(
                          value: warehouse['id'],
                          child: Text(
                            '${warehouse['name']} - ${warehouse['capacity']}',
                            style: const TextStyle(fontSize: 14),
                            overflow: TextOverflow.ellipsis,
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedWarehouse = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار المستودع';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة خيارات إضافية
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'خيارات إضافية',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.teal,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SwitchListTile(
                      title:
                          const Text('تطبيق على جميع المستخدمين في نفس الدور'),
                      subtitle: const Text(
                          'تطبيق نفس الإعدادات على جميع المستخدمين في نفس الدور'),
                      value: _applyToAllUsers,
                      onChanged: (value) {
                        setState(() {
                          _applyToAllUsers = value;
                        });
                      },
                    ),
                    SwitchListTile(
                      title: const Text('إرسال إشعار للمستخدم'),
                      subtitle: const Text('إرسال إشعار بالتغيير للمستخدم'),
                      value: _sendNotification,
                      onChanged: (value) {
                        setState(() {
                          _sendNotification = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // معاينة التغيير
            if (_selectedUser != null &&
                _selectedBranch != null &&
                _selectedWarehouse != null)
              Card(
                color: Colors.teal.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'معاينة التغيير',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.teal,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildPreviewRow('المستخدم:', _getUserName()),
                      _buildPreviewRow('الفرع الجديد:', _getNewBranchName()),
                      _buildPreviewRow(
                          'المستودع الجديد:', _getNewWarehouseName()),
                      _buildPreviewRow(
                          'تطبيق على الكل:', _applyToAllUsers ? 'نعم' : 'لا'),
                      _buildPreviewRow(
                          'إرسال إشعار:', _sendNotification ? 'نعم' : 'لا'),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 24),

            // أزرار العمليات
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isProcessing ? null : _saveChanges,
                    icon: _isProcessing
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.save),
                    label:
                        Text(_isProcessing ? 'جاري الحفظ...' : 'حفظ التغييرات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.teal,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _resetForm,
                    icon: const Icon(Icons.refresh),
                    label: const Text('إعادة تعيين'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Widget _buildPreviewRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  String _getUserName() {
    if (_selectedUser == null) return 'غير محدد';
    final user = _users.firstWhere((u) => u['id'] == _selectedUser);
    return user['name']!;
  }

  String _getCurrentBranchName() {
    if (_selectedUser == null) return 'غير محدد';
    final user = _users.firstWhere((u) => u['id'] == _selectedUser);
    return user['currentBranch']!;
  }

  String _getCurrentWarehouseName() {
    if (_selectedUser == null) return 'غير محدد';
    final user = _users.firstWhere((u) => u['id'] == _selectedUser);
    return user['currentWarehouse']!;
  }

  String _getNewBranchName() {
    if (_selectedBranch == null) return 'غير محدد';
    final branch = _branches.firstWhere((b) => b['id'] == _selectedBranch);
    return '${branch['name']} - ${branch['location']}';
  }

  String _getNewWarehouseName() {
    if (_selectedWarehouse == null) return 'غير محدد';
    final warehouse =
        _warehouses.firstWhere((w) => w['id'] == _selectedWarehouse);
    return '${warehouse['name']} - ${warehouse['capacity']}';
  }

  Future<void> _saveChanges() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isProcessing = true;
      });

      // محاكاة عملية الحفظ
      await Future.delayed(const Duration(seconds: 2));

      setState(() {
        _isProcessing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تغيير إعدادات المستخدم ${_getUserName()} بنجاح'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
        _resetForm();
      }
    }
  }

  void _resetForm() {
    setState(() {
      _selectedUser = null;
      _selectedBranch = null;
      _selectedWarehouse = null;
      _applyToAllUsers = false;
      _sendNotification = true;
    });
  }

  void _showChangeHistory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض سجل تغييرات الفروع والمستودعات')),
    );
  }

  void _showBranchesReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تقرير الفروع والمستودعات')),
    );
  }
}
