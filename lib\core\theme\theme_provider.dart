import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// مزود المظاهر المتقدم لعام 2025
class ThemeProvider extends ChangeNotifier {
  ThemeMode _themeMode = ThemeMode.system;
  String _selectedTheme = 'system';
  Color _primaryColor = Colors.blue;
  bool _useMaterial3 = true;
  bool _useGlassEffect = false;
  bool _useNeuomorphism = false;
  double _borderRadius = 12.0;
  bool _useCustomFont = false;
  String _fontFamily = 'Roboto';
  bool _useAnimations = true;
  double _animationSpeed = 1.0;

  // Getters
  ThemeMode get themeMode => _themeMode;
  String get selectedTheme => _selectedTheme;
  Color get primaryColor => _primaryColor;
  bool get useMaterial3 => _useMaterial3;
  bool get useGlassEffect => _useGlassEffect;
  bool get useNeuomorphism => _useNeuomorphism;
  double get borderRadius => _borderRadius;
  bool get useCustomFont => _useCustomFont;
  String get fontFamily => _fontFamily;
  bool get useAnimations => _useAnimations;
  double get animationSpeed => _animationSpeed;

  ThemeProvider() {
    _loadThemeSettings();
  }

  /// تحميل إعدادات المظهر المحفوظة
  Future<void> _loadThemeSettings() async {
    final prefs = await SharedPreferences.getInstance();

    _selectedTheme = prefs.getString('selected_theme') ?? 'system';
    _themeMode = _getThemeModeFromString(_selectedTheme);

    final colorValue = prefs.getInt('primary_color');
    if (colorValue != null) {
      _primaryColor = Color(colorValue);
    }

    _useMaterial3 = prefs.getBool('use_material3') ?? true;
    _useGlassEffect = prefs.getBool('use_glass_effect') ?? false;
    _useNeuomorphism = prefs.getBool('use_neuomorphism') ?? false;
    _borderRadius = prefs.getDouble('border_radius') ?? 12.0;
    _useCustomFont = prefs.getBool('use_custom_font') ?? false;
    _fontFamily = prefs.getString('font_family') ?? 'Roboto';
    _useAnimations = prefs.getBool('use_animations') ?? true;
    _animationSpeed = prefs.getDouble('animation_speed') ?? 1.0;

    notifyListeners();
  }

  /// تغيير وضع المظهر
  Future<void> setThemeMode(String theme) async {
    _selectedTheme = theme;
    _themeMode = _getThemeModeFromString(theme);

    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('selected_theme', theme);

    notifyListeners();
  }

  /// تغيير اللون الأساسي
  Future<void> setPrimaryColor(Color color) async {
    _primaryColor = color;

    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('primary_color', color.value);

    notifyListeners();
  }

  /// تفعيل/إلغاء Material 3
  Future<void> setUseMaterial3(bool value) async {
    _useMaterial3 = value;

    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('use_material3', value);

    notifyListeners();
  }

  /// تفعيل/إلغاء التأثير الزجاجي
  Future<void> setUseGlassEffect(bool value) async {
    _useGlassEffect = value;

    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('use_glass_effect', value);

    notifyListeners();
  }

  /// تفعيل/إلغاء Neuomorphism
  Future<void> setUseNeuomorphism(bool value) async {
    _useNeuomorphism = value;

    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('use_neuomorphism', value);

    notifyListeners();
  }

  /// تغيير نصف قطر الحواف
  Future<void> setBorderRadius(double radius) async {
    _borderRadius = radius;

    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble('border_radius', radius);

    notifyListeners();
  }

  /// تفعيل/إلغاء الخط المخصص
  Future<void> setUseCustomFont(bool value) async {
    _useCustomFont = value;

    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('use_custom_font', value);

    notifyListeners();
  }

  /// تغيير عائلة الخط
  Future<void> setFontFamily(String fontFamily) async {
    _fontFamily = fontFamily;

    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('font_family', fontFamily);

    notifyListeners();
  }

  /// تفعيل/إلغاء الحركات
  Future<void> setUseAnimations(bool value) async {
    _useAnimations = value;

    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('use_animations', value);

    notifyListeners();
  }

  /// تغيير سرعة الحركات
  Future<void> setAnimationSpeed(double speed) async {
    _animationSpeed = speed;

    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble('animation_speed', speed);

    notifyListeners();
  }

  /// تحويل النص إلى ThemeMode
  ThemeMode _getThemeModeFromString(String theme) {
    switch (theme) {
      case 'light':
        return ThemeMode.light;
      case 'dark':
        return ThemeMode.dark;
      case 'system':
      default:
        return ThemeMode.system;
    }
  }

  /// الحصول على المظهر الفاتح
  ThemeData get lightTheme {
    return _buildTheme(Brightness.light);
  }

  /// الحصول على المظهر الداكن
  ThemeData get darkTheme {
    return _buildTheme(Brightness.dark);
  }

  /// بناء المظهر حسب الإعدادات
  ThemeData _buildTheme(Brightness brightness) {
    final colorScheme = ColorScheme.fromSeed(
      seedColor: _primaryColor,
      brightness: brightness,
    );

    return ThemeData(
      useMaterial3: _useMaterial3,
      colorScheme: colorScheme,
      fontFamily: _useCustomFont ? _fontFamily : null,

      // إعدادات الحواف
      cardTheme: CardTheme(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_borderRadius),
        ),
      ),

      // إعدادات الأزرار
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_borderRadius),
          ),
        ),
      ),

      // إعدادات حقول النص
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_borderRadius),
        ),
      ),

      // إعدادات الحركات
      pageTransitionsTheme: _useAnimations
          ? PageTransitionsTheme(
              builders: {
                TargetPlatform.android: CupertinoPageTransitionsBuilder(),
                TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
                TargetPlatform.windows: FadeUpwardsPageTransitionsBuilder(),
                TargetPlatform.macOS: CupertinoPageTransitionsBuilder(),
                TargetPlatform.linux: FadeUpwardsPageTransitionsBuilder(),
              },
            )
          : const PageTransitionsTheme(
              builders: {
                TargetPlatform.android: FadeUpwardsPageTransitionsBuilder(),
                TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
                TargetPlatform.windows: FadeUpwardsPageTransitionsBuilder(),
                TargetPlatform.macOS: CupertinoPageTransitionsBuilder(),
                TargetPlatform.linux: FadeUpwardsPageTransitionsBuilder(),
              },
            ),
    );
  }

  /// إعادة تعيين جميع الإعدادات
  Future<void> resetToDefaults() async {
    _themeMode = ThemeMode.system;
    _selectedTheme = 'system';
    _primaryColor = Colors.blue;
    _useMaterial3 = true;
    _useGlassEffect = false;
    _useNeuomorphism = false;
    _borderRadius = 12.0;
    _useCustomFont = false;
    _fontFamily = 'Roboto';
    _useAnimations = true;
    _animationSpeed = 1.0;

    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();

    notifyListeners();
  }
}
