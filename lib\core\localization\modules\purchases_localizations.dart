class PurchasesLocalizations {
  static const Map<String, Map<String, String>> values = {
    'en': {
      // صفحات وحدة المشتريات
      'purchases': 'Purchases',
      'purchase_invoice': 'Purchase Invoice',
      'purchase_return_invoice': 'Purchase Return Invoice',
      'purchase_order': 'Purchase Order',
      'request_price_quotes': 'Request Price Quotes',
      'settle_previous_purchase_invoices': 'Settle Previous Purchase Invoices',
      'purchases_details': 'Purchases Details',
      'monthly_branch_purchases_movement': 'Monthly Branch Purchases Movement',
      'total_purchases_today': 'Total Purchases Today',
      'invoice_count': 'Invoice Count',
      'quick_operations': 'Quick Operations',
      'navigate_to': 'Navigate to',
    },
    'ar': {
      // صفحات وحدة المشتريات
      'purchases': 'المشتريات',
      'purchase_invoice': 'فاتورة مشتريات',
      'purchase_return_invoice': 'فاتورة مرتجع مشتريات',
      'purchase_order': 'أمر شراء',
      'request_price_quotes': 'طلب عروض أسعار',
      'settle_previous_purchase_invoices': 'تسوية فواتير المشتريات السابقة',
      'purchases_details': 'تفاصيل المشتريات',
      'monthly_branch_purchases_movement': 'حركة مشتريات الفروع الشهرية',
      'total_purchases_today': 'إجمالي المشتريات اليوم',
      'invoice_count': 'عدد الفواتير',
      'quick_operations': 'العمليات السريعة',
      'navigate_to': 'الانتقال إلى',
    },
  };
}
