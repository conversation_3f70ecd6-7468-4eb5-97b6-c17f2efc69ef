import 'package:flutter/material.dart';

/// نموذج بيانات المستودع
class WarehouseData {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController location1Controller = TextEditingController();
  final TextEditingController location2Controller = TextEditingController();
  final TextEditingController location3Controller = TextEditingController();
  final TextEditingController currentQuantityController =
      TextEditingController();
  final TextEditingController initialBalanceController =
      TextEditingController();
  final TextEditingController incomingController = TextEditingController();
  final TextEditingController outgoingController = TextEditingController();
  final TextEditingController purchasesController = TextEditingController();
  final TextEditingController salesController = TextEditingController();
  final TextEditingController minLimitController = TextEditingController();
  final TextEditingController maxLimitController = TextEditingController();

  void dispose() {
    nameController.dispose();
    location1Controller.dispose();
    location2Controller.dispose();
    location3Controller.dispose();
    currentQuantityController.dispose();
    initialBalanceController.dispose();
    incomingController.dispose();
    outgoingController.dispose();
    purchasesController.dispose();
    salesController.dispose();
    minLimitController.dispose();
    maxLimitController.dispose();
  }
}

/// نموذج بيانات المورد
class SupplierData {
  final TextEditingController accountNumberController = TextEditingController();
  final TextEditingController arabicNameController = TextEditingController();
  final TextEditingController englishNameController = TextEditingController();

  void dispose() {
    accountNumberController.dispose();
    arabicNameController.dispose();
    englishNameController.dispose();
  }
}

/// نموذج بيانات الصنف التابع
class RelatedItemData {
  final TextEditingController itemCodeController = TextEditingController();
  final TextEditingController arabicNameController = TextEditingController();
  final TextEditingController quantityController = TextEditingController();

  void dispose() {
    itemCodeController.dispose();
    arabicNameController.dispose();
    quantityController.dispose();
  }
}

/// صفحة عرض الأصناف
class ViewItemsPage extends StatefulWidget {
  const ViewItemsPage({super.key});

  @override
  State<ViewItemsPage> createState() => _ViewItemsPageState();
}

class _ViewItemsPageState extends State<ViewItemsPage>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // متحكمات النصوص - البيانات الأساسية
  final TextEditingController _itemCodeController = TextEditingController();
  final TextEditingController _arabicNameController = TextEditingController();
  final TextEditingController _englishNameController = TextEditingController();
  final TextEditingController _localBarcodeController = TextEditingController();
  final TextEditingController _externalBarcodeController =
      TextEditingController();
  final TextEditingController _internalBarcodeController =
      TextEditingController();
  final TextEditingController _imagePathController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  // متحكمات النصوص - الوحدات
  final TextEditingController _unit1EquivalentController =
      TextEditingController();
  final TextEditingController _unit2EquivalentController =
      TextEditingController();
  final TextEditingController _itemDateController = TextEditingController();

  // متغيرات التصنيفات
  String _selectedClassification1 = '';
  String _selectedClassification2 = '';
  String _selectedClassification3 = '';
  String _selectedClassification4 = '';
  String _selectedClassification5 = '';
  String _selectedClassification6 = '';
  String _selectedClassification7 = '';
  String _selectedClassification8 = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    // تنظيف متحكمات النصوص
    _itemCodeController.dispose();
    _arabicNameController.dispose();
    _englishNameController.dispose();
    _localBarcodeController.dispose();
    _externalBarcodeController.dispose();
    _internalBarcodeController.dispose();
    _imagePathController.dispose();
    _notesController.dispose();
    _unit1EquivalentController.dispose();
    _unit2EquivalentController.dispose();
    _itemDateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('عرض الأصناف'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          // أزرار الأدوات في AppBar
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveItem,
            tooltip: 'حفظ',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _newItem,
            tooltip: 'جديد',
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: _deleteItem,
            tooltip: 'حذف',
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _searchItem,
            tooltip: 'بحث',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط التنقل المحسن
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              border: Border(
                bottom: BorderSide(color: Colors.grey[300]!),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Row(
              children: [
                // مؤشر التبويب الحالي
                Expanded(
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.green[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.green[200]!),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _getTabIcon(_tabController.index),
                          color: Colors.green[700],
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _getTabName(_tabController.index),
                            style: TextStyle(
                              color: Colors.green[700],
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Text(
                          '${_tabController.index + 1}/6',
                          style: TextStyle(
                            color: Colors.green[600],
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 16),

                // أزرار التنقل
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildNavigationButton(
                      icon: Icons.first_page,
                      tooltip: 'التبويب الأول',
                      onPressed: _tabController.index > 0 ? _goToFirst : null,
                      isEnabled: _tabController.index > 0,
                    ),
                    const SizedBox(width: 4),
                    _buildNavigationButton(
                      icon: Icons.chevron_left,
                      tooltip: 'التبويب السابق',
                      onPressed:
                          _tabController.index > 0 ? _goToPrevious : null,
                      isEnabled: _tabController.index > 0,
                    ),
                    const SizedBox(width: 4),
                    _buildNavigationButton(
                      icon: Icons.chevron_right,
                      tooltip: 'التبويب التالي',
                      onPressed: _tabController.index < 5 ? _goToNext : null,
                      isEnabled: _tabController.index < 5,
                    ),
                    const SizedBox(width: 4),
                    _buildNavigationButton(
                      icon: Icons.last_page,
                      tooltip: 'التبويب الأخير',
                      onPressed: _tabController.index < 5 ? _goToLast : null,
                      isEnabled: _tabController.index < 5,
                    ),
                  ],
                ),
              ],
            ),
          ),
          // التبويبات
          TabBar(
            controller: _tabController,
            labelColor: Colors.green,
            unselectedLabelColor: Colors.grey,
            indicatorColor: Colors.green,
            isScrollable: true,
            tabs: const [
              Tab(text: 'البيانات الأساسية'),
              Tab(text: 'التصنيفات'),
              Tab(text: 'الوحدات'),
              Tab(text: 'المستودعات'),
              Tab(text: 'الفروع'),
              Tab(text: 'الموردين'),
            ],
          ),
          // محتوى التبويبات
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildBasicDataTab(),
                _buildClassificationsTab(),
                _buildUnitsTab(),
                _buildWarehousesTab(),
                _buildBranchesTab(),
                _buildSuppliersTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // دوال الأدوات
  void _saveItem() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم حفظ الصنف بنجاح')),
    );
  }

  void _newItem() {
    setState(() {
      // مسح جميع الحقول
      _itemCodeController.clear();
      _arabicNameController.clear();
      _englishNameController.clear();
      _localBarcodeController.clear();
      _externalBarcodeController.clear();
      _internalBarcodeController.clear();
      _imagePathController.clear();
      _notesController.clear();
      _unit1EquivalentController.clear();
      _unit2EquivalentController.clear();
      _itemDateController.clear();
    });
  }

  void _deleteItem() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل تريد حذف هذا الصنف؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم حذف الصنف')),
              );
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _searchItem() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('وظيفة البحث')),
    );
  }

  // دوال التنقل المحسنة
  void _goToFirst() {
    setState(() {
      _tabController.animateTo(0);
    });
  }

  void _goToPrevious() {
    if (_tabController.index > 0) {
      setState(() {
        _tabController.animateTo(_tabController.index - 1);
      });
    }
  }

  void _goToNext() {
    if (_tabController.index < 5) {
      setState(() {
        _tabController.animateTo(_tabController.index + 1);
      });
    }
  }

  void _goToLast() {
    setState(() {
      _tabController.animateTo(5);
    });
  }

  // دوال مساعدة للتنقل
  IconData _getTabIcon(int index) {
    switch (index) {
      case 0:
        return Icons.info_outline;
      case 1:
        return Icons.category_outlined;
      case 2:
        return Icons.straighten_outlined;
      case 3:
        return Icons.warehouse_outlined;
      case 4:
        return Icons.store_outlined;
      case 5:
        return Icons.local_shipping_outlined;
      default:
        return Icons.tab;
    }
  }

  String _getTabName(int index) {
    switch (index) {
      case 0:
        return 'البيانات الأساسية';
      case 1:
        return 'التصنيفات';
      case 2:
        return 'الوحدات';
      case 3:
        return 'المستودعات';
      case 4:
        return 'الفروع';
      case 5:
        return 'الموردين';
      default:
        return 'تبويب';
    }
  }

  Widget _buildNavigationButton({
    required IconData icon,
    required String tooltip,
    required VoidCallback? onPressed,
    required bool isEnabled,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6),
        color: isEnabled ? Colors.green[50] : Colors.grey[100],
        border: Border.all(
          color: isEnabled ? Colors.green[200]! : Colors.grey[300]!,
        ),
      ),
      child: IconButton(
        icon: Icon(
          icon,
          size: 20,
          color: isEnabled ? Colors.green[700] : Colors.grey[400],
        ),
        onPressed: onPressed,
        tooltip: tooltip,
        padding: const EdgeInsets.all(8),
        constraints: const BoxConstraints(
          minWidth: 40,
          minHeight: 40,
        ),
      ),
    );
  }

  // بناء التبويبات
  Widget _buildBasicDataTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // قسم البحث والاختيار
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'البحث والاختيار',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // خانة البحث عن الصنف
                  Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: TextField(
                          decoration: InputDecoration(
                            labelText: 'البحث عن الصنف (رقم أو اسم)',
                            hintText: 'ادخل رقم الصنف أو اسمه للبحث',
                            border: const OutlineInputBorder(),
                            prefixIcon: const Icon(Icons.search),
                            suffixIcon: IconButton(
                              icon: const Icon(Icons.qr_code_scanner),
                              onPressed: () {
                                // وظيفة مسح الباركود
                              },
                              tooltip: 'مسح الباركود',
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8),
                          ),
                          onChanged: (value) {
                            // وظيفة البحث
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          decoration: const InputDecoration(
                            labelText: 'اختيار الفرع',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8),
                          ),
                          items: const [
                            DropdownMenuItem(
                                value: 'main', child: Text('الفرع الرئيسي')),
                            DropdownMenuItem(
                                value: 'branch1', child: Text('الفرع الأول')),
                            DropdownMenuItem(
                                value: 'branch2', child: Text('الفرع الثاني')),
                            DropdownMenuItem(
                                value: 'branch3', child: Text('الفرع الثالث')),
                          ],
                          onChanged: (value) {
                            // وظيفة تغيير الفرع
                          },
                          value: 'main',
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          // قسم معلومات الصنف
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'معلومات الصنف',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // صورة الصنف
                  Container(
                    height: 150,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.grey[50],
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.inventory_2,
                          size: 48,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'صورة الصنف',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),

                  // البيانات الأساسية للعرض
                  Row(
                    children: [
                      Expanded(
                        child: _buildViewField('كود الصنف', 'ITM-001'),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildViewField(
                            'الاسم العربي', 'جهاز كمبيوتر محمول'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  Row(
                    children: [
                      Expanded(
                        child: _buildViewField(
                            'الاسم الإنجليزي', 'Laptop Computer'),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildViewField('تاريخ الإنشاء', '2024-01-15'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // الباركود
                  const Text(
                    'الباركود',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: _buildViewField('باركود محلي', '1234567890123'),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildViewField('باركود خارجي', '9876543210987'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  _buildViewField('باركود داخلي', 'INT-001-2024'),
                  const SizedBox(height: 16),

                  _buildViewField('ملاحظات',
                      'جهاز كمبيوتر محمول عالي الأداء مناسب للأعمال والألعاب'),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          // قسم الكمية والوحدات
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'الكمية والوحدات',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.orange,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: _buildViewField('الكمية المتاحة', '150'),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildViewField('الوحدة الأساسية', 'قطعة'),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildViewField('يعادل', '1'),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildViewField('العدد الإجمالي', '150 قطعة'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          // قسم أسعار البيع
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'أسعار البيع',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.purple,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // الصف الأول من الأسعار
                  Row(
                    children: [
                      Expanded(
                        child: _buildPriceField(
                            'سعر البيع أ', '2,500.00', 'ريال', Colors.green),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildPriceField(
                            'سعر البيع ب', '2,400.00', 'ريال', Colors.blue),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // الصف الثاني من الأسعار
                  Row(
                    children: [
                      Expanded(
                        child: _buildPriceField(
                            'سعر البيع ج', '2,300.00', 'ريال', Colors.orange),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildPriceField(
                            'سعر البيع د', '2,200.00', 'ريال', Colors.red),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // معلومات إضافية عن الأسعار
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue[200]!),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.info_outline,
                            color: Colors.blue[700], size: 20),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'أسعار البيع تشمل الضريبة • آخر تحديث: 2024-01-15',
                            style: TextStyle(
                              color: Colors.blue[700],
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClassificationsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          const Text(
            'التصنيفات',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),

          // التصنيفات الثمانية
          for (int i = 1; i <= 8; i++) ...[
            _buildDropdownField(
              _getClassificationLabel(i),
              _getClassificationValue(i),
              _getClassificationOptions(i),
              (value) => _setClassificationValue(i, value ?? ''),
            ),
            const SizedBox(height: 16),
          ],
        ],
      ),
    );
  }

  Widget _buildUnitsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'الوحدات',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),

          // قسم الوحدة الأساسية
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'الوحدة الأساسية',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildViewField('وحدة أساسية البيع', 'قطعة'),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          // قسم الوحدات الفرعية
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'الوحدات الفرعية',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // الوحدة 1
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.green[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.green[200]!),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.radio_button_checked,
                            color: Colors.green, size: 20),
                        const SizedBox(width: 8),
                        Expanded(
                          flex: 2,
                          child: _buildViewField('الوحدة 1', 'علبة'),
                        ),
                        const SizedBox(width: 16),
                        const Text(
                          'تعادل',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildViewField('', '12'),
                        ),
                        const SizedBox(width: 8),
                        const Text('قطعة'),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),

                  // الوحدة 2
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.radio_button_unchecked,
                            color: Colors.grey, size: 20),
                        const SizedBox(width: 8),
                        Expanded(
                          flex: 2,
                          child: _buildViewField('الوحدة 2', 'كرتون'),
                        ),
                        const SizedBox(width: 16),
                        const Text(
                          'تعادل',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildViewField('', '144'),
                        ),
                        const SizedBox(width: 8),
                        const Text('قطعة'),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),

                  // الوحدة 3
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.radio_button_unchecked,
                            color: Colors.grey, size: 20),
                        const SizedBox(width: 8),
                        Expanded(
                          flex: 2,
                          child: _buildViewField('الوحدة 3', 'باليت'),
                        ),
                        const Spacer(),
                        const Text('(وحدة فقط)'),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          // قسم معلومات إضافية
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'معلومات إضافية',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.orange,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // تاريخ الصنف
                  _buildViewField('تاريخ الصنف', '2024-01-15'),
                  const SizedBox(height: 16),

                  // صنف مجمع
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue[200]!),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.check_circle, color: Colors.blue, size: 20),
                        const SizedBox(width: 8),
                        const Text(
                          'صنف مجمع',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),

                  // أفضلية البيع
                  _buildViewField('أفضلية البيع', 'أفضلية عالية'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWarehousesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان فقط
          const Text(
            'المستودعات',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),

          // قائمة المستودعات للعرض فقط
          ..._buildWarehouseViewCards(),
        ],
      ),
    );
  }

  List<Widget> _buildWarehouseViewCards() {
    // بيانات وهمية للمستودعات للعرض
    final List<Map<String, String>> warehousesData = [
      {
        'name': 'المستودع الرئيسي',
        'location1': 'الرف A1',
        'location2': 'الرف A2',
        'location3': 'الرف A3',
        'currentQuantity': '150',
        'initialBalance': '200',
        'incoming': '50',
        'outgoing': '30',
        'purchases': '100',
        'sales': '80',
        'minLimit': '10',
        'maxLimit': '500',
      },
      {
        'name': 'مستودع الفرع الأول',
        'location1': 'الرف B1',
        'location2': 'الرف B2',
        'location3': 'الرف B3',
        'currentQuantity': '75',
        'initialBalance': '100',
        'incoming': '25',
        'outgoing': '15',
        'purchases': '50',
        'sales': '40',
        'minLimit': '5',
        'maxLimit': '200',
      },
    ];

    return warehousesData.asMap().entries.map((entry) {
      int index = entry.key;
      Map<String, String> warehouse = entry.value;

      return Card(
        margin: const EdgeInsets.only(bottom: 16),
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس الكرت للعرض فقط
              Text(
                'مستودع ${index + 1}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
              const SizedBox(height: 16),

              // اسم المستودع
              _buildViewField('اسم المستودع', warehouse['name']!),
              const SizedBox(height: 16),

              // المواقع الثلاثة
              const Text(
                'المواقع',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: _buildViewField('موقع 1', warehouse['location1']!),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildViewField('موقع 2', warehouse['location2']!),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildViewField('موقع 3', warehouse['location3']!),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // الكميات والأرصدة
              const Text(
                'الكميات والأرصدة',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: _buildViewField(
                        'الكمية الحالية', warehouse['currentQuantity']!),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildViewField(
                        'رصيد البداية', warehouse['initialBalance']!),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // الحركات
              const Text(
                'الحركات',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: _buildViewField('وارد', warehouse['incoming']!),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildViewField('منصرف', warehouse['outgoing']!),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildViewField('مشتريات', warehouse['purchases']!),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildViewField('مبيعات', warehouse['sales']!),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // الحدود
              const Text(
                'الحدود',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: _buildViewField('حد أدنى', warehouse['minLimit']!),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildViewField('حد أعلى', warehouse['maxLimit']!),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    }).toList();
  }

  Widget _buildBranchesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'الفروع',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),

          // معلومات الفرع الأساسية
          Row(
            children: [
              Expanded(
                child: _buildViewField('رقم الفرع', '001'),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildViewField('اسم الفرع', 'الفرع الرئيسي'),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // التكاليف والأرصدة
          Row(
            children: [
              Expanded(
                child: _buildViewField('تكلفة أول المدة', '1500.00'),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildViewField('متوسط التكلفة', '1250.00'),
              ),
            ],
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildViewField('أعلى رصيد', '2000.00'),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildViewField('أدنى رصيد', '100.00'),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // الأسعار الأربعة
          const Text(
            'الأسعار',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildViewField('سعر أ', '125.50'),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildViewField('سعر ب', '120.00'),
              ),
            ],
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildViewField('سعر ج', '115.00'),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildViewField('سعر د', '110.00'),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // الخصومات
          Row(
            children: [
              Expanded(
                child: _buildViewField('الخصم الافتراضي', '5%'),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildViewField('الحد الأعلى للخصم', '15%'),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // التكاليف الأخيرة
          Row(
            children: [
              Expanded(
                child: _buildViewField('آخر تكلفة', '100.00'),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildViewField('آخر تكلفة (عملة)', '100.00 ريال'),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // الطلبات
          _buildViewField('طلبات مثبتة', '25'),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildViewField('نوتة الطلبات خارجي', '50'),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildViewField('نوتة الطلبات داخلي', '30'),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // كمية سعر الجملة
          _buildViewField('كمية سعر الجملة', '100'),
          const SizedBox(height: 16),

          // الباركود
          Row(
            children: [
              Expanded(
                child: _buildViewField('باركود', '1234567890123'),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(4),
                    color: Colors.grey[50],
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: Colors.green,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        'تفعيل الباركود',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // الكميات والأسعار الأخيرة
          Row(
            children: [
              Expanded(
                child: _buildViewField('كمية محجوزة', '15'),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildViewField('سعر المنافس', '118.00'),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // حد المباع
          _buildViewField('حد المباع', '500'),
        ],
      ),
    );
  }

  Widget _buildSuppliersTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان فقط
          const Text(
            'الموردين',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),

          // قائمة الموردين للعرض فقط
          ..._buildSupplierViewCards(),
        ],
      ),
    );
  }

  List<Widget> _buildSupplierViewCards() {
    // بيانات وهمية للموردين للعرض
    final List<Map<String, String>> suppliersData = [
      {
        'accountNumber': '1001',
        'arabicName': 'شركة الأصناف المتقدمة',
        'englishName': 'Advanced Items Company',
      },
      {
        'accountNumber': '1002',
        'arabicName': 'مؤسسة التجارة الحديثة',
        'englishName': 'Modern Trade Corporation',
      },
      {
        'accountNumber': '1003',
        'arabicName': 'شركة الإمدادات الذكية',
        'englishName': 'Smart Supplies Company',
      },
    ];

    return suppliersData.asMap().entries.map((entry) {
      int index = entry.key;
      Map<String, String> supplier = entry.value;

      return Card(
        margin: const EdgeInsets.only(bottom: 16),
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس الكرت للعرض فقط
              Text(
                'مورد ${index + 1}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
              const SizedBox(height: 16),

              // رقم الحساب
              _buildViewField('رقم الحساب', supplier['accountNumber']!),
              const SizedBox(height: 16),

              // الأسماء
              Row(
                children: [
                  Expanded(
                    child: _buildViewField(
                        'اسم الحساب عربي', supplier['arabicName']!),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildViewField(
                        'اسم الحساب En', supplier['englishName']!),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    }).toList();
  }

  // دوال مساعدة للتصنيفات
  String _getClassificationLabel(int index) {
    const labels = ['1-أ', '2-ب', '3-ج', '4-د', '5-ه', '6-و', '7-ز', '8-ح'];
    return labels[index - 1];
  }

  String _getClassificationValue(int index) {
    switch (index) {
      case 1:
        return _selectedClassification1;
      case 2:
        return _selectedClassification2;
      case 3:
        return _selectedClassification3;
      case 4:
        return _selectedClassification4;
      case 5:
        return _selectedClassification5;
      case 6:
        return _selectedClassification6;
      case 7:
        return _selectedClassification7;
      case 8:
        return _selectedClassification8;
      default:
        return '';
    }
  }

  List<String> _getClassificationOptions(int index) {
    return [
      'تصنيف ${_getClassificationLabel(index)} - 1',
      'تصنيف ${_getClassificationLabel(index)} - 2',
      'تصنيف ${_getClassificationLabel(index)} - 3',
      'تصنيف ${_getClassificationLabel(index)} - 4',
    ];
  }

  void _setClassificationValue(int index, String value) {
    setState(() {
      switch (index) {
        case 1:
          _selectedClassification1 = value;
          break;
        case 2:
          _selectedClassification2 = value;
          break;
        case 3:
          _selectedClassification3 = value;
          break;
        case 4:
          _selectedClassification4 = value;
          break;
        case 5:
          _selectedClassification5 = value;
          break;
        case 6:
          _selectedClassification6 = value;
          break;
        case 7:
          _selectedClassification7 = value;
          break;
        case 8:
          _selectedClassification8 = value;
          break;
      }
    });
  }

  Widget _buildDropdownField(String label, String value, List<String> items,
      ValueChanged<String?> onChanged) {
    return DropdownButtonFormField<String>(
      value: value.isEmpty ? null : value,
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      items: items.map((String item) {
        return DropdownMenuItem<String>(
          value: item,
          child: Text(item),
        );
      }).toList(),
      onChanged: onChanged,
    );
  }

  // دالة لعرض البيانات فقط (بدون تعديل)
  Widget _buildViewField(String label, String value) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(4),
        color: Colors.grey[50],
      ),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  // دالة لعرض الأسعار بتصميم مميز
  Widget _buildPriceField(
      String label, String price, String currency, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: color.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: color.withValues(alpha: 0.1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                price,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                currency,
                style: TextStyle(
                  fontSize: 14,
                  color: color.withValues(alpha: 0.8),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
