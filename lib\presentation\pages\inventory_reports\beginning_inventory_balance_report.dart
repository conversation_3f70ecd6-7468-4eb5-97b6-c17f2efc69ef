import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير باقي البضاعة أول المدة
/// يعرض أرصدة البضاعة المتبقية من بداية الفترة المحاسبية
class BeginningInventoryBalanceReportPage extends StatefulWidget {
  const BeginningInventoryBalanceReportPage({super.key});

  @override
  State<BeginningInventoryBalanceReportPage> createState() => _BeginningInventoryBalanceReportPageState();
}

class _BeginningInventoryBalanceReportPageState extends State<BeginningInventoryBalanceReportPage> {
  DateTime? _periodStartDate;
  String? _selectedWarehouse;
  String? _selectedCategory;
  String? _valuationMethod = 'fifo';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('باقي البضاعة أول المدة'),
        backgroundColor: Colors.brown,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.brown[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'بداية الفترة المحاسبية',
                          border: OutlineInputBorder(),
                          suffixIcon: Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context),
                        controller: TextEditingController(
                          text: _periodStartDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.warehouse,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedWarehouse,
                        items: [
                          DropdownMenuItem(value: 'all', child: Text(localizations.allWarehouses)),
                          DropdownMenuItem(value: 'main', child: Text(localizations.mainWarehouse)),
                          DropdownMenuItem(value: 'branch1', child: Text('${localizations.branchWarehouse} الأول')),
                          DropdownMenuItem(value: 'branch2', child: Text('${localizations.branchWarehouse} الثاني')),
                        ],
                        onChanged: (value) => setState(() => _selectedWarehouse = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.category,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedCategory,
                        items: [
                          DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                          DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                          DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                          DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                        ],
                        onChanged: (value) => setState(() => _selectedCategory = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'طريقة التقييم',
                          border: OutlineInputBorder(),
                        ),
                        value: _valuationMethod,
                        items: const [
                          DropdownMenuItem(value: 'fifo', child: Text('الوارد أولاً صادر أولاً (FIFO)')),
                          DropdownMenuItem(value: 'lifo', child: Text('الوارد أخيراً صادر أولاً (LIFO)')),
                          DropdownMenuItem(value: 'average', child: Text('المتوسط المرجح')),
                          DropdownMenuItem(value: 'standard', child: Text('التكلفة المعيارية')),
                        ],
                        onChanged: (value) => setState(() => _valuationMethod = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.search),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.brown,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص الأرصدة الافتتاحية
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.inventory_2, color: Colors.brown, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'ملخص الأرصدة الافتتاحية',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSummaryCard('إجمالي الأصناف', '1,245', Colors.blue, Icons.inventory),
                              _buildSummaryCard('إجمالي الكمية', '15,680', Colors.green, Icons.numbers),
                              _buildSummaryCard('إجمالي القيمة', '2,850,000 ر.س', Colors.brown, Icons.monetization_on),
                              _buildSummaryCard('متوسط التكلفة', '182 ر.س', Colors.purple, Icons.calculate),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // توزيع الأرصدة حسب التصنيف
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'توزيع الأرصدة حسب التصنيف',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildCategoryCard('إلكترونيات', '485', '1,250,000 ر.س', Colors.blue),
                              _buildCategoryCard('ملابس', '320', '850,000 ر.س', Colors.green),
                              _buildCategoryCard('مواد غذائية', '285', '450,000 ر.س', Colors.orange),
                              _buildCategoryCard('أخرى', '155', '300,000 ر.س', Colors.grey),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول تفاصيل الأرصدة الافتتاحية
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل الأرصدة الافتتاحية',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('كود الصنف')),
                                DataColumn(label: Text('اسم الصنف')),
                                DataColumn(label: Text('التصنيف')),
                                DataColumn(label: Text('الوحدة')),
                                DataColumn(label: Text('الكمية الافتتاحية')),
                                DataColumn(label: Text('تكلفة الوحدة')),
                                DataColumn(label: Text('إجمالي التكلفة')),
                                DataColumn(label: Text('المستودع')),
                                DataColumn(label: Text('تاريخ آخر جرد')),
                              ],
                              rows: _buildBeginningBalanceRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // تحليل طرق التقييم
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.analytics, color: Colors.indigo, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'مقارنة طرق التقييم',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildValuationCard('FIFO', '2,850,000 ر.س', Colors.blue),
                              _buildValuationCard('LIFO', '2,720,000 ر.س', Colors.green),
                              _buildValuationCard('المتوسط المرجح', '2,785,000 ر.س', Colors.orange),
                              _buildValuationCard('التكلفة المعيارية', '2,800,000 ر.س', Colors.purple),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // أكبر الأرصدة قيمة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.trending_up, color: Colors.green, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'أكبر الأرصدة قيمة',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          ..._buildTopValueItemsList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _adjustBeginningBalance,
                                  icon: const Icon(Icons.edit),
                                  label: const Text('تعديل الأرصدة'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _reconcileInventory,
                                  icon: const Icon(Icons.balance),
                                  label: const Text('تسوية المخزون'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _generateJournalEntry,
                                  icon: const Icon(Icons.book),
                                  label: const Text('قيد افتتاحي'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _archiveData,
                                  icon: const Icon(Icons.archive),
                                  label: const Text('أرشفة البيانات'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<DataRow> _buildBeginningBalanceRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('ITEM-001')),
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('قطعة')),
        const DataCell(Text('50')),
        const DataCell(Text('2,500 ر.س')),
        const DataCell(Text('125,000 ر.س')),
        const DataCell(Text('المستودع الرئيسي')),
        const DataCell(Text('2024-01-01')),
      ]),
      DataRow(cells: [
        const DataCell(Text('ITEM-002')),
        const DataCell(Text('قميص قطني')),
        const DataCell(Text('ملابس')),
        const DataCell(Text('قطعة')),
        const DataCell(Text('200')),
        const DataCell(Text('85 ر.س')),
        const DataCell(Text('17,000 ر.س')),
        const DataCell(Text('الفرع الأول')),
        const DataCell(Text('2024-01-01')),
      ]),
      DataRow(cells: [
        const DataCell(Text('ITEM-003')),
        const DataCell(Text('عصير برتقال')),
        const DataCell(Text('مواد غذائية')),
        const DataCell(Text('علبة')),
        const DataCell(Text('500')),
        const DataCell(Text('12 ر.س')),
        const DataCell(Text('6,000 ر.س')),
        const DataCell(Text('الفرع الثاني')),
        const DataCell(Text('2024-01-01')),
      ]),
    ];
  }

  List<Widget> _buildTopValueItemsList() {
    final items = [
      {'name': 'لابتوب ديل XPS 13', 'quantity': '50', 'value': '125,000 ر.س'},
      {'name': 'طابعة HP LaserJet', 'quantity': '30', 'value': '85,000 ر.س'},
      {'name': 'هاتف آيفون 15', 'quantity': '25', 'value': '75,000 ر.س'},
      {'name': 'ساعة ذكية', 'quantity': '40', 'value': '65,000 ر.س'},
    ];

    return items.map((item) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.brown.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.brown.withOpacity(0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.inventory, color: Colors.brown, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item['name']!,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'الكمية: ${item['quantity']}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Text(
            item['value']!,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.brown,
            ),
          ),
        ],
      ),
    )).toList();
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryCard(String category, String count, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                category,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildValuationCard(String method, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                method,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        _periodStartDate = picked;
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير باقي البضاعة أول المدة بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _adjustBeginningBalance() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('فتح أداة تعديل الأرصدة الافتتاحية')),
    );
  }

  void _reconcileInventory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('بدء عملية تسوية المخزون')),
    );
  }

  void _generateJournalEntry() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء القيد الافتتاحي للمخزون')),
    );
  }

  void _archiveData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم أرشفة بيانات الأرصدة الافتتاحية')),
    );
  }
}
