import 'package:flutter/material.dart';
import '../../../widgets/global_buttons/new_button.dart';
import '../../../widgets/global_buttons/query_button.dart';
import '../../../widgets/global_buttons/delete_button.dart';
import '../../../widgets/global_buttons/undo_button.dart';
import '../../../widgets/global_buttons/save_button.dart';
import '../../../widgets/global_buttons/print_button.dart';

/// نموذج بيانات العميل
class CustomerData {
  // المعلومات الأساسية
  String accountType;
  String accountNumber;
  String branchNumber;
  String branchName;
  String securityLevel;
  bool unlinkFromSalesman;
  bool isMainAccount;
  String defaultPaymentMethod;
  bool enableSmsNotification;

  // معلومات عامة
  String arabicName;
  String englishName;
  String accountManagerName;
  String accountManagerPhone;
  bool accountManagerSms;
  String purchaseManagerName;
  String purchaseManagerPhone;
  bool purchaseManagerSms;
  String financeManagerName;
  String financeManagerPhone;
  bool financeManagerSms;
  String receivingManagerName;
  String receivingManagerPhone;
  bool receivingManagerSms;
  String outlet;
  String phone;
  String address;
  String country;
  String city;
  String district;
  String street;
  String buildingNumber;
  String postalCode;
  String additionalNumber;
  String unit;
  String shortAddress;
  String longitude;
  String latitude;
  String plusCode;
  String website;
  String email;

  // معلومات محاسبية
  String salesmanNumber;
  String accountLevel;
  String classification1;
  String classification2;
  double creditLimit;
  int duePeriod;
  double invoiceDiscount;
  String balanceFreeze;
  String balanceWarning;
  double targetAmount;
  double currentBalance;
  String notes;
  String taxId;
  String taxApplication;
  String customerType;
  String currency;

  CustomerData({
    this.accountType = 'عميل',
    this.accountNumber = '',
    this.branchNumber = '001',
    this.branchName = '',
    this.securityLevel = 'عادي',
    this.unlinkFromSalesman = false,
    this.isMainAccount = false,
    this.defaultPaymentMethod = 'نقدي',
    this.enableSmsNotification = false,
    this.arabicName = '',
    this.englishName = '',
    this.accountManagerName = '',
    this.accountManagerPhone = '',
    this.accountManagerSms = false,
    this.purchaseManagerName = '',
    this.purchaseManagerPhone = '',
    this.purchaseManagerSms = false,
    this.financeManagerName = '',
    this.financeManagerPhone = '',
    this.financeManagerSms = false,
    this.receivingManagerName = '',
    this.receivingManagerPhone = '',
    this.receivingManagerSms = false,
    this.outlet = '',
    this.phone = '',
    this.address = '',
    this.country = 'السعودية',
    this.city = '',
    this.district = '',
    this.street = '',
    this.buildingNumber = '',
    this.postalCode = '',
    this.additionalNumber = '',
    this.unit = '',
    this.shortAddress = '',
    this.longitude = '',
    this.latitude = '',
    this.plusCode = '',
    this.website = '',
    this.email = '',
    this.salesmanNumber = '',
    this.accountLevel = 'مستوى 1',
    this.classification1 = '',
    this.classification2 = '',
    this.creditLimit = 0.0,
    this.duePeriod = 30,
    this.invoiceDiscount = 0.0,
    this.balanceFreeze = 'بدون',
    this.balanceWarning = 'بدون',
    this.targetAmount = 0.0,
    this.currentBalance = 0.0,
    this.notes = '',
    this.taxId = '',
    this.taxApplication = 'خاضع للضريبة',
    this.customerType = 'عميل عادي',
    this.currency = 'ريال سعودي',
  });
}

/// صفحة العملاء
class Customers extends StatefulWidget {
  const Customers({super.key});

  @override
  State<Customers> createState() => _CustomersState();
}

class _CustomersState extends State<Customers> with TickerProviderStateMixin {
  late TabController _tabController;
  CustomerData _customerData = CustomerData();

  // متحكمات النصوص
  final Map<String, TextEditingController> _controllers = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 7, vsync: this);
    _initializeControllers();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _disposeControllers();
    super.dispose();
  }

  /// تهيئة متحكمات النصوص
  void _initializeControllers() {
    final fields = [
      'arabicName',
      'englishName',
      'accountManagerName',
      'accountManagerPhone',
      'purchaseManagerName',
      'purchaseManagerPhone',
      'financeManagerName',
      'financeManagerPhone',
      'receivingManagerName',
      'receivingManagerPhone',
      'outlet',
      'phone',
      'address',
      'country',
      'city',
      'district',
      'street',
      'buildingNumber',
      'postalCode',
      'additionalNumber',
      'unit',
      'shortAddress',
      'longitude',
      'latitude',
      'plusCode',
      'website',
      'email',
      'salesmanNumber',
      'creditLimit',
      'duePeriod',
      'invoiceDiscount',
      'targetAmount',
      'notes',
      'taxId'
    ];

    for (String field in fields) {
      _controllers[field] = TextEditingController();
    }
  }

  /// تنظيف متحكمات النصوص
  void _disposeControllers() {
    for (var controller in _controllers.values) {
      controller.dispose();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'العملاء',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        elevation: 2,
      ),
      body: Column(
        children: [
          // أزرار التحكم
          _buildControlButtons(),

          // التبويبات
          _buildTabBar(),

          // محتوى التبويبات
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildBasicInfoTab(), // المعلومات الأساسية
                _buildGeneralInfoTab(), // معلومات عامة
                _buildAccountingInfoTab(), // معلومات محاسبية
                _buildCustomerIncentiveTab(), // تقرير حافز العميل
                _buildReportsTab(), // التقارير
                _buildUserInfoTab(), // معلومات المستخدم
                _buildAdditionalTab(), // تبويب إضافي
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء أزرار التحكم
  Widget _buildControlButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Row(
        children: [
          NewButton(onPressed: _addNewCustomer),
          const SizedBox(width: 8),
          QueryButton(onPressed: _queryCustomer),
          const SizedBox(width: 8),
          DeleteButton(onPressed: _deleteCustomer),
          const SizedBox(width: 8),
          UndoButton(onPressed: _undoChanges),
          const SizedBox(width: 8),
          SaveButton(onPressed: _saveCustomer),
          const SizedBox(width: 8),
          PrintButton(onPressed: _printCustomer),
        ],
      ),
    );
  }

  /// بناء شريط التبويبات
  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        labelColor: Colors.blue[700],
        unselectedLabelColor: Colors.grey[600],
        indicatorColor: Colors.blue[700],
        indicatorWeight: 3,
        labelStyle: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 14,
        ),
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.normal,
          fontSize: 14,
        ),
        tabs: const [
          Tab(text: 'المعلومات الأساسية'),
          Tab(text: 'معلومات عامة'),
          Tab(text: 'معلومات محاسبية'),
          Tab(text: 'تقرير حافز العميل'),
          Tab(text: 'التقارير'),
          Tab(text: 'معلومات المستخدم'),
          Tab(text: 'إضافي'),
        ],
      ),
    );
  }

  // ===== دوال أزرار التحكم =====

  /// إضافة عميل جديد
  void _addNewCustomer() {
    setState(() {
      _customerData = CustomerData();
      _clearAllControllers();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إنشاء عميل جديد'),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// استعلام عن عميل
  void _queryCustomer() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('استعلام عن العميل'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// حذف عميل
  void _deleteCustomer() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا العميل؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم حذف العميل'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// تراجع عن التغييرات
  void _undoChanges() {
    setState(() {
      _clearAllControllers();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم التراجع عن التغييرات'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  /// حفظ العميل
  void _saveCustomer() {
    _updateCustomerDataFromControllers();

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم حفظ بيانات العميل'),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// طباعة بيانات العميل
  void _printCustomer() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري طباعة بيانات العميل'),
        backgroundColor: Colors.purple,
      ),
    );
  }

  /// مسح جميع متحكمات النصوص
  void _clearAllControllers() {
    for (var controller in _controllers.values) {
      controller.clear();
    }
  }

  /// تحديث بيانات العميل من متحكمات النصوص
  void _updateCustomerDataFromControllers() {
    _customerData.arabicName = _controllers['arabicName']?.text ?? '';
    _customerData.englishName = _controllers['englishName']?.text ?? '';
    _customerData.phone = _controllers['phone']?.text ?? '';
    _customerData.email = _controllers['email']?.text ?? '';
    // إضافة باقي الحقول حسب الحاجة
  }

  // ===== بناء التبويبات =====

  /// التبويب الأول: المعلومات الأساسية
  Widget _buildBasicInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('المعلومات الأساسية'),
          const SizedBox(height: 16),

          // الصف الأول
          Row(
            children: [
              Expanded(
                child: _buildDropdownField(
                  'نوع الحساب',
                  _customerData.accountType,
                  ['عميل', 'مورد', 'عميل ومورد'],
                  (value) => setState(() => _customerData.accountType = value!),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDropdownField(
                  'رقم الحساب',
                  _customerData.accountNumber,
                  ['تلقائي', '001', '002', '003'],
                  (value) =>
                      setState(() => _customerData.accountNumber = value!),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // الصف الثاني
          Row(
            children: [
              Expanded(
                child: _buildDropdownField(
                  'رقم فرع الحساب',
                  _customerData.branchNumber,
                  ['001', '002', '003', '004'],
                  (value) =>
                      setState(() => _customerData.branchNumber = value!),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child:
                    _buildDisplayField('اسم الفرع', _customerData.branchName),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // الصف الثالث
          Row(
            children: [
              Expanded(
                child: _buildDropdownField(
                  'مستوى السرية',
                  _customerData.securityLevel,
                  ['عادي', 'سري', 'سري جداً'],
                  (value) =>
                      setState(() => _customerData.securityLevel = value!),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDropdownField(
                  'طريقة الدفع الافتراضية',
                  _customerData.defaultPaymentMethod,
                  ['نقدي', 'آجل', 'شيك', 'تحويل بنكي'],
                  (value) => setState(
                      () => _customerData.defaultPaymentMethod = value!),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // مربعات الاختيار
          _buildCheckboxField(
            'إلغاء ربط بالمندوب',
            _customerData.unlinkFromSalesman,
            (value) =>
                setState(() => _customerData.unlinkFromSalesman = value!),
          ),

          const SizedBox(height: 8),

          _buildCheckboxField(
            'حساب رئيسي',
            _customerData.isMainAccount,
            (value) => setState(() => _customerData.isMainAccount = value!),
          ),

          const SizedBox(height: 8),

          _buildCheckboxField(
            'تفعيل خدمة إشعار برسالة SMS',
            _customerData.enableSmsNotification,
            (value) =>
                setState(() => _customerData.enableSmsNotification = value!),
          ),
        ],
      ),
    );
  }

  /// التبويب الثاني: معلومات عامة
  Widget _buildGeneralInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('معلومات عامة'),
          const SizedBox(height: 16),

          // الأسماء
          Row(
            children: [
              Expanded(
                child: _buildTextFormField(
                  'اسم الحساب بالعربي',
                  _controllers['arabicName']!,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTextFormField(
                  'الاسم الإنجليزي',
                  _controllers['englishName']!,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // المسؤول عن الحساب
          _buildContactSection(
            'اسم الشخص المسؤول عن الحساب',
            'accountManager',
          ),

          const SizedBox(height: 16),

          // المسؤول عن المشتريات
          _buildContactSection(
            'اسم الشخص المسؤول عن المشتريات',
            'purchaseManager',
          ),
        ],
      ),
    );
  }

  /// التبويب الثالث: معلومات محاسبية
  Widget _buildAccountingInfoTab() {
    return const Center(
      child: Text('معلومات محاسبية - قيد التطوير'),
    );
  }

  /// التبويب الرابع: تقرير حافز العميل
  Widget _buildCustomerIncentiveTab() {
    return Container(
      color: Colors.grey[50],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // بطاقة العميل الرئيسية
            _buildCustomerCard(),

            const SizedBox(height: 20),

            // شبكة البيانات المالية
            Expanded(
              child: _buildFinancialDataGrid(),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة معلومات العميل
  Widget _buildCustomerCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 2,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // رقم التسلسل
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: Colors.blue[600],
              shape: BoxShape.circle,
            ),
            child: const Center(
              child: Text(
                '1',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),

          const SizedBox(width: 20),

          // معلومات العميل
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'شركة الأمل للتجارة',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'رقم الحساب: CUST001',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء شبكة البيانات المالية
  Widget _buildFinancialDataGrid() {
    return Column(
      children: [
        // الصف الأول
        Row(
          children: [
            Expanded(
              child: _buildDataCard(
                'المرتجعات',
                '25000.00',
                Colors.red[100]!,
                Colors.red[600]!,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDataCard(
                'إجمالي المبيعات',
                '850000.00',
                Colors.green[100]!,
                Colors.green[600]!,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // الصف الثاني
        Row(
          children: [
            Expanded(
              child: _buildDataCard(
                'الفواتير المخفضة',
                '35000.00',
                Colors.purple[100]!,
                Colors.purple[600]!,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDataCard(
                'إشعارات الخصم',
                '15000.00',
                Colors.orange[100]!,
                Colors.orange[600]!,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // الصف الثالث
        Row(
          children: [
            Expanded(
              child: _buildDataCard(
                'المسدد خلال 90 يوم',
                '720000.00',
                Colors.teal[100]!,
                Colors.teal[600]!,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDataCard(
                'صافي المبيعات',
                '775000.00',
                Colors.blue[100]!,
                Colors.blue[600]!,
              ),
            ),
          ],
        ),

        const SizedBox(height: 20),

        // بطاقة الحافز المميزة
        _buildIncentiveCard(),
      ],
    );
  }

  /// بناء بطاقة بيانات واحدة
  Widget _buildDataCard(
      String title, String amount, Color bgColor, Color textColor) {
    return Container(
      height: 100,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              color: textColor,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.right,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                'ر.س',
                style: TextStyle(
                  fontSize: 12,
                  color: textColor.withValues(alpha: 0.7),
                ),
              ),
              const SizedBox(width: 4),
              Text(
                amount,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: textColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة الحافز المميزة
  Widget _buildIncentiveCard() {
    return Container(
      width: double.infinity,
      height: 80,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.green[400]!, Colors.green[600]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const Text(
            'الحافز',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          Row(
            children: [
              const Text(
                'ر.س',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                ),
              ),
              const SizedBox(width: 8),
              const Text(
                '38750.00',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// التبويب الخامس: التقارير
  Widget _buildReportsTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.assessment,
            size: 64,
            color: Colors.green,
          ),
          SizedBox(height: 16),
          Text(
            'التقارير',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          Text('قيد التطوير'),
        ],
      ),
    );
  }

  /// التبويب السادس: معلومات المستخدم
  Widget _buildUserInfoTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person,
            size: 64,
            color: Colors.orange,
          ),
          SizedBox(height: 16),
          Text(
            'معلومات المستخدم',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          Text('اسم المستخدم: admin'),
          Text('تاريخ التعديل: 2024-01-15'),
        ],
      ),
    );
  }

  /// التبويب السابع: إضافي
  Widget _buildAdditionalTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.more_horiz,
            size: 64,
            color: Colors.purple,
          ),
          SizedBox(height: 16),
          Text(
            'معلومات إضافية',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          Text('قيد التطوير'),
        ],
      ),
    );
  }

  // ===== دوال مساعدة لبناء عناصر الواجهة =====

  /// بناء عنوان القسم
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Colors.blue[700],
      ),
    );
  }

  /// بناء حقل قائمة منسدلة
  Widget _buildDropdownField(
    String label,
    String value,
    List<String> items,
    ValueChanged<String?> onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: items.contains(value) ? value : items.first,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: items.map((String item) {
            return DropdownMenuItem<String>(
              value: item,
              child: Text(item),
            );
          }).toList(),
          onChanged: onChanged,
        ),
      ],
    );
  }

  /// بناء حقل عرض فقط
  Widget _buildDisplayField(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[400]!),
            borderRadius: BorderRadius.circular(8),
            color: Colors.grey[100],
          ),
          child: Text(
            value.isEmpty ? 'غير محدد' : value,
            style: TextStyle(
              color: value.isEmpty ? Colors.grey[600] : Colors.black,
            ),
          ),
        ),
      ],
    );
  }

  /// بناء مربع اختيار
  Widget _buildCheckboxField(
    String label,
    bool value,
    ValueChanged<bool?> onChanged,
  ) {
    return IntrinsicWidth(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Checkbox(
            value: value,
            onChanged: onChanged,
            activeColor: Colors.blue[700],
          ),
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حقل إدخال نص
  Widget _buildTextFormField(
    String label,
    TextEditingController controller, {
    TextInputType keyboardType = TextInputType.text,
    int maxLines = 1,
    String? suffix,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          maxLines: maxLines,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            suffixText: suffix,
          ),
        ),
      ],
    );
  }

  /// بناء قسم معلومات الاتصال
  Widget _buildContactSection(String title, String prefix) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 12),
        IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 2,
                child: _buildTextFormField(
                  'الاسم',
                  _controllers['${prefix}Name']!,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildTextFormField(
                  'رقم الجوال',
                  _controllers['${prefix}Phone']!,
                  keyboardType: TextInputType.phone,
                ),
              ),
              const SizedBox(width: 12),
              Container(
                padding: const EdgeInsets.only(top: 24),
                child: _buildCheckboxField(
                  'SMS',
                  _getContactSmsValue(prefix),
                  (value) => _setContactSmsValue(prefix, value!),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// الحصول على قيمة SMS للمسؤول
  bool _getContactSmsValue(String prefix) {
    switch (prefix) {
      case 'accountManager':
        return _customerData.accountManagerSms;
      case 'purchaseManager':
        return _customerData.purchaseManagerSms;
      case 'financeManager':
        return _customerData.financeManagerSms;
      case 'receivingManager':
        return _customerData.receivingManagerSms;
      default:
        return false;
    }
  }

  /// تعيين قيمة SMS للمسؤول
  void _setContactSmsValue(String prefix, bool value) {
    setState(() {
      switch (prefix) {
        case 'accountManager':
          _customerData.accountManagerSms = value;
          break;
        case 'purchaseManager':
          _customerData.purchaseManagerSms = value;
          break;
        case 'financeManager':
          _customerData.financeManagerSms = value;
          break;
        case 'receivingManager':
          _customerData.receivingManagerSms = value;
          break;
      }
    });
  }
}
