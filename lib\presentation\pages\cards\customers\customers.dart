import 'package:flutter/material.dart';
import '../../../widgets/global_buttons/new_button.dart';
import '../../../widgets/global_buttons/query_button.dart';
import '../../../widgets/global_buttons/delete_button.dart';
import '../../../widgets/global_buttons/undo_button.dart';
import '../../../widgets/global_buttons/save_button.dart';
import '../../../widgets/global_buttons/print_button.dart';

/// نموذج بيانات العميل
class CustomerData {
  // المعلومات الأساسية
  String accountType;
  String accountNumber;
  String branchNumber;
  String branchName;
  String securityLevel;
  bool unlinkFromSalesman;
  bool isMainAccount;
  String defaultPaymentMethod;
  bool enableSmsNotification;

  // معلومات عامة
  String arabicName;
  String englishName;
  String accountManagerName;
  String accountManagerPhone;
  bool accountManagerSms;
  String purchaseManagerName;
  String purchaseManagerPhone;
  bool purchaseManagerSms;
  String financeManagerName;
  String financeManagerPhone;
  bool financeManagerSms;
  String receivingManagerName;
  String receivingManagerPhone;
  bool receivingManagerSms;
  String outlet;
  String phone;
  String address;
  String country;
  String city;
  String district;
  String street;
  String buildingNumber;
  String postalCode;
  String additionalNumber;
  String unit;
  String shortAddress;
  String longitude;
  String latitude;
  String plusCode;
  String website;
  String email;

  // معلومات محاسبية
  String salesmanNumber;
  String accountLevel;
  String classification1;
  String classification2;
  double creditLimit;
  int duePeriod;
  double invoiceDiscount;
  String balanceFreeze;
  String balanceWarning;
  double targetAmount;
  double currentBalance;
  String notes;
  String taxId;
  String taxApplication;
  String customerType;
  String currency;

  CustomerData({
    this.accountType = 'عميل',
    this.accountNumber = '',
    this.branchNumber = '001',
    this.branchName = '',
    this.securityLevel = 'عادي',
    this.unlinkFromSalesman = false,
    this.isMainAccount = false,
    this.defaultPaymentMethod = 'نقدي',
    this.enableSmsNotification = false,
    this.arabicName = '',
    this.englishName = '',
    this.accountManagerName = '',
    this.accountManagerPhone = '',
    this.accountManagerSms = false,
    this.purchaseManagerName = '',
    this.purchaseManagerPhone = '',
    this.purchaseManagerSms = false,
    this.financeManagerName = '',
    this.financeManagerPhone = '',
    this.financeManagerSms = false,
    this.receivingManagerName = '',
    this.receivingManagerPhone = '',
    this.receivingManagerSms = false,
    this.outlet = '',
    this.phone = '',
    this.address = '',
    this.country = 'السعودية',
    this.city = '',
    this.district = '',
    this.street = '',
    this.buildingNumber = '',
    this.postalCode = '',
    this.additionalNumber = '',
    this.unit = '',
    this.shortAddress = '',
    this.longitude = '',
    this.latitude = '',
    this.plusCode = '',
    this.website = '',
    this.email = '',
    this.salesmanNumber = '',
    this.accountLevel = 'مستوى 1',
    this.classification1 = '',
    this.classification2 = '',
    this.creditLimit = 0.0,
    this.duePeriod = 30,
    this.invoiceDiscount = 0.0,
    this.balanceFreeze = 'بدون',
    this.balanceWarning = 'بدون',
    this.targetAmount = 0.0,
    this.currentBalance = 0.0,
    this.notes = '',
    this.taxId = '',
    this.taxApplication = 'خاضع للضريبة',
    this.customerType = 'عميل عادي',
    this.currency = 'ريال سعودي',
  });
}

/// صفحة العملاء
class Customers extends StatefulWidget {
  const Customers({super.key});

  @override
  State<Customers> createState() => _CustomersState();
}

class _CustomersState extends State<Customers> with TickerProviderStateMixin {
  late TabController _tabController;
  CustomerData _customerData = CustomerData();

  // متحكمات النصوص
  final Map<String, TextEditingController> _controllers = {};

  // بيانات تجريبية للاستعلام
  final List<Map<String, dynamic>> _financialYears = [
    {'id': 1, 'year': '2021'},
    {'id': 2, 'year': '2022'},
    {'id': 3, 'year': '2023'},
    {'id': 4, 'year': '2024'},
    {'id': 5, 'year': '2025'},
  ];

  final List<Map<String, dynamic>> _branches = [
    {'id': 1, 'name': 'الفرع الرئيسي - الرياض'},
    {'id': 2, 'name': 'فرع جدة'},
    {'id': 3, 'name': 'فرع الدمام'},
    {'id': 4, 'name': 'فرع مكة'},
    {'id': 5, 'name': 'فرع المدينة'},
  ];

  final List<Map<String, dynamic>> _customers = [
    {'accountNumber': '125458', 'name': 'شركة الأمل للتجارة'},
    {'accountNumber': '125459', 'name': 'مؤسسة النور التجارية'},
    {'accountNumber': '125460', 'name': 'شركة الفجر للاستثمار'},
    {'accountNumber': '125461', 'name': 'مجموعة الشروق التجارية'},
    {'accountNumber': '125462', 'name': 'شركة الضياء للتطوير'},
  ];

  // بيانات الحوافز التجريبية
  final Map<String, Map<String, dynamic>> _incentiveDatabase = {
    '2024_1_125458': {
      'year': '2024',
      'branch': 'الفرع الرئيسي - الرياض',
      'customer': 'شركة الأمل للتجارة',
      'accountNumber': '125458',
      'totalSales': 850000.00,
      'returns': 25000.00,
      'discountNotifications': 15000.00,
      'discountedInvoices': 35000.00,
      'netSales': 775000.00,
      'paidWithin90Days': 720000.00,
      'incentive': 38750.00,
    },
    '2024_1_125459': {
      'year': '2024',
      'branch': 'الفرع الرئيسي - الرياض',
      'customer': 'مؤسسة النور التجارية',
      'accountNumber': '125459',
      'totalSales': 650000.00,
      'returns': 18000.00,
      'discountNotifications': 12000.00,
      'discountedInvoices': 28000.00,
      'netSales': 592000.00,
      'paidWithin90Days': 580000.00,
      'incentive': 29600.00,
    },
    '2023_1_125458': {
      'year': '2023',
      'branch': 'الفرع الرئيسي - الرياض',
      'customer': 'شركة الأمل للتجارة',
      'accountNumber': '125458',
      'totalSales': 920000.00,
      'returns': 32000.00,
      'discountNotifications': 18000.00,
      'discountedInvoices': 42000.00,
      'netSales': 828000.00,
      'paidWithin90Days': 800000.00,
      'incentive': 41400.00,
    },
  };

  // بيانات الحافز المعروضة حالياً
  Map<String, dynamic>? _currentIncentiveData;

  // متغيرات نافذة الاستعلام الجديدة
  final TextEditingController _searchController = TextEditingController();
  Map<String, dynamic>? _selectedCustomer;
  List<Map<String, dynamic>> _incentiveRows = [];
  bool _canSaveOrEdit = false;
  List<Map<String, dynamic>> _searchResults = [];
  bool _showSearchResults = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 7, vsync: this);
    _initializeControllers();

    // إضافة مستمع لتغيير التبويبات لتحديث الأزرار
    _tabController.addListener(() {
      if (mounted) {
        setState(() {
          // تحديث الواجهة عند تغيير التبويب
        });
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _disposeControllers();
    super.dispose();
  }

  /// تهيئة متحكمات النصوص
  void _initializeControllers() {
    final fields = [
      'arabicName',
      'englishName',
      'accountManagerName',
      'accountManagerPhone',
      'purchaseManagerName',
      'purchaseManagerPhone',
      'financeManagerName',
      'financeManagerPhone',
      'receivingManagerName',
      'receivingManagerPhone',
      'outlet',
      'phone',
      'address',
      'country',
      'city',
      'district',
      'street',
      'buildingNumber',
      'postalCode',
      'additionalNumber',
      'unit',
      'shortAddress',
      'longitude',
      'latitude',
      'plusCode',
      'website',
      'email',
      'salesmanNumber',
      'creditLimit',
      'duePeriod',
      'invoiceDiscount',
      'targetAmount',
      'notes',
      'taxId'
    ];

    for (String field in fields) {
      _controllers[field] = TextEditingController();
    }
  }

  /// تنظيف متحكمات النصوص
  void _disposeControllers() {
    for (var controller in _controllers.values) {
      controller.dispose();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'العملاء',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        elevation: 2,
      ),
      body: Column(
        children: [
          // أزرار التحكم
          _buildControlButtons(),

          // التبويبات
          _buildTabBar(),

          // محتوى التبويبات
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildBasicInfoTab(), // المعلومات الأساسية
                _buildGeneralInfoTab(), // معلومات عامة
                _buildAccountingInfoTab(), // معلومات محاسبية
                _buildCustomerIncentiveTab(), // تقرير حافز العميل
                _buildReportsTab(), // التقارير
                _buildUserInfoTab(), // معلومات المستخدم
                _buildAdditionalTab(), // تبويب إضافي
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء أزرار التحكم
  Widget _buildControlButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Row(
        children: [
          NewButton(onPressed: _getCurrentAddFunction()),
          const SizedBox(width: 8),
          QueryButton(onPressed: _getCurrentQueryFunction()),
          const SizedBox(width: 8),
          DeleteButton(onPressed: _getCurrentDeleteFunction()),
          const SizedBox(width: 8),
          UndoButton(onPressed: _getCurrentUndoFunction()),
          const SizedBox(width: 8),
          SaveButton(onPressed: _getCurrentSaveFunction()),
          const SizedBox(width: 8),
          PrintButton(onPressed: _getCurrentPrintFunction()),
        ],
      ),
    );
  }

  /// بناء شريط التبويبات
  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        labelColor: Colors.blue[700],
        unselectedLabelColor: Colors.grey[600],
        indicatorColor: Colors.blue[700],
        indicatorWeight: 3,
        labelStyle: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 14,
        ),
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.normal,
          fontSize: 14,
        ),
        tabs: const [
          Tab(text: 'المعلومات الأساسية'),
          Tab(text: 'معلومات عامة'),
          Tab(text: 'معلومات محاسبية'),
          Tab(text: 'تقرير حافز العميل'),
          Tab(text: 'التقارير'),
          Tab(text: 'معلومات المستخدم'),
          Tab(text: 'إضافي'),
        ],
      ),
    );
  }

  // ===== دوال أزرار التحكم =====

  /// إضافة عميل جديد
  void _addNewCustomer() {
    setState(() {
      _customerData = CustomerData();
      _clearAllControllers();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إنشاء عميل جديد'),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// استعلام عن عميل
  void _queryCustomer() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('استعلام عن العميل'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// حذف عميل
  void _deleteCustomer() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا العميل؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم حذف العميل'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// تراجع عن التغييرات
  void _undoChanges() {
    setState(() {
      _clearAllControllers();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم التراجع عن التغييرات'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  /// حفظ العميل
  void _saveCustomer() {
    _updateCustomerDataFromControllers();

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم حفظ بيانات العميل'),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// طباعة بيانات العميل
  void _printCustomer() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري طباعة بيانات العميل'),
        backgroundColor: Colors.purple,
      ),
    );
  }

  /// مسح جميع متحكمات النصوص
  void _clearAllControllers() {
    for (var controller in _controllers.values) {
      controller.clear();
    }
  }

  /// تحديث بيانات العميل من متحكمات النصوص
  void _updateCustomerDataFromControllers() {
    _customerData.arabicName = _controllers['arabicName']?.text ?? '';
    _customerData.englishName = _controllers['englishName']?.text ?? '';
    _customerData.phone = _controllers['phone']?.text ?? '';
    _customerData.email = _controllers['email']?.text ?? '';
    // إضافة باقي الحقول حسب الحاجة
  }

  // ===== دوال الأزرار الديناميكية =====

  /// الحصول على دالة الإضافة حسب التبويب النشط
  VoidCallback _getCurrentAddFunction() {
    switch (_tabController.index) {
      case 0: // المعلومات الأساسية
        return _addBasicInfo;
      case 1: // معلومات عامة
        return _addGeneralInfo;
      case 2: // معلومات محاسبية
        return _addAccountingInfo;
      case 3: // تقرير حافز العميل
        return _addIncentiveInfo;
      case 4: // التقارير
        return _addReportInfo;
      case 5: // معلومات المستخدم
        return _addUserInfo;
      case 6: // إضافي
        return _addAdditionalInfo;
      default:
        return _addNewCustomer;
    }
  }

  /// الحصول على دالة الاستعلام حسب التبويب النشط
  VoidCallback _getCurrentQueryFunction() {
    switch (_tabController.index) {
      case 0: // المعلومات الأساسية
        return _queryBasicInfo;
      case 1: // معلومات عامة
        return _queryGeneralInfo;
      case 2: // معلومات محاسبية
        return _queryAccountingInfo;
      case 3: // تقرير حافز العميل
        return _queryIncentiveInfo;
      case 4: // التقارير
        return _queryReportInfo;
      case 5: // معلومات المستخدم
        return _queryUserInfo;
      case 6: // إضافي
        return _queryAdditionalInfo;
      default:
        return _queryCustomer;
    }
  }

  /// الحصول على دالة الحذف حسب التبويب النشط
  VoidCallback _getCurrentDeleteFunction() {
    switch (_tabController.index) {
      case 0: // المعلومات الأساسية
        return _deleteBasicInfo;
      case 1: // معلومات عامة
        return _deleteGeneralInfo;
      case 2: // معلومات محاسبية
        return _deleteAccountingInfo;
      case 3: // تقرير حافز العميل
        return _deleteIncentiveInfo;
      case 4: // التقارير
        return _deleteReportInfo;
      case 5: // معلومات المستخدم
        return _deleteUserInfo;
      case 6: // إضافي
        return _deleteAdditionalInfo;
      default:
        return _deleteCustomer;
    }
  }

  /// الحصول على دالة التراجع حسب التبويب النشط
  VoidCallback _getCurrentUndoFunction() {
    switch (_tabController.index) {
      case 0: // المعلومات الأساسية
        return _undoBasicInfo;
      case 1: // معلومات عامة
        return _undoGeneralInfo;
      case 2: // معلومات محاسبية
        return _undoAccountingInfo;
      case 3: // تقرير حافز العميل
        return _undoIncentiveInfo;
      case 4: // التقارير
        return _undoReportInfo;
      case 5: // معلومات المستخدم
        return _undoUserInfo;
      case 6: // إضافي
        return _undoAdditionalInfo;
      default:
        return _undoChanges;
    }
  }

  /// الحصول على دالة الحفظ حسب التبويب النشط
  VoidCallback _getCurrentSaveFunction() {
    switch (_tabController.index) {
      case 0: // المعلومات الأساسية
        return _saveBasicInfo;
      case 1: // معلومات عامة
        return _saveGeneralInfo;
      case 2: // معلومات محاسبية
        return _saveAccountingInfo;
      case 3: // تقرير حافز العميل
        return _saveIncentiveInfo;
      case 4: // التقارير
        return _saveReportInfo;
      case 5: // معلومات المستخدم
        return _saveUserInfo;
      case 6: // إضافي
        return _saveAdditionalInfo;
      default:
        return _saveCustomer;
    }
  }

  /// الحصول على دالة الطباعة حسب التبويب النشط
  VoidCallback _getCurrentPrintFunction() {
    switch (_tabController.index) {
      case 0: // المعلومات الأساسية
        return _printBasicInfo;
      case 1: // معلومات عامة
        return _printGeneralInfo;
      case 2: // معلومات محاسبية
        return _printAccountingInfo;
      case 3: // تقرير حافز العميل
        return _printIncentiveInfo;
      case 4: // التقارير
        return _printReportInfo;
      case 5: // معلومات المستخدم
        return _printUserInfo;
      case 6: // إضافي
        return _printAdditionalInfo;
      default:
        return _printCustomer;
    }
  }

  // ===== بناء التبويبات =====

  /// التبويب الأول: المعلومات الأساسية
  Widget _buildBasicInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('المعلومات الأساسية'),
          const SizedBox(height: 16),

          // الصف الأول
          Row(
            children: [
              Expanded(
                child: _buildDropdownField(
                  'نوع الحساب',
                  _customerData.accountType,
                  ['عميل', 'مورد', 'عميل ومورد'],
                  (value) => setState(() => _customerData.accountType = value!),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDropdownField(
                  'رقم الحساب',
                  _customerData.accountNumber,
                  ['تلقائي', '001', '002', '003'],
                  (value) =>
                      setState(() => _customerData.accountNumber = value!),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // الصف الثاني
          Row(
            children: [
              Expanded(
                child: _buildDropdownField(
                  'رقم فرع الحساب',
                  _customerData.branchNumber,
                  ['001', '002', '003', '004'],
                  (value) =>
                      setState(() => _customerData.branchNumber = value!),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child:
                    _buildDisplayField('اسم الفرع', _customerData.branchName),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // الصف الثالث
          Row(
            children: [
              Expanded(
                child: _buildDropdownField(
                  'مستوى السرية',
                  _customerData.securityLevel,
                  ['عادي', 'سري', 'سري جداً'],
                  (value) =>
                      setState(() => _customerData.securityLevel = value!),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDropdownField(
                  'طريقة الدفع الافتراضية',
                  _customerData.defaultPaymentMethod,
                  ['نقدي', 'آجل', 'شيك', 'تحويل بنكي'],
                  (value) => setState(
                      () => _customerData.defaultPaymentMethod = value!),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // مربعات الاختيار
          _buildCheckboxField(
            'إلغاء ربط بالمندوب',
            _customerData.unlinkFromSalesman,
            (value) =>
                setState(() => _customerData.unlinkFromSalesman = value!),
          ),

          const SizedBox(height: 8),

          _buildCheckboxField(
            'حساب رئيسي',
            _customerData.isMainAccount,
            (value) => setState(() => _customerData.isMainAccount = value!),
          ),

          const SizedBox(height: 8),

          _buildCheckboxField(
            'تفعيل خدمة إشعار برسالة SMS',
            _customerData.enableSmsNotification,
            (value) =>
                setState(() => _customerData.enableSmsNotification = value!),
          ),
        ],
      ),
    );
  }

  /// التبويب الثاني: معلومات عامة
  Widget _buildGeneralInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('معلومات عامة'),
          const SizedBox(height: 16),

          // الأسماء
          Row(
            children: [
              Expanded(
                child: _buildTextFormField(
                  'اسم الحساب بالعربي',
                  _controllers['arabicName']!,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTextFormField(
                  'الاسم الإنجليزي',
                  _controllers['englishName']!,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // المسؤول عن الحساب
          _buildContactSection(
            'اسم الشخص المسؤول عن الحساب',
            'accountManager',
          ),

          const SizedBox(height: 16),

          // المسؤول عن المشتريات
          _buildContactSection(
            'اسم الشخص المسؤول عن المشتريات',
            'purchaseManager',
          ),
        ],
      ),
    );
  }

  /// التبويب الثالث: معلومات محاسبية
  Widget _buildAccountingInfoTab() {
    return const Center(
      child: Text('معلومات محاسبية - قيد التطوير'),
    );
  }

  /// التبويب الرابع: تقرير حافز العميل
  Widget _buildCustomerIncentiveTab() {
    return Container(
      color: Colors.grey[50],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // بطاقة العميل الرئيسية
            _buildCustomerCard(),

            const SizedBox(height: 20),

            // شبكة البيانات المالية
            Expanded(
              child: _buildFinancialDataGrid(),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة معلومات العميل
  Widget _buildCustomerCard() {
    // استخدام البيانات المستعلم عنها أو البيانات الافتراضية
    String customerName =
        _currentIncentiveData?['customer'] ?? 'شركة الأمل للتجارة';
    String accountNumber = _currentIncentiveData?['accountNumber'] ?? 'CUST001';
    String year = _currentIncentiveData?['year'] ?? '2024';

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 2,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // رقم التسلسل
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: Colors.blue[600],
              shape: BoxShape.circle,
            ),
            child: const Center(
              child: Text(
                '1',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),

          const SizedBox(width: 20),

          // معلومات العميل
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  customerName,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'رقم الحساب: $accountNumber',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'السنة المالية: $year',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء شبكة البيانات المالية
  Widget _buildFinancialDataGrid() {
    // استخدام البيانات المستعلم عنها أو البيانات الافتراضية
    double totalSales =
        _currentIncentiveData?['totalSales']?.toDouble() ?? 850000.00;
    double returns = _currentIncentiveData?['returns']?.toDouble() ?? 25000.00;
    double discountNotifications =
        _currentIncentiveData?['discountNotifications']?.toDouble() ?? 15000.00;
    double discountedInvoices =
        _currentIncentiveData?['discountedInvoices']?.toDouble() ?? 35000.00;
    double netSales =
        _currentIncentiveData?['netSales']?.toDouble() ?? 775000.00;
    double paidWithin90Days =
        _currentIncentiveData?['paidWithin90Days']?.toDouble() ?? 720000.00;

    return Column(
      children: [
        // الصف الأول
        Row(
          children: [
            Expanded(
              child: _buildDataCard(
                'المرتجعات',
                returns.toStringAsFixed(2),
                Colors.red[100]!,
                Colors.red[600]!,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDataCard(
                'إجمالي المبيعات',
                totalSales.toStringAsFixed(2),
                Colors.green[100]!,
                Colors.green[600]!,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // الصف الثاني
        Row(
          children: [
            Expanded(
              child: _buildDataCard(
                'الفواتير المخفضة',
                discountedInvoices.toStringAsFixed(2),
                Colors.purple[100]!,
                Colors.purple[600]!,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDataCard(
                'إشعارات الخصم',
                discountNotifications.toStringAsFixed(2),
                Colors.orange[100]!,
                Colors.orange[600]!,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // الصف الثالث
        Row(
          children: [
            Expanded(
              child: _buildDataCard(
                'المسدد خلال 90 يوم',
                paidWithin90Days.toStringAsFixed(2),
                Colors.teal[100]!,
                Colors.teal[600]!,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDataCard(
                'صافي المبيعات',
                netSales.toStringAsFixed(2),
                Colors.blue[100]!,
                Colors.blue[600]!,
              ),
            ),
          ],
        ),

        const SizedBox(height: 20),

        // بطاقة الحافز المميزة
        _buildIncentiveCard(),
      ],
    );
  }

  /// بناء بطاقة بيانات واحدة
  Widget _buildDataCard(
      String title, String amount, Color bgColor, Color textColor) {
    return Container(
      height: 100,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              color: textColor,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.right,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                'ر.س',
                style: TextStyle(
                  fontSize: 12,
                  color: textColor.withValues(alpha: 0.7),
                ),
              ),
              const SizedBox(width: 4),
              Text(
                amount,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: textColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة الحافز المميزة
  Widget _buildIncentiveCard() {
    // استخدام البيانات المستعلم عنها أو البيانات الافتراضية
    double incentive =
        _currentIncentiveData?['incentive']?.toDouble() ?? 38750.00;

    return Container(
      width: double.infinity,
      height: 80,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.green[400]!, Colors.green[600]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const Text(
            'الحافز',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          Row(
            children: [
              const Text(
                'ر.س',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                incentive.toStringAsFixed(2),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// التبويب الخامس: التقارير
  Widget _buildReportsTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.assessment,
            size: 64,
            color: Colors.green,
          ),
          SizedBox(height: 16),
          Text(
            'التقارير',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          Text('قيد التطوير'),
        ],
      ),
    );
  }

  /// التبويب السادس: معلومات المستخدم
  Widget _buildUserInfoTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person,
            size: 64,
            color: Colors.orange,
          ),
          SizedBox(height: 16),
          Text(
            'معلومات المستخدم',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          Text('اسم المستخدم: admin'),
          Text('تاريخ التعديل: 2024-01-15'),
        ],
      ),
    );
  }

  /// التبويب السابع: إضافي
  Widget _buildAdditionalTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.more_horiz,
            size: 64,
            color: Colors.purple,
          ),
          SizedBox(height: 16),
          Text(
            'معلومات إضافية',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          Text('قيد التطوير'),
        ],
      ),
    );
  }

  // ===== دوال مساعدة لبناء عناصر الواجهة =====

  /// بناء عنوان القسم
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Colors.blue[700],
      ),
    );
  }

  /// بناء حقل قائمة منسدلة
  Widget _buildDropdownField(
    String label,
    String value,
    List<String> items,
    ValueChanged<String?> onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: items.contains(value) ? value : items.first,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: items.map((String item) {
            return DropdownMenuItem<String>(
              value: item,
              child: Text(item),
            );
          }).toList(),
          onChanged: onChanged,
        ),
      ],
    );
  }

  /// بناء حقل عرض فقط
  Widget _buildDisplayField(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[400]!),
            borderRadius: BorderRadius.circular(8),
            color: Colors.grey[100],
          ),
          child: Text(
            value.isEmpty ? 'غير محدد' : value,
            style: TextStyle(
              color: value.isEmpty ? Colors.grey[600] : Colors.black,
            ),
          ),
        ),
      ],
    );
  }

  /// بناء مربع اختيار
  Widget _buildCheckboxField(
    String label,
    bool value,
    ValueChanged<bool?> onChanged,
  ) {
    return IntrinsicWidth(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Checkbox(
            value: value,
            onChanged: onChanged,
            activeColor: Colors.blue[700],
          ),
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حقل إدخال نص
  Widget _buildTextFormField(
    String label,
    TextEditingController controller, {
    TextInputType keyboardType = TextInputType.text,
    int maxLines = 1,
    String? suffix,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          maxLines: maxLines,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            suffixText: suffix,
          ),
        ),
      ],
    );
  }

  /// بناء قسم معلومات الاتصال
  Widget _buildContactSection(String title, String prefix) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 12),
        IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 2,
                child: _buildTextFormField(
                  'الاسم',
                  _controllers['${prefix}Name']!,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildTextFormField(
                  'رقم الجوال',
                  _controllers['${prefix}Phone']!,
                  keyboardType: TextInputType.phone,
                ),
              ),
              const SizedBox(width: 12),
              Container(
                padding: const EdgeInsets.only(top: 24),
                child: _buildCheckboxField(
                  'SMS',
                  _getContactSmsValue(prefix),
                  (value) => _setContactSmsValue(prefix, value!),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// الحصول على قيمة SMS للمسؤول
  bool _getContactSmsValue(String prefix) {
    switch (prefix) {
      case 'accountManager':
        return _customerData.accountManagerSms;
      case 'purchaseManager':
        return _customerData.purchaseManagerSms;
      case 'financeManager':
        return _customerData.financeManagerSms;
      case 'receivingManager':
        return _customerData.receivingManagerSms;
      default:
        return false;
    }
  }

  /// تعيين قيمة SMS للمسؤول
  void _setContactSmsValue(String prefix, bool value) {
    setState(() {
      switch (prefix) {
        case 'accountManager':
          _customerData.accountManagerSms = value;
          break;
        case 'purchaseManager':
          _customerData.purchaseManagerSms = value;
          break;
        case 'financeManager':
          _customerData.financeManagerSms = value;
          break;
        case 'receivingManager':
          _customerData.receivingManagerSms = value;
          break;
      }
    });
  }

  // ===== دوال الأزرار المخصصة لكل تبويب =====

  // دوال تبويب "تقرير حافز العميل" (التبويب الرابع)

  /// إضافة معلومات حافز العميل
  void _addIncentiveInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة معلومات حافز العميل'),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                decoration: const InputDecoration(
                  labelText: 'السنة',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                decoration: const InputDecoration(
                  labelText: 'إجمالي المبيعات',
                  border: OutlineInputBorder(),
                  suffixText: 'ر.س',
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              TextField(
                decoration: const InputDecoration(
                  labelText: 'المرتجعات',
                  border: OutlineInputBorder(),
                  suffixText: 'ر.س',
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              TextField(
                decoration: const InputDecoration(
                  labelText: 'إشعارات الخصم',
                  border: OutlineInputBorder(),
                  suffixText: 'ر.س',
                ),
                keyboardType: TextInputType.number,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إضافة معلومات الحافز بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  /// استعلام عن معلومات حافز العميل
  void _queryIncentiveInfo() {
    _showIncentiveQueryDialog();
  }

  /// عرض نافذة الاستعلام الجديدة
  void _showIncentiveQueryDialog() {
    // إعادة تعيين المتغيرات
    _searchController.clear();
    _selectedCustomer = null;
    _incentiveRows = [];
    _canSaveOrEdit = false;
    _searchResults = [];
    _showSearchResults = false;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('إدارة حوافز العملاء'),
          content: SizedBox(
            width: 700,
            height: 500,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // مربع البحث عن العميل
                _buildCustomerSearchField(setDialogState),

                const SizedBox(height: 16),

                // معلومات العميل المختار
                if (_selectedCustomer != null) _buildSelectedCustomerInfo(),

                const SizedBox(height: 16),

                // جدول الحوافز
                const Text(
                  'جدول الحوافز',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),

                const SizedBox(height: 8),

                Expanded(
                  child: _buildIncentiveTable(setDialogState),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: _canSaveOrEdit
                  ? () => _saveIncentiveData(setDialogState)
                  : null,
              child: const Text('حفظ'),
            ),
            ElevatedButton(
              onPressed: _canSaveOrEdit
                  ? () => _editIncentiveData(setDialogState)
                  : null,
              child: const Text('تعديل'),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حقل البحث عن العميل
  Widget _buildCustomerSearchField(StateSetter setDialogState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'البحث عن العميل',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),

        // حقل البحث
        TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'ابحث برقم الحساب أو اسم العميل',
            border: const OutlineInputBorder(),
            prefixIcon: const Icon(Icons.search),
            suffixIcon: _searchController.text.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      setDialogState(() {
                        _searchController.clear();
                        _selectedCustomer = null;
                        _incentiveRows = [];
                        _canSaveOrEdit = false;
                        _searchResults = [];
                        _showSearchResults = false;
                      });
                    },
                  )
                : null,
          ),
          onChanged: (value) {
            setDialogState(() {
              if (value.isNotEmpty) {
                // البحث في قائمة العملاء
                _searchResults = _customers.where((customer) {
                  return customer['accountNumber'].toString().contains(value) ||
                      customer['name']
                          .toString()
                          .toLowerCase()
                          .contains(value.toLowerCase());
                }).toList();

                _showSearchResults = _searchResults.isNotEmpty;

                // إذا كان هناك تطابق تام، اختر العميل مباشرة
                if (_searchResults.length == 1) {
                  _selectCustomer(_searchResults.first, setDialogState);
                }
              } else {
                _selectedCustomer = null;
                _incentiveRows = [];
                _canSaveOrEdit = false;
                _searchResults = [];
                _showSearchResults = false;
              }
            });
          },
        ),

        // نتائج البحث
        if (_showSearchResults && _searchResults.isNotEmpty)
          Container(
            margin: const EdgeInsets.only(top: 4),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8),
              color: Colors.white,
            ),
            constraints: const BoxConstraints(maxHeight: 200),
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: _searchResults.length,
              itemBuilder: (context, index) {
                final customer = _searchResults[index];
                return ListTile(
                  dense: true,
                  leading: Icon(Icons.person, color: Colors.blue[600]),
                  title: Text(
                    customer['name'],
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  subtitle: Text(
                    'رقم الحساب: ${customer['accountNumber']}',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                  onTap: () {
                    _selectCustomer(customer, setDialogState);
                  },
                  hoverColor: Colors.blue[50],
                );
              },
            ),
          ),
      ],
    );
  }

  /// بناء معلومات العميل المختار
  Widget _buildSelectedCustomerInfo() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Row(
        children: [
          Icon(Icons.person, color: Colors.blue[700]),
          const SizedBox(width: 8),
          Text(
            'العميل: ${_selectedCustomer!['name']}',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(width: 16),
          Text(
            'رقم الحساب: ${_selectedCustomer!['accountNumber']}',
            style: TextStyle(color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  /// بناء جدول الحوافز
  Widget _buildIncentiveTable(StateSetter setDialogState) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // رأس الجدول
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: const Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    'المبلغ من',
                    style: TextStyle(fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'المبلغ إلى',
                    style: TextStyle(fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'الحافز',
                    style: TextStyle(fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'إجراءات',
                    style: TextStyle(fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),

          // صفوف الجدول
          Expanded(
            child: _selectedCustomer == null
                ? const Center(
                    child: Text(
                      'يرجى اختيار عميل أولاً',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 16,
                      ),
                    ),
                  )
                : ListView.builder(
                    itemCount: _incentiveRows.length + 1,
                    itemBuilder: (context, index) {
                      if (index == _incentiveRows.length) {
                        // صف إضافة جديد
                        return _buildAddNewRowButton(setDialogState);
                      }
                      return _buildIncentiveRow(index, setDialogState);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  /// اختيار العميل
  void _selectCustomer(
      Map<String, dynamic> customer, StateSetter setDialogState) {
    setDialogState(() {
      _selectedCustomer = customer;
      _searchController.text =
          '${customer['accountNumber']} - ${customer['name']}';
      _showSearchResults = false;
      _searchResults = [];
      _loadCustomerIncentiveData();
    });
  }

  /// تحميل بيانات حوافز العميل
  void _loadCustomerIncentiveData() {
    // هنا يمكن تحميل البيانات الموجودة للعميل
    // حالياً سنبدأ بصف فارغ
    _incentiveRows = [
      {
        'amountFrom': TextEditingController(),
        'amountTo': TextEditingController(),
        'incentive': TextEditingController(),
      }
    ];
    _checkCanSaveOrEdit();
  }

  /// بناء صف الحافز
  Widget _buildIncentiveRow(int index, StateSetter setDialogState) {
    final row = _incentiveRows[index];
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        children: [
          // المبلغ من
          Expanded(
            flex: 2,
            child: TextField(
              controller: row['amountFrom'],
              decoration: const InputDecoration(
                hintText: '0.00',
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              ),
              keyboardType: TextInputType.number,
              onChanged: (value) {
                setDialogState(() {
                  _checkCanSaveOrEdit();
                });
              },
            ),
          ),

          const SizedBox(width: 8),

          // المبلغ إلى
          Expanded(
            flex: 2,
            child: TextField(
              controller: row['amountTo'],
              decoration: const InputDecoration(
                hintText: '0.00',
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              ),
              keyboardType: TextInputType.number,
              onChanged: (value) {
                setDialogState(() {
                  _checkCanSaveOrEdit();
                });
              },
            ),
          ),

          const SizedBox(width: 8),

          // الحافز
          Expanded(
            flex: 2,
            child: TextField(
              controller: row['incentive'],
              decoration: const InputDecoration(
                hintText: '0.00',
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              ),
              keyboardType: TextInputType.number,
              onChanged: (value) {
                setDialogState(() {
                  _checkCanSaveOrEdit();
                });
              },
            ),
          ),

          const SizedBox(width: 8),

          // أزرار الإجراءات
          Expanded(
            flex: 1,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // زر الإضافة
                IconButton(
                  onPressed: () {
                    setDialogState(() {
                      _incentiveRows.add({
                        'amountFrom': TextEditingController(),
                        'amountTo': TextEditingController(),
                        'incentive': TextEditingController(),
                      });
                    });
                  },
                  icon: const Icon(Icons.add_circle, color: Colors.green),
                  tooltip: 'إضافة صف جديد',
                ),

                // زر الحذف (إذا كان هناك أكثر من صف واحد)
                if (_incentiveRows.length > 1)
                  IconButton(
                    onPressed: () {
                      setDialogState(() {
                        // حذف الصف الحالي
                        if (index < _incentiveRows.length) {
                          // تنظيف المتحكمات
                          _incentiveRows[index]['amountFrom']?.dispose();
                          _incentiveRows[index]['amountTo']?.dispose();
                          _incentiveRows[index]['incentive']?.dispose();

                          _incentiveRows.removeAt(index);
                          _checkCanSaveOrEdit();
                        }
                      });
                    },
                    icon: const Icon(Icons.remove_circle, color: Colors.red),
                    tooltip: 'حذف الصف',
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء زر إضافة صف جديد
  Widget _buildAddNewRowButton(StateSetter setDialogState) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Center(
        child: ElevatedButton.icon(
          onPressed: () {
            setDialogState(() {
              _incentiveRows.add({
                'amountFrom': TextEditingController(),
                'amountTo': TextEditingController(),
                'incentive': TextEditingController(),
              });
            });
          },
          icon: const Icon(Icons.add),
          label: const Text('إضافة صف جديد'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green[100],
            foregroundColor: Colors.green[700],
          ),
        ),
      ),
    );
  }

  /// فحص إمكانية الحفظ أو التعديل
  void _checkCanSaveOrEdit() {
    bool hasData = false;

    for (var row in _incentiveRows) {
      if (row['amountFrom'].text.isNotEmpty ||
          row['amountTo'].text.isNotEmpty ||
          row['incentive'].text.isNotEmpty) {
        hasData = true;
        break;
      }
    }

    _canSaveOrEdit = _selectedCustomer != null && hasData;
  }

  /// حفظ بيانات الحافز
  void _saveIncentiveData(StateSetter setDialogState) {
    if (_selectedCustomer == null) return;

    // هنا يتم حفظ البيانات في قاعدة البيانات
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content:
            Text('تم حفظ بيانات حافز العميل ${_selectedCustomer!['name']}'),
        backgroundColor: Colors.green,
      ),
    );

    Navigator.pop(context);
  }

  /// تعديل بيانات الحافز
  void _editIncentiveData(StateSetter setDialogState) {
    if (_selectedCustomer == null) return;

    // هنا يتم تحديث البيانات في قاعدة البيانات
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content:
            Text('تم تحديث بيانات حافز العميل ${_selectedCustomer!['name']}'),
        backgroundColor: Colors.blue,
      ),
    );

    // تحديث البيانات في التبويب
    setState(() {
      // تحديث البيانات المعروضة
    });

    Navigator.pop(context);
  }

  /// حذف معلومات حافز العميل
  void _deleteIncentiveInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف معلومات الحافز'),
        content: const Text('هل أنت متأكد من حذف معلومات حافز هذا العميل؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم حذف معلومات الحافز'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// تراجع عن تغييرات حافز العميل
  void _undoIncentiveInfo() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم التراجع عن تغييرات حافز العميل'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  /// حفظ معلومات حافز العميل
  void _saveIncentiveInfo() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم حفظ معلومات حافز العميل'),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// طباعة تقرير حافز العميل
  void _printIncentiveInfo() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري طباعة تقرير حافز العميل'),
        backgroundColor: Colors.purple,
      ),
    );
  }

  // دوال باقي التبويبات (مبسطة)

  // دوال التبويب الأول: المعلومات الأساسية
  void _addBasicInfo() => _addNewCustomer();
  void _queryBasicInfo() => _queryCustomer();
  void _deleteBasicInfo() => _deleteCustomer();
  void _undoBasicInfo() => _undoChanges();
  void _saveBasicInfo() => _saveCustomer();
  void _printBasicInfo() => _printCustomer();

  // دوال التبويب الثاني: معلومات عامة
  void _addGeneralInfo() => _addNewCustomer();
  void _queryGeneralInfo() => _queryCustomer();
  void _deleteGeneralInfo() => _deleteCustomer();
  void _undoGeneralInfo() => _undoChanges();
  void _saveGeneralInfo() => _saveCustomer();
  void _printGeneralInfo() => _printCustomer();

  // دوال التبويب الثالث: معلومات محاسبية
  void _addAccountingInfo() => _addNewCustomer();
  void _queryAccountingInfo() => _queryCustomer();
  void _deleteAccountingInfo() => _deleteCustomer();
  void _undoAccountingInfo() => _undoChanges();
  void _saveAccountingInfo() => _saveCustomer();
  void _printAccountingInfo() => _printCustomer();

  // دوال التبويب الخامس: التقارير
  void _addReportInfo() => _addNewCustomer();
  void _queryReportInfo() => _queryCustomer();
  void _deleteReportInfo() => _deleteCustomer();
  void _undoReportInfo() => _undoChanges();
  void _saveReportInfo() => _saveCustomer();
  void _printReportInfo() => _printCustomer();

  // دوال التبويب السادس: معلومات المستخدم
  void _addUserInfo() => _addNewCustomer();
  void _queryUserInfo() => _queryCustomer();
  void _deleteUserInfo() => _deleteCustomer();
  void _undoUserInfo() => _undoChanges();
  void _saveUserInfo() => _saveCustomer();
  void _printUserInfo() => _printCustomer();

  // دوال التبويب السابع: إضافي
  void _addAdditionalInfo() => _addNewCustomer();
  void _queryAdditionalInfo() => _queryCustomer();
  void _deleteAdditionalInfo() => _deleteCustomer();
  void _undoAdditionalInfo() => _undoChanges();
  void _saveAdditionalInfo() => _saveCustomer();
  void _printAdditionalInfo() => _printCustomer();
}
