import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير عمليات الأصناف
/// تعرض جميع العمليات التي تمت على الأصناف
class ItemsOperationsReportPage extends StatefulWidget {
  const ItemsOperationsReportPage({super.key});

  @override
  State<ItemsOperationsReportPage> createState() =>
      _ItemsOperationsReportPageState();
}

class _ItemsOperationsReportPageState extends State<ItemsOperationsReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedOperation;
  String? _selectedWarehouse;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.itemsOperationsReport),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.operationType,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedOperation,
                        items: const [
                          DropdownMenuItem(
                              value: 'all', child: Text('جميع العمليات')),
                          DropdownMenuItem(
                              value: 'purchase', child: Text('مشتريات')),
                          DropdownMenuItem(
                              value: 'sales', child: Text('مبيعات')),
                          DropdownMenuItem(
                              value: 'transfer', child: Text('تحويلات')),
                          DropdownMenuItem(
                              value: 'adjustment', child: Text('تسويات')),
                          DropdownMenuItem(
                              value: 'return', child: Text('مرتجعات')),
                        ],
                        onChanged: (value) =>
                            setState(() => _selectedOperation = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.warehouse,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedWarehouse,
                        items: const [
                          DropdownMenuItem(
                              value: 'all', child: Text('جميع المستودعات')),
                          DropdownMenuItem(
                              value: 'main', child: Text('المستودع الرئيسي')),
                          DropdownMenuItem(
                              value: 'branch1',
                              child: Text('مستودع الفرع الأول')),
                        ],
                        onChanged: (value) =>
                            setState(() => _selectedWarehouse = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.fromDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, true),
                        controller: TextEditingController(
                          text: _startDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.toDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, false),
                        controller: TextEditingController(
                          text: _endDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.search),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.teal,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // إحصائيات العمليات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إحصائيات العمليات',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildOperationCard('مشتريات', '45', Colors.green,
                                  Icons.shopping_cart),
                              _buildOperationCard(
                                  'مبيعات', '78', Colors.blue, Icons.sell),
                              _buildOperationCard('تحويلات', '12',
                                  Colors.orange, Icons.swap_horiz),
                              _buildOperationCard(
                                  'تسويات', '8', Colors.purple, Icons.tune),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول العمليات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل العمليات',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('التاريخ')),
                                DataColumn(label: Text('نوع العملية')),
                                DataColumn(label: Text('رقم المستند')),
                                DataColumn(label: Text('كود الصنف')),
                                DataColumn(label: Text('اسم الصنف')),
                                DataColumn(label: Text('الكمية')),
                                DataColumn(label: Text('السعر')),
                                DataColumn(label: Text('القيمة')),
                                DataColumn(label: Text('المستودع')),
                                DataColumn(label: Text('المستخدم')),
                              ],
                              rows: _buildOperationRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // ملخص القيم
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'ملخص القيم',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSummaryCard('إجمالي المشتريات',
                                  '125,000 ر.س', Colors.green),
                              _buildSummaryCard('إجمالي المبيعات',
                                  '180,000 ر.س', Colors.blue),
                              _buildSummaryCard('إجمالي التحويلات',
                                  '25,000 ر.س', Colors.orange),
                              _buildSummaryCard(
                                  'صافي الحركة', '55,000 ر.س', Colors.purple),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOperationCard(
      String title, String count, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                count,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<DataRow> _buildOperationRows() {
    return [
      const DataRow(cells: [
        DataCell(Text('2024-01-15')),
        DataCell(Text('مشتريات')),
        DataCell(Text('PO-001')),
        DataCell(Text('001')),
        DataCell(Text('جهاز كمبيوتر محمول')),
        DataCell(Text('5')),
        DataCell(Text('2,500.00')),
        DataCell(Text('12,500.00')),
        DataCell(Text('المستودع الرئيسي')),
        DataCell(Text('أحمد محمد')),
      ]),
      const DataRow(cells: [
        DataCell(Text('2024-01-16')),
        DataCell(Text('مبيعات')),
        DataCell(Text('INV-001')),
        DataCell(Text('001')),
        DataCell(Text('جهاز كمبيوتر محمول')),
        DataCell(Text('2')),
        DataCell(Text('3,200.00')),
        DataCell(Text('6,400.00')),
        DataCell(Text('المستودع الرئيسي')),
        DataCell(Text('سارة أحمد')),
      ]),
      const DataRow(cells: [
        DataCell(Text('2024-01-17')),
        DataCell(Text('تحويل')),
        DataCell(Text('TR-001')),
        DataCell(Text('002')),
        DataCell(Text('طابعة ليزر')),
        DataCell(Text('3')),
        DataCell(Text('800.00')),
        DataCell(Text('2,400.00')),
        DataCell(Text('مستودع الفرع الأول')),
        DataCell(Text('محمد علي')),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء التقرير بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }
}
