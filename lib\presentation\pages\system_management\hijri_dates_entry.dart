import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة إدخال التواريخ الهجرية
/// تتيح إدخال وتحويل التواريخ بين الهجري والميلادي
class HijriDatesEntryPage extends StatefulWidget {
  const HijriDatesEntryPage({super.key});

  @override
  State<HijriDatesEntryPage> createState() => _HijriDatesEntryPageState();
}

class _HijriDatesEntryPageState extends State<HijriDatesEntryPage> {
  final _formKey = GlobalKey<FormState>();
  final _hijriDayController = TextEditingController();
  final _hijriMonthController = TextEditingController();
  final _hijriYearController = TextEditingController();
  final _gregorianDateController = TextEditingController();
  final _notesController = TextEditingController();

  String _conversionDirection =
      'hijri_to_gregorian'; // hijri_to_gregorian or gregorian_to_hijri
  DateTime? _selectedGregorianDate;
  bool _isProcessing = false;

  final List<String> _hijriMonths = [
    'محرم',
    'صفر',
    'ربيع الأول',
    'ربيع الآخر',
    'جمادى الأولى',
    'جمادى الآخرة',
    'رجب',
    'شعبان',
    'رمضان',
    'شوال',
    'ذو القعدة',
    'ذو الحجة'
  ];

  final List<Map<String, dynamic>> _savedDates = [
    {
      'id': 1,
      'hijri': '15 رمضان 1445',
      'gregorian': '2024/03/25',
      'notes': 'بداية العشر الأواخر من رمضان',
      'type': 'religious'
    },
    {
      'id': 2,
      'hijri': '1 شوال 1445',
      'gregorian': '2024/04/10',
      'notes': 'عيد الفطر المبارك',
      'type': 'holiday'
    },
    {
      'id': 3,
      'hijri': '10 ذو الحجة 1445',
      'gregorian': '2024/06/16',
      'notes': 'عيد الأضحى المبارك',
      'type': 'holiday'
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.hijriDatesEntry),
        backgroundColor: Colors.deepOrange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.calendar_view_month),
            onPressed: _showHijriCalendar,
            tooltip: 'التقويم الهجري',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // بطاقة اختيار نوع التحويل
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'نوع التحويل',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.deepOrange,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: RadioListTile<String>(
                            title: const Text('هجري إلى ميلادي'),
                            subtitle:
                                const Text('تحويل التاريخ الهجري إلى ميلادي'),
                            value: 'hijri_to_gregorian',
                            groupValue: _conversionDirection,
                            onChanged: (value) {
                              setState(() {
                                _conversionDirection = value!;
                                _clearFields();
                              });
                            },
                          ),
                        ),
                        Expanded(
                          child: RadioListTile<String>(
                            title: const Text('ميلادي إلى هجري'),
                            subtitle:
                                const Text('تحويل التاريخ الميلادي إلى هجري'),
                            value: 'gregorian_to_hijri',
                            groupValue: _conversionDirection,
                            onChanged: (value) {
                              setState(() {
                                _conversionDirection = value!;
                                _clearFields();
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة إدخال التاريخ
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _conversionDirection == 'hijri_to_gregorian'
                          ? 'إدخال التاريخ الهجري'
                          : 'إدخال التاريخ الميلادي',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.deepOrange,
                      ),
                    ),
                    const SizedBox(height: 16),

                    if (_conversionDirection == 'hijri_to_gregorian') ...[
                      // إدخال التاريخ الهجري
                      Row(
                        children: [
                          // اليوم
                          Expanded(
                            child: TextFormField(
                              controller: _hijriDayController,
                              decoration: const InputDecoration(
                                labelText: 'اليوم',
                                hintText: '1-30',
                                prefixIcon: Icon(Icons.today),
                                border: OutlineInputBorder(),
                              ),
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'مطلوب';
                                }
                                final day = int.tryParse(value);
                                if (day == null || day < 1 || day > 30) {
                                  return 'يوم غير صحيح';
                                }
                                return null;
                              },
                            ),
                          ),

                          const SizedBox(width: 16),

                          // الشهر
                          Expanded(
                            flex: 2,
                            child: DropdownButtonFormField<String>(
                              value: _hijriMonthController.text.isEmpty
                                  ? null
                                  : _hijriMonthController.text,
                              decoration: const InputDecoration(
                                labelText: 'الشهر الهجري',
                                prefixIcon: Icon(Icons.calendar_month),
                                border: OutlineInputBorder(),
                              ),
                              items: _hijriMonths.asMap().entries.map((entry) {
                                return DropdownMenuItem<String>(
                                  value: entry.value,
                                  child:
                                      Text('${entry.key + 1}. ${entry.value}'),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _hijriMonthController.text = value ?? '';
                                });
                              },
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'يرجى اختيار الشهر';
                                }
                                return null;
                              },
                            ),
                          ),

                          const SizedBox(width: 16),

                          // السنة
                          Expanded(
                            child: TextFormField(
                              controller: _hijriYearController,
                              decoration: const InputDecoration(
                                labelText: 'السنة الهجرية',
                                hintText: '1445',
                                prefixIcon: Icon(Icons.date_range),
                                border: OutlineInputBorder(),
                              ),
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'مطلوب';
                                }
                                final year = int.tryParse(value);
                                if (year == null ||
                                    year < 1400 ||
                                    year > 1500) {
                                  return 'سنة غير صحيحة';
                                }
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),
                    ] else ...[
                      // إدخال التاريخ الميلادي
                      ListTile(
                        leading: const Icon(Icons.calendar_today),
                        title: const Text('التاريخ الميلادي'),
                        subtitle: Text(_selectedGregorianDate != null
                            ? '${_selectedGregorianDate!.day}/${_selectedGregorianDate!.month}/${_selectedGregorianDate!.year}'
                            : 'اختر التاريخ'),
                        trailing: const Icon(Icons.edit),
                        onTap: _selectGregorianDate,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                          side: BorderSide(color: Colors.grey.shade300),
                        ),
                      ),
                    ],

                    const SizedBox(height: 16),

                    // ملاحظات
                    TextFormField(
                      controller: _notesController,
                      decoration: const InputDecoration(
                        labelText: 'ملاحظات (اختياري)',
                        hintText: 'أدخل أي ملاحظات حول هذا التاريخ',
                        prefixIcon: Icon(Icons.note),
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 2,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة النتيجة
            Card(
              color: Colors.deepOrange.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'نتيجة التحويل',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.deepOrange,
                      ),
                    ),
                    const SizedBox(height: 12),
                    if (_conversionDirection == 'hijri_to_gregorian') ...[
                      _buildResultRow('التاريخ الهجري:', _getHijriDateString()),
                      _buildResultRow(
                          'التاريخ الميلادي:', _getConvertedGregorianDate()),
                    ] else ...[
                      _buildResultRow(
                          'التاريخ الميلادي:', _getGregorianDateString()),
                      _buildResultRow(
                          'التاريخ الهجري:', _getConvertedHijriDate()),
                    ],
                    _buildResultRow('اليوم:', _getDayOfWeek()),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة التواريخ المحفوظة
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'التواريخ المحفوظة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.deepOrange,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ...(_savedDates.map((date) => ListTile(
                          leading: CircleAvatar(
                            backgroundColor: date['type'] == 'religious'
                                ? Colors.green
                                : Colors.blue,
                            child: Icon(
                              date['type'] == 'religious'
                                  ? Icons.mosque
                                  : Icons.celebration,
                              color: Colors.white,
                            ),
                          ),
                          title:
                              Text('${date['hijri']} - ${date['gregorian']}'),
                          subtitle: Text(date['notes']),
                          trailing: IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red),
                            onPressed: () => _deleteSavedDate(date['id']),
                          ),
                        ))),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // أزرار العمليات
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isProcessing ? null : _convertDate,
                    icon: _isProcessing
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.transform),
                    label: Text(
                        _isProcessing ? 'جاري التحويل...' : 'تحويل التاريخ'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.deepOrange,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _saveDate,
                    icon: const Icon(Icons.save),
                    label: const Text('حفظ التاريخ'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  String _getHijriDateString() {
    if (_hijriDayController.text.isEmpty ||
        _hijriMonthController.text.isEmpty ||
        _hijriYearController.text.isEmpty) {
      return 'غير محدد';
    }
    return '${_hijriDayController.text} ${_hijriMonthController.text} ${_hijriYearController.text}';
  }

  String _getGregorianDateString() {
    if (_selectedGregorianDate == null) return 'غير محدد';
    return '${_selectedGregorianDate!.day}/${_selectedGregorianDate!.month}/${_selectedGregorianDate!.year}';
  }

  String _getConvertedGregorianDate() {
    // محاكاة تحويل التاريخ الهجري إلى ميلادي
    if (_hijriDayController.text.isEmpty ||
        _hijriMonthController.text.isEmpty ||
        _hijriYearController.text.isEmpty) {
      return 'غير محدد';
    }
    return '25/03/2024'; // مثال
  }

  String _getConvertedHijriDate() {
    // محاكاة تحويل التاريخ الميلادي إلى هجري
    if (_selectedGregorianDate == null) return 'غير محدد';
    return '15 رمضان 1445'; // مثال
  }

  String _getDayOfWeek() {
    if (_conversionDirection == 'hijri_to_gregorian') {
      if (_hijriDayController.text.isEmpty) return 'غير محدد';
      return 'الاثنين'; // مثال
    } else {
      if (_selectedGregorianDate == null) return 'غير محدد';
      final weekdays = [
        'الأحد',
        'الاثنين',
        'الثلاثاء',
        'الأربعاء',
        'الخميس',
        'الجمعة',
        'السبت'
      ];
      return weekdays[_selectedGregorianDate!.weekday % 7];
    }
  }

  Future<void> _selectGregorianDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedGregorianDate ?? DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime(2100),
    );
    if (picked != null) {
      setState(() {
        _selectedGregorianDate = picked;
      });
    }
  }

  Future<void> _convertDate() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isProcessing = true;
      });

      // محاكاة عملية التحويل
      await Future.delayed(const Duration(seconds: 1));

      setState(() {
        _isProcessing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحويل التاريخ بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  void _saveDate() {
    if (_formKey.currentState!.validate()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حفظ التاريخ بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _clearFields() {
    _hijriDayController.clear();
    _hijriMonthController.clear();
    _hijriYearController.clear();
    _selectedGregorianDate = null;
    _notesController.clear();
  }

  void _deleteSavedDate(int id) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم حذف التاريخ رقم $id')),
    );
  }

  void _showHijriCalendar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض التقويم الهجري')),
    );
  }

  @override
  void dispose() {
    _hijriDayController.dispose();
    _hijriMonthController.dispose();
    _hijriYearController.dispose();
    _gregorianDateController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
