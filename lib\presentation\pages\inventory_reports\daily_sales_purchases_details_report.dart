import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير تفاصيل المبيعات والمشتريات اليومية
/// يعرض تفاصيل المبيعات والمشتريات لكل يوم
class DailySalesPurchasesDetailsReportPage extends StatefulWidget {
  const DailySalesPurchasesDetailsReportPage({super.key});

  @override
  State<DailySalesPurchasesDetailsReportPage> createState() => _DailySalesPurchasesDetailsReportPageState();
}

class _DailySalesPurchasesDetailsReportPageState extends State<DailySalesPurchasesDetailsReportPage> {
  DateTime? _selectedDate;
  String? _selectedBranch;
  String? _reportType = 'both';
  String? _selectedEmployee;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل المبيعات والمشتريات اليومية'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
            tooltip: 'تحديث البيانات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          _buildFilterSection(localizations),
          
          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildComparisonSection(),
                  const SizedBox(height: 16),
                  _buildSalesDetailsSection(),
                  const SizedBox(height: 16),
                  _buildPurchasesDetailsSection(),
                  const SizedBox(height: 16),
                  _buildHourlyAnalysisSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.teal[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'التاريخ',
                    border: OutlineInputBorder(),
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context),
                  controller: TextEditingController(
                    text: _selectedDate?.toString().split(' ')[0] ?? DateTime.now().toString().split(' ')[0],
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'الفرع',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedBranch,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الفروع')),
                    DropdownMenuItem(value: 'main', child: Text('الفرع الرئيسي')),
                    DropdownMenuItem(value: 'branch1', child: Text('الفرع الأول')),
                    DropdownMenuItem(value: 'branch2', child: Text('الفرع الثاني')),
                    DropdownMenuItem(value: 'branch3', child: Text('الفرع الثالث')),
                  ],
                  onChanged: (value) => setState(() => _selectedBranch = value),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'نوع التقرير',
                    border: OutlineInputBorder(),
                  ),
                  value: _reportType,
                  items: const [
                    DropdownMenuItem(value: 'both', child: Text('المبيعات والمشتريات')),
                    DropdownMenuItem(value: 'sales', child: Text('المبيعات فقط')),
                    DropdownMenuItem(value: 'purchases', child: Text('المشتريات فقط')),
                  ],
                  onChanged: (value) => setState(() => _reportType = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'الموظف',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedEmployee,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الموظفين')),
                    DropdownMenuItem(value: 'emp1', child: Text('أحمد محمد')),
                    DropdownMenuItem(value: 'emp2', child: Text('فاطمة علي')),
                    DropdownMenuItem(value: 'emp3', child: Text('محمد سالم')),
                  ],
                  onChanged: (value) => setState(() => _selectedEmployee = value),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.today, color: Colors.teal, size: 24),
                const SizedBox(width: 8),
                Text(
                  'ملخص اليوم - ${_selectedDate?.toString().split(' ')[0] ?? DateTime.now().toString().split(' ')[0]}',
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي المبيعات', '125,000 ر.س', Colors.green, Icons.trending_up),
                _buildSummaryCard('إجمالي المشتريات', '85,000 ر.س', Colors.blue, Icons.shopping_cart),
                _buildSummaryCard('صافي الربح', '40,000 ر.س', Colors.orange, Icons.monetization_on),
                _buildSummaryCard('عدد المعاملات', '185', Colors.purple, Icons.receipt),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComparisonSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.compare, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'مقارنة مع الفترات السابقة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildComparisonCard('مقارنة بالأمس', '+15%', Colors.green),
                _buildComparisonCard('مقارنة بالأسبوع الماضي', '+8%', Colors.blue),
                _buildComparisonCard('مقارنة بالشهر الماضي', '+22%', Colors.orange),
                _buildComparisonCard('مقارنة بالسنة الماضية', '+35%', Colors.purple),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalesDetailsSection() {
    if (_reportType == 'purchases') return const SizedBox.shrink();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل المبيعات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('رقم الفاتورة')),
                  DataColumn(label: Text('الوقت')),
                  DataColumn(label: Text('العميل')),
                  DataColumn(label: Text('المبلغ')),
                  DataColumn(label: Text('طريقة الدفع')),
                  DataColumn(label: Text('الموظف')),
                  DataColumn(label: Text('الفرع')),
                ],
                rows: _buildSalesRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPurchasesDetailsSection() {
    if (_reportType == 'sales') return const SizedBox.shrink();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل المشتريات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('رقم الفاتورة')),
                  DataColumn(label: Text('الوقت')),
                  DataColumn(label: Text('المورد')),
                  DataColumn(label: Text('المبلغ')),
                  DataColumn(label: Text('طريقة الدفع')),
                  DataColumn(label: Text('الموظف')),
                  DataColumn(label: Text('المستودع')),
                ],
                rows: _buildPurchasesRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHourlyAnalysisSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.schedule, color: Colors.amber, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'التحليل الساعي',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildHourlyAnalysisList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _generateDailyReport,
                    icon: const Icon(Icons.today),
                    label: const Text('تقرير يومي'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _sendToManagement,
                    icon: const Icon(Icons.send),
                    label: const Text('إرسال للإدارة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _scheduleReport,
                    icon: const Icon(Icons.schedule_send),
                    label: const Text('جدولة التقرير'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _compareWithTargets,
                    icon: const Icon(Icons.track_changes),
                    label: const Text('مقارنة بالأهداف'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildHourlyAnalysisList() {
    final hourlyData = [
      {'time': '09:00-12:00', 'sales': '45,000 ر.س', 'purchases': '25,000 ر.س', 'transactions': '65'},
      {'time': '12:00-15:00', 'sales': '35,000 ر.س', 'purchases': '30,000 ر.س', 'transactions': '55'},
      {'time': '15:00-18:00', 'sales': '30,000 ر.س', 'purchases': '20,000 ر.س', 'transactions': '45'},
      {'time': '18:00-21:00', 'sales': '15,000 ر.س', 'purchases': '10,000 ر.س', 'transactions': '20'},
    ];

    return hourlyData.map((data) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.amber.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.amber.withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.schedule, color: Colors.amber, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  data['time']!,
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  'مبيعات: ${data['sales']} • مشتريات: ${data['purchases']} • معاملات: ${data['transactions']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildSalesRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('INV-001')),
        const DataCell(Text('09:15')),
        const DataCell(Text('أحمد محمد')),
        const DataCell(Text('4,500 ر.س')),
        DataCell(_buildPaymentBadge('نقدي', Colors.green)),
        const DataCell(Text('فاطمة علي')),
        const DataCell(Text('الفرع الرئيسي')),
      ]),
      DataRow(cells: [
        const DataCell(Text('INV-002')),
        const DataCell(Text('10:30')),
        const DataCell(Text('سارة أحمد')),
        const DataCell(Text('3,200 ر.س')),
        DataCell(_buildPaymentBadge('بطاقة', Colors.blue)),
        const DataCell(Text('محمد سالم')),
        const DataCell(Text('الفرع الأول')),
      ]),
    ];
  }

  List<DataRow> _buildPurchasesRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('PUR-001')),
        const DataCell(Text('08:30')),
        const DataCell(Text('شركة التقنية المتقدمة')),
        const DataCell(Text('25,000 ر.س')),
        DataCell(_buildPaymentBadge('آجل', Colors.orange)),
        const DataCell(Text('أحمد محمد')),
        const DataCell(Text('المستودع الرئيسي')),
      ]),
      DataRow(cells: [
        const DataCell(Text('PUR-002')),
        const DataCell(Text('11:45')),
        const DataCell(Text('مؤسسة الإلكترونيات')),
        const DataCell(Text('15,000 ر.س')),
        DataCell(_buildPaymentBadge('نقدي', Colors.green)),
        const DataCell(Text('فاطمة علي')),
        const DataCell(Text('مستودع الفرع الأول')),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildComparisonCard(String title, String percentage, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                percentage,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentBadge(String method, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(method, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تحديث البيانات')),
    );
  }

  void _generateDailyReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء التقرير اليومي الشامل')),
    );
  }

  void _sendToManagement() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إرسال التقرير للإدارة')),
    );
  }

  void _scheduleReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جدولة التقرير اليومي')),
    );
  }

  void _compareWithTargets() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مقارنة الأداء بالأهداف المحددة')),
    );
  }
}
