import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير أصناف بيعت أكبر من سعر البيع
/// يعرض الأصناف التي تم بيعها بأسعار أعلى من السعر المحدد
class ItemsSoldAbovePriceReportPage extends StatefulWidget {
  const ItemsSoldAbovePriceReportPage({super.key});

  @override
  State<ItemsSoldAbovePriceReportPage> createState() => _ItemsSoldAbovePriceReportPageState();
}

class _ItemsSoldAbovePriceReportPageState extends State<ItemsSoldAbovePriceReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedCategory;
  String? _selectedEmployee;
  String? _markupThreshold = '5';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('أصناف بيعت أكبر من سعر البيع'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.trending_up),
            onPressed: _showProfitAnalysis,
            tooltip: 'تحليل الأرباح',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          _buildFilterSection(localizations),
          
          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildMarkupAnalysisSection(),
                  const SizedBox(height: 16),
                  _buildItemsTableSection(),
                  const SizedBox(height: 16),
                  _buildEmployeePerformanceSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.green[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.fromDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, true),
                  controller: TextEditingController(
                    text: _startDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.toDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, false),
                  controller: TextEditingController(
                    text: _endDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: localizations.category,
                    border: const OutlineInputBorder(),
                  ),
                  value: _selectedCategory,
                  items: [
                    DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                    DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                    DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                    DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                  ],
                  onChanged: (value) => setState(() => _selectedCategory = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'الموظف',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedEmployee,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الموظفين')),
                    DropdownMenuItem(value: 'emp1', child: Text('أحمد محمد')),
                    DropdownMenuItem(value: 'emp2', child: Text('فاطمة علي')),
                    DropdownMenuItem(value: 'emp3', child: Text('محمد سالم')),
                  ],
                  onChanged: (value) => setState(() => _selectedEmployee = value),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'حد الزيادة (%)',
                    border: OutlineInputBorder(),
                  ),
                  value: _markupThreshold,
                  items: const [
                    DropdownMenuItem(value: '5', child: Text('أكثر من 5%')),
                    DropdownMenuItem(value: '10', child: Text('أكثر من 10%')),
                    DropdownMenuItem(value: '15', child: Text('أكثر من 15%')),
                    DropdownMenuItem(value: '20', child: Text('أكثر من 20%')),
                  ],
                  onChanged: (value) => setState(() => _markupThreshold = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.analytics),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.trending_up, color: Colors.green, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص المبيعات بزيادة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي العمليات', '125', Colors.green, Icons.receipt),
                _buildSummaryCard('إجمالي الربح الإضافي', '85,000 ر.س', Colors.blue, Icons.attach_money),
                _buildSummaryCard('متوسط الزيادة', '18.5%', Colors.orange, Icons.percent),
                _buildSummaryCard('أعلى زيادة', '45%', Colors.purple, Icons.star),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMarkupAnalysisSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.analytics, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'تحليل الزيادات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildMarkupCard('زيادة 5-15%', '65 عملية', Colors.green),
                _buildMarkupCard('زيادة 15-25%', '35 عملية', Colors.blue),
                _buildMarkupCard('زيادة 25-35%', '20 عملية', Colors.orange),
                _buildMarkupCard('زيادة +35%', '5 عمليات', Colors.purple),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل الأصناف المباعة بزيادة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('اسم الصنف')),
                  DataColumn(label: Text('السعر الأصلي')),
                  DataColumn(label: Text('سعر البيع')),
                  DataColumn(label: Text('نسبة الزيادة')),
                  DataColumn(label: Text('الربح الإضافي')),
                  DataColumn(label: Text('الكمية')),
                  DataColumn(label: Text('الموظف')),
                  DataColumn(label: Text('التاريخ')),
                  DataColumn(label: Text('السبب')),
                ],
                rows: _buildItemsRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmployeePerformanceSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.person, color: Colors.teal, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'أداء الموظفين في تحقيق الأرباح',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildEmployeePerformanceList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _rewardTopPerformers,
                    icon: const Icon(Icons.star),
                    label: const Text('مكافأة المتميزين'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _optimizePricing,
                    icon: const Icon(Icons.tune),
                    label: const Text('تحسين التسعير'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _shareStrategies,
                    icon: const Icon(Icons.share),
                    label: const Text('مشاركة الاستراتيجيات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _createIncentivePlan,
                    icon: const Icon(Icons.emoji_events),
                    label: const Text('خطة حوافز'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildEmployeePerformanceList() {
    final employees = [
      {'name': 'أحمد محمد', 'sales': '45 عملية', 'avgMarkup': '22.5%', 'totalProfit': '32,500 ر.س', 'rating': 'ممتاز'},
      {'name': 'فاطمة علي', 'sales': '35 عملية', 'avgMarkup': '18.2%', 'totalProfit': '28,750 ر.س', 'rating': 'جيد جداً'},
      {'name': 'محمد سالم', 'sales': '25 عملية', 'avgMarkup': '15.8%', 'totalProfit': '18,200 ر.س', 'rating': 'جيد'},
      {'name': 'سارة أحمد', 'sales': '20 عملية', 'avgMarkup': '12.2%', 'totalProfit': '12,800 ر.س', 'rating': 'مقبول'},
    ];

    return employees.map((emp) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: _getRatingColor(emp['rating']!).withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: _getRatingColor(emp['rating']!).withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.person, color: Colors.teal, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  emp['name']!,
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  '${emp['sales']} • متوسط: ${emp['avgMarkup']} • ربح: ${emp['totalProfit']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getRatingColor(emp['rating']!).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              emp['rating']!,
              style: TextStyle(
                color: _getRatingColor(emp['rating']!),
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildItemsRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('4,500 ر.س')),
        const DataCell(Text('5,400 ر.س')),
        DataCell(_buildMarkupBadge('20%', Colors.green)),
        const DataCell(Text('900 ر.س')),
        const DataCell(Text('2')),
        const DataCell(Text('أحمد محمد')),
        const DataCell(Text('2024-01-15')),
        const DataCell(Text('طلب خاص')),
      ]),
      DataRow(cells: [
        const DataCell(Text('هاتف آيفون 15')),
        const DataCell(Text('3,800 ر.س')),
        const DataCell(Text('4,560 ر.س')),
        DataCell(_buildMarkupBadge('20%', Colors.green)),
        const DataCell(Text('760 ر.س')),
        const DataCell(Text('1')),
        const DataCell(Text('فاطمة علي')),
        const DataCell(Text('2024-01-16')),
        const DataCell(Text('عميل مؤسسي')),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMarkupCard(String range, String count, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(range, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMarkupBadge(String markup, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(markup, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Color _getRatingColor(String rating) {
    switch (rating) {
      case 'ممتاز':
        return Colors.green;
      case 'جيد جداً':
        return Colors.blue;
      case 'جيد':
        return Colors.orange;
      case 'مقبول':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير الأصناف المباعة بزيادة بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showProfitAnalysis() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تحليل الأرباح المفصل')),
    );
  }

  void _rewardTopPerformers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مكافأة الموظفين المتميزين')),
    );
  }

  void _optimizePricing() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحسين استراتيجية التسعير')),
    );
  }

  void _shareStrategies() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مشاركة الاستراتيجيات الناجحة')),
    );
  }

  void _createIncentivePlan() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء خطة حوافز للموظفين')),
    );
  }
}
