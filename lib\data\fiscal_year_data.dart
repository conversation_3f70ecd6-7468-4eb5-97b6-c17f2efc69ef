/// ملف البيانات المشتركة للسنوات المالية
/// يحتوي على البيانات التي تستخدمها جميع صفحات السنوات المالية
library;

class FiscalYearData {
  static final FiscalYearData _instance = FiscalYearData._internal();
  factory FiscalYearData() => _instance;
  FiscalYearData._internal();

  // قائمة السنوات المالية المشتركة
  final List<Map<String, dynamic>> _fiscalYears = [
    {
      'id': 1,
      'companyNumber': '1',
      'fiscalYearNumber': '1',
      'arabicName': 'السنة المالية 2024',
      'englishName': 'Fiscal Year 2024',
      'startDate': '2024-01-01',
      'endDate': '2024-12-31',
      'continueSequence': 'نعم',
      'skipNumber': '',
    },
    {
      'id': 2,
      'companyNumber': '2',
      'fiscalYearNumber': '2',
      'arabicName': 'السنة المالية 2023',
      'englishName': 'Fiscal Year 2023',
      'startDate': '2023-01-01',
      'endDate': '2023-12-31',
      'continueSequence': 'لا',
      'skipNumber': '100',
    },
    {
      'id': 3,
      'companyNumber': '1',
      'fiscalYearNumber': '3',
      'arabicName': 'السنة المالية 2022',
      'englishName': 'Fiscal Year 2022',
      'startDate': '2022-01-01',
      'endDate': '2022-12-31',
      'continueSequence': 'نعم',
      'skipNumber': '',
    },
    {
      'id': 4,
      'companyNumber': '3',
      'fiscalYearNumber': '4',
      'arabicName': 'السنة المالية 2021',
      'englishName': 'Fiscal Year 2021',
      'startDate': '2021-01-01',
      'endDate': '2021-12-31',
      'continueSequence': 'لا',
      'skipNumber': '50',
    },
  ];

  /// الحصول على قائمة السنوات المالية
  List<Map<String, dynamic>> get fiscalYears => List.unmodifiable(_fiscalYears);

  /// إضافة سنة مالية جديدة
  void addFiscalYear(Map<String, dynamic> fiscalYear) {
    fiscalYear['id'] = _fiscalYears.length + 1;
    _fiscalYears.add(fiscalYear);
  }

  /// تحديث سنة مالية موجودة
  void updateFiscalYear(int id, Map<String, dynamic> updatedData) {
    final index = _fiscalYears.indexWhere((year) => year['id'] == id);
    if (index != -1) {
      _fiscalYears[index] = {..._fiscalYears[index], ...updatedData};
    }
  }

  /// حذف سنة مالية
  void deleteFiscalYear(int id) {
    _fiscalYears.removeWhere((year) => year['id'] == id);
  }

  /// البحث في السنوات المالية
  List<Map<String, dynamic>> searchFiscalYears(String query) {
    if (query.isEmpty) return List<Map<String, dynamic>>.from(_fiscalYears);

    return _fiscalYears.where((year) {
      return year['arabicName']
              .toString()
              .toLowerCase()
              .contains(query.toLowerCase()) ||
          year['englishName']
              .toString()
              .toLowerCase()
              .contains(query.toLowerCase()) ||
          year['companyNumber'].toString().contains(query) ||
          year['fiscalYearNumber'].toString().contains(query);
    }).toList();
  }

  /// الحصول على سنة مالية بالمعرف
  Map<String, dynamic>? getFiscalYearById(int id) {
    try {
      return _fiscalYears.firstWhere((year) => year['id'] == id);
    } catch (e) {
      return null;
    }
  }
}
