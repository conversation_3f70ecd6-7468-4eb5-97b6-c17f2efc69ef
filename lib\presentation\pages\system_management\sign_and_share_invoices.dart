import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة توقيع ومشاركة الفواتير
/// تتيح توقيع الفواتير رقمياً ومشاركتها
class SignAndShareInvoicesPage extends StatefulWidget {
  const SignAndShareInvoicesPage({super.key});

  @override
  State<SignAndShareInvoicesPage> createState() =>
      _SignAndShareInvoicesPageState();
}

class _SignAndShareInvoicesPageState extends State<SignAndShareInvoicesPage> {
  final _searchController = TextEditingController();

  String _searchQuery = '';
  String? _selectedSignature;
  bool _selectAll = false;

  final List<Map<String, String>> _signatures = [
    {'id': 'manager', 'name': 'توقيع المدير العام'},
    {'id': 'accountant', 'name': 'توقيع المحاسب'},
    {'id': 'cashier', 'name': 'توقيع أمين الصندوق'},
    {'id': 'digital', 'name': 'التوقيع الرقمي'},
  ];

  final List<Map<String, dynamic>> _invoices = [
    {
      'id': 'inv1',
      'number': 'INV-2024-001',
      'customer': 'شركة الأمل للتجارة',
      'date': '2024/01/25',
      'amount': 15750.0,
      'status': 'غير موقعة',
      'isSelected': false,
      'signed': false,
      'signedBy': '',
      'signDate': '',
      'shared': false,
    },
    {
      'id': 'inv2',
      'number': 'INV-2024-002',
      'customer': 'مؤسسة النور',
      'date': '2024/01/24',
      'amount': 8900.0,
      'status': 'موقعة',
      'isSelected': false,
      'signed': true,
      'signedBy': 'المدير العام',
      'signDate': '2024/01/24 16:30',
      'shared': true,
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.signAndShareInvoices),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.select_all),
            onPressed: _toggleSelectAll,
            tooltip: 'تحديد الكل',
          ),
        ],
      ),
      body: Column(
        children: [
          // بطاقة الإعدادات
          Card(
            margin: const EdgeInsets.all(16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      labelText: 'البحث برقم الفاتورة أو العميل',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<String>(
                    value: _selectedSignature,
                    decoration: const InputDecoration(
                      labelText: 'نوع التوقيع',
                      border: OutlineInputBorder(),
                    ),
                    items: [
                      const DropdownMenuItem(
                          value: null, child: Text('اختر نوع التوقيع')),
                      ..._signatures.map((sig) => DropdownMenuItem(
                            value: sig['id'],
                            child: Text(sig['name']!),
                          )),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedSignature = value;
                      });
                    },
                  ),
                ],
              ),
            ),
          ),

          // قائمة الفواتير
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _invoices.length,
              itemBuilder: (context, index) {
                final invoice = _invoices[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: CheckboxListTile(
                    value: invoice['isSelected'],
                    onChanged: (value) {
                      setState(() {
                        invoice['isSelected'] = value ?? false;
                      });
                    },
                    secondary: CircleAvatar(
                      backgroundColor:
                          invoice['signed'] ? Colors.green : Colors.orange,
                      child: Icon(
                        invoice['signed'] ? Icons.verified : Icons.pending,
                        color: Colors.white,
                      ),
                    ),
                    title: Text(invoice['number'],
                        style: const TextStyle(fontWeight: FontWeight.bold)),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                            '${invoice['customer']} | ${invoice['amount']} ر.س'),
                        Text('${invoice['date']} | ${invoice['status']}'),
                        if (invoice['signed'])
                          Text(
                              'موقعة بواسطة ${invoice['signedBy']} في ${invoice['signDate']}',
                              style: const TextStyle(
                                  color: Colors.green, fontSize: 12)),
                      ],
                    ),
                    isThreeLine: true,
                  ),
                );
              },
            ),
          ),
        ],
      ),
      bottomNavigationBar: _getSelectedInvoices().isNotEmpty &&
              _selectedSignature != null
          ? Container(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _signSelected,
                      icon: const Icon(Icons.edit),
                      label: Text('توقيع (${_getSelectedInvoices().length})'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.indigo,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _signAndShare,
                      icon: const Icon(Icons.share),
                      label: const Text('توقيع ومشاركة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
            )
          : null,
    );
  }

  List<Map<String, dynamic>> _getSelectedInvoices() {
    return _invoices.where((invoice) => invoice['isSelected'] == true).toList();
  }

  String _getSignatureName() {
    if (_selectedSignature == null) return '';
    return _signatures
        .firstWhere((sig) => sig['id'] == _selectedSignature)['name']!;
  }

  void _toggleSelectAll() {
    setState(() {
      _selectAll = !_selectAll;
      for (var invoice in _invoices) {
        invoice['isSelected'] = _selectAll;
      }
    });
  }

  void _signSelected() {
    final selectedInvoices = _getSelectedInvoices();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد التوقيع'),
        content: Text(
            'هل أنت متأكد من توقيع ${selectedInvoices.length} فاتورة بـ ${_getSignatureName()}؟'),
        actions: [
          TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _performSigning(selectedInvoices, false);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.indigo),
            child: const Text('توقيع', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _signAndShare() {
    final selectedInvoices = _getSelectedInvoices();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد التوقيع والمشاركة'),
        content: Text(
            'هل أنت متأكد من توقيع ومشاركة ${selectedInvoices.length} فاتورة؟'),
        actions: [
          TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _performSigning(selectedInvoices, true);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
            child: const Text('توقيع ومشاركة',
                style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _performSigning(
      List<Map<String, dynamic>> selectedInvoices, bool share) {
    setState(() {
      for (var invoice in selectedInvoices) {
        invoice['signed'] = true;
        invoice['signedBy'] = _getSignatureName();
        invoice['signDate'] =
            '${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year} ${DateTime.now().hour}:${DateTime.now().minute}';
        invoice['status'] = 'موقعة';
        if (share) {
          invoice['shared'] = true;
        }
        invoice['isSelected'] = false;
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(share
            ? 'تم توقيع ومشاركة ${selectedInvoices.length} فاتورة'
            : 'تم توقيع ${selectedInvoices.length} فاتورة'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
