import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير حركة صنف خلال فترة لسنة سابقة
/// تعرض حركة صنف معين خلال فترة زمنية محددة للسنة السابقة
class ItemMovementDuringPeriodPreviousYearPage extends StatefulWidget {
  const ItemMovementDuringPeriodPreviousYearPage({super.key});

  @override
  State<ItemMovementDuringPeriodPreviousYearPage> createState() =>
      _ItemMovementDuringPeriodPreviousYearPageState();
}

class _ItemMovementDuringPeriodPreviousYearPageState
    extends State<ItemMovementDuringPeriodPreviousYearPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedItem;
  String? _selectedWarehouse;
  int _selectedYear = DateTime.now().year - 1;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.itemMovementPreviousYear),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<int>(
                        decoration: InputDecoration(
                          labelText: localizations.year,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedYear,
                        items: List.generate(5, (index) {
                          int year = DateTime.now().year - 1 - index;
                          return DropdownMenuItem(
                            value: year,
                            child: Text(year.toString()),
                          );
                        }),
                        onChanged: (value) =>
                            setState(() => _selectedYear = value!),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.itemName,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedItem,
                        items: const [
                          DropdownMenuItem(
                              value: 'item1',
                              child: Text('جهاز كمبيوتر محمول')),
                          DropdownMenuItem(
                              value: 'item2', child: Text('طابعة ليزر')),
                          DropdownMenuItem(
                              value: 'item3', child: Text('ماوس لاسلكي')),
                          DropdownMenuItem(
                              value: 'item4', child: Text('لوحة مفاتيح')),
                        ],
                        onChanged: (value) =>
                            setState(() => _selectedItem = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.search),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.teal,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات التقرير
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'تقرير حركة الصنف للسنة $_selectedYear',
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          const Text('الصنف: جهاز كمبيوتر محمول'),
                          const Text('كود الصنف: 001'),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول الحركات الشهرية
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'الحركة الشهرية',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: [
                                DataColumn(label: Text(localizations.month)),
                                DataColumn(
                                    label: Text(localizations.openingBalance)),
                                DataColumn(label: Text(localizations.incoming)),
                                DataColumn(label: Text(localizations.outgoing)),
                                DataColumn(
                                    label: Text(localizations.closingBalance)),
                                DataColumn(
                                    label: Text(localizations.averageCost)),
                                DataColumn(label: Text(localizations.value)),
                              ],
                              rows: _buildMonthlyRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // ملخص السنة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'ملخص السنة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSummaryCard(
                                  'إجمالي الوارد', '60', Colors.green),
                              _buildSummaryCard(
                                  'إجمالي الصادر', '45', Colors.red),
                              _buildSummaryCard(
                                  'متوسط الحركة الشهرية', '8.75', Colors.blue),
                              _buildSummaryCard('إجمالي القيمة', '150,000 ر.س',
                                  Colors.purple),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<DataRow> _buildMonthlyRows() {
    final months = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر'
    ];

    return months
        .map((month) => DataRow(cells: [
              DataCell(Text(month)),
              DataCell(Text('${(10 + months.indexOf(month) * 2)}')),
              DataCell(Text('${(5 + months.indexOf(month))}')),
              DataCell(Text('${(3 + months.indexOf(month))}')),
              DataCell(Text('${(12 + months.indexOf(month) * 2)}')),
              DataCell(Text('2,500.00')),
              DataCell(Text('${(30000 + months.indexOf(month) * 5000)}.00')),
            ]))
        .toList();
  }

  Widget _buildSummaryCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء التقرير بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }
}
