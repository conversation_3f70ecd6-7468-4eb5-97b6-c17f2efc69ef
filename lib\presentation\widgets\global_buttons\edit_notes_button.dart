import 'package:flutter/material.dart';

class EditNotesButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String? tooltip;
  final bool isLoading;
  final bool isDisabled;

  const EditNotesButton({
    super.key,
    this.onPressed,
    this.tooltip = 'Edit Notes',
    this.isLoading = false,
    this.isDisabled = false,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip ?? 'Edit Notes',
      child: ElevatedButton.icon(
        onPressed: isDisabled || isLoading ? null : onPressed,
        icon: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              )
            : const Icon(Icons.note_alt),
        label: const Text('Edit Notes'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.teal.shade700,
          foregroundColor: Colors.white,
        ),
      ),
    );
  }
}
