import 'package:flutter/material.dart';

class PurchaseInvoiceFiltersButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String? tooltip;
  final bool isLoading;
  final bool isDisabled;

  const PurchaseInvoiceFiltersButton({
    super.key,
    this.onPressed,
    this.tooltip = 'Purchase Invoice Filters',
    this.isLoading = false,
    this.isDisabled = false,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip ?? 'Purchase Invoice Filters',
      child: ElevatedButton.icon(
        onPressed: isDisabled || isLoading ? null : onPressed,
        icon: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              )
            : const Icon(Icons.filter_list),
        label: const Text('Purchase Invoice Filters'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.teal.shade800,
          foregroundColor: Colors.white,
        ),
      ),
    );
  }
}
