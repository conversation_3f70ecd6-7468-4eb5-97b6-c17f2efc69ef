import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تكليف الجرد
/// تتيح إدارة تكليفات فرق الجرد والمهام
class InventoryAssignmentPage extends StatefulWidget {
  const InventoryAssignmentPage({super.key});

  @override
  State<InventoryAssignmentPage> createState() => _InventoryAssignmentPageState();
}

class _InventoryAssignmentPageState extends State<InventoryAssignmentPage> {
  String _searchQuery = '';
  String _selectedStatus = 'all';

  // بيانات تجريبية لتكليفات الجرد
  final List<Map<String, dynamic>> _assignments = [
    {
      'id': 'ASG001',
      'title': 'جرد قسم الإلكترونيات',
      'assignedTo': 'أحمد محمد',
      'team': ['فاطمة أحمد', 'محمد علي'],
      'warehouse': 'المستودع الرئيسي',
      'section': 'الإلكترونيات',
      'itemsCount': 250,
      'assignedDate': '2024-01-15',
      'dueDate': '2024-01-18',
      'status': 'مكتمل',
      'progress': 100,
      'priority': 'عالية',
      'notes': 'جرد الأجهزة الإلكترونية والهواتف الذكية',
    },
    {
      'id': 'ASG002',
      'title': 'جرد قسم الملابس',
      'assignedTo': 'فاطمة أحمد',
      'team': ['سارة خالد', 'نورا سعد'],
      'warehouse': 'مستودع الفرع الغربي',
      'section': 'الملابس',
      'itemsCount': 180,
      'assignedDate': '2024-02-01',
      'dueDate': '2024-02-03',
      'status': 'جاري',
      'progress': 65,
      'priority': 'متوسطة',
      'notes': 'جرد الملابس الرجالية والنسائية',
    },
    {
      'id': 'ASG003',
      'title': 'جرد قسم المواد الغذائية',
      'assignedTo': 'محمد عبدالله',
      'team': ['علي أحمد', 'هند محمد'],
      'warehouse': 'مستودع التبريد',
      'section': 'المواد الغذائية',
      'itemsCount': 320,
      'assignedDate': '2024-02-10',
      'dueDate': '2024-02-12',
      'status': 'مجدول',
      'progress': 0,
      'priority': 'عالية',
      'notes': 'جرد المواد المبردة والمجمدة',
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.inventoryAssignment),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _createAssignment,
            tooltip: 'إنشاء تكليف جديد',
          ),
          IconButton(
            icon: const Icon(Icons.people),
            onPressed: _manageTeams,
            tooltip: 'إدارة الفرق',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                TextField(
                  decoration: const InputDecoration(
                    hintText: 'البحث في التكليفات...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedStatus,
                        decoration: const InputDecoration(
                          labelText: 'حالة التكليف',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الحالات')),
                          DropdownMenuItem(value: 'مجدول', child: Text('مجدول')),
                          DropdownMenuItem(value: 'جاري', child: Text('جاري')),
                          DropdownMenuItem(value: 'مكتمل', child: Text('مكتمل')),
                          DropdownMenuItem(value: 'متأخر', child: Text('متأخر')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedStatus = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                _buildStatCard('المجموع', _assignments.length.toString(), Colors.blue),
                _buildStatCard('مكتملة', _assignments.where((a) => a['status'] == 'مكتمل').length.toString(), Colors.green),
                _buildStatCard('جارية', _assignments.where((a) => a['status'] == 'جاري').length.toString(), Colors.orange),
                _buildStatCard('مجدولة', _assignments.where((a) => a['status'] == 'مجدول').length.toString(), Colors.purple),
              ],
            ),
          ),

          // قائمة التكليفات
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _assignments.length,
              itemBuilder: (context, index) {
                final assignment = _assignments[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: _getStatusColor(assignment['status']),
                      child: Icon(
                        _getStatusIcon(assignment['status']),
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    title: Text(
                      assignment['title'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('المكلف: ${assignment['assignedTo']}'),
                        Text('القسم: ${assignment['section']} | الأصناف: ${assignment['itemsCount']}'),
                        Text('الموعد النهائي: ${assignment['dueDate']}'),
                        Row(
                          children: [
                            Text('التقدم: ${assignment['progress']}%'),
                            const SizedBox(width: 8),
                            Expanded(
                              child: LinearProgressIndicator(
                                value: assignment['progress'] / 100,
                                backgroundColor: Colors.grey[300],
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  assignment['progress'] == 100 ? Colors.green :
                                  assignment['progress'] > 50 ? Colors.orange : Colors.red,
                                ),
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            Icon(
                              _getPriorityIcon(assignment['priority']),
                              size: 16,
                              color: _getPriorityColor(assignment['priority']),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'الأولوية: ${assignment['priority']}',
                              style: TextStyle(
                                color: _getPriorityColor(assignment['priority']),
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    trailing: PopupMenuButton<String>(
                      onSelected: (value) => _handleAction(value, assignment),
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'view',
                          child: ListTile(
                            leading: Icon(Icons.visibility),
                            title: Text('عرض التفاصيل'),
                          ),
                        ),
                        if (assignment['status'] != 'مكتمل')
                          const PopupMenuItem(
                            value: 'edit',
                            child: ListTile(
                              leading: Icon(Icons.edit),
                              title: Text('تعديل'),
                            ),
                          ),
                        const PopupMenuItem(
                          value: 'team',
                          child: ListTile(
                            leading: Icon(Icons.people),
                            title: Text('إدارة الفريق'),
                          ),
                        ),
                        if (assignment['status'] == 'مجدول')
                          const PopupMenuItem(
                            value: 'start',
                            child: ListTile(
                              leading: Icon(Icons.play_arrow, color: Colors.green),
                              title: Text('بدء التكليف'),
                            ),
                          ),
                        if (assignment['status'] == 'جاري')
                          const PopupMenuItem(
                            value: 'complete',
                            child: ListTile(
                              leading: Icon(Icons.check_circle, color: Colors.green),
                              title: Text('إكمال التكليف'),
                            ),
                          ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: ListTile(
                            leading: Icon(Icons.delete, color: Colors.red),
                            title: Text('حذف', style: TextStyle(color: Colors.red)),
                          ),
                        ),
                      ],
                    ),
                    isThreeLine: true,
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _createAssignment,
        backgroundColor: Colors.deepPurple,
        icon: const Icon(Icons.add),
        label: const Text('تكليف جديد'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'مكتمل':
        return Colors.green;
      case 'جاري':
        return Colors.orange;
      case 'مجدول':
        return Colors.blue;
      case 'متأخر':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'مكتمل':
        return Icons.check_circle;
      case 'جاري':
        return Icons.hourglass_empty;
      case 'مجدول':
        return Icons.schedule;
      case 'متأخر':
        return Icons.warning;
      default:
        return Icons.assignment;
    }
  }

  Color _getPriorityColor(String priority) {
    switch (priority) {
      case 'عالية':
        return Colors.red;
      case 'متوسطة':
        return Colors.orange;
      case 'منخفضة':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  IconData _getPriorityIcon(String priority) {
    switch (priority) {
      case 'عالية':
        return Icons.priority_high;
      case 'متوسطة':
        return Icons.remove;
      case 'منخفضة':
        return Icons.keyboard_arrow_down;
      default:
        return Icons.help;
    }
  }

  void _createAssignment() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء تكليف جرد جديد')),
    );
  }

  void _manageTeams() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إدارة فرق الجرد')),
    );
  }

  void _handleAction(String action, Map<String, dynamic> assignment) {
    switch (action) {
      case 'view':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('عرض تفاصيل ${assignment['title']}')),
        );
        break;
      case 'edit':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تعديل ${assignment['title']}')),
        );
        break;
      case 'team':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('إدارة فريق ${assignment['title']}')),
        );
        break;
      case 'start':
        setState(() {
          assignment['status'] = 'جاري';
          assignment['progress'] = 10;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم بدء ${assignment['title']}'),
            backgroundColor: Colors.green,
          ),
        );
        break;
      case 'complete':
        setState(() {
          assignment['status'] = 'مكتمل';
          assignment['progress'] = 100;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إكمال ${assignment['title']}'),
            backgroundColor: Colors.green,
          ),
        );
        break;
      case 'delete':
        _showDeleteConfirmation(assignment);
        break;
    }
  }

  void _showDeleteConfirmation(Map<String, dynamic> assignment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف التكليف ${assignment['title']}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _assignments.removeWhere((a) => a['id'] == assignment['id']);
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف التكليف ${assignment['title']}'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
