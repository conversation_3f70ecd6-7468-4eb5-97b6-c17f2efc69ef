import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة الطرفيات
/// تتيح إدارة ومراقبة الطرفيات المتصلة بالنظام
class TerminalsPage extends StatefulWidget {
  const TerminalsPage({super.key});

  @override
  State<TerminalsPage> createState() => _TerminalsPageState();
}

class _TerminalsPageState extends State<TerminalsPage> {
  final _searchController = TextEditingController();

  String _searchQuery = '';
  String _selectedFilter = 'all';
  bool _autoRefresh = true;

  final List<Map<String, dynamic>> _terminals = [
    {
      'id': 'term1',
      'name': 'طرفية الكاشير الرئيسي',
      'type': 'POS',
      'location': 'الفرع الرئيسي - الطابق الأرضي',
      'ipAddress': '*************',
      'macAddress': '00:1B:44:11:3A:B7',
      'status': 'متصل',
      'lastActivity': '2024/01/25 14:30',
      'version': 'v2.1.5',
      'user': 'أحمد محمد',
      'transactions': 156,
      'uptime': '5 أيام 12 ساعة',
      'memory': '2.1 GB / 4 GB',
      'storage': '45 GB / 128 GB',
    },
    {
      'id': 'term2',
      'name': 'طرفية المخزن',
      'type': 'Inventory',
      'location': 'المستودع الرئيسي',
      'ipAddress': '*************',
      'macAddress': '00:1B:44:11:3A:B8',
      'status': 'متصل',
      'lastActivity': '2024/01/25 14:25',
      'version': 'v2.1.3',
      'user': 'فاطمة علي',
      'transactions': 89,
      'uptime': '3 أيام 8 ساعات',
      'memory': '1.8 GB / 4 GB',
      'storage': '32 GB / 128 GB',
    },
    {
      'id': 'term3',
      'name': 'طرفية الفرع الشرقي',
      'type': 'POS',
      'location': 'الفرع الشرقي',
      'ipAddress': '*************',
      'macAddress': '00:1B:44:11:3A:B9',
      'status': 'غير متصل',
      'lastActivity': '2024/01/25 12:15',
      'version': 'v2.1.5',
      'user': 'محمد سالم',
      'transactions': 234,
      'uptime': '0',
      'memory': 'غير متاح',
      'storage': 'غير متاح',
    },
    {
      'id': 'term4',
      'name': 'طرفية المحاسبة',
      'type': 'Accounting',
      'location': 'قسم المحاسبة',
      'ipAddress': '*************',
      'macAddress': '00:1B:44:11:3A:C0',
      'status': 'متصل',
      'lastActivity': '2024/01/25 14:28',
      'version': 'v2.1.4',
      'user': 'نورا أحمد',
      'transactions': 67,
      'uptime': '2 أيام 15 ساعة',
      'memory': '2.5 GB / 8 GB',
      'storage': '78 GB / 256 GB',
    },
    {
      'id': 'term5',
      'name': 'طرفية الإدارة',
      'type': 'Management',
      'location': 'مكتب الإدارة',
      'ipAddress': '*************',
      'macAddress': '00:1B:44:11:3A:C1',
      'status': 'خامل',
      'lastActivity': '2024/01/25 13:45',
      'version': 'v2.1.5',
      'user': 'خالد عبدالله',
      'transactions': 23,
      'uptime': '1 يوم 6 ساعات',
      'memory': '1.2 GB / 4 GB',
      'storage': '25 GB / 128 GB',
    },
  ];

  List<Map<String, dynamic>> get _filteredTerminals {
    List<Map<String, dynamic>> filtered = _terminals;

    // تطبيق فلتر الحالة
    if (_selectedFilter == 'connected') {
      filtered =
          filtered.where((terminal) => terminal['status'] == 'متصل').toList();
    } else if (_selectedFilter == 'disconnected') {
      filtered = filtered
          .where((terminal) => terminal['status'] == 'غير متصل')
          .toList();
    } else if (_selectedFilter == 'idle') {
      filtered =
          filtered.where((terminal) => terminal['status'] == 'خامل').toList();
    }

    // تطبيق البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered
          .where((terminal) =>
              terminal['name']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              terminal['location']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              terminal['user']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              terminal['ipAddress'].toString().contains(_searchQuery))
          .toList();
    }

    return filtered;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.terminals),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(_autoRefresh ? Icons.refresh : Icons.refresh_outlined),
            onPressed: _toggleAutoRefresh,
            tooltip: _autoRefresh
                ? 'إيقاف التحديث التلقائي'
                : 'تفعيل التحديث التلقائي',
          ),
          IconButton(
            icon: const Icon(Icons.add_circle),
            onPressed: _addNewTerminal,
            tooltip: 'إضافة طرفية جديدة',
          ),
        ],
      ),
      body: Column(
        children: [
          // بطاقة البحث والفلترة
          Card(
            margin: const EdgeInsets.all(16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // شريط البحث
                  TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      labelText: 'البحث في الطرفيات',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),

                  const SizedBox(height: 16),

                  // فلتر الحالة
                  DropdownButtonFormField<String>(
                    value: _selectedFilter,
                    decoration: const InputDecoration(
                      labelText: 'فلتر حسب الحالة',
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    ),
                    items: const [
                      DropdownMenuItem(
                          value: 'all', child: Text('جميع الطرفيات')),
                      DropdownMenuItem(
                          value: 'connected', child: Text('المتصلة')),
                      DropdownMenuItem(
                          value: 'disconnected', child: Text('غير المتصلة')),
                      DropdownMenuItem(value: 'idle', child: Text('الخاملة')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedFilter = value!;
                      });
                    },
                  ),
                ],
              ),
            ),
          ),

          // إحصائيات سريعة
          Card(
            margin: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatCard(
                      'المجموع', _terminals.length.toString(), Colors.blue),
                  _buildStatCard(
                      'متصل',
                      _terminals
                          .where((t) => t['status'] == 'متصل')
                          .length
                          .toString(),
                      Colors.green),
                  _buildStatCard(
                      'غير متصل',
                      _terminals
                          .where((t) => t['status'] == 'غير متصل')
                          .length
                          .toString(),
                      Colors.red),
                  _buildStatCard(
                      'خامل',
                      _terminals
                          .where((t) => t['status'] == 'خامل')
                          .length
                          .toString(),
                      Colors.orange),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // قائمة الطرفيات
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _filteredTerminals.length,
              itemBuilder: (context, index) {
                final terminal = _filteredTerminals[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: _getStatusColor(terminal['status']),
                      child: Icon(
                        _getTerminalTypeIcon(terminal['type']),
                        color: Colors.white,
                      ),
                    ),
                    title: Text(
                      terminal['name'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('${terminal['location']} | ${terminal['user']}'),
                        Text(
                            '${terminal['ipAddress']} | آخر نشاط: ${terminal['lastActivity']}',
                            style: const TextStyle(fontSize: 12)),
                      ],
                    ),
                    trailing: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getStatusColor(terminal['status'])
                            .withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        terminal['status'],
                        style: TextStyle(
                          color: _getStatusColor(terminal['status']),
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            _buildDetailRow('النوع:', terminal['type']),
                            _buildDetailRow('عنوان IP:', terminal['ipAddress']),
                            _buildDetailRow(
                                'عنوان MAC:', terminal['macAddress']),
                            _buildDetailRow('الإصدار:', terminal['version']),
                            _buildDetailRow('المعاملات:',
                                '${terminal['transactions']} معاملة'),
                            _buildDetailRow('وقت التشغيل:', terminal['uptime']),
                            _buildDetailRow('الذاكرة:', terminal['memory']),
                            _buildDetailRow('التخزين:', terminal['storage']),

                            const SizedBox(height: 16),

                            // أزرار العمليات
                            Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: [
                                ElevatedButton.icon(
                                  onPressed: () => _connectTerminal(terminal),
                                  icon: const Icon(Icons.link, size: 16),
                                  label: const Text('اتصال'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                                ElevatedButton.icon(
                                  onPressed: () =>
                                      _disconnectTerminal(terminal),
                                  icon: const Icon(Icons.link_off, size: 16),
                                  label: const Text('قطع'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                                ElevatedButton.icon(
                                  onPressed: () => _restartTerminal(terminal),
                                  icon: const Icon(Icons.restart_alt, size: 16),
                                  label: const Text('إعادة تشغيل'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                                ElevatedButton.icon(
                                  onPressed: () => _configureTerminal(terminal),
                                  icon: const Icon(Icons.settings, size: 16),
                                  label: const Text('إعدادات'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                                ElevatedButton.icon(
                                  onPressed: () => _viewTerminalLogs(terminal),
                                  icon: const Icon(Icons.history, size: 16),
                                  label: const Text('السجلات'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                                ElevatedButton.icon(
                                  onPressed: () => _updateTerminal(terminal),
                                  icon:
                                      const Icon(Icons.system_update, size: 16),
                                  label: const Text('تحديث'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.indigo,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshTerminals,
        backgroundColor: Colors.blue,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Column(
      children: [
        CircleAvatar(
          backgroundColor: color,
          child: Text(
            value,
            style: const TextStyle(
                color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'متصل':
        return Colors.green;
      case 'غير متصل':
        return Colors.red;
      case 'خامل':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getTerminalTypeIcon(String type) {
    switch (type) {
      case 'POS':
        return Icons.point_of_sale;
      case 'Inventory':
        return Icons.inventory;
      case 'Accounting':
        return Icons.account_balance;
      case 'Management':
        return Icons.admin_panel_settings;
      default:
        return Icons.computer;
    }
  }

  void _toggleAutoRefresh() {
    setState(() {
      _autoRefresh = !_autoRefresh;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(_autoRefresh
            ? 'تم تفعيل التحديث التلقائي'
            : 'تم إيقاف التحديث التلقائي'),
        backgroundColor: _autoRefresh ? Colors.green : Colors.orange,
      ),
    );
  }

  void _refreshTerminals() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث قائمة الطرفيات'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _addNewTerminal() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إضافة طرفية جديدة')),
    );
  }

  void _connectTerminal(Map<String, dynamic> terminal) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('الاتصال بالطرفية ${terminal['name']}')),
    );
  }

  void _disconnectTerminal(Map<String, dynamic> terminal) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('قطع الاتصال مع الطرفية ${terminal['name']}')),
    );
  }

  void _restartTerminal(Map<String, dynamic> terminal) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('إعادة تشغيل الطرفية ${terminal['name']}')),
    );
  }

  void _configureTerminal(Map<String, dynamic> terminal) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('إعدادات الطرفية ${terminal['name']}')),
    );
  }

  void _viewTerminalLogs(Map<String, dynamic> terminal) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض سجلات الطرفية ${terminal['name']}')),
    );
  }

  void _updateTerminal(Map<String, dynamic> terminal) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تحديث الطرفية ${terminal['name']}')),
    );
  }
}
