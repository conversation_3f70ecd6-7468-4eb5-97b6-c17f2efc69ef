import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير حركة مبيعات الموردين
/// يعرض تحليل مبيعات الأصناف حسب الموردين
class SupplierSalesMovementReportPage extends StatefulWidget {
  const SupplierSalesMovementReportPage({super.key});

  @override
  State<SupplierSalesMovementReportPage> createState() => _SupplierSalesMovementReportPageState();
}

class _SupplierSalesMovementReportPageState extends State<SupplierSalesMovementReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedSupplier;
  String? _selectedCategory;
  String? _sortBy = 'sales_value';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('حركة مبيعات الموردين'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showAnalytics,
            tooltip: 'تحليل المبيعات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.teal[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.fromDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, true),
                        controller: TextEditingController(
                          text: _startDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.toDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, false),
                        controller: TextEditingController(
                          text: _endDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'المورد',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedSupplier,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الموردين')),
                          DropdownMenuItem(value: 'supplier1', child: Text('شركة التقنية المتقدمة')),
                          DropdownMenuItem(value: 'supplier2', child: Text('مؤسسة الجودة العالمية')),
                          DropdownMenuItem(value: 'supplier3', child: Text('شركة الإمداد الشامل')),
                        ],
                        onChanged: (value) => setState(() => _selectedSupplier = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.category,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedCategory,
                        items: [
                          DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                          DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                          DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                          DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                        ],
                        onChanged: (value) => setState(() => _selectedCategory = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'ترتيب حسب',
                          border: OutlineInputBorder(),
                        ),
                        value: _sortBy,
                        items: const [
                          DropdownMenuItem(value: 'sales_value', child: Text('قيمة المبيعات')),
                          DropdownMenuItem(value: 'quantity', child: Text('الكمية المباعة')),
                          DropdownMenuItem(value: 'profit', child: Text('هامش الربح')),
                          DropdownMenuItem(value: 'items_count', child: Text('عدد الأصناف')),
                        ],
                        onChanged: (value) => setState(() => _sortBy = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _generateReport,
                        icon: const Icon(Icons.search),
                        label: Text(localizations.generateReport),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.teal,
                          foregroundColor: Colors.white,
                          minimumSize: const Size(double.infinity, 48),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص مبيعات الموردين
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.business, color: Colors.teal, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'ملخص مبيعات الموردين',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSummaryCard('إجمالي المبيعات', '3,250,000 ر.س', Colors.blue, Icons.monetization_on),
                              _buildSummaryCard('عدد الموردين', '45', Colors.green, Icons.business),
                              _buildSummaryCard('عدد الأصناف', '1,245', Colors.orange, Icons.inventory),
                              _buildSummaryCard('متوسط الربح', '28.5%', Colors.purple, Icons.trending_up),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // أفضل الموردين
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'أفضل الموردين (حسب قيمة المبيعات)',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSupplierCard('شركة التقنية المتقدمة', '1,250,000 ر.س', '38.5%', Colors.blue),
                              _buildSupplierCard('مؤسسة الجودة العالمية', '850,000 ر.س', '26.2%', Colors.green),
                              _buildSupplierCard('شركة الإمداد الشامل', '650,000 ر.س', '20.0%', Colors.orange),
                              _buildSupplierCard('موردين آخرين', '500,000 ر.س', '15.3%', Colors.grey),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول تفاصيل مبيعات الموردين
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل مبيعات الموردين',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('المورد')),
                                DataColumn(label: Text('عدد الأصناف')),
                                DataColumn(label: Text('الكمية المباعة')),
                                DataColumn(label: Text('قيمة المبيعات')),
                                DataColumn(label: Text('تكلفة البضاعة')),
                                DataColumn(label: Text('إجمالي الربح')),
                                DataColumn(label: Text('هامش الربح')),
                                DataColumn(label: Text('نسبة المساهمة')),
                                DataColumn(label: Text('التقييم')),
                              ],
                              rows: _buildSupplierSalesRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // أفضل الأصناف حسب المورد
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.star, color: Colors.amber, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'أفضل الأصناف حسب المورد',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          ..._buildTopItemsBySupplierList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // تحليل الأداء
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.analytics, color: Colors.indigo, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'تحليل أداء الموردين',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildPerformanceCard('أفضل مورد', 'شركة التقنية المتقدمة', 'أعلى مبيعات', Colors.green, Icons.emoji_events),
                              _buildPerformanceCard('أعلى ربحية', 'مؤسسة الجودة العالمية', '32% هامش ربح', Colors.blue, Icons.trending_up),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              _buildPerformanceCard('أسرع نمو', 'شركة الإمداد الشامل', '+45% نمو', Colors.orange, Icons.speed),
                              _buildPerformanceCard('أكثر تنوع', 'مؤسسة التوريد الشاملة', '285 صنف', Colors.purple, Icons.category),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _contactSupplier,
                                  icon: const Icon(Icons.phone),
                                  label: const Text('اتصال بمورد'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _createPurchaseOrder,
                                  icon: const Icon(Icons.shopping_cart),
                                  label: const Text('أمر شراء'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _evaluateSupplier,
                                  icon: const Icon(Icons.assessment),
                                  label: const Text('تقييم مورد'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _negotiatePrices,
                                  icon: const Icon(Icons.handshake),
                                  label: const Text('تفاوض أسعار'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<DataRow> _buildSupplierSalesRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('شركة التقنية المتقدمة')),
        const DataCell(Text('285')),
        const DataCell(Text('1,250')),
        const DataCell(Text('1,250,000 ر.س')),
        const DataCell(Text('850,000 ر.س')),
        const DataCell(Text('400,000 ر.س')),
        const DataCell(Text('32.0%')),
        const DataCell(Text('38.5%')),
        DataCell(_buildRatingBadge('ممتاز', Colors.green)),
      ]),
      DataRow(cells: [
        const DataCell(Text('مؤسسة الجودة العالمية')),
        const DataCell(Text('195')),
        const DataCell(Text('850')),
        const DataCell(Text('850,000 ر.س')),
        const DataCell(Text('580,000 ر.س')),
        const DataCell(Text('270,000 ر.س')),
        const DataCell(Text('31.8%')),
        const DataCell(Text('26.2%')),
        DataCell(_buildRatingBadge('جيد جداً', Colors.blue)),
      ]),
      DataRow(cells: [
        const DataCell(Text('شركة الإمداد الشامل')),
        const DataCell(Text('165')),
        const DataCell(Text('650')),
        const DataCell(Text('650,000 ر.س')),
        const DataCell(Text('480,000 ر.س')),
        const DataCell(Text('170,000 ر.س')),
        const DataCell(Text('26.2%')),
        const DataCell(Text('20.0%')),
        DataCell(_buildRatingBadge('جيد', Colors.orange)),
      ]),
    ];
  }

  List<Widget> _buildTopItemsBySupplierList() {
    final topItems = [
      {'supplier': 'شركة التقنية المتقدمة', 'item': 'لابتوب ديل XPS 13', 'sales': '485,000 ر.س', 'quantity': '195'},
      {'supplier': 'مؤسسة الجودة العالمية', 'item': 'طابعة HP LaserJet', 'sales': '320,000 ر.س', 'quantity': '160'},
      {'supplier': 'شركة الإمداد الشامل', 'item': 'هاتف آيفون 15', 'sales': '285,000 ر.س', 'quantity': '95'},
      {'supplier': 'مؤسسة التوريد الشاملة', 'item': 'ساعة ذكية', 'sales': '185,000 ر.س', 'quantity': '125'},
    ];

    return topItems.map((item) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.teal.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.teal.withOpacity(0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.star, color: Colors.amber, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item['item']!,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${item['supplier']} • الكمية: ${item['quantity']}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Text(
            item['sales']!,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.teal,
            ),
          ),
        ],
      ),
    )).toList();
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSupplierCard(String supplier, String sales, String percentage, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            children: [
              Text(
                sales,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                percentage,
                style: TextStyle(
                  fontSize: 12,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                supplier,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPerformanceCard(String title, String value, String subtitle, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                title,
                style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
                textAlign: TextAlign.center,
              ),
              Text(
                subtitle,
                style: TextStyle(fontSize: 10, color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRatingBadge(String rating, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        rating,
        style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير حركة مبيعات الموردين بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showAnalytics() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تحليل مفصل لمبيعات الموردين')),
    );
  }

  void _contactSupplier() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('الاتصال بالمورد المحدد')),
    );
  }

  void _createPurchaseOrder() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء أمر شراء جديد')),
    );
  }

  void _evaluateSupplier() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تقييم أداء المورد')),
    );
  }

  void _negotiatePrices() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('بدء مفاوضات الأسعار')),
    );
  }
}
