import 'package:flutter/material.dart';
import '../../data/models/cost_center_data.dart';

/// نموذج إدخال بيانات مركز التكلفة
class CostCenterDataForm extends StatefulWidget {
  final CostCenterData? initialData;
  final bool isEditMode;
  final String? parentCenterCode;
  final List<CostCenterData>? savedCenters;
  final Function(CostCenterData) onSave;
  final VoidCallback? onCancel;

  const CostCenterDataForm({
    super.key,
    this.initialData,
    this.isEditMode = false,
    this.parentCenterCode,
    this.savedCenters,
    required this.onSave,
    this.onCancel,
  });

  @override
  State<CostCenterDataForm> createState() => _CostCenterDataFormState();
}

class _CostCenterDataFormState extends State<CostCenterDataForm> {
  final _formKey = GlobalKey<FormState>();
  late CostCenterData _centerData;

  // متحكمات النصوص
  late TextEditingController _branchNumberController;
  late TextEditingController _centerCodeController;
  late TextEditingController _arabicNameController;
  late TextEditingController _englishNameController;

  @override
  void initState() {
    super.initState();
    
    // تهيئة البيانات
    _centerData = widget.initialData ?? CostCenterData();
    
    // تهيئة المتحكمات
    _branchNumberController = TextEditingController(text: _centerData.branchNumber);
    _arabicNameController = TextEditingController(text: _centerData.arabicName);
    _englishNameController = TextEditingController(text: _centerData.englishName);
    
    // إذا كان في وضع الإضافة، احسب رقم المركز التلقائي
    if (!widget.isEditMode && widget.parentCenterCode != null) {
      final newCenterCode = _generateNextCenterCode(widget.parentCenterCode!);
      _centerData.centerCode = newCenterCode;
    }
    
    _centerCodeController = TextEditingController(text: _centerData.centerCode);
  }

  @override
  void dispose() {
    _branchNumberController.dispose();
    _centerCodeController.dispose();
    _arabicNameController.dispose();
    _englishNameController.dispose();
    super.dispose();
  }

  /// توليد رقم المركز التالي وفقاً للنظام السعودي الجديد (بدون أصفار زائدة)
  String _generateNextCenterCode(String parentCode) {
    // النظام الجديد: الأرقام بدون أصفار زائدة
    // المستوى الأول: 1, 2, 3, 4
    // المستوى الثاني: 11, 12, 21, 22, 31, 32, 41, 42, 43, 44
    // المستوى الثالث: 111, 112, 113, إلخ
    
    String cleanParentCode = parentCode;
    
    // تحديد طول الرقم الجديد بناءً على النظام السعودي الجديد
    int newLength;
    if (cleanParentCode.length == 1) {
      // من المستوى الأول إلى الثاني (1 -> 11, 12)
      newLength = 2;
    } else if (cleanParentCode.length == 2) {
      // من المستوى الثاني إلى الثالث (11 -> 111, 112, 113)
      newLength = 3;
    } else if (cleanParentCode.length == 3) {
      // من المستوى الثالث إلى الرابع (111 -> 1111, 1112, 1113)
      newLength = 4;
    } else {
      // للمستويات الأعمق، إضافة خانة واحدة
      newLength = cleanParentCode.length + 1;
    }
    
    // البحث عن أعلى رقم فرعي موجود
    int nextNumber = _getNextAvailableNumber(cleanParentCode, newLength);
    
    // تكوين رقم المركز الجديد
    final digitsToAdd = newLength - cleanParentCode.length;
    final newSubNumberStr = nextNumber.toString().padLeft(digitsToAdd, '0');
    final newCenterCode = cleanParentCode + newSubNumberStr;
    
    // إرجاع الرقم بدون أصفار زائدة (النظام الجديد)
    return newCenterCode;
  }

  /// الحصول على الرقم التالي المتاح وفقاً للنظام السعودي الجديد
  int _getNextAvailableNumber(String parentCode, int targetLength) {
    // إذا لم تكن هناك مراكز محفوظة، ابدأ من 1
    if (widget.savedCenters == null || widget.savedCenters!.isEmpty) {
      return 1;
    }

    // البحث عن أعلى رقم فرعي موجود
    int maxSubNumber = 0;
    
    for (final center in widget.savedCenters!) {
      final centerCode = center.centerCode;
      
      // التحقق من أن المركز يبدأ بـ parentCode وله نفس الطول المطلوب
      if (centerCode.startsWith(parentCode) && centerCode.length == targetLength) {
        // استخراج الرقم الفرعي (الجزء الأخير)
        final subNumberStr = centerCode.substring(parentCode.length);
        final subNumber = int.tryParse(subNumberStr) ?? 0;
        if (subNumber > maxSubNumber) {
          maxSubNumber = subNumber;
        }
      }
    }
    
    // إرجاع الرقم التالي
    return maxSubNumber + 1;
  }

  /// حفظ البيانات
  void _saveData() {
    if (_formKey.currentState!.validate()) {
      // تحديث البيانات من الحقول
      _centerData.branchNumber = _branchNumberController.text;
      _centerData.centerCode = _centerCodeController.text;
      _centerData.arabicName = _arabicNameController.text;
      _centerData.englishName = _englishNameController.text;

      // استدعاء دالة الحفظ
      widget.onSave(_centerData);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان
          Row(
            children: [
              Icon(
                widget.isEditMode ? Icons.edit : Icons.add,
                color: Colors.blue[700],
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                widget.isEditMode ? 'تعديل بيانات مركز التكلفة' : 'إضافة مركز تكلفة جديد',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // المحتوى القابل للتمرير
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // نوع المركز (مجلد أم ملف)
                  if (!widget.isEditMode) ...[
                    Card(
                      color: Colors.blue[50],
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'نوع مركز التكلفة',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue[700],
                              ),
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: RadioListTile<bool>(
                                    title: const Row(
                                      children: [
                                        Icon(Icons.folder, color: Colors.blue),
                                        SizedBox(width: 8),
                                        Text('مجلد'),
                                      ],
                                    ),
                                    value: true,
                                    groupValue: _centerData.isFolder,
                                    onChanged: (value) {
                                      setState(() {
                                        _centerData.isFolder = value!;
                                      });
                                    },
                                  ),
                                ),
                                Expanded(
                                  child: RadioListTile<bool>(
                                    title: const Row(
                                      children: [
                                        Icon(Icons.description, color: Colors.grey),
                                        SizedBox(width: 8),
                                        Text('ملف'),
                                      ],
                                    ),
                                    value: false,
                                    groupValue: _centerData.isFolder,
                                    onChanged: (value) {
                                      setState(() {
                                        _centerData.isFolder = value!;
                                      });
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // رقم فرع المركز
                  Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: TextFormField(
                          controller: _branchNumberController,
                          keyboardType: TextInputType.number,
                          decoration: const InputDecoration(
                            labelText: 'رقم فرع المركز',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'مطلوب';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        flex: 3,
                        child: DropdownButtonFormField<String>(
                          value: null,
                          decoration: const InputDecoration(
                            labelText: 'اختر الفرع',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ),
                          items: const [
                            DropdownMenuItem(value: '001', child: Text('الفرع الرئيسي')),
                            DropdownMenuItem(value: '002', child: Text('فرع الرياض')),
                            DropdownMenuItem(value: '003', child: Text('فرع جدة')),
                          ],
                          onChanged: (value) {
                            if (value != null) {
                              _branchNumberController.text = value;
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // رقم المركز
                  TextFormField(
                    controller: _centerCodeController,
                    readOnly: !widget.isEditMode,
                    decoration: InputDecoration(
                      labelText: 'رقم مركز التكلفة',
                      border: const OutlineInputBorder(),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      fillColor: widget.isEditMode ? null : Colors.grey[100],
                      filled: !widget.isEditMode,
                      suffixIcon: widget.parentCenterCode != null
                          ? Tooltip(
                              message: 'سيتم إنشاء المركز داخل: ${widget.parentCenterCode}',
                              child: Icon(Icons.folder_open, color: Colors.blue[600]),
                            )
                          : null,
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'مطلوب';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 12),
                ],
              ),
            ),
          ),

          // أزرار الحفظ والإلغاء
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: widget.onCancel,
                child: const Text('إلغاء'),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: _saveData,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
                child: Text(widget.isEditMode ? 'تحديث' : 'حفظ'),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
