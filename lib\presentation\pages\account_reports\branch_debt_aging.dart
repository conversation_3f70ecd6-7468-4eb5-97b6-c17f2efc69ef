import 'package:flutter/material.dart';

/// صفحة تقرير أعمار ديون الفروع
/// تعرض أعمار الديون المستحقة على الفروع المختلفة
class BranchDebtAgingPage extends StatefulWidget {
  const BranchDebtAgingPage({super.key});

  @override
  State<BranchDebtAgingPage> createState() => _BranchDebtAgingPageState();
}

class _BranchDebtAgingPageState extends State<BranchDebtAgingPage> {
  String _selectedBranch = 'all';
  String _selectedAgeGroup = 'all';

  // بيانات تجريبية لأعمار ديون الفروع
  final List<Map<String, dynamic>> _branchDebtAging = [
    {
      'branchCode': 'BR001',
      'branchName': 'فرع الرياض الرئيسي',
      'manager': 'أحمد محمد',
      'totalDebt': 185000.0,
      'current': 85000.0,
      'days30': 45000.0,
      'days60': 30000.0,
      'days90': 15000.0,
      'over90': 10000.0,
      'riskLevel': 'متوسط',
      'lastPayment': '2024-01-10',
      'customers': [
        {'name': 'شركة الأمل', 'debt': 50000.0, 'age': 25},
        {'name': 'مؤسسة النور', 'debt': 35000.0, 'age': 45},
        {'name': 'شركة الفجر', 'debt': 25000.0, 'age': 65},
        {'name': 'مكتب الإبداع', 'debt': 15000.0, 'age': 95},
      ],
    },
    {
      'branchCode': 'BR002',
      'branchName': 'فرع جدة التجاري',
      'manager': 'فاطمة أحمد',
      'totalDebt': 125000.0,
      'current': 65000.0,
      'days30': 35000.0,
      'days60': 15000.0,
      'days90': 8000.0,
      'over90': 2000.0,
      'riskLevel': 'منخفض',
      'lastPayment': '2024-01-15',
      'customers': [
        {'name': 'شركة البحر', 'debt': 40000.0, 'age': 15},
        {'name': 'مؤسسة الساحل', 'debt': 30000.0, 'age': 35},
        {'name': 'شركة الميناء', 'debt': 25000.0, 'age': 55},
        {'name': 'مكتب التجارة', 'debt': 10000.0, 'age': 75},
      ],
    },
    {
      'branchCode': 'BR003',
      'branchName': 'فرع الدمام الصناعي',
      'manager': 'محمد علي',
      'totalDebt': 245000.0,
      'current': 95000.0,
      'days30': 65000.0,
      'days60': 45000.0,
      'days90': 25000.0,
      'over90': 15000.0,
      'riskLevel': 'عالي',
      'lastPayment': '2023-12-20',
      'customers': [
        {'name': 'شركة البترول', 'debt': 80000.0, 'age': 35},
        {'name': 'مؤسسة الصناعة', 'debt': 60000.0, 'age': 55},
        {'name': 'شركة الكيماويات', 'debt': 45000.0, 'age': 75},
        {'name': 'مصنع المعادن', 'debt': 25000.0, 'age': 105},
      ],
    },
    {
      'branchCode': 'BR004',
      'branchName': 'فرع مكة المكرمة',
      'manager': 'سارة سعد',
      'totalDebt': 95000.0,
      'current': 55000.0,
      'days30': 25000.0,
      'days60': 10000.0,
      'days90': 3000.0,
      'over90': 2000.0,
      'riskLevel': 'منخفض',
      'lastPayment': '2024-01-12',
      'customers': [
        {'name': 'شركة الحج والعمرة', 'debt': 35000.0, 'age': 20},
        {'name': 'مؤسسة الضيافة', 'debt': 25000.0, 'age': 40},
        {'name': 'شركة النقل', 'debt': 20000.0, 'age': 60},
        {'name': 'مكتب السياحة', 'debt': 8000.0, 'age': 80},
      ],
    },
  ];

  @override
  Widget build(BuildContext context) {
    final filteredData = _getFilteredData();

    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير أعمار ديون الفروع'),
        backgroundColor: Colors.brown,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedBranch,
                        decoration: const InputDecoration(
                          labelText: 'الفرع',
                          border: OutlineInputBorder(),
                        ),
                        items: [
                          const DropdownMenuItem(
                              value: 'all', child: Text('جميع الفروع')),
                          ..._branchDebtAging.map((branch) {
                            return DropdownMenuItem<String>(
                              value: branch['branchCode'],
                              child: Text(branch['branchName']),
                            );
                          }),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedBranch = value!;
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedAgeGroup,
                        decoration: const InputDecoration(
                          labelText: 'فئة العمر',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(
                              value: 'all', child: Text('جميع الفئات')),
                          DropdownMenuItem(
                              value: 'current', child: Text('جاري (0-30 يوم)')),
                          DropdownMenuItem(
                              value: 'days30', child: Text('31-60 يوم')),
                          DropdownMenuItem(
                              value: 'days60', child: Text('61-90 يوم')),
                          DropdownMenuItem(
                              value: 'over90', child: Text('أكثر من 90 يوم')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedAgeGroup = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // ملخص أعمار الديون
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              color: Colors.brown[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    const Text(
                      'ملخص أعمار ديون الفروع',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Column(
                          children: [
                            const Text('إجمالي الديون'),
                            Text(
                              '${_getTotalDebt(filteredData)} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.red,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('الديون المتأخرة'),
                            Text(
                              '${_getOverdueDebt(filteredData)} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.orange,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('نسبة المخاطر'),
                            Text(
                              '${_getRiskPercentage(filteredData)}%',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.brown,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                _buildStatCard(
                    'الفروع', filteredData.length.toString(), Colors.blue),
                _buildStatCard('مخاطر عالية',
                    _getHighRiskCount(filteredData).toString(), Colors.red),
                _buildStatCard(
                    'مخاطر متوسطة',
                    _getMediumRiskCount(filteredData).toString(),
                    Colors.orange),
                _buildStatCard('مخاطر منخفضة',
                    _getLowRiskCount(filteredData).toString(), Colors.green),
              ],
            ),
          ),

          // قائمة الفروع
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: filteredData.length,
              itemBuilder: (context, index) {
                final branch = filteredData[index];

                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: _getRiskColor(branch['riskLevel']),
                      child: Text(
                        branch['branchCode'].substring(2),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    title: Text(
                      '${branch['branchCode']} - ${branch['branchName']}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('المدير: ${branch['manager']}'),
                        Text(
                            'إجمالي الديون: ${branch['totalDebt'].toStringAsFixed(2)} ر.س'),
                        Text('آخر دفعة: ${branch['lastPayment']}'),
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: _getRiskColor(branch['riskLevel']),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'مخاطر ${branch['riskLevel']}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            // توزيع أعمار الديون
                            const Text(
                              'توزيع أعمار الديون:',
                              style: TextStyle(
                                  fontWeight: FontWeight.bold, fontSize: 16),
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildAgeCard(
                                      'جاري', branch['current'], Colors.green),
                                ),
                                Expanded(
                                  child: _buildAgeCard('31-60',
                                      branch['days30'], Colors.yellow[700]!),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildAgeCard(
                                      '61-90', branch['days60'], Colors.orange),
                                ),
                                Expanded(
                                  child: _buildAgeCard(
                                      '+90', branch['over90'], Colors.red),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),

                            // مخطط شريطي لأعمار الديون
                            SizedBox(
                              height: 20,
                              child: Row(
                                children: [
                                  Expanded(
                                    flex: (branch['current'] /
                                            branch['totalDebt'] *
                                            100)
                                        .round(),
                                    child: Container(
                                      decoration: const BoxDecoration(
                                        color: Colors.green,
                                        borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(10),
                                          bottomLeft: Radius.circular(10),
                                        ),
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    flex: (branch['days30'] /
                                            branch['totalDebt'] *
                                            100)
                                        .round(),
                                    child: Container(color: Colors.yellow[700]),
                                  ),
                                  Expanded(
                                    flex: (branch['days60'] /
                                            branch['totalDebt'] *
                                            100)
                                        .round(),
                                    child: Container(color: Colors.orange),
                                  ),
                                  Expanded(
                                    flex: (branch['over90'] /
                                            branch['totalDebt'] *
                                            100)
                                        .round(),
                                    child: Container(
                                      decoration: const BoxDecoration(
                                        color: Colors.red,
                                        borderRadius: BorderRadius.only(
                                          topRight: Radius.circular(10),
                                          bottomRight: Radius.circular(10),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            const SizedBox(height: 12),
                            const Text(
                              'أكبر العملاء المدينين:',
                              style: TextStyle(
                                  fontWeight: FontWeight.bold, fontSize: 16),
                            ),
                            const SizedBox(height: 8),
                            ...branch['customers'].map<Widget>((customer) {
                              return ListTile(
                                leading: CircleAvatar(
                                  backgroundColor:
                                      _getAgeColor(customer['age']),
                                  radius: 15,
                                  child: Text(
                                    '${customer['age']}',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                title: Text(customer['name']),
                                subtitle:
                                    Text('عمر الدين: ${customer['age']} يوم'),
                                trailing: Text(
                                  '${customer['debt'].toStringAsFixed(2)} ر.س',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: _getAgeColor(customer['age']),
                                  ),
                                ),
                              );
                            }).toList(),

                            const SizedBox(height: 12),
                            Row(
                              children: [
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () => _contactBranch(branch),
                                    icon: const Icon(Icons.phone),
                                    label: const Text('اتصال'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.green,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () => _sendReminder(branch),
                                    icon: const Icon(Icons.email),
                                    label: const Text('تذكير'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.orange,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () => _viewDetails(branch),
                                    icon: const Icon(Icons.visibility),
                                    label: const Text('التفاصيل'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.blue,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshData,
        backgroundColor: Colors.brown,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث البيانات'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAgeCard(String title, double amount, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Text(
              title,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                color: color,
              ),
            ),
            Text(
              '${amount.toStringAsFixed(0)} ر.س',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getRiskColor(String riskLevel) {
    switch (riskLevel) {
      case 'عالي':
        return Colors.red;
      case 'متوسط':
        return Colors.orange;
      case 'منخفض':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  Color _getAgeColor(int age) {
    if (age <= 30) return Colors.green;
    if (age <= 60) return Colors.yellow[700]!;
    if (age <= 90) return Colors.orange;
    return Colors.red;
  }

  List<Map<String, dynamic>> _getFilteredData() {
    if (_selectedBranch == 'all') {
      return _branchDebtAging;
    }
    return _branchDebtAging
        .where((branch) => branch['branchCode'] == _selectedBranch)
        .toList();
  }

  double _getTotalDebt(List<Map<String, dynamic>> data) {
    return data.fold(0.0, (sum, branch) => sum + branch['totalDebt']);
  }

  double _getOverdueDebt(List<Map<String, dynamic>> data) {
    return data.fold(
        0.0, (sum, branch) => sum + branch['days60'] + branch['over90']);
  }

  String _getRiskPercentage(List<Map<String, dynamic>> data) {
    double totalDebt = _getTotalDebt(data);
    double overdueDebt = _getOverdueDebt(data);
    if (totalDebt == 0) return '0.0';
    return ((overdueDebt / totalDebt) * 100).toStringAsFixed(1);
  }

  int _getHighRiskCount(List<Map<String, dynamic>> data) {
    return data.where((branch) => branch['riskLevel'] == 'عالي').length;
  }

  int _getMediumRiskCount(List<Map<String, dynamic>> data) {
    return data.where((branch) => branch['riskLevel'] == 'متوسط').length;
  }

  int _getLowRiskCount(List<Map<String, dynamic>> data) {
    return data.where((branch) => branch['riskLevel'] == 'منخفض').length;
  }

  void _contactBranch(Map<String, dynamic> branch) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('الاتصال بـ ${branch['branchName']}')),
    );
  }

  void _sendReminder(Map<String, dynamic> branch) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('إرسال تذكير لـ ${branch['branchName']}')),
    );
  }

  void _viewDetails(Map<String, dynamic> branch) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض تفاصيل ${branch['branchName']}')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة تقرير أعمار ديون الفروع')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير تقرير أعمار ديون الفروع')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث بيانات أعمار ديون الفروع'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
