import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير حركة مبيعات الفروع
/// يعرض تحليل مبيعات الفروع المختلفة
class BranchSalesMovementReportPage extends StatefulWidget {
  const BranchSalesMovementReportPage({super.key});

  @override
  State<BranchSalesMovementReportPage> createState() => _BranchSalesMovementReportPageState();
}

class _BranchSalesMovementReportPageState extends State<BranchSalesMovementReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedBranch;
  String? _comparisonType = 'branches';
  String? _sortBy = 'sales_value';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('حركة مبيعات الفروع'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.compare),
            onPressed: _compareBranches,
            tooltip: 'مقارنة الفروع',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.blue[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.fromDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, true),
                        controller: TextEditingController(
                          text: _startDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.toDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, false),
                        controller: TextEditingController(
                          text: _endDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'الفرع',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedBranch,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الفروع')),
                          DropdownMenuItem(value: 'main', child: Text('الفرع الرئيسي')),
                          DropdownMenuItem(value: 'branch1', child: Text('فرع الرياض')),
                          DropdownMenuItem(value: 'branch2', child: Text('فرع جدة')),
                          DropdownMenuItem(value: 'branch3', child: Text('فرع الدمام')),
                        ],
                        onChanged: (value) => setState(() => _selectedBranch = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'نوع المقارنة',
                          border: OutlineInputBorder(),
                        ),
                        value: _comparisonType,
                        items: const [
                          DropdownMenuItem(value: 'branches', child: Text('مقارنة الفروع')),
                          DropdownMenuItem(value: 'periods', child: Text('مقارنة الفترات')),
                          DropdownMenuItem(value: 'products', child: Text('مقارنة المنتجات')),
                          DropdownMenuItem(value: 'performance', child: Text('تحليل الأداء')),
                        ],
                        onChanged: (value) => setState(() => _comparisonType = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'ترتيب حسب',
                          border: OutlineInputBorder(),
                        ),
                        value: _sortBy,
                        items: const [
                          DropdownMenuItem(value: 'sales_value', child: Text('قيمة المبيعات')),
                          DropdownMenuItem(value: 'quantity', child: Text('الكمية المباعة')),
                          DropdownMenuItem(value: 'profit', child: Text('الربح')),
                          DropdownMenuItem(value: 'growth', child: Text('معدل النمو')),
                        ],
                        onChanged: (value) => setState(() => _sortBy = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _generateReport,
                        icon: const Icon(Icons.analytics),
                        label: Text(localizations.generateReport),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          minimumSize: const Size(double.infinity, 48),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص مبيعات الفروع
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.store, color: Colors.blue, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'ملخص مبيعات الفروع',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSummaryCard('إجمالي المبيعات', '4,850,000 ر.س', Colors.blue, Icons.monetization_on),
                              _buildSummaryCard('عدد الفروع', '4', Colors.green, Icons.store),
                              _buildSummaryCard('متوسط المبيعات', '1,212,500 ر.س', Colors.orange, Icons.calculate),
                              _buildSummaryCard('أفضل فرع', 'الرياض', Colors.purple, Icons.emoji_events),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // ترتيب الفروع
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'ترتيب الفروع حسب المبيعات',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ..._buildBranchRankingList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول تفاصيل مبيعات الفروع
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل مبيعات الفروع',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('الفرع')),
                                DataColumn(label: Text('قيمة المبيعات')),
                                DataColumn(label: Text('عدد الفواتير')),
                                DataColumn(label: Text('متوسط الفاتورة')),
                                DataColumn(label: Text('الربح')),
                                DataColumn(label: Text('هامش الربح')),
                                DataColumn(label: Text('النمو')),
                                DataColumn(label: Text('التقييم')),
                                DataColumn(label: Text('إجراءات')),
                              ],
                              rows: _buildBranchSalesRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // مقارنة أداء الفروع
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.compare_arrows, color: Colors.indigo, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'مقارنة أداء الفروع',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildPerformanceCard('أعلى مبيعات', 'فرع الرياض', '1,850,000 ر.س', Colors.green, Icons.trending_up),
                              _buildPerformanceCard('أعلى ربحية', 'فرع جدة', '32% هامش', Colors.blue, Icons.attach_money),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              _buildPerformanceCard('أسرع نمو', 'فرع الدمام', '+25% نمو', Colors.orange, Icons.speed),
                              _buildPerformanceCard('أكثر فواتير', 'الفرع الرئيسي', '1,245 فاتورة', Colors.purple, Icons.receipt),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // أفضل المنتجات في كل فرع
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.star, color: Colors.amber, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'أفضل المنتجات في كل فرع',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          ..._buildTopProductsByBranchList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _transferInventory,
                                  icon: const Icon(Icons.transfer_within_a_station),
                                  label: const Text('نقل مخزون'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _optimizeStaffing,
                                  icon: const Icon(Icons.people),
                                  label: const Text('تحسين الموظفين'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _createPromotions,
                                  icon: const Icon(Icons.local_offer),
                                  label: const Text('إنشاء عروض'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _analyzeTrends,
                                  icon: const Icon(Icons.trending_up),
                                  label: const Text('تحليل الاتجاهات'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildBranchRankingList() {
    final branches = [
      {'name': 'فرع الرياض', 'sales': '1,850,000 ر.س', 'rank': '1', 'growth': '+15%'},
      {'name': 'فرع جدة', 'sales': '1,450,000 ر.س', 'rank': '2', 'growth': '+12%'},
      {'name': 'الفرع الرئيسي', 'sales': '1,200,000 ر.س', 'rank': '3', 'growth': '+8%'},
      {'name': 'فرع الدمام', 'sales': '350,000 ر.س', 'rank': '4', 'growth': '+25%'},
    ];

    return branches.map((branch) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.blue.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.blue.withOpacity(0.05),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getRankColor(int.parse(branch['rank']!)),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Center(
              child: Text(
                branch['rank']!,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  branch['name']!,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'المبيعات: ${branch['sales']} • النمو: ${branch['growth']}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.emoji_events,
            color: _getRankColor(int.parse(branch['rank']!)),
            size: 24,
          ),
        ],
      ),
    )).toList();
  }

  List<Widget> _buildTopProductsByBranchList() {
    final products = [
      {'branch': 'فرع الرياض', 'product': 'لابتوب ديل XPS 13', 'sales': '485,000 ر.س'},
      {'branch': 'فرع جدة', 'product': 'هاتف آيفون 15', 'sales': '320,000 ر.س'},
      {'branch': 'الفرع الرئيسي', 'product': 'طابعة HP LaserJet', 'sales': '285,000 ر.س'},
      {'branch': 'فرع الدمام', 'product': 'ساعة ذكية', 'sales': '185,000 ر.س'},
    ];

    return products.map((product) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.amber.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.amber.withOpacity(0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.star, color: Colors.amber, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  product['product']!,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${product['branch']} • ${product['sales']}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildBranchSalesRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('فرع الرياض')),
        const DataCell(Text('1,850,000 ر.س')),
        const DataCell(Text('485')),
        const DataCell(Text('3,814 ر.س')),
        const DataCell(Text('555,000 ر.س')),
        const DataCell(Text('30.0%')),
        const DataCell(Text('+15%')),
        DataCell(_buildPerformanceBadge('ممتاز', Colors.green)),
        DataCell(IconButton(
          icon: const Icon(Icons.visibility, color: Colors.blue, size: 16),
          onPressed: () => _viewBranchDetails('فرع الرياض'),
        )),
      ]),
      DataRow(cells: [
        const DataCell(Text('فرع جدة')),
        const DataCell(Text('1,450,000 ر.س')),
        const DataCell(Text('385')),
        const DataCell(Text('3,766 ر.س')),
        const DataCell(Text('464,000 ر.س')),
        const DataCell(Text('32.0%')),
        const DataCell(Text('+12%')),
        DataCell(_buildPerformanceBadge('جيد جداً', Colors.blue)),
        DataCell(IconButton(
          icon: const Icon(Icons.visibility, color: Colors.blue, size: 16),
          onPressed: () => _viewBranchDetails('فرع جدة'),
        )),
      ]),
      DataRow(cells: [
        const DataCell(Text('فرع الدمام')),
        const DataCell(Text('350,000 ر.س')),
        const DataCell(Text('125')),
        const DataCell(Text('2,800 ر.س')),
        const DataCell(Text('87,500 ر.س')),
        const DataCell(Text('25.0%')),
        const DataCell(Text('+25%')),
        DataCell(_buildPerformanceBadge('جيد', Colors.orange)),
        DataCell(IconButton(
          icon: const Icon(Icons.visibility, color: Colors.blue, size: 16),
          onPressed: () => _viewBranchDetails('فرع الدمام'),
        )),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPerformanceCard(String title, String branch, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                title,
                style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                branch,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
                textAlign: TextAlign.center,
              ),
              Text(
                value,
                style: TextStyle(fontSize: 10, color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPerformanceBadge(String performance, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        performance,
        style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold),
      ),
    );
  }

  Color _getRankColor(int rank) {
    switch (rank) {
      case 1:
        return Colors.amber;
      case 2:
        return Colors.grey;
      case 3:
        return Colors.brown;
      default:
        return Colors.blue;
    }
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير حركة مبيعات الفروع بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _compareBranches() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مقارنة تفصيلية بين الفروع')),
    );
  }

  void _transferInventory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('نقل المخزون بين الفروع')),
    );
  }

  void _optimizeStaffing() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحسين توزيع الموظفين')),
    );
  }

  void _createPromotions() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء عروض ترويجية للفروع')),
    );
  }

  void _analyzeTrends() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحليل اتجاهات مبيعات الفروع')),
    );
  }

  void _viewBranchDetails(String branchName) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض تفاصيل $branchName')),
    );
  }
}
