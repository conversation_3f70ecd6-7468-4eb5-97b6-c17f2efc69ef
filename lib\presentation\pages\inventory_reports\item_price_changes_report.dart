import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير تغير أسعار الأصناف
/// يعرض تغييرات أسعار الأصناف خلال فترة معينة
class ItemPriceChangesReportPage extends StatefulWidget {
  const ItemPriceChangesReportPage({super.key});

  @override
  State<ItemPriceChangesReportPage> createState() => _ItemPriceChangesReportPageState();
}

class _ItemPriceChangesReportPageState extends State<ItemPriceChangesReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedCategory;
  String? _priceChangeType = 'all';
  String? _selectedUser;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('تغير أسعار الأصناف'),
        backgroundColor: Colors.deepOrange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showPriceAnalysis,
            tooltip: 'تحليل الأسعار',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.deepOrange[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.fromDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, true),
                        controller: TextEditingController(
                          text: _startDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.toDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, false),
                        controller: TextEditingController(
                          text: _endDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.category,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedCategory,
                        items: [
                          DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                          DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                          DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                          DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                        ],
                        onChanged: (value) => setState(() => _selectedCategory = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'نوع التغيير',
                          border: OutlineInputBorder(),
                        ),
                        value: _priceChangeType,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع التغييرات')),
                          DropdownMenuItem(value: 'increase', child: Text('زيادة الأسعار')),
                          DropdownMenuItem(value: 'decrease', child: Text('تخفيض الأسعار')),
                          DropdownMenuItem(value: 'major', child: Text('تغييرات كبيرة (>10%)')),
                        ],
                        onChanged: (value) => setState(() => _priceChangeType = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'المستخدم',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedUser,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع المستخدمين')),
                          DropdownMenuItem(value: 'admin', child: Text('المدير العام')),
                          DropdownMenuItem(value: 'manager', child: Text('مدير المبيعات')),
                          DropdownMenuItem(value: 'employee', child: Text('موظف المخزون')),
                        ],
                        onChanged: (value) => setState(() => _selectedUser = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _generateReport,
                        icon: const Icon(Icons.search),
                        label: Text(localizations.generateReport),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.deepOrange,
                          foregroundColor: Colors.white,
                          minimumSize: const Size(double.infinity, 48),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص تغييرات الأسعار
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.price_change, color: Colors.deepOrange, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'ملخص تغييرات الأسعار',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSummaryCard('إجمالي التغييرات', '156', Colors.blue, Icons.edit),
                              _buildSummaryCard('زيادة الأسعار', '89', Colors.green, Icons.trending_up),
                              _buildSummaryCard('تخفيض الأسعار', '67', Colors.red, Icons.trending_down),
                              _buildSummaryCard('متوسط التغيير', '8.5%', Colors.purple, Icons.percent),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // الرسم البياني لتوزيع التغييرات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'توزيع تغييرات الأسعار',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Container(
                            height: 200,
                            width: double.infinity,
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey[300]!),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  flex: 57,
                                  child: Container(
                                    margin: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.green.withOpacity(0.7),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: const Center(
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Text('زيادة الأسعار', style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
                                          Text('89 تغيير', style: TextStyle(color: Colors.white, fontSize: 12)),
                                          Text('57%', style: TextStyle(color: Colors.white, fontSize: 12)),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                Expanded(
                                  flex: 43,
                                  child: Container(
                                    margin: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.red.withOpacity(0.7),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: const Center(
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Text('تخفيض الأسعار', style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
                                          Text('67 تغيير', style: TextStyle(color: Colors.white, fontSize: 12)),
                                          Text('43%', style: TextStyle(color: Colors.white, fontSize: 12)),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول تفاصيل تغييرات الأسعار
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل تغييرات الأسعار',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('كود الصنف')),
                                DataColumn(label: Text('اسم الصنف')),
                                DataColumn(label: Text('السعر السابق')),
                                DataColumn(label: Text('السعر الجديد')),
                                DataColumn(label: Text('نسبة التغيير')),
                                DataColumn(label: Text('نوع التغيير')),
                                DataColumn(label: Text('تاريخ التغيير')),
                                DataColumn(label: Text('المستخدم')),
                                DataColumn(label: Text('السبب')),
                              ],
                              rows: _buildPriceChangeRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // أكبر التغييرات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.trending_up, color: Colors.green, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'أكبر التغييرات في الأسعار',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          ..._buildTopPriceChangesList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _bulkPriceUpdate,
                                  icon: const Icon(Icons.update),
                                  label: const Text('تحديث جماعي'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _revertPriceChange,
                                  icon: const Icon(Icons.undo),
                                  label: const Text('التراجع عن تغيير'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _schedulePriceUpdate,
                                  icon: const Icon(Icons.schedule),
                                  label: const Text('جدولة تحديث'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _auditPriceChanges,
                                  icon: const Icon(Icons.security),
                                  label: const Text('مراجعة التغييرات'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<DataRow> _buildPriceChangeRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('ITEM-001')),
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('2,400 ر.س')),
        const DataCell(Text('2,500 ر.س')),
        DataCell(_buildPercentageBadge('****%', Colors.green)),
        DataCell(_buildChangeTypeBadge('زيادة', Colors.green)),
        const DataCell(Text('2024-02-15')),
        const DataCell(Text('أحمد محمد')),
        const DataCell(Text('زيادة تكلفة الشراء')),
      ]),
      DataRow(cells: [
        const DataCell(Text('ITEM-002')),
        const DataCell(Text('قميص قطني')),
        const DataCell(Text('95 ر.س')),
        const DataCell(Text('85 ر.س')),
        DataCell(_buildPercentageBadge('-10.5%', Colors.red)),
        DataCell(_buildChangeTypeBadge('تخفيض', Colors.red)),
        const DataCell(Text('2024-02-14')),
        const DataCell(Text('فاطمة أحمد')),
        const DataCell(Text('عرض ترويجي')),
      ]),
      DataRow(cells: [
        const DataCell(Text('ITEM-003')),
        const DataCell(Text('هاتف آيفون 15')),
        const DataCell(Text('2,800 ر.س')),
        const DataCell(Text('3,000 ر.س')),
        DataCell(_buildPercentageBadge('****%', Colors.green)),
        DataCell(_buildChangeTypeBadge('زيادة', Colors.green)),
        const DataCell(Text('2024-02-13')),
        const DataCell(Text('محمد علي')),
        const DataCell(Text('تحديث أسعار الموردين')),
      ]),
    ];
  }

  List<Widget> _buildTopPriceChangesList() {
    final changes = [
      {'name': 'هاتف آيفون 15', 'oldPrice': '2,800 ر.س', 'newPrice': '3,000 ر.س', 'change': '****%', 'type': 'increase'},
      {'name': 'قميص قطني', 'oldPrice': '95 ر.س', 'newPrice': '85 ر.س', 'change': '-10.5%', 'type': 'decrease'},
      {'name': 'لابتوب ديل XPS 13', 'oldPrice': '2,400 ر.س', 'newPrice': '2,500 ر.س', 'change': '****%', 'type': 'increase'},
      {'name': 'ساعة ذكية', 'oldPrice': '1,200 ر.س', 'newPrice': '1,050 ر.س', 'change': '-12.5%', 'type': 'decrease'},
    ];

    return changes.map((change) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.deepOrange.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.deepOrange.withOpacity(0.05),
      ),
      child: Row(
        children: [
          Icon(
            change['type'] == 'increase' ? Icons.trending_up : Icons.trending_down,
            color: change['type'] == 'increase' ? Colors.green : Colors.red,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  change['name']!,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${change['oldPrice']} ← ${change['newPrice']}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: change['type'] == 'increase' ? Colors.green : Colors.red,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              change['change']!,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPercentageBadge(String percentage, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        percentage,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildChangeTypeBadge(String type, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        type,
        style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير تغير أسعار الأصناف بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showPriceAnalysis() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تحليل مفصل للأسعار')),
    );
  }

  void _bulkPriceUpdate() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('فتح أداة التحديث الجماعي للأسعار')),
    );
  }

  void _revertPriceChange() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('التراجع عن آخر تغيير في الأسعار')),
    );
  }

  void _schedulePriceUpdate() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جدولة تحديث الأسعار')),
    );
  }

  void _auditPriceChanges() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مراجعة وتدقيق تغييرات الأسعار')),
    );
  }
}
