import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تحديد ملف التشغيل الرئيسي على السيرفر
/// تتيح للمدير تحديد وإدارة ملف التشغيل الرئيسي للنظام على السيرفر
class ServerMainFilePage extends StatefulWidget {
  const ServerMainFilePage({super.key});

  @override
  State<ServerMainFilePage> createState() => _ServerMainFilePageState();
}

class _ServerMainFilePageState extends State<ServerMainFilePage> {
  final _formKey = GlobalKey<FormState>();
  final _filePathController = TextEditingController();
  final _backupPathController = TextEditingController();

  String? _selectedServer;
  String? _selectedEnvironment;
  String? _selectedFileType;
  bool _isProcessing = false;
  bool _autoBackup = true;
  bool _validateFile = true;

  final List<Map<String, String>> _servers = [
    {
      'id': 'prod',
      'name': 'خادم الإنتاج',
      'ip': '*************',
      'status': 'متصل'
    },
    {
      'id': 'test',
      'name': 'خادم الاختبار',
      'ip': '*************',
      'status': 'متصل'
    },
    {
      'id': 'dev',
      'name': 'خادم التطوير',
      'ip': '*************',
      'status': 'غير متصل'
    },
    {
      'id': 'backup',
      'name': 'خادم النسخ الاحتياطي',
      'ip': '*************',
      'status': 'متصل'
    },
  ];

  final List<Map<String, String>> _environments = [
    {
      'id': 'production',
      'name': 'بيئة الإنتاج',
      'description': 'البيئة الرئيسية للعمل'
    },
    {
      'id': 'staging',
      'name': 'بيئة التجهيز',
      'description': 'بيئة ما قبل الإنتاج'
    },
    {
      'id': 'testing',
      'name': 'بيئة الاختبار',
      'description': 'بيئة اختبار الميزات'
    },
    {
      'id': 'development',
      'name': 'بيئة التطوير',
      'description': 'بيئة تطوير النظام'
    },
  ];

  final List<Map<String, String>> _fileTypes = [
    {
      'id': 'exe',
      'name': 'ملف تنفيذي (.exe)',
      'description': 'ملف تطبيق Windows'
    },
    {'id': 'jar', 'name': 'ملف Java (.jar)', 'description': 'تطبيق Java'},
    {'id': 'war', 'name': 'ملف ويب (.war)', 'description': 'تطبيق ويب Java'},
    {'id': 'dll', 'name': 'مكتبة (.dll)', 'description': 'مكتبة ديناميكية'},
    {
      'id': 'service',
      'name': 'خدمة Windows',
      'description': 'خدمة نظام Windows'
    },
  ];

  final List<Map<String, dynamic>> _currentFiles = [
    {
      'name': 'SalesPro.exe',
      'path': 'C:\\Program Files\\SalesPro\\SalesPro.exe',
      'size': '25.6 MB',
      'version': '1.2.3',
      'lastModified': '2024/01/25 10:30',
      'isActive': true,
      'server': 'prod',
    },
    {
      'name': 'SalesProTest.exe',
      'path': 'C:\\Program Files\\SalesPro\\Test\\SalesProTest.exe',
      'size': '26.1 MB',
      'version': '1.3.0-beta',
      'lastModified': '2024/01/24 15:45',
      'isActive': false,
      'server': 'test',
    },
    {
      'name': 'SalesProService.exe',
      'path': 'C:\\Services\\SalesPro\\SalesProService.exe',
      'size': '18.2 MB',
      'version': '1.2.3',
      'lastModified': '2024/01/23 09:15',
      'isActive': true,
      'server': 'prod',
    },
  ];

  @override
  void dispose() {
    _filePathController.dispose();
    _backupPathController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.serverMainFile),
        backgroundColor: Colors.deepOrange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.cloud_sync),
            onPressed: _syncWithServer,
            tooltip: 'مزامنة مع السيرفر',
          ),
          IconButton(
            icon: const Icon(Icons.monitor),
            onPressed: _showServerStatus,
            tooltip: 'حالة السيرفرات',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // بطاقة اختيار السيرفر والبيئة
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'إعدادات السيرفر والبيئة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.deepOrange,
                      ),
                    ),
                    const SizedBox(height: 16),
                    // السيرفر
                    DropdownButtonFormField<String>(
                      value: _selectedServer,
                      decoration: const InputDecoration(
                        labelText: 'السيرفر',
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 4, vertical: 4),
                      ),
                      items: _servers.map<DropdownMenuItem<String>>((server) {
                        return DropdownMenuItem<String>(
                          value: server['id'],
                          child: Text(
                            server['name']!,
                            style: const TextStyle(fontSize: 12),
                            overflow: TextOverflow.ellipsis,
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedServer = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار السيرفر';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // البيئة
                    DropdownButtonFormField<String>(
                      value: _selectedEnvironment,
                      decoration: const InputDecoration(
                        labelText: 'البيئة',
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 4, vertical: 4),
                      ),
                      items: _environments.map<DropdownMenuItem<String>>((env) {
                        return DropdownMenuItem<String>(
                          value: env['id'],
                          child: Text(
                            env['name']!,
                            style: const TextStyle(fontSize: 12),
                            overflow: TextOverflow.ellipsis,
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedEnvironment = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار البيئة';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة تحديد الملف
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'تحديد ملف التشغيل',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.deepOrange,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // نوع الملف
                    DropdownButtonFormField<String>(
                      value: _selectedFileType,
                      decoration: const InputDecoration(
                        labelText: 'نوع الملف',
                        prefixIcon: Icon(Icons.insert_drive_file, size: 20),
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                      ),
                      items: _fileTypes.map<DropdownMenuItem<String>>((type) {
                        return DropdownMenuItem<String>(
                          value: type['id'],
                          child: Text(
                            type['name']!,
                            style: const TextStyle(fontSize: 14),
                            overflow: TextOverflow.ellipsis,
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedFileType = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار نوع الملف';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // مسار الملف
                    TextFormField(
                      controller: _filePathController,
                      decoration: InputDecoration(
                        labelText: 'مسار الملف',
                        prefixIcon: const Icon(Icons.folder, size: 20),
                        suffixIcon: IconButton(
                          icon: const Icon(Icons.folder_open),
                          onPressed: _browseFile,
                        ),
                        border: const OutlineInputBorder(),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 8),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال مسار الملف';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // مسار النسخة الاحتياطية
                    TextFormField(
                      controller: _backupPathController,
                      decoration: InputDecoration(
                        labelText: 'مسار النسخة الاحتياطية',
                        prefixIcon: const Icon(Icons.backup, size: 20),
                        suffixIcon: IconButton(
                          icon: const Icon(Icons.folder_open),
                          onPressed: _browseBackupPath,
                        ),
                        border: const OutlineInputBorder(),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 8),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة خيارات إضافية
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'خيارات إضافية',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.deepOrange,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SwitchListTile(
                      title: const Text('إنشاء نسخة احتياطية تلقائية'),
                      subtitle: const Text(
                          'إنشاء نسخة احتياطية من الملف الحالي قبل التحديث'),
                      value: _autoBackup,
                      onChanged: (value) {
                        setState(() {
                          _autoBackup = value;
                        });
                      },
                    ),
                    SwitchListTile(
                      title: const Text('التحقق من صحة الملف'),
                      subtitle:
                          const Text('التحقق من صحة الملف وتوافقه مع النظام'),
                      value: _validateFile,
                      onChanged: (value) {
                        setState(() {
                          _validateFile = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة الملفات الحالية
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'الملفات الحالية على السيرفر',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.deepOrange,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ...(_currentFiles.map((file) => Card(
                          margin: const EdgeInsets.symmetric(vertical: 4),
                          child: ListTile(
                            leading: CircleAvatar(
                              backgroundColor:
                                  file['isActive'] ? Colors.green : Colors.grey,
                              child: Icon(
                                file['isActive']
                                    ? Icons.play_arrow
                                    : Icons.pause,
                                color: Colors.white,
                              ),
                            ),
                            title: Text(file['name']),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'المسار: ${file['path']}',
                                  style: const TextStyle(fontSize: 12),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                                Text(
                                  'الحجم: ${file['size']} | الإصدار: ${file['version']}',
                                  style: const TextStyle(fontSize: 12),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                                Text(
                                  'آخر تعديل: ${file['lastModified']}',
                                  style: const TextStyle(fontSize: 12),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              ],
                            ),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                if (file['isActive'])
                                  const Icon(Icons.star,
                                      color: Colors.orange, size: 20),
                                IconButton(
                                  icon: const Icon(Icons.more_vert),
                                  onPressed: () => _showFileOptions(file),
                                ),
                              ],
                            ),
                            isThreeLine: true,
                          ),
                        ))),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // معاينة العملية
            if (_selectedServer != null &&
                _selectedEnvironment != null &&
                _selectedFileType != null)
              Card(
                color: Colors.deepOrange.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'معاينة العملية',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.deepOrange,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildPreviewRow('السيرفر:', _getServerName()),
                      _buildPreviewRow('البيئة:', _getEnvironmentName()),
                      _buildPreviewRow('نوع الملف:', _getFileTypeName()),
                      _buildPreviewRow(
                          'مسار الملف:',
                          _filePathController.text.isEmpty
                              ? 'غير محدد'
                              : _filePathController.text),
                      _buildPreviewRow(
                          'نسخة احتياطية:', _autoBackup ? 'نعم' : 'لا'),
                      _buildPreviewRow(
                          'التحقق من الملف:', _validateFile ? 'نعم' : 'لا'),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 24),

            // أزرار العمليات
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isProcessing ? null : _updateMainFile,
                    icon: _isProcessing
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.upload),
                    label: Text(_isProcessing
                        ? 'جاري التحديث...'
                        : 'تحديث ملف التشغيل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.deepOrange,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _testFile,
                    icon: const Icon(Icons.play_circle),
                    label: const Text('اختبار الملف'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(
              value,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        ],
      ),
    );
  }

  String _getServerName() {
    if (_selectedServer == null) return 'غير محدد';
    final server = _servers.firstWhere((s) => s['id'] == _selectedServer);
    return '${server['name']} (${server['ip']})';
  }

  String _getEnvironmentName() {
    if (_selectedEnvironment == null) return 'غير محدد';
    final env =
        _environments.firstWhere((e) => e['id'] == _selectedEnvironment);
    return env['name']!;
  }

  String _getFileTypeName() {
    if (_selectedFileType == null) return 'غير محدد';
    final type = _fileTypes.firstWhere((t) => t['id'] == _selectedFileType);
    return type['name']!;
  }

  void _browseFile() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('فتح متصفح الملفات لاختيار الملف')),
    );
  }

  void _browseBackupPath() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
          content: Text('فتح متصفح الملفات لاختيار مجلد النسخ الاحتياطي')),
    );
  }

  Future<void> _updateMainFile() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isProcessing = true;
      });

      // محاكاة عملية التحديث
      await Future.delayed(const Duration(seconds: 3));

      setState(() {
        _isProcessing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث ملف التشغيل الرئيسي بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _testFile() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('اختبار الملف وتشغيله تجريبياً')),
    );
  }

  void _showFileOptions(Map<String, dynamic> file) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'خيارات الملف: ${file['name']}',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.info),
              title: const Text('تفاصيل الملف'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('عرض تفاصيل الملف ${file['name']}')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.download),
              title: const Text('تحميل الملف'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('تحميل الملف ${file['name']}')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title:
                  const Text('حذف الملف', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.pop(context);
                _showDeleteFileConfirmation(file);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteFileConfirmation(Map<String, dynamic> file) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الملف ${file['name']}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _currentFiles.remove(file);
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف الملف ${file['name']}'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _syncWithServer() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مزامنة البيانات مع السيرفر')),
    );
  }

  void _showServerStatus() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض حالة جميع السيرفرات')),
    );
  }
}
