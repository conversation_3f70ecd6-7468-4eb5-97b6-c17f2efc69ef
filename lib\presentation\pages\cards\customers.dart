import 'package:flutter/material.dart';
import '../../widgets/global_buttons/new_button.dart';
import '../../widgets/global_buttons/query_button.dart';
import '../../widgets/global_buttons/delete_button.dart';
import '../../widgets/global_buttons/undo_button.dart';
import '../../widgets/global_buttons/save_button.dart';
import '../../widgets/global_buttons/print_button.dart';

/// نموذج بيانات العميل
class CustomerData {
  // المعلومات الأساسية
  String accountType;
  String accountNumber;
  String branchNumber;
  String branchName;
  String securityLevel;
  bool unlinkFromSalesman;
  bool isMainAccount;
  String defaultPaymentMethod;
  bool enableSmsNotification;

  // معلومات عامة
  String arabicName;
  String englishName;
  String accountManagerName;
  String accountManagerPhone;
  bool accountManagerSms;
  String purchaseManagerName;
  String purchaseManagerPhone;
  bool purchaseManagerSms;
  String financeManagerName;
  String financeManagerPhone;
  bool financeManagerSms;
  String receivingManagerName;
  String receivingManagerPhone;
  bool receivingManagerSms;
  String outlet;
  String phone;
  String address;
  String country;
  String city;
  String district;
  String street;
  String buildingNumber;
  String postalCode;
  String additionalNumber;
  String unit;
  String shortAddress;
  String longitude;
  String latitude;
  String plusCode;
  String website;
  String email;

  // معلومات محاسبية
  String salesmanNumber;
  String accountLevel;
  String classification1;
  String classification2;
  double creditLimit;
  int duePeriod;
  double invoiceDiscount;
  String balanceFreeze;
  String balanceWarning;
  double targetAmount;
  double currentBalance;
  String notes;
  String taxId;
  String taxApplication;
  String customerType;
  String currency;

  CustomerData({
    this.accountType = 'عميل',
    this.accountNumber = '',
    this.branchNumber = '001',
    this.branchName = '',
    this.securityLevel = 'عادي',
    this.unlinkFromSalesman = false,
    this.isMainAccount = false,
    this.defaultPaymentMethod = 'نقدي',
    this.enableSmsNotification = false,
    this.arabicName = '',
    this.englishName = '',
    this.accountManagerName = '',
    this.accountManagerPhone = '',
    this.accountManagerSms = false,
    this.purchaseManagerName = '',
    this.purchaseManagerPhone = '',
    this.purchaseManagerSms = false,
    this.financeManagerName = '',
    this.financeManagerPhone = '',
    this.financeManagerSms = false,
    this.receivingManagerName = '',
    this.receivingManagerPhone = '',
    this.receivingManagerSms = false,
    this.outlet = '',
    this.phone = '',
    this.address = '',
    this.country = 'السعودية',
    this.city = '',
    this.district = '',
    this.street = '',
    this.buildingNumber = '',
    this.postalCode = '',
    this.additionalNumber = '',
    this.unit = '',
    this.shortAddress = '',
    this.longitude = '',
    this.latitude = '',
    this.plusCode = '',
    this.website = '',
    this.email = '',
    this.salesmanNumber = '',
    this.accountLevel = 'مستوى 1',
    this.classification1 = '',
    this.classification2 = '',
    this.creditLimit = 0.0,
    this.duePeriod = 30,
    this.invoiceDiscount = 0.0,
    this.balanceFreeze = 'بدون',
    this.balanceWarning = 'بدون',
    this.targetAmount = 0.0,
    this.currentBalance = 0.0,
    this.notes = '',
    this.taxId = '',
    this.taxApplication = 'خاضع للضريبة',
    this.customerType = 'عميل عادي',
    this.currency = 'ريال سعودي',
  });
}

/// صفحة العملاء
class Customers extends StatefulWidget {
  const Customers({super.key});

  @override
  State<Customers> createState() => _CustomersState();
}

class _CustomersState extends State<Customers> with TickerProviderStateMixin {
  late TabController _tabController;
  CustomerData _customerData = CustomerData();

  // متحكمات النصوص
  final Map<String, TextEditingController> _controllers = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 7, vsync: this);
    _initializeControllers();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _disposeControllers();
    super.dispose();
  }

  /// تهيئة متحكمات النصوص
  void _initializeControllers() {
    final fields = [
      'arabicName',
      'englishName',
      'accountManagerName',
      'accountManagerPhone',
      'purchaseManagerName',
      'purchaseManagerPhone',
      'financeManagerName',
      'financeManagerPhone',
      'receivingManagerName',
      'receivingManagerPhone',
      'outlet',
      'phone',
      'address',
      'country',
      'city',
      'district',
      'street',
      'buildingNumber',
      'postalCode',
      'additionalNumber',
      'unit',
      'shortAddress',
      'longitude',
      'latitude',
      'plusCode',
      'website',
      'email',
      'salesmanNumber',
      'creditLimit',
      'duePeriod',
      'invoiceDiscount',
      'targetAmount',
      'notes',
      'taxId'
    ];

    for (String field in fields) {
      _controllers[field] = TextEditingController();
    }
  }

  /// تنظيف متحكمات النصوص
  void _disposeControllers() {
    for (var controller in _controllers.values) {
      controller.dispose();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'العملاء',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        elevation: 2,
      ),
      body: Column(
        children: [
          // أزرار التحكم
          _buildControlButtons(),

          // التبويبات
          _buildTabBar(),

          // محتوى التبويبات
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildBasicInfoTab(), // المعلومات الأساسية
                _buildGeneralInfoTab(), // معلومات عامة
                _buildAccountingInfoTab(), // معلومات محاسبية
                _buildCustomerIncentiveTab(), // تقرير حافز العميل
                _buildReportsTab(), // التقارير
                _buildUserInfoTab(), // معلومات المستخدم
                _buildAdditionalTab(), // تبويب إضافي
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء أزرار التحكم
  Widget _buildControlButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Row(
        children: [
          NewButton(onPressed: _addNewCustomer),
          const SizedBox(width: 8),
          QueryButton(onPressed: _queryCustomer),
          const SizedBox(width: 8),
          DeleteButton(onPressed: _deleteCustomer),
          const SizedBox(width: 8),
          UndoButton(onPressed: _undoChanges),
          const SizedBox(width: 8),
          SaveButton(onPressed: _saveCustomer),
          const SizedBox(width: 8),
          PrintButton(onPressed: _printCustomer),
        ],
      ),
    );
  }

  /// بناء شريط التبويبات
  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        labelColor: Colors.blue[700],
        unselectedLabelColor: Colors.grey[600],
        indicatorColor: Colors.blue[700],
        indicatorWeight: 3,
        labelStyle: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 14,
        ),
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.normal,
          fontSize: 14,
        ),
        tabs: const [
          Tab(text: 'المعلومات الأساسية'),
          Tab(text: 'معلومات عامة'),
          Tab(text: 'معلومات محاسبية'),
          Tab(text: 'تقرير حافز العميل'),
          Tab(text: 'التقارير'),
          Tab(text: 'معلومات المستخدم'),
          Tab(text: 'إضافي'),
        ],
      ),
    );
  }

  // ===== دوال أزرار التحكم =====

  /// إضافة عميل جديد
  void _addNewCustomer() {
    setState(() {
      _customerData = CustomerData();
      _clearAllControllers();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إنشاء عميل جديد'),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// استعلام عن عميل
  void _queryCustomer() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('استعلام عن العميل'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// حذف عميل
  void _deleteCustomer() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا العميل؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم حذف العميل'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// تراجع عن التغييرات
  void _undoChanges() {
    setState(() {
      _clearAllControllers();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم التراجع عن التغييرات'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  /// حفظ العميل
  void _saveCustomer() {
    _updateCustomerDataFromControllers();

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم حفظ بيانات العميل'),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// طباعة بيانات العميل
  void _printCustomer() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري طباعة بيانات العميل'),
        backgroundColor: Colors.purple,
      ),
    );
  }

  /// مسح جميع متحكمات النصوص
  void _clearAllControllers() {
    for (var controller in _controllers.values) {
      controller.clear();
    }
  }

  /// تحديث بيانات العميل من متحكمات النصوص
  void _updateCustomerDataFromControllers() {
    _customerData.arabicName = _controllers['arabicName']?.text ?? '';
    _customerData.englishName = _controllers['englishName']?.text ?? '';
    _customerData.phone = _controllers['phone']?.text ?? '';
    _customerData.email = _controllers['email']?.text ?? '';
    // إضافة باقي الحقول حسب الحاجة
  }

  // ===== بناء التبويبات =====

  /// التبويب الأول: المعلومات الأساسية
  Widget _buildBasicInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('المعلومات الأساسية'),
          const SizedBox(height: 16),

          // الصف الأول
          Row(
            children: [
              Expanded(
                child: _buildDropdownField(
                  'نوع الحساب',
                  _customerData.accountType,
                  ['عميل', 'مورد', 'عميل ومورد'],
                  (value) => setState(() => _customerData.accountType = value!),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDropdownField(
                  'رقم الحساب',
                  _customerData.accountNumber,
                  ['تلقائي', '001', '002', '003'],
                  (value) =>
                      setState(() => _customerData.accountNumber = value!),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // الصف الثاني
          Row(
            children: [
              Expanded(
                child: _buildDropdownField(
                  'رقم فرع الحساب',
                  _customerData.branchNumber,
                  ['001', '002', '003', '004'],
                  (value) =>
                      setState(() => _customerData.branchNumber = value!),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child:
                    _buildDisplayField('اسم الفرع', _customerData.branchName),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // الصف الثالث
          Row(
            children: [
              Expanded(
                child: _buildDropdownField(
                  'مستوى السرية',
                  _customerData.securityLevel,
                  ['عادي', 'سري', 'سري جداً'],
                  (value) =>
                      setState(() => _customerData.securityLevel = value!),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDropdownField(
                  'طريقة الدفع الافتراضية',
                  _customerData.defaultPaymentMethod,
                  ['نقدي', 'آجل', 'شيك', 'تحويل بنكي'],
                  (value) => setState(
                      () => _customerData.defaultPaymentMethod = value!),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // مربعات الاختيار
          _buildCheckboxField(
            'إلغاء ربط بالمندوب',
            _customerData.unlinkFromSalesman,
            (value) =>
                setState(() => _customerData.unlinkFromSalesman = value!),
          ),

          const SizedBox(height: 8),

          _buildCheckboxField(
            'حساب رئيسي',
            _customerData.isMainAccount,
            (value) => setState(() => _customerData.isMainAccount = value!),
          ),

          const SizedBox(height: 8),

          _buildCheckboxField(
            'تفعيل خدمة إشعار برسالة SMS',
            _customerData.enableSmsNotification,
            (value) =>
                setState(() => _customerData.enableSmsNotification = value!),
          ),
        ],
      ),
    );
  }

  // ===== دوال مساعدة لبناء عناصر الواجهة =====

  /// بناء عنوان القسم
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Colors.blue[700],
      ),
    );
  }

  /// بناء حقل قائمة منسدلة
  Widget _buildDropdownField(
    String label,
    String value,
    List<String> items,
    ValueChanged<String?> onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: items.contains(value) ? value : items.first,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: items.map((String item) {
            return DropdownMenuItem<String>(
              value: item,
              child: Text(item),
            );
          }).toList(),
          onChanged: onChanged,
        ),
      ],
    );
  }

  /// بناء حقل عرض فقط
  Widget _buildDisplayField(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[400]!),
            borderRadius: BorderRadius.circular(8),
            color: Colors.grey[100],
          ),
          child: Text(
            value.isEmpty ? 'غير محدد' : value,
            style: TextStyle(
              color: value.isEmpty ? Colors.grey[600] : Colors.black,
            ),
          ),
        ),
      ],
    );
  }

  /// بناء مربع اختيار
  Widget _buildCheckboxField(
    String label,
    bool value,
    ValueChanged<bool?> onChanged,
  ) {
    return IntrinsicWidth(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Checkbox(
            value: value,
            onChanged: onChanged,
            activeColor: Colors.blue[700],
          ),
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حقل إدخال نص
  Widget _buildTextFormField(
    String label,
    TextEditingController controller, {
    TextInputType keyboardType = TextInputType.text,
    int maxLines = 1,
    String? suffix,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          maxLines: maxLines,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            suffixText: suffix,
          ),
        ),
      ],
    );
  }

  /// التبويب الثاني: معلومات عامة
  Widget _buildGeneralInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('معلومات عامة'),
          const SizedBox(height: 16),

          // الأسماء
          Row(
            children: [
              Expanded(
                child: _buildTextFormField(
                  'اسم الحساب بالعربي',
                  _controllers['arabicName']!,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTextFormField(
                  'الاسم الإنجليزي',
                  _controllers['englishName']!,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // المسؤول عن الحساب
          _buildContactSection(
            'اسم الشخص المسؤول عن الحساب',
            'accountManager',
          ),

          const SizedBox(height: 16),

          // المسؤول عن المشتريات
          _buildContactSection(
            'اسم الشخص المسؤول عن المشتريات',
            'purchaseManager',
          ),

          const SizedBox(height: 16),

          // المسؤول عن الأمور المالية
          _buildContactSection(
            'اسم الشخص المسؤول عن الأمور المالية',
            'financeManager',
          ),

          const SizedBox(height: 16),

          // المسؤول عن استلام البضاعة
          _buildContactSection(
            'اسم الشخص المسؤول عن استلام البضاعة',
            'receivingManager',
          ),

          const SizedBox(height: 16),

          // معلومات الاتصال العامة
          Row(
            children: [
              Expanded(
                child: _buildTextFormField(
                  'المخرج',
                  _controllers['outlet']!,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTextFormField(
                  'هاتف',
                  _controllers['phone']!,
                  keyboardType: TextInputType.phone,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // العنوان
          _buildSectionTitle('معلومات العنوان'),
          const SizedBox(height: 12),

          _buildTextFormField(
            'العنوان',
            _controllers['address']!,
            maxLines: 2,
          ),

          const SizedBox(height: 16),

          // الدولة والمدينة
          Row(
            children: [
              Expanded(
                child: _buildTextFormField(
                  'الدولة',
                  _controllers['country']!,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTextFormField(
                  'المدينة',
                  _controllers['city']!,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // الحي والشارع
          Row(
            children: [
              Expanded(
                child: _buildTextFormField(
                  'الحي',
                  _controllers['district']!,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTextFormField(
                  'الشارع',
                  _controllers['street']!,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // رقم المبنى والرمز البريدي
          Row(
            children: [
              Expanded(
                child: _buildTextFormField(
                  'رقم المبنى',
                  _controllers['buildingNumber']!,
                  keyboardType: TextInputType.number,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTextFormField(
                  'رمز بريدي',
                  _controllers['postalCode']!,
                  keyboardType: TextInputType.number,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // رقم إضافي والوحدة
          Row(
            children: [
              Expanded(
                child: _buildTextFormField(
                  'رقم إضافي',
                  _controllers['additionalNumber']!,
                  keyboardType: TextInputType.number,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTextFormField(
                  'الوحدة',
                  _controllers['unit']!,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          _buildTextFormField(
            'عنوان مختصر',
            _controllers['shortAddress']!,
          ),

          const SizedBox(height: 16),

          // معلومات الموقع
          _buildSectionTitle('معلومات الموقع'),
          const SizedBox(height: 12),

          Row(
            children: [
              Expanded(
                child: _buildTextFormField(
                  'خطوط الطول',
                  _controllers['longitude']!,
                  keyboardType: TextInputType.numberWithOptions(decimal: true),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTextFormField(
                  'خطوط العرض',
                  _controllers['latitude']!,
                  keyboardType: TextInputType.numberWithOptions(decimal: true),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          _buildTextFormField(
            'رمز Plus Codes',
            _controllers['plusCode']!,
          ),

          const SizedBox(height: 16),

          // معلومات الاتصال الإلكتروني
          _buildSectionTitle('معلومات الاتصال الإلكتروني'),
          const SizedBox(height: 12),

          Row(
            children: [
              Expanded(
                child: _buildTextFormField(
                  'الموقع الإلكتروني',
                  _controllers['website']!,
                  keyboardType: TextInputType.url,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTextFormField(
                  'بريد إلكتروني',
                  _controllers['email']!,
                  keyboardType: TextInputType.emailAddress,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء قسم معلومات الاتصال
  Widget _buildContactSection(String title, String prefix) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 15,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              flex: 2,
              child: _buildTextFormField(
                'الاسم',
                _controllers['${prefix}Name']!,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildTextFormField(
                'رقم الجوال',
                _controllers['${prefix}Phone']!,
                keyboardType: TextInputType.phone,
              ),
            ),
            const SizedBox(width: 12),
            Container(
              padding: const EdgeInsets.only(top: 24),
              child: _buildCheckboxField(
                'SMS',
                _getContactSmsValue(prefix),
                (value) => _setContactSmsValue(prefix, value!),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// الحصول على قيمة SMS للمسؤول
  bool _getContactSmsValue(String prefix) {
    switch (prefix) {
      case 'accountManager':
        return _customerData.accountManagerSms;
      case 'purchaseManager':
        return _customerData.purchaseManagerSms;
      case 'financeManager':
        return _customerData.financeManagerSms;
      case 'receivingManager':
        return _customerData.receivingManagerSms;
      default:
        return false;
    }
  }

  /// تعيين قيمة SMS للمسؤول
  void _setContactSmsValue(String prefix, bool value) {
    setState(() {
      switch (prefix) {
        case 'accountManager':
          _customerData.accountManagerSms = value;
          break;
        case 'purchaseManager':
          _customerData.purchaseManagerSms = value;
          break;
        case 'financeManager':
          _customerData.financeManagerSms = value;
          break;
        case 'receivingManager':
          _customerData.receivingManagerSms = value;
          break;
      }
    });
  }

  /// التبويب الثالث: معلومات محاسبية
  Widget _buildAccountingInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('معلومات محاسبية'),
          const SizedBox(height: 16),

          // الصف الأول
          Row(
            children: [
              Expanded(
                child: _buildTextFormField(
                  'رقم المندوب',
                  _controllers['salesmanNumber']!,
                  keyboardType: TextInputType.number,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDropdownField(
                  'مستوى الحساب',
                  _customerData.accountLevel,
                  ['مستوى 1', 'مستوى 2', 'مستوى 3'],
                  (value) =>
                      setState(() => _customerData.accountLevel = value!),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // التصنيفات
          Row(
            children: [
              Expanded(
                child: _buildDropdownField(
                  'التصنيف 1',
                  _customerData.classification1,
                  ['تصنيف أ', 'تصنيف ب', 'تصنيف ج'],
                  (value) =>
                      setState(() => _customerData.classification1 = value!),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDropdownField(
                  'التصنيف 2',
                  _customerData.classification2,
                  ['فئة 1', 'فئة 2', 'فئة 3'],
                  (value) =>
                      setState(() => _customerData.classification2 = value!),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // الحدود المالية
          Row(
            children: [
              Expanded(
                child: _buildTextFormField(
                  'الحد الأعلى للرصيد',
                  _controllers['creditLimit']!,
                  keyboardType: TextInputType.numberWithOptions(decimal: true),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTextFormField(
                  'فترة الاستحقاق',
                  _controllers['duePeriod']!,
                  keyboardType: TextInputType.number,
                  suffix: 'يوماً',
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          _buildTextFormField(
            'خصم الفاتورة',
            _controllers['invoiceDiscount']!,
            keyboardType: TextInputType.numberWithOptions(decimal: true),
            suffix: '%',
          ),

          const SizedBox(height: 16),

          // تجميد الرصيد
          _buildSectionTitle('إعدادات الرصيد'),
          const SizedBox(height: 12),

          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'تجميد الرصيد',
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 8),
              _buildRadioGroup(
                ['مدين', 'دائن', 'كلاهما', 'بدون'],
                _customerData.balanceFreeze,
                (value) => setState(() => _customerData.balanceFreeze = value),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'تحذير الرصيد',
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 8),
              _buildRadioGroup(
                ['مدين', 'دائن', 'بدون'],
                _customerData.balanceWarning,
                (value) => setState(() => _customerData.balanceWarning = value),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // المبلغ المستهدف والرصيد
          Row(
            children: [
              Expanded(
                child: _buildTextFormField(
                  'المبلغ المستهدف',
                  _controllers['targetAmount']!,
                  keyboardType: TextInputType.numberWithOptions(decimal: true),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDisplayField(
                    'الرصيد', _customerData.currentBalance.toString()),
              ),
            ],
          ),

          const SizedBox(height: 16),

          _buildTextFormField(
            'ملاحظات',
            _controllers['notes']!,
            maxLines: 3,
          ),

          const SizedBox(height: 16),

          // المعلومات الضريبية
          _buildSectionTitle('المعلومات الضريبية والعملة'),
          const SizedBox(height: 12),

          Row(
            children: [
              Expanded(
                child: _buildTextFormField(
                  'رقم التعريف الضريبي',
                  _controllers['taxId']!,
                  keyboardType: TextInputType.number,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDropdownField(
                  'تطبيق الضريبة',
                  _customerData.taxApplication,
                  ['خاضع للضريبة', 'معفى من الضريبة', 'ضريبة صفر'],
                  (value) =>
                      setState(() => _customerData.taxApplication = value!),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildDropdownField(
                  'نوع العميل',
                  _customerData.customerType,
                  ['عميل عادي', 'عميل VIP', 'عميل تجاري', 'عميل حكومي'],
                  (value) =>
                      setState(() => _customerData.customerType = value!),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDropdownField(
                  'العملة',
                  _customerData.currency,
                  ['ريال سعودي', 'دولار أمريكي', 'يورو', 'جنيه إسترليني'],
                  (value) => setState(() => _customerData.currency = value!),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء مجموعة أزرار راديو
  Widget _buildRadioGroup(
    List<String> options,
    String selectedValue,
    ValueChanged<String> onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: options.map((option) {
        return InkWell(
          onTap: () => onChanged(option),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Radio<String>(
                  value: option,
                  groupValue: selectedValue,
                  onChanged: (value) => onChanged(value!),
                  activeColor: Colors.blue[700],
                ),
                Text(
                  option,
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  /// التبويب الرابع: تقرير حافز العميل
  Widget _buildCustomerIncentiveTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('تقرير حافز العميل'),
          const SizedBox(height: 16),
          Center(
            child: Column(
              children: [
                Icon(
                  Icons.analytics,
                  size: 64,
                  color: Colors.blue[300],
                ),
                const SizedBox(height: 16),
                Text(
                  'تقرير حافز العميل',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'سيتم إضافة تقارير الحوافز هنا',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// التبويب الخامس: التقارير
  Widget _buildReportsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('التقارير'),
          const SizedBox(height: 16),
          Center(
            child: Column(
              children: [
                Icon(
                  Icons.assessment,
                  size: 64,
                  color: Colors.green[300],
                ),
                const SizedBox(height: 16),
                Text(
                  'التقارير',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'سيتم إضافة التقارير المختلفة هنا',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// التبويب السادس: معلومات المستخدم
  Widget _buildUserInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('معلومات المستخدم'),
          const SizedBox(height: 16),
          _buildDisplayField('اسم المستخدم', 'admin'),
          const SizedBox(height: 16),
          _buildDisplayField(
              'تاريخ التعديل', DateTime.now().toString().split(' ')[0]),
        ],
      ),
    );
  }

  /// التبويب السابع: إضافي
  Widget _buildAdditionalTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('معلومات إضافية'),
          const SizedBox(height: 16),
          Center(
            child: Column(
              children: [
                Icon(
                  Icons.extension,
                  size: 64,
                  color: Colors.purple[300],
                ),
                const SizedBox(height: 16),
                Text(
                  'معلومات إضافية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'سيتم إضافة المعلومات الإضافية هنا',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
