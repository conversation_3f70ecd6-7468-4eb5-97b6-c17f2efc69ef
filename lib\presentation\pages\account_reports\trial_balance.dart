import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة ميزان المراجعة
/// تعرض أرصدة جميع الحسابات في تاريخ محدد
class TrialBalancePage extends StatefulWidget {
  const TrialBalancePage({super.key});

  @override
  State<TrialBalancePage> createState() => _TrialBalancePageState();
}

class _TrialBalancePageState extends State<TrialBalancePage> {
  String _searchQuery = '';
  String _selectedPeriod = 'current_month';

  // بيانات تجريبية لميزان المراجعة
  final List<Map<String, dynamic>> _trialBalanceData = [
    {
      'accountCode': '1101',
      'accountName': 'النقدية في الصندوق',
      'debitBalance': 15000.0,
      'creditBalance': 0.0,
      'accountType': 'أصول',
    },
    {
      'accountCode': '1102',
      'accountName': 'البنك الأهلي',
      'debitBalance': 85000.0,
      'creditBalance': 0.0,
      'accountType': 'أصول',
    },
    {
      'accountCode': '1201',
      'accountName': 'العملاء',
      'debitBalance': 45000.0,
      'creditBalance': 0.0,
      'accountType': 'أصول',
    },
    {
      'accountCode': '2101',
      'accountName': 'الموردين',
      'debitBalance': 0.0,
      'creditBalance': 25000.0,
      'accountType': 'خصوم',
    },
    {
      'accountCode': '3101',
      'accountName': 'رأس المال',
      'debitBalance': 0.0,
      'creditBalance': 100000.0,
      'accountType': 'حقوق ملكية',
    },
    {
      'accountCode': '4101',
      'accountName': 'إيرادات المبيعات',
      'debitBalance': 0.0,
      'creditBalance': 75000.0,
      'accountType': 'إيرادات',
    },
    {
      'accountCode': '5101',
      'accountName': 'مصروفات الرواتب',
      'debitBalance': 35000.0,
      'creditBalance': 0.0,
      'accountType': 'مصروفات',
    },
    {
      'accountCode': '5201',
      'accountName': 'مصروفات الإيجار',
      'debitBalance': 20000.0,
      'creditBalance': 0.0,
      'accountType': 'مصروفات',
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.trialBalance),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                TextField(
                  decoration: const InputDecoration(
                    hintText: 'البحث في الحسابات...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedPeriod,
                        decoration: const InputDecoration(
                          labelText: 'الفترة المالية',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'current_month', child: Text('الشهر الحالي')),
                          DropdownMenuItem(value: 'current_quarter', child: Text('الربع الحالي')),
                          DropdownMenuItem(value: 'current_year', child: Text('السنة الحالية')),
                          DropdownMenuItem(value: 'custom', child: Text('فترة مخصصة')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedPeriod = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                _buildStatCard('إجمالي المدين', '${_getTotalDebit()} ر.س', Colors.green),
                _buildStatCard('إجمالي الدائن', '${_getTotalCredit()} ر.س', Colors.red),
                _buildStatCard('عدد الحسابات', _trialBalanceData.length.toString(), Colors.blue),
                _buildStatCard('الفرق', '${_getBalanceDifference()} ر.س', Colors.orange),
              ],
            ),
          ),

          // قائمة الحسابات
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _trialBalanceData.length,
              itemBuilder: (context, index) {
                final account = _trialBalanceData[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: _getAccountTypeColor(account['accountType']),
                      child: Text(
                        account['accountCode'].substring(0, 2),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    title: Text(
                      account['accountName'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('رقم الحساب: ${account['accountCode']}'),
                        Text('نوع الحساب: ${account['accountType']}'),
                      ],
                    ),
                    trailing: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        if (account['debitBalance'] > 0)
                          Text(
                            'مدين: ${account['debitBalance'].toStringAsFixed(2)} ر.س',
                            style: const TextStyle(
                              color: Colors.green,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        if (account['creditBalance'] > 0)
                          Text(
                            'دائن: ${account['creditBalance'].toStringAsFixed(2)} ر.س',
                            style: const TextStyle(
                              color: Colors.red,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                      ],
                    ),
                    isThreeLine: true,
                  ),
                );
              },
            ),
          ),

          // إجمالي الميزان
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              border: Border(top: BorderSide(color: Colors.blue[200]!)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Text(
                  'إجمالي المدين: ${_getTotalDebit()} ر.س',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
                Text(
                  'إجمالي الدائن: ${_getTotalCredit()} ر.س',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshData,
        backgroundColor: Colors.blue,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث البيانات'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getAccountTypeColor(String accountType) {
    switch (accountType) {
      case 'أصول':
        return Colors.green;
      case 'خصوم':
        return Colors.red;
      case 'حقوق ملكية':
        return Colors.blue;
      case 'إيرادات':
        return Colors.purple;
      case 'مصروفات':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  String _getTotalDebit() {
    double total = _trialBalanceData.fold(0.0, (sum, account) => sum + account['debitBalance']);
    return total.toStringAsFixed(2);
  }

  String _getTotalCredit() {
    double total = _trialBalanceData.fold(0.0, (sum, account) => sum + account['creditBalance']);
    return total.toStringAsFixed(2);
  }

  double _getBalanceDifference() {
    double totalDebit = _trialBalanceData.fold(0.0, (sum, account) => sum + account['debitBalance']);
    double totalCredit = _trialBalanceData.fold(0.0, (sum, account) => sum + account['creditBalance']);
    return (totalDebit - totalCredit).abs();
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة ميزان المراجعة')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير ميزان المراجعة')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث بيانات ميزان المراجعة'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
