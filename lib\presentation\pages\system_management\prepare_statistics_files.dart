import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تجهيز ملفات الإحصائية
/// تتيح إنشاء وتصدير ملفات إحصائية مختلفة للنظام
class PrepareStatisticsFilesPage extends StatefulWidget {
  const PrepareStatisticsFilesPage({super.key});

  @override
  State<PrepareStatisticsFilesPage> createState() =>
      _PrepareStatisticsFilesPageState();
}

class _PrepareStatisticsFilesPageState
    extends State<PrepareStatisticsFilesPage> {
  final _formKey = GlobalKey<FormState>();

  String? _selectedPeriod;
  String? _selectedFileType;
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  final Set<String> _selectedReports = {};
  bool _isProcessing = false;
  bool _includeCharts = true;
  bool _includeDetails = true;

  final List<Map<String, String>> _timePeriods = [
    {'id': 'daily', 'name': 'يومي', 'description': 'إحصائيات يومية'},
    {'id': 'weekly', 'name': 'أسبوعي', 'description': 'إحصائيات أسبوعية'},
    {'id': 'monthly', 'name': 'شهري', 'description': 'إحصائيات شهرية'},
    {
      'id': 'quarterly',
      'name': 'ربع سنوي',
      'description': 'إحصائيات ربع سنوية'
    },
    {'id': 'yearly', 'name': 'سنوي', 'description': 'إحصائيات سنوية'},
    {'id': 'custom', 'name': 'فترة مخصصة', 'description': 'تحديد فترة مخصصة'},
  ];

  final List<Map<String, String>> _fileTypes = [
    {'id': 'pdf', 'name': 'PDF', 'description': 'ملف PDF للطباعة'},
    {'id': 'excel', 'name': 'Excel', 'description': 'ملف Excel للتحليل'},
    {'id': 'csv', 'name': 'CSV', 'description': 'ملف CSV للبيانات'},
    {'id': 'json', 'name': 'JSON', 'description': 'ملف JSON للتطبيقات'},
  ];

  final List<Map<String, dynamic>> _reportTypes = [
    {
      'id': 'sales',
      'name': 'تقرير المبيعات',
      'icon': Icons.trending_up,
      'description': 'إحصائيات المبيعات والإيرادات',
      'size': '2.5 MB'
    },
    {
      'id': 'purchases',
      'name': 'تقرير المشتريات',
      'icon': Icons.shopping_cart,
      'description': 'إحصائيات المشتريات والمصروفات',
      'size': '1.8 MB'
    },
    {
      'id': 'inventory',
      'name': 'تقرير المخزون',
      'icon': Icons.inventory,
      'description': 'حالة المخزون والأصناف',
      'size': '3.2 MB'
    },
    {
      'id': 'customers',
      'name': 'تقرير العملاء',
      'icon': Icons.people,
      'description': 'إحصائيات العملاء والحسابات',
      'size': '1.5 MB'
    },
    {
      'id': 'financial',
      'name': 'التقرير المالي',
      'icon': Icons.account_balance,
      'description': 'الميزانية والأرباح والخسائر',
      'size': '2.1 MB'
    },
    {
      'id': 'performance',
      'name': 'تقرير الأداء',
      'icon': Icons.analytics,
      'description': 'مؤشرات الأداء الرئيسية',
      'size': '1.2 MB'
    },
    {
      'id': 'taxes',
      'name': 'تقرير الضرائب',
      'icon': Icons.receipt,
      'description': 'ضريبة القيمة المضافة والزكاة',
      'size': '0.8 MB'
    },
    {
      'id': 'employees',
      'name': 'تقرير الموظفين',
      'icon': Icons.badge,
      'description': 'إحصائيات الموظفين والرواتب',
      'size': '1.0 MB'
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.prepareStatisticsFiles),
        backgroundColor: Colors.lightBlue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.folder),
            onPressed: _showGeneratedFiles,
            tooltip: 'الملفات المُنشأة',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // بطاقة اختيار الفترة الزمنية
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'الفترة الزمنية',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.lightBlue,
                      ),
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: _selectedPeriod,
                      decoration: const InputDecoration(
                        labelText: 'اختر الفترة الزمنية',
                        prefixIcon: Icon(Icons.date_range),
                        border: OutlineInputBorder(),
                      ),
                      items:
                          _timePeriods.map<DropdownMenuItem<String>>((period) {
                        return DropdownMenuItem<String>(
                          value: period['id'],
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(period['name']!),
                              Text(
                                period['description']!,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedPeriod = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار الفترة الزمنية';
                        }
                        return null;
                      },
                    ),
                    if (_selectedPeriod == 'custom') ...[
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          // تاريخ البداية
                          Expanded(
                            child: ListTile(
                              leading: const Icon(Icons.calendar_today),
                              title: const Text('من تاريخ'),
                              subtitle: Text(
                                  '${_startDate.day}/${_startDate.month}/${_startDate.year}'),
                              onTap: () => _selectDate(true),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                                side: BorderSide(color: Colors.grey.shade300),
                              ),
                            ),
                          ),

                          const SizedBox(width: 16),

                          // تاريخ النهاية
                          Expanded(
                            child: ListTile(
                              leading: const Icon(Icons.event),
                              title: const Text('إلى تاريخ'),
                              subtitle: Text(
                                  '${_endDate.day}/${_endDate.month}/${_endDate.year}'),
                              onTap: () => _selectDate(false),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                                side: BorderSide(color: Colors.grey.shade300),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة اختيار نوع الملف
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'نوع الملف',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.lightBlue,
                      ),
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: _selectedFileType,
                      decoration: const InputDecoration(
                        labelText: 'اختر نوع الملف',
                        prefixIcon: Icon(Icons.file_present),
                        border: OutlineInputBorder(),
                      ),
                      items:
                          _fileTypes.map<DropdownMenuItem<String>>((fileType) {
                        return DropdownMenuItem<String>(
                          value: fileType['id'],
                          child: Row(
                            children: [
                              Icon(_getFileIcon(fileType['id']!)),
                              const SizedBox(width: 8),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(fileType['name']!),
                                  Text(
                                    fileType['description']!,
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedFileType = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار نوع الملف';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة اختيار التقارير
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Text(
                          'التقارير المطلوبة',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.lightBlue,
                          ),
                        ),
                        const Spacer(),
                        TextButton(
                          onPressed: _selectAllReports,
                          child: const Text('تحديد الكل'),
                        ),
                        TextButton(
                          onPressed: _clearAllReports,
                          child: const Text('إلغاء الكل'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    ...(_reportTypes.map((report) => CheckboxListTile(
                          value: _selectedReports.contains(report['id']),
                          onChanged: (bool? value) {
                            setState(() {
                              if (value == true) {
                                _selectedReports.add(report['id']);
                              } else {
                                _selectedReports.remove(report['id']);
                              }
                            });
                          },
                          title: Row(
                            children: [
                              Icon(report['icon'], color: Colors.lightBlue),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      report['name'],
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold),
                                    ),
                                    Text(
                                      report['description'],
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          subtitle: Text(
                            'الحجم المتوقع: ${report['size']}',
                            style: const TextStyle(color: Colors.blue),
                          ),
                          controlAffinity: ListTileControlAffinity.leading,
                        ))),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة خيارات إضافية
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'خيارات إضافية',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.lightBlue,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SwitchListTile(
                      title: const Text('تضمين الرسوم البيانية'),
                      subtitle: const Text('إضافة الرسوم البيانية والمخططات'),
                      value: _includeCharts,
                      onChanged: (value) {
                        setState(() {
                          _includeCharts = value;
                        });
                      },
                    ),
                    SwitchListTile(
                      title: const Text('تضمين التفاصيل'),
                      subtitle: const Text('إضافة البيانات التفصيلية'),
                      value: _includeDetails,
                      onChanged: (value) {
                        setState(() {
                          _includeDetails = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // معاينة العملية
            if (_selectedPeriod != null &&
                _selectedFileType != null &&
                _selectedReports.isNotEmpty)
              Card(
                color: Colors.lightBlue.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'معاينة الملفات المُنشأة',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.lightBlue,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildPreviewRow('الفترة الزمنية:', _getPeriodName()),
                      _buildPreviewRow('نوع الملف:', _getFileTypeName()),
                      _buildPreviewRow(
                          'عدد التقارير:', '${_selectedReports.length} تقرير'),
                      _buildPreviewRow('الحجم المتوقع:', _getTotalSize()),
                      _buildPreviewRow('الرسوم البيانية:',
                          _includeCharts ? 'مُضمنة' : 'غير مُضمنة'),
                      _buildPreviewRow('التفاصيل:',
                          _includeDetails ? 'مُضمنة' : 'غير مُضمنة'),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 24),

            // أزرار العمليات
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isProcessing || _selectedReports.isEmpty
                        ? null
                        : _generateFiles,
                    icon: _isProcessing
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.file_download),
                    label: Text(
                        _isProcessing ? 'جاري الإنشاء...' : 'إنشاء الملفات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.lightBlue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _resetForm,
                    icon: const Icon(Icons.refresh),
                    label: const Text('إعادة تعيين'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  IconData _getFileIcon(String fileType) {
    switch (fileType) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'excel':
        return Icons.table_chart;
      case 'csv':
        return Icons.grid_on;
      case 'json':
        return Icons.code;
      default:
        return Icons.file_present;
    }
  }

  String _getPeriodName() {
    if (_selectedPeriod == null) return 'غير محدد';
    final period = _timePeriods.firstWhere((p) => p['id'] == _selectedPeriod);
    return period['name']!;
  }

  String _getFileTypeName() {
    if (_selectedFileType == null) return 'غير محدد';
    final fileType = _fileTypes.firstWhere((f) => f['id'] == _selectedFileType);
    return fileType['name']!;
  }

  String _getTotalSize() {
    double totalMB = 0;
    for (String reportId in _selectedReports) {
      final report = _reportTypes.firstWhere((r) => r['id'] == reportId);
      final sizeStr = report['size'] as String;
      final size = double.parse(sizeStr.replaceAll(' MB', ''));
      totalMB += size;
    }
    return '${totalMB.toStringAsFixed(1)} MB';
  }

  Future<void> _selectDate(bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : _endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _selectAllReports() {
    setState(() {
      _selectedReports.clear();
      _selectedReports
          .addAll(_reportTypes.map((report) => report['id'] as String));
    });
  }

  void _clearAllReports() {
    setState(() {
      _selectedReports.clear();
    });
  }

  Future<void> _generateFiles() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isProcessing = true;
      });

      // محاكاة عملية إنشاء الملفات
      await Future.delayed(const Duration(seconds: 3));

      setState(() {
        _isProcessing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('تم إنشاء ${_selectedReports.length} ملف إحصائي بنجاح'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 4),
          ),
        );
        _resetForm();
      }
    }
  }

  void _resetForm() {
    setState(() {
      _selectedPeriod = null;
      _selectedFileType = null;
      _selectedReports.clear();
      _includeCharts = true;
      _includeDetails = true;
      _startDate = DateTime.now().subtract(const Duration(days: 30));
      _endDate = DateTime.now();
    });
  }

  void _showGeneratedFiles() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض الملفات المُنشأة')),
    );
  }
}
