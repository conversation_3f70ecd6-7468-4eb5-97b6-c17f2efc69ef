import 'package:flutter/material.dart';

/// صفحة تقييم مركز التكلفة
/// تعرض تقييم شامل لأداء مراكز التكلفة
class CostCenterEvaluationPage extends StatefulWidget {
  const CostCenterEvaluationPage({super.key});

  @override
  State<CostCenterEvaluationPage> createState() =>
      _CostCenterEvaluationPageState();
}

class _CostCenterEvaluationPageState extends State<CostCenterEvaluationPage> {
  String _selectedCostCenter = 'all';
  String _selectedPeriod = 'current_month';

  // بيانات تجريبية لتقييم مراكز التكلفة
  final List<Map<String, dynamic>> _costCenterEvaluations = [
    {
      'costCenterCode': 'CC001',
      'costCenterName': 'قسم المبيعات',
      'manager': 'أحمد محمد',
      'totalRevenue': 250000.0,
      'totalExpenses': 85000.0,
      'netProfit': 165000.0,
      'profitMargin': 66.0,
      'roi': 194.1,
      'efficiency': 85.5,
      'performance': 'ممتاز',
      'grade': 'A',
      'kpis': [
        {
          'metric': 'تحقيق الإيرادات',
          'target': 230000.0,
          'actual': 250000.0,
          'score': 108.7
        },
        {
          'metric': 'التحكم في التكاليف',
          'target': 90000.0,
          'actual': 85000.0,
          'score': 105.9
        },
        {
          'metric': 'رضا العملاء',
          'target': 85.0,
          'actual': 88.0,
          'score': 103.5
        },
        {'metric': 'الإنتاجية', 'target': 80.0, 'actual': 85.0, 'score': 106.3},
      ],
      'strengths': [
        'تجاوز أهداف الإيرادات',
        'كفاءة في إدارة التكاليف',
        'رضا عملاء عالي'
      ],
      'weaknesses': ['حاجة لتحسين العمليات', 'تطوير المهارات التقنية'],
      'recommendations': ['زيادة الاستثمار في التدريب', 'تحسين أنظمة CRM'],
    },
    {
      'costCenterCode': 'CC002',
      'costCenterName': 'قسم الإنتاج',
      'manager': 'فاطمة أحمد',
      'totalRevenue': 180000.0,
      'totalExpenses': 125000.0,
      'netProfit': 55000.0,
      'profitMargin': 30.6,
      'roi': 44.0,
      'efficiency': 72.3,
      'performance': 'جيد',
      'grade': 'B',
      'kpis': [
        {
          'metric': 'تحقيق الإيرادات',
          'target': 200000.0,
          'actual': 180000.0,
          'score': 90.0
        },
        {
          'metric': 'التحكم في التكاليف',
          'target': 120000.0,
          'actual': 125000.0,
          'score': 96.0
        },
        {
          'metric': 'جودة المنتج',
          'target': 95.0,
          'actual': 92.0,
          'score': 96.8
        },
        {'metric': 'الإنتاجية', 'target': 75.0, 'actual': 72.0, 'score': 96.0},
      ],
      'strengths': ['جودة منتج عالية', 'فريق عمل متخصص'],
      'weaknesses': ['تجاوز الميزانية', 'انخفاض الإنتاجية'],
      'recommendations': ['تحسين العمليات الإنتاجية', 'تقليل الهدر'],
    },
    {
      'costCenterCode': 'CC003',
      'costCenterName': 'قسم التطوير',
      'manager': 'محمد علي',
      'totalRevenue': 120000.0,
      'totalExpenses': 65000.0,
      'netProfit': 55000.0,
      'profitMargin': 45.8,
      'roi': 84.6,
      'efficiency': 78.9,
      'performance': 'جيد جداً',
      'grade': 'B+',
      'kpis': [
        {
          'metric': 'تحقيق الإيرادات',
          'target': 110000.0,
          'actual': 120000.0,
          'score': 109.1
        },
        {
          'metric': 'التحكم في التكاليف',
          'target': 70000.0,
          'actual': 65000.0,
          'score': 107.7
        },
        {'metric': 'الابتكار', 'target': 80.0, 'actual': 85.0, 'score': 106.3},
        {
          'metric': 'سرعة التطوير',
          'target': 70.0,
          'actual': 68.0,
          'score': 97.1
        },
      ],
      'strengths': ['ابتكار عالي', 'كفاءة في التكاليف', 'تجاوز الأهداف'],
      'weaknesses': ['بطء في بعض المشاريع', 'حاجة لتحسين التواصل'],
      'recommendations': ['تحسين إدارة المشاريع', 'تعزيز التواصل الداخلي'],
    },
    {
      'costCenterCode': 'CC004',
      'costCenterName': 'قسم التسويق',
      'manager': 'سارة سعد',
      'totalRevenue': 95000.0,
      'totalExpenses': 45000.0,
      'netProfit': 50000.0,
      'profitMargin': 52.6,
      'roi': 111.1,
      'efficiency': 81.2,
      'performance': 'جيد جداً',
      'grade': 'B+',
      'kpis': [
        {
          'metric': 'تحقيق الإيرادات',
          'target': 100000.0,
          'actual': 95000.0,
          'score': 95.0
        },
        {
          'metric': 'التحكم في التكاليف',
          'target': 50000.0,
          'actual': 45000.0,
          'score': 111.1
        },
        {
          'metric': 'الوصول للعملاء',
          'target': 75.0,
          'actual': 80.0,
          'score': 106.7
        },
        {
          'metric': 'فعالية الحملات',
          'target': 70.0,
          'actual': 75.0,
          'score': 107.1
        },
      ],
      'strengths': ['كفاءة عالية في التكاليف', 'وصول جيد للعملاء'],
      'weaknesses': ['عدم تحقيق هدف الإيرادات', 'حاجة لتنويع القنوات'],
      'recommendations': [
        'تطوير استراتيجيات جديدة',
        'زيادة الاستثمار في الإعلان الرقمي'
      ],
    },
  ];

  @override
  Widget build(BuildContext context) {
    final filteredData = _getFilteredData();

    return Scaffold(
      appBar: AppBar(
        title: const Text('تقييم مركز التكلفة'),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedCostCenter,
                        decoration: const InputDecoration(
                          labelText: 'مركز التكلفة',
                          border: OutlineInputBorder(),
                        ),
                        items: [
                          const DropdownMenuItem(
                              value: 'all', child: Text('جميع المراكز')),
                          ..._costCenterEvaluations.map((center) {
                            return DropdownMenuItem<String>(
                              value: center['costCenterCode'],
                              child: Text(center['costCenterName']),
                            );
                          }),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedCostCenter = value!;
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedPeriod,
                        decoration: const InputDecoration(
                          labelText: 'الفترة',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(
                              value: 'current_month',
                              child: Text('الشهر الحالي')),
                          DropdownMenuItem(
                              value: 'current_quarter',
                              child: Text('الربع الحالي')),
                          DropdownMenuItem(
                              value: 'current_year',
                              child: Text('السنة الحالية')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedPeriod = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // ملخص التقييم
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              color: Colors.indigo[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    const Text(
                      'ملخص تقييم مراكز التكلفة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Column(
                          children: [
                            const Text('إجمالي الأرباح'),
                            Text(
                              '${_getTotalProfit(filteredData)} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('متوسط الكفاءة'),
                            Text(
                              '${_getAverageEfficiency(filteredData)}%',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('متوسط العائد'),
                            Text(
                              '${_getAverageROI(filteredData)}%',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.orange,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                _buildStatCard('مراكز التكلفة', filteredData.length.toString(),
                    Colors.blue),
                _buildStatCard('تقييم ممتاز',
                    _getExcellentCount(filteredData).toString(), Colors.green),
                _buildStatCard('تقييم جيد',
                    _getGoodCount(filteredData).toString(), Colors.orange),
                _buildStatCard('متوسط الربحية',
                    '${_getAverageProfitMargin(filteredData)}%', Colors.purple),
              ],
            ),
          ),

          // قائمة مراكز التكلفة
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: filteredData.length,
              itemBuilder: (context, index) {
                final costCenter = filteredData[index];

                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: _getGradeColor(costCenter['grade']),
                      child: Text(
                        costCenter['grade'],
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    title: Text(
                      '${costCenter['costCenterCode']} - ${costCenter['costCenterName']}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('المدير: ${costCenter['manager']}'),
                        Text('الأداء: ${costCenter['performance']}'),
                        Text(
                            'الربحية: ${costCenter['profitMargin'].toStringAsFixed(1)}%'),
                        Text(
                            'العائد على الاستثمار: ${costCenter['roi'].toStringAsFixed(1)}%'),
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: _getGradeColor(costCenter['grade']),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'تقييم ${costCenter['grade']}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard(
                                      'صافي الربح',
                                      '${costCenter['netProfit'].toStringAsFixed(2)} ر.س',
                                      Icons.trending_up,
                                      Colors.green),
                                ),
                                Expanded(
                                  child: _buildDetailCard(
                                      'هامش الربح',
                                      '${costCenter['profitMargin'].toStringAsFixed(1)}%',
                                      Icons.percent,
                                      Colors.blue),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard(
                                      'العائد على الاستثمار',
                                      '${costCenter['roi'].toStringAsFixed(1)}%',
                                      Icons.show_chart,
                                      Colors.orange),
                                ),
                                Expanded(
                                  child: _buildDetailCard(
                                      'الكفاءة',
                                      '${costCenter['efficiency'].toStringAsFixed(1)}%',
                                      Icons.speed,
                                      Colors.purple),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            const Text(
                              'مؤشرات الأداء الرئيسية:',
                              style: TextStyle(
                                  fontWeight: FontWeight.bold, fontSize: 16),
                            ),
                            const SizedBox(height: 8),
                            ...costCenter['kpis'].map<Widget>((kpi) {
                              return ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: _getScoreColor(kpi['score']),
                                  radius: 15,
                                  child: Text(
                                    '${kpi['score'].toStringAsFixed(0)}',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                title: Text(kpi['metric']),
                                subtitle: Text(
                                    'الهدف: ${kpi['target']} | الفعلي: ${kpi['actual']}'),
                                trailing: Text(
                                  '${kpi['score'].toStringAsFixed(1)}%',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: _getScoreColor(kpi['score']),
                                  ),
                                ),
                              );
                            }).toList(),
                            const SizedBox(height: 12),
                            const Text(
                              'نقاط القوة:',
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  color: Colors.green),
                            ),
                            ...costCenter['strengths'].map<Widget>((strength) {
                              return ListTile(
                                leading: const Icon(Icons.check_circle,
                                    color: Colors.green, size: 16),
                                title: Text(strength,
                                    style: const TextStyle(fontSize: 12)),
                                dense: true,
                              );
                            }).toList(),
                            const Text(
                              'نقاط الضعف:',
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  color: Colors.red),
                            ),
                            ...costCenter['weaknesses'].map<Widget>((weakness) {
                              return ListTile(
                                leading: const Icon(Icons.warning,
                                    color: Colors.red, size: 16),
                                title: Text(weakness,
                                    style: const TextStyle(fontSize: 12)),
                                dense: true,
                              );
                            }).toList(),
                            const Text(
                              'التوصيات:',
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  color: Colors.blue),
                            ),
                            ...costCenter['recommendations']
                                .map<Widget>((recommendation) {
                              return ListTile(
                                leading: const Icon(Icons.lightbulb,
                                    color: Colors.blue, size: 16),
                                title: Text(recommendation,
                                    style: const TextStyle(fontSize: 12)),
                                dense: true,
                              );
                            }).toList(),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshData,
        backgroundColor: Colors.indigo,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث البيانات'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailCard(
      String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Icon(icon, size: 20, color: color),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                color: color,
              ),
            ),
            Text(
              title,
              style: const TextStyle(fontSize: 10),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Color _getGradeColor(String grade) {
    switch (grade) {
      case 'A':
        return Colors.green;
      case 'B+':
        return Colors.lightGreen;
      case 'B':
        return Colors.orange;
      case 'C':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Color _getScoreColor(double score) {
    if (score >= 100) return Colors.green;
    if (score >= 80) return Colors.orange;
    return Colors.red;
  }

  List<Map<String, dynamic>> _getFilteredData() {
    if (_selectedCostCenter == 'all') {
      return _costCenterEvaluations;
    }
    return _costCenterEvaluations
        .where((center) => center['costCenterCode'] == _selectedCostCenter)
        .toList();
  }

  double _getTotalProfit(List<Map<String, dynamic>> data) {
    return data.fold(0.0, (sum, center) => sum + center['netProfit']);
  }

  String _getAverageEfficiency(List<Map<String, dynamic>> data) {
    if (data.isEmpty) return '0.0';
    double total = data.fold(0.0, (sum, center) => sum + center['efficiency']);
    return (total / data.length).toStringAsFixed(1);
  }

  String _getAverageROI(List<Map<String, dynamic>> data) {
    if (data.isEmpty) return '0.0';
    double total = data.fold(0.0, (sum, center) => sum + center['roi']);
    return (total / data.length).toStringAsFixed(1);
  }

  int _getExcellentCount(List<Map<String, dynamic>> data) {
    return data.where((center) => center['performance'] == 'ممتاز').length;
  }

  int _getGoodCount(List<Map<String, dynamic>> data) {
    return data.where((center) => center['performance'].contains('جيد')).length;
  }

  String _getAverageProfitMargin(List<Map<String, dynamic>> data) {
    if (data.isEmpty) return '0.0';
    double total =
        data.fold(0.0, (sum, center) => sum + center['profitMargin']);
    return (total / data.length).toStringAsFixed(1);
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة تقرير تقييم مركز التكلفة')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير تقرير تقييم مركز التكلفة')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث بيانات تقييم مركز التكلفة'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
