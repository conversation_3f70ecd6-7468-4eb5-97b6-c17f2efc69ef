import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير حركة مبيعات الفروع 2
/// يعرض تحليل متقدم لحركة مبيعات الفروع مع مقارنات
class BranchSalesMovement2ReportPage extends StatefulWidget {
  const BranchSalesMovement2ReportPage({super.key});

  @override
  State<BranchSalesMovement2ReportPage> createState() => _BranchSalesMovement2ReportPageState();
}

class _BranchSalesMovement2ReportPageState extends State<BranchSalesMovement2ReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedBranch;
  String? _comparisonPeriod = 'previous_month';
  final String _analysisType = 'detailed';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('حركة مبيعات الفروع - تحليل متقدم'),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showAdvancedAnalytics,
            tooltip: 'تحليلات متقدمة',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilterSection(localizations),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildComparisonSection(),
                  const SizedBox(height: 16),
                  _buildBranchPerformanceSection(),
                  const SizedBox(height: 16),
                  _buildTrendAnalysisSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.indigo[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.fromDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, true),
                  controller: TextEditingController(
                    text: _startDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.toDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, false),
                  controller: TextEditingController(
                    text: _endDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'الفرع',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedBranch,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الفروع')),
                    DropdownMenuItem(value: 'main', child: Text('الفرع الرئيسي')),
                    DropdownMenuItem(value: 'branch1', child: Text('الفرع الأول')),
                    DropdownMenuItem(value: 'branch2', child: Text('الفرع الثاني')),
                    DropdownMenuItem(value: 'branch3', child: Text('الفرع الثالث')),
                  ],
                  onChanged: (value) => setState(() => _selectedBranch = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'فترة المقارنة',
                    border: OutlineInputBorder(),
                  ),
                  value: _comparisonPeriod,
                  items: const [
                    DropdownMenuItem(value: 'previous_month', child: Text('الشهر السابق')),
                    DropdownMenuItem(value: 'same_month_last_year', child: Text('نفس الشهر العام الماضي')),
                    DropdownMenuItem(value: 'quarter', child: Text('الربع السابق')),
                    DropdownMenuItem(value: 'year', child: Text('السنة السابقة')),
                  ],
                  onChanged: (value) => setState(() => _comparisonPeriod = value),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.store, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص أداء الفروع',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي الفروع', '4', Colors.indigo, Icons.store),
                _buildSummaryCard('إجمالي المبيعات', '2,850,000 ر.س', Colors.green, Icons.monetization_on),
                _buildSummaryCard('أفضل فرع', 'الفرع الرئيسي', Colors.amber, Icons.star),
                _buildSummaryCard('نمو المبيعات', '+15.2%', Colors.blue, Icons.trending_up),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComparisonSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.compare, color: Colors.teal, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'مقارنة الأداء',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('الفرع')),
                  DataColumn(label: Text('الفترة الحالية')),
                  DataColumn(label: Text('فترة المقارنة')),
                  DataColumn(label: Text('النمو')),
                  DataColumn(label: Text('النسبة من الإجمالي')),
                  DataColumn(label: Text('التقييم')),
                ],
                rows: _buildComparisonRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBranchPerformanceSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.assessment, color: Colors.purple, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'تحليل أداء الفروع',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildBranchPerformanceList(),
          ],
        ),
      ),
    );
  }

  Widget _buildTrendAnalysisSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.trending_up, color: Colors.orange, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'تحليل الاتجاهات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildTrendCard('اتجاه صاعد', 'الفرع الرئيسي', Colors.green),
                _buildTrendCard('اتجاه مستقر', 'الفرع الأول', Colors.blue),
                _buildTrendCard('اتجاه متذبذب', 'الفرع الثاني', Colors.orange),
                _buildTrendCard('اتجاه هابط', 'الفرع الثالث', Colors.red),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _optimizeBranchPerformance,
                    icon: const Icon(Icons.tune),
                    label: const Text('تحسين الأداء'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _redistributeResources,
                    icon: const Icon(Icons.swap_horiz),
                    label: const Text('إعادة توزيع الموارد'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _setBranchTargets,
                    icon: const Icon(Icons.flag),
                    label: const Text('تحديد أهداف الفروع'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _createActionPlan,
                    icon: const Icon(Icons.assignment),
                    label: const Text('خطة عمل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildBranchPerformanceList() {
    final branches = [
      {'name': 'الفرع الرئيسي', 'sales': '1,285,000 ر.س', 'growth': '+18%', 'rank': '1', 'performance': 'ممتاز'},
      {'name': 'الفرع الأول', 'sales': '685,000 ر.س', 'growth': '+12%', 'rank': '2', 'performance': 'جيد'},
      {'name': 'الفرع الثاني', 'sales': '485,000 ر.س', 'growth': '+8%', 'rank': '3', 'performance': 'متوسط'},
      {'name': 'الفرع الثالث', 'sales': '395,000 ر.س', 'growth': '-5%', 'rank': '4', 'performance': 'ضعيف'},
    ];

    return branches.map((branch) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: _getPerformanceColor(branch['performance']!).withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: _getPerformanceColor(branch['performance']!).withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: _getRankColor(branch['rank']!),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Center(
              child: Text(
                branch['rank']!,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  branch['name']!,
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  'مبيعات: ${branch['sales']} • نمو: ${branch['growth']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getPerformanceColor(branch['performance']!).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              branch['performance']!,
              style: TextStyle(
                color: _getPerformanceColor(branch['performance']!),
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildComparisonRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('الفرع الرئيسي')),
        const DataCell(Text('1,285,000 ر.س')),
        const DataCell(Text('1,089,000 ر.س')),
        DataCell(_buildGrowthBadge('+18%', Colors.green)),
        const DataCell(Text('45.1%')),
        DataCell(_buildPerformanceBadge('ممتاز', Colors.green)),
      ]),
      DataRow(cells: [
        const DataCell(Text('الفرع الأول')),
        const DataCell(Text('685,000 ر.س')),
        const DataCell(Text('612,000 ر.س')),
        DataCell(_buildGrowthBadge('+12%', Colors.green)),
        const DataCell(Text('24.0%')),
        DataCell(_buildPerformanceBadge('جيد', Colors.blue)),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTrendCard(String trend, String branch, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                trend,
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(branch, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGrowthBadge(String growth, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(growth, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Widget _buildPerformanceBadge(String performance, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(performance, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Color _getPerformanceColor(String performance) {
    switch (performance) {
      case 'ممتاز':
        return Colors.green;
      case 'جيد':
        return Colors.blue;
      case 'متوسط':
        return Colors.orange;
      case 'ضعيف':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Color _getRankColor(String rank) {
    switch (rank) {
      case '1':
        return Colors.amber;
      case '2':
        return Colors.grey;
      case '3':
        return Colors.brown;
      case '4':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showAdvancedAnalytics() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض التحليلات المتقدمة')),
    );
  }

  void _optimizeBranchPerformance() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحسين أداء الفروع')),
    );
  }

  void _redistributeResources() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إعادة توزيع الموارد بين الفروع')),
    );
  }

  void _setBranchTargets() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحديد أهداف الفروع')),
    );
  }

  void _createActionPlan() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء خطة عمل للفروع')),
    );
  }
}
