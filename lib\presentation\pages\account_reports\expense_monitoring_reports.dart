import 'package:flutter/material.dart';

/// صفحة تقارير مراقبة النفقات
/// تعرض تقارير مراقبة ومتابعة النفقات والمصروفات
class ExpenseMonitoringReportsPage extends StatefulWidget {
  const ExpenseMonitoringReportsPage({super.key});

  @override
  State<ExpenseMonitoringReportsPage> createState() => _ExpenseMonitoringReportsPageState();
}

class _ExpenseMonitoringReportsPageState extends State<ExpenseMonitoringReportsPage> {
  String _selectedPeriod = 'current_month';
  String _selectedCategory = 'all';

  // بيانات تجريبية لمراقبة النفقات
  final List<Map<String, dynamic>> _expenseData = [
    {
      'category': 'مصروفات الرواتب',
      'budgetAmount': 50000.0,
      'actualAmount': 48500.0,
      'variance': -1500.0,
      'variancePercentage': -3.0,
      'status': 'ضمن الميزانية',
      'lastMonthAmount': 47000.0,
      'growth': 3.2,
      'details': [
        {'item': 'رواتب الموظفين', 'budget': 40000.0, 'actual': 39500.0},
        {'item': 'بدلات', 'budget': 8000.0, 'actual': 7500.0},
        {'item': 'تأمينات اجتماعية', 'budget': 2000.0, 'actual': 1500.0},
      ],
    },
    {
      'category': 'مصروفات التسويق',
      'budgetAmount': 15000.0,
      'actualAmount': 18500.0,
      'variance': 3500.0,
      'variancePercentage': 23.3,
      'status': 'تجاوز الميزانية',
      'lastMonthAmount': 14000.0,
      'growth': 32.1,
      'details': [
        {'item': 'إعلانات رقمية', 'budget': 8000.0, 'actual': 10000.0},
        {'item': 'مطبوعات', 'budget': 4000.0, 'actual': 5000.0},
        {'item': 'فعاليات', 'budget': 3000.0, 'actual': 3500.0},
      ],
    },
    {
      'category': 'مصروفات التشغيل',
      'budgetAmount': 25000.0,
      'actualAmount': 22800.0,
      'variance': -2200.0,
      'variancePercentage': -8.8,
      'status': 'ضمن الميزانية',
      'lastMonthAmount': 24500.0,
      'growth': -6.9,
      'details': [
        {'item': 'كهرباء ومياه', 'budget': 8000.0, 'actual': 7500.0},
        {'item': 'صيانة', 'budget': 10000.0, 'actual': 9000.0},
        {'item': 'مواد استهلاكية', 'budget': 7000.0, 'actual': 6300.0},
      ],
    },
    {
      'category': 'مصروفات إدارية',
      'budgetAmount': 12000.0,
      'actualAmount': 13200.0,
      'variance': 1200.0,
      'variancePercentage': 10.0,
      'status': 'تجاوز طفيف',
      'lastMonthAmount': 11500.0,
      'growth': 14.8,
      'details': [
        {'item': 'قرطاسية', 'budget': 3000.0, 'actual': 3500.0},
        {'item': 'اتصالات', 'budget': 5000.0, 'actual': 5200.0},
        {'item': 'مصروفات أخرى', 'budget': 4000.0, 'actual': 4500.0},
      ],
    },
    {
      'category': 'مصروفات السفر',
      'budgetAmount': 8000.0,
      'actualAmount': 6500.0,
      'variance': -1500.0,
      'variancePercentage': -18.8,
      'status': 'ضمن الميزانية',
      'lastMonthAmount': 7200.0,
      'growth': -9.7,
      'details': [
        {'item': 'تذاكر طيران', 'budget': 5000.0, 'actual': 4000.0},
        {'item': 'إقامة', 'budget': 2000.0, 'actual': 1800.0},
        {'item': 'مواصلات', 'budget': 1000.0, 'actual': 700.0},
      ],
    },
  ];

  @override
  Widget build(BuildContext context) {
    final filteredData = _getFilteredData();
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقارير مراقبة النفقات'),
        backgroundColor: Colors.redAccent,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedPeriod,
                        decoration: const InputDecoration(
                          labelText: 'الفترة',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'current_month', child: Text('الشهر الحالي')),
                          DropdownMenuItem(value: 'current_quarter', child: Text('الربع الحالي')),
                          DropdownMenuItem(value: 'current_year', child: Text('السنة الحالية')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedPeriod = value!;
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedCategory,
                        decoration: const InputDecoration(
                          labelText: 'فئة النفقات',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الفئات')),
                          DropdownMenuItem(value: 'مصروفات الرواتب', child: Text('مصروفات الرواتب')),
                          DropdownMenuItem(value: 'مصروفات التسويق', child: Text('مصروفات التسويق')),
                          DropdownMenuItem(value: 'مصروفات التشغيل', child: Text('مصروفات التشغيل')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedCategory = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // ملخص النفقات
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              color: Colors.redAccent[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    const Text(
                      'ملخص مراقبة النفقات',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Column(
                          children: [
                            const Text('الميزانية المخططة'),
                            Text(
                              '${_getTotalBudget(filteredData)} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('النفقات الفعلية'),
                            Text(
                              '${_getTotalActual(filteredData)} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.red,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('الانحراف'),
                            Text(
                              '${_getTotalVariance(filteredData)} ر.س',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: _getTotalVariance(filteredData) >= 0 ? Colors.red : Colors.green,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                _buildStatCard('فئات النفقات', filteredData.length.toString(), Colors.blue),
                _buildStatCard('ضمن الميزانية', _getWithinBudgetCount(filteredData).toString(), Colors.green),
                _buildStatCard('تجاوز الميزانية', _getOverBudgetCount(filteredData).toString(), Colors.red),
                _buildStatCard('متوسط الانحراف', '${_getAverageVariance(filteredData)}%', Colors.orange),
              ],
            ),
          ),

          // قائمة النفقات
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: filteredData.length,
              itemBuilder: (context, index) {
                final expense = filteredData[index];
                
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: _getStatusColor(expense['status']),
                      child: Icon(
                        _getStatusIcon(expense['status']),
                        color: Colors.white,
                      ),
                    ),
                    title: Text(
                      expense['category'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('الميزانية: ${expense['budgetAmount'].toStringAsFixed(2)} ر.س'),
                        Text('الفعلي: ${expense['actualAmount'].toStringAsFixed(2)} ر.س'),
                        Text('الانحراف: ${expense['variance'].toStringAsFixed(2)} ر.س (${expense['variancePercentage']}%)'),
                        const SizedBox(height: 4),
                        LinearProgressIndicator(
                          value: (expense['actualAmount'] / expense['budgetAmount']).clamp(0.0, 1.0),
                          backgroundColor: Colors.grey[300],
                          valueColor: AlwaysStoppedAnimation<Color>(
                            _getStatusColor(expense['status']),
                          ),
                        ),
                        Text('الحالة: ${expense['status']}'),
                      ],
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('الميزانية المخططة', '${expense['budgetAmount'].toStringAsFixed(2)} ر.س', Icons.account_balance, Colors.blue),
                                ),
                                Expanded(
                                  child: _buildDetailCard('النفقات الفعلية', '${expense['actualAmount'].toStringAsFixed(2)} ر.س', Icons.money_off, Colors.red),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('الانحراف', '${expense['variance'].toStringAsFixed(2)} ر.س', Icons.trending_up, expense['variance'] >= 0 ? Colors.red : Colors.green),
                                ),
                                Expanded(
                                  child: _buildDetailCard('نسبة الانحراف', '${expense['variancePercentage']}%', Icons.percent, expense['variance'] >= 0 ? Colors.red : Colors.green),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('الشهر الماضي', '${expense['lastMonthAmount'].toStringAsFixed(2)} ر.س', Icons.history, Colors.grey),
                                ),
                                Expanded(
                                  child: _buildDetailCard('النمو', '${expense['growth']}%', Icons.show_chart, expense['growth'] >= 0 ? Colors.red : Colors.green),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            const Text(
                              'تفاصيل النفقات:',
                              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                            ),
                            const SizedBox(height: 8),
                            ...expense['details'].map<Widget>((detail) {
                              double variance = detail['actual'] - detail['budget'];
                              return ListTile(
                                leading: Icon(
                                  variance >= 0 ? Icons.trending_up : Icons.trending_down,
                                  color: variance >= 0 ? Colors.red : Colors.green,
                                ),
                                title: Text(detail['item']),
                                subtitle: Text('الميزانية: ${detail['budget'].toStringAsFixed(2)} ر.س'),
                                trailing: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Text(
                                      '${detail['actual'].toStringAsFixed(2)} ر.س',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: variance >= 0 ? Colors.red : Colors.green,
                                      ),
                                    ),
                                    Text(
                                      '${variance.toStringAsFixed(2)} ر.س',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: variance >= 0 ? Colors.red : Colors.green,
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }).toList(),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshData,
        backgroundColor: Colors.redAccent,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث البيانات'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Icon(icon, size: 20, color: color),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                color: color,
              ),
            ),
            Text(
              title,
              style: const TextStyle(fontSize: 10),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'ضمن الميزانية':
        return Colors.green;
      case 'تجاوز الميزانية':
        return Colors.red;
      case 'تجاوز طفيف':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'ضمن الميزانية':
        return Icons.check_circle;
      case 'تجاوز الميزانية':
        return Icons.warning;
      case 'تجاوز طفيف':
        return Icons.info;
      default:
        return Icons.help;
    }
  }

  List<Map<String, dynamic>> _getFilteredData() {
    if (_selectedCategory == 'all') {
      return _expenseData;
    }
    return _expenseData.where((expense) => expense['category'] == _selectedCategory).toList();
  }

  double _getTotalBudget(List<Map<String, dynamic>> data) {
    return data.fold(0.0, (sum, expense) => sum + expense['budgetAmount']);
  }

  double _getTotalActual(List<Map<String, dynamic>> data) {
    return data.fold(0.0, (sum, expense) => sum + expense['actualAmount']);
  }

  double _getTotalVariance(List<Map<String, dynamic>> data) {
    return data.fold(0.0, (sum, expense) => sum + expense['variance']);
  }

  int _getWithinBudgetCount(List<Map<String, dynamic>> data) {
    return data.where((expense) => expense['status'] == 'ضمن الميزانية').length;
  }

  int _getOverBudgetCount(List<Map<String, dynamic>> data) {
    return data.where((expense) => expense['status'] == 'تجاوز الميزانية').length;
  }

  String _getAverageVariance(List<Map<String, dynamic>> data) {
    if (data.isEmpty) return '0.0';
    double total = data.fold(0.0, (sum, expense) => sum + expense['variancePercentage']);
    return (total / data.length).toStringAsFixed(1);
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة تقارير مراقبة النفقات')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير تقارير مراقبة النفقات')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث بيانات مراقبة النفقات'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
