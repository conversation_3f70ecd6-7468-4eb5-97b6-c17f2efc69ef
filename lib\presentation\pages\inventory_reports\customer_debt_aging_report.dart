import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير متوسط أعمار الديون للعملاء
/// يعرض تحليل أعمار الديون المستحقة على العملاء
class CustomerDebtAgingReportPage extends StatefulWidget {
  const CustomerDebtAgingReportPage({super.key});

  @override
  State<CustomerDebtAgingReportPage> createState() => _CustomerDebtAgingReportPageState();
}

class _CustomerDebtAgingReportPageState extends State<CustomerDebtAgingReportPage> {
  DateTime? _reportDate;
  String? _selectedCustomer;
  String? _agingPeriod = '30_days';
  String? _debtStatus = 'all';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('متوسط أعمار الديون للعملاء'),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: _sendReminders,
            tooltip: 'إرسال تذكيرات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.indigo[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'تاريخ التقرير',
                          border: OutlineInputBorder(),
                          suffixIcon: Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context),
                        controller: TextEditingController(
                          text: _reportDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'العميل',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedCustomer,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع العملاء')),
                          DropdownMenuItem(value: 'customer1', child: Text('شركة الأمل التجارية')),
                          DropdownMenuItem(value: 'customer2', child: Text('مؤسسة النور للتجارة')),
                          DropdownMenuItem(value: 'customer3', child: Text('شركة الفجر الجديد')),
                        ],
                        onChanged: (value) => setState(() => _selectedCustomer = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'فترة التقسيم',
                          border: OutlineInputBorder(),
                        ),
                        value: _agingPeriod,
                        items: const [
                          DropdownMenuItem(value: '30_days', child: Text('30 يوم')),
                          DropdownMenuItem(value: '60_days', child: Text('60 يوم')),
                          DropdownMenuItem(value: '90_days', child: Text('90 يوم')),
                          DropdownMenuItem(value: 'custom', child: Text('مخصص')),
                        ],
                        onChanged: (value) => setState(() => _agingPeriod = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'حالة الدين',
                          border: OutlineInputBorder(),
                        ),
                        value: _debtStatus,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الديون')),
                          DropdownMenuItem(value: 'overdue', child: Text('متأخرة')),
                          DropdownMenuItem(value: 'current', child: Text('جارية')),
                          DropdownMenuItem(value: 'critical', child: Text('حرجة')),
                        ],
                        onChanged: (value) => setState(() => _debtStatus = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.search),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.indigo,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص أعمار الديون
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.account_balance, color: Colors.indigo, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'ملخص أعمار الديون',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSummaryCard('إجمالي الديون', '1,850,000 ر.س', Colors.blue, Icons.monetization_on),
                              _buildSummaryCard('متوسط العمر', '45 يوم', Colors.green, Icons.schedule),
                              _buildSummaryCard('ديون متأخرة', '650,000 ر.س', Colors.red, Icons.warning),
                              _buildSummaryCard('عدد العملاء', '156', Colors.purple, Icons.people),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // توزيع أعمار الديون
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'توزيع أعمار الديون',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildAgingCard('0-30 يوم', '750,000 ر.س', '40.5%', Colors.green),
                              _buildAgingCard('31-60 يوم', '450,000 ر.س', '24.3%', Colors.orange),
                              _buildAgingCard('61-90 يوم', '350,000 ر.س', '18.9%', Colors.amber),
                              _buildAgingCard('+90 يوم', '300,000 ر.س', '16.3%', Colors.red),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول تفاصيل أعمار الديون
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل أعمار الديون للعملاء',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('العميل')),
                                DataColumn(label: Text('إجمالي الدين')),
                                DataColumn(label: Text('0-30 يوم')),
                                DataColumn(label: Text('31-60 يوم')),
                                DataColumn(label: Text('61-90 يوم')),
                                DataColumn(label: Text('+90 يوم')),
                                DataColumn(label: Text('متوسط العمر')),
                                DataColumn(label: Text('الحالة')),
                                DataColumn(label: Text('آخر دفعة')),
                              ],
                              rows: _buildDebtAgingRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // العملاء الأكثر تأخيراً
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.warning, color: Colors.red, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'العملاء الأكثر تأخيراً',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          ..._buildOverdueCustomersList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _sendPaymentReminders,
                                  icon: const Icon(Icons.email),
                                  label: const Text('تذكيرات دفع'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _generateCollectionReport,
                                  icon: const Icon(Icons.assignment),
                                  label: const Text('تقرير تحصيل'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _setCreditLimits,
                                  icon: const Icon(Icons.credit_card),
                                  label: const Text('حدود ائتمان'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _scheduleFollowUp,
                                  icon: const Icon(Icons.schedule),
                                  label: const Text('جدولة متابعة'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<DataRow> _buildDebtAgingRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('شركة الأمل التجارية')),
        const DataCell(Text('485,000 ر.س')),
        const DataCell(Text('200,000 ر.س')),
        const DataCell(Text('150,000 ر.س')),
        const DataCell(Text('85,000 ر.س')),
        const DataCell(Text('50,000 ر.س')),
        const DataCell(Text('52 يوم')),
        DataCell(_buildStatusBadge('متأخر', Colors.red)),
        const DataCell(Text('2024-01-15')),
      ]),
      DataRow(cells: [
        const DataCell(Text('مؤسسة النور للتجارة')),
        const DataCell(Text('320,000 ر.س')),
        const DataCell(Text('250,000 ر.س')),
        const DataCell(Text('70,000 ر.س')),
        const DataCell(Text('0 ر.س')),
        const DataCell(Text('0 ر.س')),
        const DataCell(Text('28 يوم')),
        DataCell(_buildStatusBadge('جيد', Colors.green)),
        const DataCell(Text('2024-02-10')),
      ]),
      DataRow(cells: [
        const DataCell(Text('شركة الفجر الجديد')),
        const DataCell(Text('285,000 ر.س')),
        const DataCell(Text('100,000 ر.س')),
        const DataCell(Text('85,000 ر.س')),
        const DataCell(Text('100,000 ر.س')),
        const DataCell(Text('0 ر.س')),
        const DataCell(Text('45 يوم')),
        DataCell(_buildStatusBadge('متوسط', Colors.orange)),
        const DataCell(Text('2024-01-28')),
      ]),
    ];
  }

  List<Widget> _buildOverdueCustomersList() {
    final overdueCustomers = [
      {'name': 'شركة الأمل التجارية', 'amount': '485,000 ر.س', 'days': '85 يوم', 'risk': 'عالي'},
      {'name': 'مؤسسة التقدم', 'amount': '320,000 ر.س', 'days': '120 يوم', 'risk': 'عالي'},
      {'name': 'شركة الإبداع', 'amount': '185,000 ر.س', 'days': '95 يوم', 'risk': 'متوسط'},
      {'name': 'مؤسسة الرائد', 'amount': '150,000 ر.س', 'days': '75 يوم', 'risk': 'متوسط'},
    ];

    return overdueCustomers.map((customer) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.red.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.red.withOpacity(0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.warning, color: Colors.red, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  customer['name']!,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'المبلغ: ${customer['amount']} • العمر: ${customer['days']}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: customer['risk'] == 'عالي' ? Colors.red : Colors.orange,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              'خطر ${customer['risk']!}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAgingCard(String period, String amount, String percentage, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            children: [
              Text(
                amount,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                percentage,
                style: TextStyle(
                  fontSize: 12,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                period,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        status,
        style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        _reportDate = picked;
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير أعمار الديون بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _sendReminders() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إرسال تذكيرات الدفع')),
    );
  }

  void _sendPaymentReminders() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إرسال تذكيرات دفع للعملاء المتأخرين')),
    );
  }

  void _generateCollectionReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء تقرير تحصيل مفصل')),
    );
  }

  void _setCreditLimits() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تعديل حدود الائتمان للعملاء')),
    );
  }

  void _scheduleFollowUp() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جدولة متابعة العملاء المتأخرين')),
    );
  }
}
