import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير تفصيلي بحركة الموردين خلال فترة
/// يعرض تفاصيل حركة الموردين والمعاملات معهم
class SuppliersDetailedMovementReportPage extends StatefulWidget {
  const SuppliersDetailedMovementReportPage({super.key});

  @override
  State<SuppliersDetailedMovementReportPage> createState() => _SuppliersDetailedMovementReportPageState();
}

class _SuppliersDetailedMovementReportPageState extends State<SuppliersDetailedMovementReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedSupplier;
  String? _movementType = 'all';
  final String _sortBy = 'transaction_date';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('حركة الموردين التفصيلية'),
        backgroundColor: Colors.brown,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.business),
            onPressed: _showSupplierAnalysis,
            tooltip: 'تحليل الموردين',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilterSection(localizations),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildMovementTypesSection(),
                  const SizedBox(height: 16),
                  _buildMovementTableSection(),
                  const SizedBox(height: 16),
                  _buildTopSuppliersSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.brown[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.fromDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, true),
                  controller: TextEditingController(
                    text: _startDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.toDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, false),
                  controller: TextEditingController(
                    text: _endDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'المورد',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedSupplier,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الموردين')),
                    DropdownMenuItem(value: 'supplier1', child: Text('شركة التقنية المتقدمة')),
                    DropdownMenuItem(value: 'supplier2', child: Text('مؤسسة الإلكترونيات')),
                    DropdownMenuItem(value: 'supplier3', child: Text('شركة الأجهزة الذكية')),
                  ],
                  onChanged: (value) => setState(() => _selectedSupplier = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'نوع الحركة',
                    border: OutlineInputBorder(),
                  ),
                  value: _movementType,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الحركات')),
                    DropdownMenuItem(value: 'purchase', child: Text('مشتريات')),
                    DropdownMenuItem(value: 'payment', child: Text('مدفوعات')),
                    DropdownMenuItem(value: 'return', child: Text('مرتجعات')),
                    DropdownMenuItem(value: 'credit', child: Text('ائتمان')),
                  ],
                  onChanged: (value) => setState(() => _movementType = value),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.business, color: Colors.brown, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص حركة الموردين',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي الموردين', '45', Colors.brown, Icons.business),
                _buildSummaryCard('إجمالي المشتريات', '1,850,000 ر.س', Colors.green, Icons.shopping_cart),
                _buildSummaryCard('إجمالي المدفوعات', '1,650,000 ر.س', Colors.blue, Icons.payment),
                _buildSummaryCard('الرصيد المستحق', '200,000 ر.س', Colors.orange, Icons.account_balance),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMovementTypesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.pie_chart, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'توزيع أنواع الحركات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildMovementCard('مشتريات', '485 حركة', Colors.green),
                _buildMovementCard('مدفوعات', '325 حركة', Colors.blue),
                _buildMovementCard('مرتجعات', '85 حركة', Colors.orange),
                _buildMovementCard('ائتمان', '125 حركة', Colors.purple),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMovementTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل حركة الموردين',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('التاريخ')),
                  DataColumn(label: Text('المورد')),
                  DataColumn(label: Text('نوع الحركة')),
                  DataColumn(label: Text('رقم المستند')),
                  DataColumn(label: Text('المبلغ')),
                  DataColumn(label: Text('الرصيد')),
                  DataColumn(label: Text('طريقة الدفع')),
                  DataColumn(label: Text('الحالة')),
                  DataColumn(label: Text('ملاحظات')),
                ],
                rows: _buildMovementRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopSuppliersSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.star, color: Colors.amber, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'أهم الموردين',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildTopSuppliersList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _reconcileAccounts,
                    icon: const Icon(Icons.balance),
                    label: const Text('تسوية الحسابات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _generatePaymentSchedule,
                    icon: const Icon(Icons.schedule),
                    label: const Text('جدولة المدفوعات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildTopSuppliersList() {
    final topSuppliers = [
      {'name': 'شركة التقنية المتقدمة', 'amount': '485,000 ر.س', 'transactions': '125', 'balance': '25,000 ر.س'},
      {'name': 'مؤسسة الإلكترونيات', 'amount': '385,000 ر.س', 'transactions': '95', 'balance': '15,000 ر.س'},
      {'name': 'شركة الأجهزة الذكية', 'amount': '285,000 ر.س', 'transactions': '75', 'balance': '8,000 ر.س'},
    ];

    return topSuppliers.map((supplier) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.amber.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.amber.withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.business, color: Colors.amber, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  supplier['name']!,
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  'مبلغ: ${supplier['amount']} • معاملات: ${supplier['transactions']} • رصيد: ${supplier['balance']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildMovementRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('2024-01-15')),
        const DataCell(Text('شركة التقنية المتقدمة')),
        DataCell(_buildMovementTypeBadge('مشتريات', Colors.green)),
        const DataCell(Text('PUR-001')),
        const DataCell(Text('25,000 ر.س')),
        const DataCell(Text('125,000 ر.س')),
        const DataCell(Text('آجل')),
        DataCell(_buildStatusBadge('مؤكد', Colors.green)),
        const DataCell(Text('طلب شراء عادي')),
      ]),
      DataRow(cells: [
        const DataCell(Text('2024-01-18')),
        const DataCell(Text('مؤسسة الإلكترونيات')),
        DataCell(_buildMovementTypeBadge('مدفوعات', Colors.blue)),
        const DataCell(Text('PAY-002')),
        const DataCell(Text('15,000 ر.س')),
        const DataCell(Text('85,000 ر.س')),
        const DataCell(Text('تحويل بنكي')),
        DataCell(_buildStatusBadge('مكتمل', Colors.green)),
        const DataCell(Text('دفعة جزئية')),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMovementCard(String type, String count, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(type, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMovementTypeBadge(String type, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(type, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(status, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showSupplierAnalysis() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تحليل الموردين')),
    );
  }

  void _reconcileAccounts() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تسوية حسابات الموردين')),
    );
  }

  void _generatePaymentSchedule() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء جدولة المدفوعات')),
    );
  }
}
