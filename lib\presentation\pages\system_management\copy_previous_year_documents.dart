import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة نسخ المستندات من السنة السابقة
/// تتيح نسخ المستندات والبيانات من سنة مالية سابقة إلى السنة الحالية
class CopyPreviousYearDocumentsPage extends StatefulWidget {
  const CopyPreviousYearDocumentsPage({super.key});

  @override
  State<CopyPreviousYearDocumentsPage> createState() =>
      _CopyPreviousYearDocumentsPageState();
}

class _CopyPreviousYearDocumentsPageState
    extends State<CopyPreviousYearDocumentsPage> {
  final _formKey = GlobalKey<FormState>();

  String? _selectedSourceYear;
  String? _selectedTargetYear;
  final Set<String> _selectedDocumentTypes = {};
  bool _isProcessing = false;
  bool _includeAttachments = true;
  bool _overwriteExisting = false;

  final List<String> _availableYears = [
    '2024',
    '2023',
    '2022',
    '2021',
    '2020',
    '2019'
  ];

  final List<Map<String, dynamic>> _documentTypes = [
    {
      'id': 'invoices',
      'name': 'فواتير المبيعات',
      'icon': Icons.receipt_long,
      'count': 1250,
      'description': 'جميع فواتير المبيعات والخدمات'
    },
    {
      'id': 'purchases',
      'name': 'فواتير المشتريات',
      'icon': Icons.shopping_cart,
      'count': 890,
      'description': 'فواتير المشتريات من الموردين'
    },
    {
      'id': 'customers',
      'name': 'بيانات العملاء',
      'icon': Icons.people,
      'count': 450,
      'description': 'معلومات العملاء والحسابات'
    },
    {
      'id': 'suppliers',
      'name': 'بيانات الموردين',
      'icon': Icons.business,
      'count': 120,
      'description': 'معلومات الموردين والحسابات'
    },
    {
      'id': 'products',
      'name': 'بيانات الأصناف',
      'icon': Icons.inventory_2,
      'count': 2300,
      'description': 'أصناف المنتجات والخدمات'
    },
    {
      'id': 'accounts',
      'name': 'دليل الحسابات',
      'icon': Icons.account_tree,
      'count': 180,
      'description': 'شجرة الحسابات المالية'
    },
    {
      'id': 'reports',
      'name': 'التقارير المحفوظة',
      'icon': Icons.analytics,
      'count': 75,
      'description': 'التقارير والتحليلات المحفوظة'
    },
    {
      'id': 'settings',
      'name': 'إعدادات النظام',
      'icon': Icons.settings,
      'count': 25,
      'description': 'إعدادات وتكوينات النظام'
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.copyDocumentsPreviousYear),
        backgroundColor: Colors.pink,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: _showCopyHistory,
            tooltip: 'سجل عمليات النسخ',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // بطاقة اختيار السنوات
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'اختيار السنوات المالية',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.pink,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        // السنة المصدر
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedSourceYear,
                            decoration: const InputDecoration(
                              labelText: 'السنة المصدر (النسخ منها)',
                              prefixIcon: Icon(Icons.source),
                              border: OutlineInputBorder(),
                            ),
                            items: _availableYears.map((year) {
                              return DropdownMenuItem<String>(
                                value: year,
                                child: Text('السنة المالية $year'),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedSourceYear = value;
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'يرجى اختيار السنة المصدر';
                              }
                              return null;
                            },
                          ),
                        ),

                        const SizedBox(width: 16),

                        // السنة الهدف
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedTargetYear,
                            decoration: const InputDecoration(
                              labelText: 'السنة الهدف (النسخ إليها)',
                              prefixIcon: Icon(Icons.flag),
                              border: OutlineInputBorder(),
                            ),
                            items: _availableYears
                                .where((year) => year != _selectedSourceYear)
                                .map((year) {
                              return DropdownMenuItem<String>(
                                value: year,
                                child: Text('السنة المالية $year'),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedTargetYear = value;
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'يرجى اختيار السنة الهدف';
                              }
                              if (value == _selectedSourceYear) {
                                return 'لا يمكن النسخ إلى نفس السنة';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة اختيار أنواع المستندات
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Text(
                          'اختيار أنواع المستندات',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.pink,
                          ),
                        ),
                        const Spacer(),
                        TextButton(
                          onPressed: _selectAllDocuments,
                          child: const Text('تحديد الكل'),
                        ),
                        TextButton(
                          onPressed: _clearAllDocuments,
                          child: const Text('إلغاء الكل'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    ...(_documentTypes.map((docType) => CheckboxListTile(
                          value: _selectedDocumentTypes.contains(docType['id']),
                          onChanged: (bool? value) {
                            setState(() {
                              if (value == true) {
                                _selectedDocumentTypes.add(docType['id']);
                              } else {
                                _selectedDocumentTypes.remove(docType['id']);
                              }
                            });
                          },
                          title: Row(
                            children: [
                              Icon(docType['icon'], color: Colors.pink),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      docType['name'],
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold),
                                    ),
                                    Text(
                                      docType['description'],
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          subtitle: Text(
                            'العدد: ${docType['count']} عنصر',
                            style: const TextStyle(color: Colors.blue),
                          ),
                          controlAffinity: ListTileControlAffinity.leading,
                        ))),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة خيارات النسخ
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'خيارات النسخ',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.pink,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SwitchListTile(
                      title: const Text('تضمين المرفقات والملفات'),
                      subtitle: const Text('نسخ الملفات المرفقة مع المستندات'),
                      value: _includeAttachments,
                      onChanged: (value) {
                        setState(() {
                          _includeAttachments = value;
                        });
                      },
                    ),
                    SwitchListTile(
                      title: const Text('استبدال البيانات الموجودة'),
                      subtitle: const Text(
                          'استبدال البيانات المتطابقة في السنة الهدف'),
                      value: _overwriteExisting,
                      onChanged: (value) {
                        setState(() {
                          _overwriteExisting = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // معاينة العملية
            if (_selectedSourceYear != null &&
                _selectedTargetYear != null &&
                _selectedDocumentTypes.isNotEmpty)
              Card(
                color: Colors.pink.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'معاينة عملية النسخ',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.pink,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildPreviewRow(
                          'من السنة:', 'السنة المالية $_selectedSourceYear'),
                      _buildPreviewRow(
                          'إلى السنة:', 'السنة المالية $_selectedTargetYear'),
                      _buildPreviewRow('عدد الأنواع:',
                          '${_selectedDocumentTypes.length} نوع مستند'),
                      _buildPreviewRow(
                          'إجمالي العناصر:', '${_getTotalItemsCount()} عنصر'),
                      _buildPreviewRow('تضمين المرفقات:',
                          _includeAttachments ? 'نعم' : 'لا'),
                      _buildPreviewRow('استبدال الموجود:',
                          _overwriteExisting ? 'نعم' : 'لا'),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 24),

            // أزرار العمليات
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isProcessing || _selectedDocumentTypes.isEmpty
                        ? null
                        : _startCopyProcess,
                    icon: _isProcessing
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.copy),
                    label: Text(
                        _isProcessing ? 'جاري النسخ...' : 'بدء عملية النسخ'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.pink,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _resetForm,
                    icon: const Icon(Icons.refresh),
                    label: const Text('إعادة تعيين'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  int _getTotalItemsCount() {
    int total = 0;
    for (String typeId in _selectedDocumentTypes) {
      final docType = _documentTypes.firstWhere((type) => type['id'] == typeId);
      total += docType['count'] as int;
    }
    return total;
  }

  void _selectAllDocuments() {
    setState(() {
      _selectedDocumentTypes.clear();
      _selectedDocumentTypes
          .addAll(_documentTypes.map((type) => type['id'] as String));
    });
  }

  void _clearAllDocuments() {
    setState(() {
      _selectedDocumentTypes.clear();
    });
  }

  Future<void> _startCopyProcess() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isProcessing = true;
      });

      // محاكاة عملية النسخ
      await Future.delayed(const Duration(seconds: 3));

      setState(() {
        _isProcessing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'تم نسخ ${_getTotalItemsCount()} عنصر بنجاح من السنة $_selectedSourceYear إلى $_selectedTargetYear'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 4),
          ),
        );
        _resetForm();
      }
    }
  }

  void _resetForm() {
    setState(() {
      _selectedSourceYear = null;
      _selectedTargetYear = null;
      _selectedDocumentTypes.clear();
      _includeAttachments = true;
      _overwriteExisting = false;
    });
  }

  void _showCopyHistory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض سجل عمليات النسخ')),
    );
  }
}
