import 'package:flutter/material.dart';

/// صفحة تقرير نشاط المستخدم خلال فترة
/// تعرض تقرير مفصل عن نشاط المستخدمين في فترة محددة
class UserActivityReportPage extends StatefulWidget {
  const UserActivityReportPage({super.key});

  @override
  State<UserActivityReportPage> createState() => _UserActivityReportPageState();
}

class _UserActivityReportPageState extends State<UserActivityReportPage> {
  final _searchController = TextEditingController();

  String _searchQuery = '';
  String? _selectedUser;
  String? _selectedActivity;
  DateTime? _fromDate;
  DateTime? _toDate;

  final List<Map<String, String>> _users = [
    {'id': 'user1', 'name': 'أحمد محمد', 'role': 'مدير'},
    {'id': 'user2', 'name': 'فاطمة علي', 'role': 'محاسب'},
    {'id': 'user3', 'name': 'محمد سالم', 'role': 'كاشير'},
    {'id': 'user4', 'name': 'نورا أحمد', 'role': 'مخزني'},
  ];

  final List<Map<String, String>> _activityTypes = [
    {'id': 'login', 'name': 'تسجيل دخول'},
    {'id': 'logout', 'name': 'تسجيل خروج'},
    {'id': 'invoice', 'name': 'إنشاء فاتورة'},
    {'id': 'payment', 'name': 'تسجيل دفعة'},
    {'id': 'report', 'name': 'عرض تقرير'},
    {'id': 'settings', 'name': 'تعديل إعدادات'},
  ];

  final List<Map<String, dynamic>> _activities = [
    {
      'id': 'act1',
      'user': 'أحمد محمد',
      'activity': 'تسجيل دخول',
      'date': '2024/01/25',
      'time': '08:30',
      'details': 'تسجيل دخول من IP: *************',
      'duration': '8 ساعات',
      'status': 'مكتمل',
    },
    {
      'id': 'act2',
      'user': 'فاطمة علي',
      'activity': 'إنشاء فاتورة',
      'date': '2024/01/25',
      'time': '10:15',
      'details': 'إنشاء فاتورة مبيعات INV-2024-001',
      'duration': '15 دقيقة',
      'status': 'مكتمل',
    },
    {
      'id': 'act3',
      'user': 'محمد سالم',
      'activity': 'تسجيل دفعة',
      'date': '2024/01/25',
      'time': '11:45',
      'details': 'تسجيل دفعة نقدية 5000 ر.س',
      'duration': '5 دقائق',
      'status': 'مكتمل',
    },
    {
      'id': 'act4',
      'user': 'نورا أحمد',
      'activity': 'عرض تقرير',
      'date': '2024/01/25',
      'time': '14:20',
      'details': 'عرض تقرير المخزون الشهري',
      'duration': '20 دقيقة',
      'status': 'مكتمل',
    },
  ];

  List<Map<String, dynamic>> get _filteredActivities {
    List<Map<String, dynamic>> filtered = _activities;

    // فلتر المستخدم
    if (_selectedUser != null) {
      final selectedUser =
          _users.where((u) => u['id'] == _selectedUser).firstOrNull;
      if (selectedUser != null) {
        final selectedUserName = selectedUser['name'];
        filtered = filtered
            .where((activity) => activity['user'] == selectedUserName)
            .toList();
      }
    }

    // فلتر نوع النشاط
    if (_selectedActivity != null) {
      final selectedActivity =
          _activityTypes.where((a) => a['id'] == _selectedActivity).firstOrNull;
      if (selectedActivity != null) {
        final selectedActivityName = selectedActivity['name'];
        filtered = filtered
            .where((activity) => activity['activity'] == selectedActivityName)
            .toList();
      }
    }

    // البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered
          .where((activity) =>
              activity['user']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              activity['activity']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              activity['details']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()))
          .toList();
    }

    return filtered;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير نشاط المستخدم'),
        backgroundColor: Colors.cyan,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // بطاقة الفلاتر
          Card(
            margin: const EdgeInsets.all(16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      labelText: 'البحث في الأنشطة',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedUser,
                          decoration: const InputDecoration(
                            labelText: 'المستخدم',
                            border: OutlineInputBorder(),
                          ),
                          items: [
                            const DropdownMenuItem(
                                value: null, child: Text('جميع المستخدمين')),
                            ..._users.map((user) => DropdownMenuItem(
                                  value: user['id'],
                                  child:
                                      Text('${user['name']} (${user['role']})'),
                                )),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedUser = value;
                            });
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedActivity,
                          decoration: const InputDecoration(
                            labelText: 'نوع النشاط',
                            border: OutlineInputBorder(),
                          ),
                          items: [
                            const DropdownMenuItem(
                                value: null, child: Text('جميع الأنشطة')),
                            ..._activityTypes
                                .map((activity) => DropdownMenuItem(
                                      value: activity['id'],
                                      child: Text(activity['name']!),
                                    )),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedActivity = value;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () => _selectDate(true),
                          child: InputDecorator(
                            decoration: const InputDecoration(
                              labelText: 'من تاريخ',
                              border: OutlineInputBorder(),
                            ),
                            child: Text(_fromDate != null
                                ? '${_fromDate!.day}/${_fromDate!.month}/${_fromDate!.year}'
                                : 'اختر التاريخ'),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: InkWell(
                          onTap: () => _selectDate(false),
                          child: InputDecorator(
                            decoration: const InputDecoration(
                              labelText: 'إلى تاريخ',
                              border: OutlineInputBorder(),
                            ),
                            child: Text(_toDate != null
                                ? '${_toDate!.day}/${_toDate!.month}/${_toDate!.year}'
                                : 'اختر التاريخ'),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // إحصائيات سريعة
          Card(
            margin: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatCard('إجمالي الأنشطة',
                      _filteredActivities.length.toString(), Colors.blue),
                  _buildStatCard('المستخدمين النشطين',
                      _getActiveUsersCount().toString(), Colors.green),
                  _buildStatCard('أنشطة اليوم',
                      _getTodayActivitiesCount().toString(), Colors.orange),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // قائمة الأنشطة
          Expanded(
            child: _filteredActivities.isEmpty
                ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.search_off, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text(
                          'لا توجد أنشطة تطابق معايير البحث',
                          style: TextStyle(fontSize: 16, color: Colors.grey),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    itemCount: _filteredActivities.length,
                    itemBuilder: (context, index) {
                      final activity = _filteredActivities[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 8.0),
                        child: ExpansionTile(
                          leading: CircleAvatar(
                            backgroundColor:
                                _getActivityColor(activity['activity']),
                            child: Icon(
                              _getActivityIcon(activity['activity']),
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                          title: Text(
                            '${activity['user']} - ${activity['activity']}',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          subtitle: Text(
                              '${activity['date']} ${activity['time']} | ${activity['duration']}'),
                          trailing: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.green.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              activity['status'],
                              style: const TextStyle(
                                color: Colors.green,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ),
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  _buildDetailRow(
                                      'المستخدم:', activity['user']),
                                  _buildDetailRow(
                                      'النشاط:', activity['activity']),
                                  _buildDetailRow('التاريخ والوقت:',
                                      '${activity['date']} ${activity['time']}'),
                                  _buildDetailRow(
                                      'المدة:', activity['duration']),
                                  _buildDetailRow(
                                      'الحالة:', activity['status']),
                                  _buildDetailRow(
                                      'التفاصيل:', activity['details']),
                                  const SizedBox(height: 16),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceEvenly,
                                    children: [
                                      ElevatedButton.icon(
                                        onPressed: () =>
                                            _viewActivityDetails(activity),
                                        icon: const Icon(Icons.visibility,
                                            size: 16),
                                        label: const Text('تفاصيل'),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.blue,
                                          foregroundColor: Colors.white,
                                        ),
                                      ),
                                      ElevatedButton.icon(
                                        onPressed: () => _viewUserActivities(
                                            activity['user']),
                                        icon:
                                            const Icon(Icons.person, size: 16),
                                        label: const Text('أنشطة المستخدم'),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.green,
                                          foregroundColor: Colors.white,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _generateReport,
        backgroundColor: Colors.cyan,
        icon: const Icon(Icons.assessment),
        label: const Text('إنشاء تقرير'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Column(
      children: [
        CircleAvatar(
          backgroundColor: color,
          child: Text(value,
              style: const TextStyle(
                  color: Colors.white, fontWeight: FontWeight.bold)),
        ),
        const SizedBox(height: 4),
        Text(title,
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold)),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
              width: 120,
              child: Text(label,
                  style: const TextStyle(
                      fontWeight: FontWeight.bold, fontSize: 12))),
          Expanded(child: Text(value, style: const TextStyle(fontSize: 12))),
        ],
      ),
    );
  }

  Color _getActivityColor(String activity) {
    switch (activity) {
      case 'تسجيل دخول':
        return Colors.green;
      case 'تسجيل خروج':
        return Colors.red;
      case 'إنشاء فاتورة':
        return Colors.blue;
      case 'تسجيل دفعة':
        return Colors.orange;
      case 'عرض تقرير':
        return Colors.purple;
      case 'تعديل إعدادات':
        return Colors.brown;
      default:
        return Colors.grey;
    }
  }

  IconData _getActivityIcon(String activity) {
    switch (activity) {
      case 'تسجيل دخول':
        return Icons.login;
      case 'تسجيل خروج':
        return Icons.logout;
      case 'إنشاء فاتورة':
        return Icons.receipt;
      case 'تسجيل دفعة':
        return Icons.payment;
      case 'عرض تقرير':
        return Icons.assessment;
      case 'تعديل إعدادات':
        return Icons.settings;
      default:
        return Icons.work;
    }
  }

  int _getActiveUsersCount() {
    return _users.length; // مبسط للمثال
  }

  int _getTodayActivitiesCount() {
    return _filteredActivities
        .where((activity) => activity['date'] == '2024/01/25')
        .length;
  }

  Future<void> _selectDate(bool isFromDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      setState(() {
        if (isFromDate) {
          _fromDate = picked;
        } else {
          _toDate = picked;
        }
      });
    }
  }

  void _viewActivityDetails(Map<String, dynamic> activity) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض تفاصيل النشاط ${activity['id']}')),
    );
  }

  void _viewUserActivities(String user) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض جميع أنشطة المستخدم $user')),
    );
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
          content: Text('إنشاء تقرير نشاط المستخدمين'),
          backgroundColor: Colors.green),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير تقرير نشاط المستخدمين')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة تقرير نشاط المستخدمين')),
    );
  }
}
