import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير الفواتير الآجلة
/// يعرض الفواتير المؤجلة السداد مع تفاصيل الدفع المؤجل
class DeferredInvoicesReportPage extends StatefulWidget {
  const DeferredInvoicesReportPage({super.key});

  @override
  State<DeferredInvoicesReportPage> createState() => _DeferredInvoicesReportPageState();
}

class _DeferredInvoicesReportPageState extends State<DeferredInvoicesReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedCustomer;
  String? _paymentPlan = 'all';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الفواتير الآجلة'),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.schedule),
            onPressed: _managePaymentSchedule,
            tooltip: 'إدارة جدولة الدفع',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.indigo[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.fromDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, true),
                        controller: TextEditingController(
                          text: _startDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.toDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, false),
                        controller: TextEditingController(
                          text: _endDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'العميل',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedCustomer,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع العملاء')),
                          DropdownMenuItem(value: 'customer1', child: Text('شركة الأحمد للتجارة')),
                          DropdownMenuItem(value: 'customer2', child: Text('مؤسسة النور')),
                          DropdownMenuItem(value: 'customer3', child: Text('شركة الخليج')),
                        ],
                        onChanged: (value) => setState(() => _selectedCustomer = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'خطة الدفع',
                          border: OutlineInputBorder(),
                        ),
                        value: _paymentPlan,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الخطط')),
                          DropdownMenuItem(value: 'monthly', child: Text('دفع شهري')),
                          DropdownMenuItem(value: 'quarterly', child: Text('دفع ربع سنوي')),
                          DropdownMenuItem(value: 'semi_annual', child: Text('دفع نصف سنوي')),
                          DropdownMenuItem(value: 'annual', child: Text('دفع سنوي')),
                        ],
                        onChanged: (value) => setState(() => _paymentPlan = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.search),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.indigo,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص الفواتير الآجلة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.schedule, color: Colors.indigo, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'ملخص الفواتير الآجلة',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSummaryCard('إجمالي الفواتير', '85', Colors.blue, Icons.receipt_long),
                              _buildSummaryCard('إجمالي المبلغ', '1,250,000 ر.س', Colors.indigo, Icons.monetization_on),
                              _buildSummaryCard('المدفوع', '450,000 ر.س', Colors.green, Icons.check_circle),
                              _buildSummaryCard('المتبقي', '800,000 ر.س', Colors.orange, Icons.pending),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // توزيع خطط الدفع
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'توزيع خطط الدفع الآجل',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildPaymentPlanCard('دفع شهري', '35', '450,000 ر.س', Colors.blue),
                              _buildPaymentPlanCard('دفع ربع سنوي', '25', '380,000 ر.س', Colors.green),
                              _buildPaymentPlanCard('دفع نصف سنوي', '15', '280,000 ر.س', Colors.orange),
                              _buildPaymentPlanCard('دفع سنوي', '10', '140,000 ر.س', Colors.purple),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول الفواتير الآجلة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل الفواتير الآجلة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('رقم الفاتورة')),
                                DataColumn(label: Text('العميل')),
                                DataColumn(label: Text('تاريخ الفاتورة')),
                                DataColumn(label: Text('إجمالي المبلغ')),
                                DataColumn(label: Text('خطة الدفع')),
                                DataColumn(label: Text('عدد الأقساط')),
                                DataColumn(label: Text('المدفوع')),
                                DataColumn(label: Text('المتبقي')),
                                DataColumn(label: Text('القسط التالي')),
                                DataColumn(label: Text('الحالة')),
                              ],
                              rows: _buildDeferredInvoiceRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدولة الأقساط القادمة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.event, color: Colors.green, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'الأقساط المستحقة خلال الشهر القادم',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('رقم الفاتورة')),
                                DataColumn(label: Text('العميل')),
                                DataColumn(label: Text('تاريخ الاستحقاق')),
                                DataColumn(label: Text('مبلغ القسط')),
                                DataColumn(label: Text('رقم القسط')),
                                DataColumn(label: Text('الحالة')),
                                DataColumn(label: Text('الإجراء')),
                              ],
                              rows: _buildUpcomingInstallmentRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // تحليل الأداء
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تحليل أداء الدفع الآجل',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildPerformanceCard('معدل الالتزام', '92%', Colors.green, Icons.check_circle),
                              _buildPerformanceCard('متوسط فترة السداد', '8.5 شهر', Colors.blue, Icons.schedule),
                              _buildPerformanceCard('معدل التأخير', '8%', Colors.orange, Icons.warning),
                              _buildPerformanceCard('نسبة التحصيل', '94%', Colors.purple, Icons.trending_up),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _sendInstallmentReminders,
                                  icon: const Icon(Icons.notifications),
                                  label: const Text('تذكيرات الأقساط'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _generatePaymentSchedule,
                                  icon: const Icon(Icons.schedule),
                                  label: const Text('جدولة الدفع'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _modifyPaymentPlan,
                                  icon: const Icon(Icons.edit),
                                  label: const Text('تعديل خطة الدفع'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _recordPayment,
                                  icon: const Icon(Icons.payment),
                                  label: const Text('تسجيل دفعة'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<DataRow> _buildDeferredInvoiceRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('INV-2024-001')),
        const DataCell(Text('شركة الأحمد للتجارة')),
        const DataCell(Text('2024-01-15')),
        const DataCell(Text('120,000')),
        const DataCell(Text('شهري')),
        const DataCell(Text('12')),
        const DataCell(Text('60,000')),
        const DataCell(Text('60,000')),
        const DataCell(Text('2024-07-15')),
        DataCell(_buildStatusBadge('نشط', Colors.green)),
      ]),
      DataRow(cells: [
        const DataCell(Text('INV-2024-002')),
        const DataCell(Text('مؤسسة النور')),
        const DataCell(Text('2024-02-01')),
        const DataCell(Text('85,000')),
        const DataCell(Text('ربع سنوي')),
        const DataCell(Text('4')),
        const DataCell(Text('42,500')),
        const DataCell(Text('42,500')),
        const DataCell(Text('2024-08-01')),
        DataCell(_buildStatusBadge('نشط', Colors.green)),
      ]),
    ];
  }

  List<DataRow> _buildUpcomingInstallmentRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('INV-2024-001')),
        const DataCell(Text('شركة الأحمد للتجارة')),
        const DataCell(Text('2024-07-15')),
        const DataCell(Text('10,000')),
        const DataCell(Text('7/12')),
        DataCell(_buildStatusBadge('مستحق', Colors.orange)),
        DataCell(ElevatedButton(
          onPressed: () => _sendReminder('INV-2024-001'),
          style: ElevatedButton.styleFrom(backgroundColor: Colors.blue, foregroundColor: Colors.white),
          child: const Text('تذكير'),
        )),
      ]),
      DataRow(cells: [
        const DataCell(Text('INV-2024-003')),
        const DataCell(Text('شركة الخليج')),
        const DataCell(Text('2024-07-20')),
        const DataCell(Text('15,000')),
        const DataCell(Text('3/6')),
        DataCell(_buildStatusBadge('قريب الاستحقاق', Colors.yellow)),
        DataCell(ElevatedButton(
          onPressed: () => _sendReminder('INV-2024-003'),
          style: ElevatedButton.styleFrom(backgroundColor: Colors.orange, foregroundColor: Colors.white),
          child: const Text('تنبيه'),
        )),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentPlanCard(String title, String count, String amount, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                amount,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPerformanceCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 28),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(status, style: const TextStyle(color: Colors.white, fontSize: 12)),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير الفواتير الآجلة بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _managePaymentSchedule() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('فتح إدارة جدولة الدفع')),
    );
  }

  void _sendInstallmentReminders() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إرسال تذكيرات الأقساط')),
    );
  }

  void _generatePaymentSchedule() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء جدولة الدفع')),
    );
  }

  void _modifyPaymentPlan() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('فتح تعديل خطة الدفع')),
    );
  }

  void _recordPayment() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('فتح تسجيل دفعة جديدة')),
    );
  }

  void _sendReminder(String invoiceNumber) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم إرسال تذكير للفاتورة $invoiceNumber')),
    );
  }
}
