import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة مشاركة فواتير المتجر
/// تتيح مشاركة الفواتير عبر وسائل مختلفة
class ShareStoreInvoicesPage extends StatefulWidget {
  const ShareStoreInvoicesPage({super.key});

  @override
  State<ShareStoreInvoicesPage> createState() => _ShareStoreInvoicesPageState();
}

class _ShareStoreInvoicesPageState extends State<ShareStoreInvoicesPage> {
  final _searchController = TextEditingController();

  String _searchQuery = '';
  String? _selectedShareMethod;
  bool _selectAll = false;

  final List<Map<String, String>> _shareMethods = [
    {'id': 'whatsapp', 'name': 'واتساب', 'icon': 'whatsapp'},
    {'id': 'email', 'name': 'بريد إلكتروني', 'icon': 'email'},
    {'id': 'sms', 'name': 'رسالة نصية', 'icon': 'sms'},
    {'id': 'link', 'name': 'رابط مشاركة', 'icon': 'link'},
  ];

  final List<Map<String, dynamic>> _invoices = [
    {
      'id': 'inv1',
      'number': 'INV-2024-001',
      'customer': 'شركة الأمل للتجارة',
      'date': '2024/01/25',
      'amount': 15750.0,
      'status': 'مدفوعة',
      'phone': '+966501234567',
      'email': '<EMAIL>',
      'isSelected': false,
      'shared': false,
      'shareMethod': '',
      'shareDate': '',
    },
    {
      'id': 'inv2',
      'number': 'INV-2024-002',
      'customer': 'مؤسسة النور',
      'date': '2024/01/24',
      'amount': 8900.0,
      'status': 'معلقة',
      'phone': '+966502345678',
      'email': '<EMAIL>',
      'isSelected': false,
      'shared': true,
      'shareMethod': 'واتساب',
      'shareDate': '2024/01/24 15:30',
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.shareStoreInvoices),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.select_all),
            onPressed: _toggleSelectAll,
            tooltip: 'تحديد الكل',
          ),
        ],
      ),
      body: Column(
        children: [
          // بطاقة البحث والإعدادات
          Card(
            margin: const EdgeInsets.all(16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      labelText: 'البحث برقم الفاتورة أو العميل',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<String>(
                    value: _selectedShareMethod,
                    decoration: const InputDecoration(
                      labelText: 'طريقة المشاركة',
                      border: OutlineInputBorder(),
                    ),
                    items: [
                      const DropdownMenuItem(
                          value: null, child: Text('اختر طريقة المشاركة')),
                      ..._shareMethods.map((method) => DropdownMenuItem(
                            value: method['id'],
                            child: Text(method['name']!),
                          )),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedShareMethod = value;
                      });
                    },
                  ),
                ],
              ),
            ),
          ),

          // قائمة الفواتير
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _invoices.length,
              itemBuilder: (context, index) {
                final invoice = _invoices[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: CheckboxListTile(
                    value: invoice['isSelected'],
                    onChanged: (value) {
                      setState(() {
                        invoice['isSelected'] = value ?? false;
                      });
                    },
                    secondary: CircleAvatar(
                      backgroundColor:
                          invoice['shared'] ? Colors.green : Colors.blue,
                      child: Icon(
                        invoice['shared'] ? Icons.check : Icons.receipt,
                        color: Colors.white,
                      ),
                    ),
                    title: Text(invoice['number'],
                        style: const TextStyle(fontWeight: FontWeight.bold)),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                            '${invoice['customer']} | ${invoice['amount']} ر.س'),
                        Text('${invoice['date']} | ${invoice['status']}'),
                        if (invoice['shared'])
                          Text(
                              'مشارك عبر ${invoice['shareMethod']} في ${invoice['shareDate']}',
                              style: const TextStyle(
                                  color: Colors.green, fontSize: 12)),
                      ],
                    ),
                    isThreeLine: true,
                  ),
                );
              },
            ),
          ),
        ],
      ),
      bottomNavigationBar:
          _getSelectedInvoices().isNotEmpty && _selectedShareMethod != null
              ? Container(
                  padding: const EdgeInsets.all(16.0),
                  child: ElevatedButton.icon(
                    onPressed: _shareSelected,
                    icon: const Icon(Icons.share),
                    label: Text(
                        'مشاركة (${_getSelectedInvoices().length}) عبر ${_getShareMethodName()}'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                )
              : null,
    );
  }

  List<Map<String, dynamic>> _getSelectedInvoices() {
    return _invoices.where((invoice) => invoice['isSelected'] == true).toList();
  }

  String _getShareMethodName() {
    if (_selectedShareMethod == null) return '';
    return _shareMethods
        .firstWhere((method) => method['id'] == _selectedShareMethod)['name']!;
  }

  void _toggleSelectAll() {
    setState(() {
      _selectAll = !_selectAll;
      for (var invoice in _invoices) {
        invoice['isSelected'] = _selectAll;
      }
    });
  }

  void _shareSelected() {
    final selectedInvoices = _getSelectedInvoices();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد المشاركة'),
        content: Text(
            'هل أنت متأكد من مشاركة ${selectedInvoices.length} فاتورة عبر ${_getShareMethodName()}؟'),
        actions: [
          TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                      'تم مشاركة ${selectedInvoices.length} فاتورة عبر ${_getShareMethodName()}'),
                  backgroundColor: Colors.green,
                ),
              );
              setState(() {
                for (var invoice in selectedInvoices) {
                  invoice['shared'] = true;
                  invoice['shareMethod'] = _getShareMethodName();
                  invoice['shareDate'] =
                      '${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year} ${DateTime.now().hour}:${DateTime.now().minute}';
                  invoice['isSelected'] = false;
                }
              });
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
            child: const Text('مشاركة', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
