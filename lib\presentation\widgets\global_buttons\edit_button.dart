import 'package:flutter/material.dart';

class EditButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String? tooltip;
  final bool isLoading;
  final bool isDisabled;

  const EditButton({
    super.key,
    this.onPressed,
    this.tooltip = 'تعديل',
    this.isLoading = false,
    this.isDisabled = false,
  });

  @override
  Widget build(BuildContext context) {
    // تحديد حجم الشاشة للتجاوب
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;
    final isMediumScreen = screenWidth < 900;

    return Tooltip(
      message: tooltip ?? 'تعديل',
      child: isSmallScreen
          ? _buildIconOnlyButton()
          : _buildFullButton(isMediumScreen),
    );
  }

  /// زر بأيقونة فقط للشاشات الصغيرة
  Widget _buildIconOnlyButton() {
    return ElevatedButton(
      onPressed: isDisabled || isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: isDisabled ? Colors.grey[400] : Colors.orange,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.all(12),
        minimumSize: const Size(48, 48),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        elevation: isDisabled ? 0 : 2,
      ),
      child: isLoading
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: Colors.white,
              ),
            )
          : Icon(
              Icons.edit,
              size: 20,
              color: isDisabled ? Colors.grey[600] : Colors.white,
            ),
    );
  }

  /// زر كامل مع نص وأيقونة للشاشات الكبيرة والمتوسطة
  Widget _buildFullButton(bool isMediumScreen) {
    return ElevatedButton.icon(
      onPressed: isDisabled || isLoading ? null : onPressed,
      icon: isLoading
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: Colors.white,
              ),
            )
          : Icon(
              Icons.edit,
              size: 20,
              color: isDisabled ? Colors.grey[600] : Colors.white,
            ),
      label: Text(
        'تعديل',
        style: TextStyle(
          fontSize: isMediumScreen ? 14 : 16,
          fontWeight: FontWeight.w600,
          color: isDisabled ? Colors.grey[600] : Colors.white,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: isDisabled ? Colors.grey[400] : Colors.orange,
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(
          horizontal: isMediumScreen ? 12 : 16,
          vertical: isMediumScreen ? 8 : 12,
        ),
        minimumSize: Size(isMediumScreen ? 80 : 100, 40),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        elevation: isDisabled ? 0 : 2,
      ),
    );
  }
}
