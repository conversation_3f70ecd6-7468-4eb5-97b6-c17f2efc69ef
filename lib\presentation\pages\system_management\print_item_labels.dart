import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة طباعة ملصقات الأصناف
/// تتيح طباعة ملصقات شاملة للأصناف مع معلومات مفصلة
class PrintItemLabelsPage extends StatefulWidget {
  const PrintItemLabelsPage({super.key});

  @override
  State<PrintItemLabelsPage> createState() => _PrintItemLabelsPageState();
}

class _PrintItemLabelsPageState extends State<PrintItemLabelsPage> {
  final _searchController = TextEditingController();

  String _searchQuery = '';
  String? _selectedCategory;
  String? _selectedLabelTemplate;
  String? _selectedPrinter;
  bool _includeImage = true;
  bool _includeDescription = true;
  bool _includeBarcode = true;
  bool _includePrice = true;
  bool _includeStock = false;
  bool _includeSupplier = false;

  final List<String> _categories = [
    'إلكترونيات',
    'أثاث',
    'ملابس',
    'أدوات منزلية',
    'كتب ومجلات',
    'رياضة وترفيه',
  ];

  final List<Map<String, String>> _labelTemplates = [
    {'id': 'standard', 'name': 'قياسي', 'size': '5×3 سم'},
    {'id': 'detailed', 'name': 'مفصل', 'size': '7×5 سم'},
    {'id': 'compact', 'name': 'مضغوط', 'size': '4×2 سم'},
    {'id': 'premium', 'name': 'فاخر', 'size': '8×6 سم'},
  ];

  final List<Map<String, String>> _printers = [
    {'id': 'label_printer', 'name': 'طابعة الملصقات'},
    {'id': 'laser_printer', 'name': 'طابعة ليزر'},
    {'id': 'inkjet_printer', 'name': 'طابعة نافثة للحبر'},
    {'id': 'pdf_export', 'name': 'تصدير PDF'},
  ];

  final List<Map<String, dynamic>> _items = [
    {
      'id': 'item1',
      'code': 'A001',
      'name': 'لابتوب ديل XPS 13',
      'category': 'إلكترونيات',
      'price': 2500.0,
      'stock': 25,
      'supplier': 'شركة التقنية المتقدمة',
      'description': 'لابتوب عالي الأداء مع معالج Intel Core i7',
      'isSelected': false,
      'quantity': 1,
    },
    {
      'id': 'item2',
      'code': 'B002',
      'name': 'طابعة HP LaserJet Pro',
      'category': 'إلكترونيات',
      'price': 450.0,
      'stock': 12,
      'supplier': 'مؤسسة الطباعة الحديثة',
      'description': 'طابعة ليزر سريعة وموفرة للحبر',
      'isSelected': false,
      'quantity': 1,
    },
    {
      'id': 'item3',
      'code': 'C003',
      'name': 'كرسي مكتب جلد طبيعي',
      'category': 'أثاث',
      'price': 350.0,
      'stock': 8,
      'supplier': 'شركة الأثاث الفاخر',
      'description': 'كرسي مكتب مريح من الجلد الطبيعي',
      'isSelected': false,
      'quantity': 1,
    },
    {
      'id': 'item4',
      'code': 'D004',
      'name': 'شاشة سامسونج 27 بوصة',
      'category': 'إلكترونيات',
      'price': 800.0,
      'stock': 15,
      'supplier': 'وكيل سامسونج المعتمد',
      'description': 'شاشة عالية الدقة 4K مع تقنية HDR',
      'isSelected': false,
      'quantity': 1,
    },
  ];

  List<Map<String, dynamic>> get _filteredItems {
    List<Map<String, dynamic>> filtered = _items;

    if (_selectedCategory != null) {
      filtered = filtered
          .where((item) => item['category'] == _selectedCategory)
          .toList();
    }

    if (_searchQuery.isNotEmpty) {
      filtered = filtered
          .where((item) =>
              item['name']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              item['code']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()))
          .toList();
    }

    return filtered;
  }

  List<Map<String, dynamic>> get _selectedItems {
    return _items.where((item) => item['isSelected'] == true).toList();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.printItemLabels),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.select_all),
            onPressed: _selectAllItems,
            tooltip: 'تحديد الكل',
          ),
          IconButton(
            icon: const Icon(Icons.clear_all),
            onPressed: _clearAllSelections,
            tooltip: 'إلغاء التحديد',
          ),
        ],
      ),
      body: Column(
        children: [
          // بطاقة البحث والفلترة
          Card(
            margin: const EdgeInsets.all(16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      labelText: 'البحث في الأصناف',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<String>(
                    value: _selectedCategory,
                    decoration: const InputDecoration(
                      labelText: 'فلتر حسب الفئة',
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    ),
                    items: [
                      const DropdownMenuItem(
                          value: null, child: Text('جميع الفئات')),
                      ..._categories.map((category) => DropdownMenuItem(
                            value: category,
                            child: Text(category),
                          )),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedCategory = value;
                      });
                    },
                  ),
                ],
              ),
            ),
          ),

          // إحصائيات التحديد
          Card(
            margin: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatCard(
                      'المجموع', _filteredItems.length.toString(), Colors.blue),
                  _buildStatCard(
                      'المحدد', _selectedItems.length.toString(), Colors.green),
                  _buildStatCard(
                      'الملصقات', _getTotalLabels().toString(), Colors.teal),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // قائمة الأصناف
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _filteredItems.length,
              itemBuilder: (context, index) {
                final item = _filteredItems[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: CheckboxListTile(
                    value: item['isSelected'],
                    onChanged: (value) {
                      setState(() {
                        item['isSelected'] = value ?? false;
                      });
                    },
                    secondary: CircleAvatar(
                      backgroundColor: Colors.teal,
                      child: Text(
                        item['code'].substring(0, 1),
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                    title: Text(
                      '${item['code']} - ${item['name']}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                            'الفئة: ${item['category']} | السعر: ${item['price']} ر.س'),
                        Text(
                            'المخزون: ${item['stock']} | المورد: ${item['supplier']}'),
                        Text(
                          item['description'],
                          style: const TextStyle(
                              fontSize: 12, fontStyle: FontStyle.italic),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                    isThreeLine: true,
                  ),
                );
              },
            ),
          ),
        ],
      ),
      bottomNavigationBar: _selectedItems.isNotEmpty
          ? Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.3),
                    spreadRadius: 1,
                    blurRadius: 5,
                    offset: const Offset(0, -3),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // إعدادات الطباعة
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedLabelTemplate,
                          decoration: const InputDecoration(
                            labelText: 'قالب الملصق',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 8),
                          ),
                          items: _labelTemplates
                              .map<DropdownMenuItem<String>>((template) {
                            return DropdownMenuItem<String>(
                              value: template['id'],
                              child: Text(
                                  '${template['name']} (${template['size']})'),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedLabelTemplate = value;
                            });
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedPrinter,
                          decoration: const InputDecoration(
                            labelText: 'الطابعة',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 8),
                          ),
                          items: _printers
                              .map<DropdownMenuItem<String>>((printer) {
                            return DropdownMenuItem<String>(
                              value: printer['id'],
                              child: Text(printer['name']!),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedPrinter = value;
                            });
                          },
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // خيارات المحتوى
                  Wrap(
                    spacing: 8,
                    runSpacing: 4,
                    children: [
                      FilterChip(
                        label: const Text('صورة'),
                        selected: _includeImage,
                        onSelected: (value) {
                          setState(() {
                            _includeImage = value;
                          });
                        },
                      ),
                      FilterChip(
                        label: const Text('وصف'),
                        selected: _includeDescription,
                        onSelected: (value) {
                          setState(() {
                            _includeDescription = value;
                          });
                        },
                      ),
                      FilterChip(
                        label: const Text('باركود'),
                        selected: _includeBarcode,
                        onSelected: (value) {
                          setState(() {
                            _includeBarcode = value;
                          });
                        },
                      ),
                      FilterChip(
                        label: const Text('سعر'),
                        selected: _includePrice,
                        onSelected: (value) {
                          setState(() {
                            _includePrice = value;
                          });
                        },
                      ),
                      FilterChip(
                        label: const Text('مخزون'),
                        selected: _includeStock,
                        onSelected: (value) {
                          setState(() {
                            _includeStock = value;
                          });
                        },
                      ),
                      FilterChip(
                        label: const Text('مورد'),
                        selected: _includeSupplier,
                        onSelected: (value) {
                          setState(() {
                            _includeSupplier = value;
                          });
                        },
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // أزرار العمليات
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _previewLabels,
                          icon: const Icon(Icons.preview),
                          label: const Text('معاينة'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _printLabels,
                          icon: const Icon(Icons.print),
                          label: Text('طباعة (${_getTotalLabels()})'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.teal,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            )
          : null,
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Column(
      children: [
        CircleAvatar(
          backgroundColor: color,
          child: Text(
            value,
            style: const TextStyle(
                color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  int _getTotalLabels() {
    return _selectedItems.fold(
        0, (sum, item) => sum + (item['quantity'] as int));
  }

  void _selectAllItems() {
    setState(() {
      for (var item in _filteredItems) {
        item['isSelected'] = true;
      }
    });
  }

  void _clearAllSelections() {
    setState(() {
      for (var item in _items) {
        item['isSelected'] = false;
        item['quantity'] = 1;
      }
    });
  }

  void _previewLabels() {
    if (_selectedLabelTemplate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار قالب الملصق'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('معاينة ${_getTotalLabels()} ملصق')),
    );
  }

  void _printLabels() {
    if (_selectedLabelTemplate == null || _selectedPrinter == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إكمال جميع الإعدادات المطلوبة'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الطباعة'),
        content: Text('طباعة ${_getTotalLabels()} ملصق للأصناف المحددة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إرسال الطباعة بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
              _clearAllSelections();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.teal),
            child: const Text('طباعة', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
