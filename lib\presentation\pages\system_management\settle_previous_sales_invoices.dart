import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تسديد فواتير المبيعات لسنوات سابقة
/// تتيح تسديد الفواتير المعلقة من السنوات السابقة
class SettlePreviousSalesInvoicesPage extends StatefulWidget {
  const SettlePreviousSalesInvoicesPage({super.key});

  @override
  State<SettlePreviousSalesInvoicesPage> createState() =>
      _SettlePreviousSalesInvoicesPageState();
}

class _SettlePreviousSalesInvoicesPageState
    extends State<SettlePreviousSalesInvoicesPage> {
  final _searchController = TextEditingController();

  String? _selectedYear;
  String? _selectedCustomer;
  String _searchQuery = '';
  final bool _isProcessing = false;
  bool _showPaidInvoices = false;

  final List<String> _years = ['2023', '2022', '2021', '2020', '2019'];

  final List<Map<String, String>> _customers = [
    {'id': 'cust1', 'name': 'شركة الأمل للتجارة', 'code': 'C001'},
    {'id': 'cust2', 'name': 'مؤسسة النور', 'code': 'C002'},
    {'id': 'cust3', 'name': 'شركة الفجر', 'code': 'C003'},
    {'id': 'cust4', 'name': 'مجموعة الشروق', 'code': 'C004'},
    {'id': 'cust5', 'name': 'شركة البناء الحديث', 'code': 'C005'},
  ];

  final List<Map<String, dynamic>> _invoices = [
    {
      'id': 'inv1',
      'number': 'INV-2023-001',
      'date': '2023/03/15',
      'customer': 'شركة الأمل للتجارة',
      'customerId': 'cust1',
      'amount': 15750.0,
      'paid': 8000.0,
      'remaining': 7750.0,
      'status': 'جزئي',
      'dueDate': '2023/04/15',
      'overdueDays': 285,
      'year': '2023',
    },
    {
      'id': 'inv2',
      'number': 'INV-2022-156',
      'date': '2022/11/20',
      'customer': 'مؤسسة النور',
      'customerId': 'cust2',
      'amount': 22400.0,
      'paid': 0.0,
      'remaining': 22400.0,
      'status': 'معلق',
      'dueDate': '2022/12/20',
      'overdueDays': 401,
      'year': '2022',
    },
    {
      'id': 'inv3',
      'number': 'INV-2023-089',
      'date': '2023/07/10',
      'customer': 'شركة الفجر',
      'customerId': 'cust3',
      'amount': 8900.0,
      'paid': 8900.0,
      'remaining': 0.0,
      'status': 'مسدد',
      'dueDate': '2023/08/10',
      'overdueDays': 0,
      'year': '2023',
    },
    {
      'id': 'inv4',
      'number': 'INV-2021-234',
      'date': '2021/09/05',
      'customer': 'مجموعة الشروق',
      'customerId': 'cust4',
      'amount': 45600.0,
      'paid': 20000.0,
      'remaining': 25600.0,
      'status': 'جزئي',
      'dueDate': '2021/10/05',
      'overdueDays': 842,
      'year': '2021',
    },
    {
      'id': 'inv5',
      'number': 'INV-2022-078',
      'date': '2022/05/18',
      'customer': 'شركة البناء الحديث',
      'customerId': 'cust5',
      'amount': 67800.0,
      'paid': 0.0,
      'remaining': 67800.0,
      'status': 'معلق',
      'dueDate': '2022/06/18',
      'overdueDays': 586,
      'year': '2022',
    },
  ];

  List<Map<String, dynamic>> get _filteredInvoices {
    List<Map<String, dynamic>> filtered = _invoices;

    // فلتر السنة
    if (_selectedYear != null) {
      filtered = filtered
          .where((invoice) => invoice['year'] == _selectedYear)
          .toList();
    }

    // فلتر العميل
    if (_selectedCustomer != null) {
      filtered = filtered
          .where((invoice) => invoice['customerId'] == _selectedCustomer)
          .toList();
    }

    // فلتر الفواتير المسددة
    if (!_showPaidInvoices) {
      filtered = filtered.where((invoice) => invoice['remaining'] > 0).toList();
    }

    // البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered
          .where((invoice) =>
              invoice['number']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              invoice['customer']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()))
          .toList();
    }

    return filtered;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.settlePreviousSalesInvoices),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showAnalytics,
            tooltip: 'تحليلات الديون',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // بطاقة الفلاتر
          Card(
            margin: const EdgeInsets.all(16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // شريط البحث
                  TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      labelText: 'البحث برقم الفاتورة أو العميل',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),

                  const SizedBox(height: 16),

                  Row(
                    children: [
                      // فلتر السنة
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedYear,
                          decoration: const InputDecoration(
                            labelText: 'السنة',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 8),
                          ),
                          items: [
                            const DropdownMenuItem(
                                value: null, child: Text('جميع السنوات')),
                            ..._years.map((year) => DropdownMenuItem(
                                  value: year,
                                  child: Text(year),
                                )),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedYear = value;
                            });
                          },
                        ),
                      ),

                      const SizedBox(width: 16),

                      // فلتر العميل
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedCustomer,
                          decoration: const InputDecoration(
                            labelText: 'العميل',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 8),
                          ),
                          items: [
                            const DropdownMenuItem(
                                value: null, child: Text('جميع العملاء')),
                            ..._customers.map((customer) => DropdownMenuItem(
                                  value: customer['id'],
                                  child: Text(
                                    customer['name']!,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                )),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedCustomer = value;
                            });
                          },
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // خيار عرض الفواتير المسددة
                  CheckboxListTile(
                    title: const Text('عرض الفواتير المسددة'),
                    value: _showPaidInvoices,
                    onChanged: (value) {
                      setState(() {
                        _showPaidInvoices = value ?? false;
                      });
                    },
                    dense: true,
                  ),
                ],
              ),
            ),
          ),

          // إحصائيات سريعة
          Card(
            margin: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatCard('المجموع', _filteredInvoices.length.toString(),
                      Colors.blue),
                  _buildStatCard(
                      'معلق',
                      _filteredInvoices
                          .where((i) => i['status'] == 'معلق')
                          .length
                          .toString(),
                      Colors.red),
                  _buildStatCard(
                      'جزئي',
                      _filteredInvoices
                          .where((i) => i['status'] == 'جزئي')
                          .length
                          .toString(),
                      Colors.orange),
                  _buildStatCard(
                      'مسدد',
                      _filteredInvoices
                          .where((i) => i['status'] == 'مسدد')
                          .length
                          .toString(),
                      Colors.green),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // قائمة الفواتير
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _filteredInvoices.length,
              itemBuilder: (context, index) {
                final invoice = _filteredInvoices[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: _getStatusColor(invoice['status']),
                      child: Icon(
                        _getStatusIcon(invoice['status']),
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    title: Text(
                      invoice['number'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(invoice['customer']),
                        Text(
                          'المبلغ: ${invoice['amount']} ر.س | المتبقي: ${invoice['remaining']} ر.س',
                          style: const TextStyle(fontSize: 12),
                        ),
                        if (invoice['overdueDays'] > 0)
                          Text(
                            'متأخر ${invoice['overdueDays']} يوم',
                            style: const TextStyle(
                                fontSize: 12, color: Colors.red),
                          ),
                      ],
                    ),
                    trailing: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color:
                            _getStatusColor(invoice['status']).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        invoice['status'],
                        style: TextStyle(
                          color: _getStatusColor(invoice['status']),
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            _buildDetailRow('تاريخ الفاتورة:', invoice['date']),
                            _buildDetailRow(
                                'تاريخ الاستحقاق:', invoice['dueDate']),
                            _buildDetailRow(
                                'إجمالي المبلغ:', '${invoice['amount']} ر.س'),
                            _buildDetailRow(
                                'المدفوع:', '${invoice['paid']} ر.س'),
                            _buildDetailRow(
                                'المتبقي:', '${invoice['remaining']} ر.س'),

                            const SizedBox(height: 16),

                            // أزرار العمليات
                            if (invoice['remaining'] > 0) ...[
                              Row(
                                children: [
                                  Expanded(
                                    child: ElevatedButton.icon(
                                      onPressed: () => _settleInvoice(invoice),
                                      icon: const Icon(Icons.payment, size: 16),
                                      label: const Text('تسديد كامل'),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.green,
                                        foregroundColor: Colors.white,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: ElevatedButton.icon(
                                      onPressed: () =>
                                          _partialSettlement(invoice),
                                      icon:
                                          const Icon(Icons.payments, size: 16),
                                      label: const Text('تسديد جزئي'),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.orange,
                                        foregroundColor: Colors.white,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Expanded(
                                    child: OutlinedButton.icon(
                                      onPressed: () =>
                                          _viewInvoiceDetails(invoice),
                                      icon: const Icon(Icons.visibility,
                                          size: 16),
                                      label: const Text('عرض التفاصيل'),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: OutlinedButton.icon(
                                      onPressed: () => _printInvoice(invoice),
                                      icon: const Icon(Icons.print, size: 16),
                                      label: const Text('طباعة'),
                                    ),
                                  ),
                                ],
                              ),
                            ] else ...[
                              ElevatedButton.icon(
                                onPressed: () => _viewInvoiceDetails(invoice),
                                icon: const Icon(Icons.visibility, size: 16),
                                label: const Text('عرض التفاصيل'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.blue,
                                  foregroundColor: Colors.white,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _bulkSettlement,
        backgroundColor: Colors.deepPurple,
        icon: const Icon(Icons.payment),
        label: const Text('تسديد جماعي'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Column(
      children: [
        CircleAvatar(
          backgroundColor: color,
          child: Text(
            value,
            style: const TextStyle(
                color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'مسدد':
        return Colors.green;
      case 'جزئي':
        return Colors.orange;
      case 'معلق':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'مسدد':
        return Icons.check_circle;
      case 'جزئي':
        return Icons.schedule;
      case 'معلق':
        return Icons.error;
      default:
        return Icons.help;
    }
  }

  void _settleInvoice(Map<String, dynamic> invoice) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تسديد كامل للفاتورة ${invoice['number']}')),
    );
  }

  void _partialSettlement(Map<String, dynamic> invoice) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تسديد جزئي للفاتورة ${invoice['number']}')),
    );
  }

  void _viewInvoiceDetails(Map<String, dynamic> invoice) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض تفاصيل الفاتورة ${invoice['number']}')),
    );
  }

  void _printInvoice(Map<String, dynamic> invoice) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('طباعة الفاتورة ${invoice['number']}')),
    );
  }

  void _bulkSettlement() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تسديد جماعي للفواتير المحددة')),
    );
  }

  void _showAnalytics() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تحليلات الديون والمتأخرات')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير تقرير الفواتير المعلقة')),
    );
  }
}
