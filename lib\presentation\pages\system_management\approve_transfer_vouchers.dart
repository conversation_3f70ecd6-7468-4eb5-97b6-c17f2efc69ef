import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تعميد سندات التحويل الغير معمدة
/// تتيح مراجعة وتعميد سندات التحويل المعلقة
class ApproveTransferVouchersPage extends StatefulWidget {
  const ApproveTransferVouchersPage({super.key});

  @override
  State<ApproveTransferVouchersPage> createState() =>
      _ApproveTransferVouchersPageState();
}

class _ApproveTransferVouchersPageState
    extends State<ApproveTransferVouchersPage> {
  final _searchController = TextEditingController();

  String _searchQuery = '';
  String? _selectedType;
  bool _selectAll = false;

  final List<Map<String, String>> _transferTypes = [
    {'id': 'warehouse', 'name': 'تحويل مخزون'},
    {'id': 'branch', 'name': 'تحويل فرع'},
    {'id': 'account', 'name': 'تحويل حساب'},
  ];

  final List<Map<String, dynamic>> _pendingVouchers = [
    {
      'id': 'tv1',
      'number': 'TV-2024-001',
      'type': 'تحويل مخزون',
      'from': 'المستودع الرئيسي',
      'to': 'مستودع الفرع الشرقي',
      'date': '2024/01/25',
      'amount': 15000.0,
      'items': 25,
      'createdBy': 'أحمد محمد',
      'reason': 'تجديد المخزون',
      'isSelected': false,
      'priority': 'عادي',
    },
    {
      'id': 'tv2',
      'number': 'TV-2024-002',
      'type': 'تحويل فرع',
      'from': 'الفرع الرئيسي',
      'to': 'الفرع الشمالي',
      'date': '2024/01/24',
      'amount': 8500.0,
      'items': 12,
      'createdBy': 'فاطمة علي',
      'reason': 'طلب عاجل',
      'isSelected': false,
      'priority': 'عاجل',
    },
    {
      'id': 'tv3',
      'number': 'TV-2024-003',
      'type': 'تحويل حساب',
      'from': 'حساب المبيعات',
      'to': 'حساب المصروفات',
      'date': '2024/01/23',
      'amount': 3200.0,
      'items': 1,
      'createdBy': 'محمد سالم',
      'reason': 'تصحيح قيد',
      'isSelected': false,
      'priority': 'عادي',
    },
  ];

  List<Map<String, dynamic>> get _filteredVouchers {
    List<Map<String, dynamic>> filtered = _pendingVouchers;

    // تطبيق فلتر النوع
    if (_selectedType != null) {
      filtered = filtered.where((voucher) {
        switch (_selectedType) {
          case 'warehouse':
            return voucher['type'] == 'تحويل مخزون';
          case 'branch':
            return voucher['type'] == 'تحويل فرع';
          case 'account':
            return voucher['type'] == 'تحويل حساب';
          default:
            return true;
        }
      }).toList();
    }

    // تطبيق البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered
          .where((voucher) =>
              voucher['number']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              voucher['createdBy']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              voucher['from']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              voucher['to']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()))
          .toList();
    }

    return filtered;
  }

  List<Map<String, dynamic>> get _selectedVouchers {
    return _pendingVouchers
        .where((voucher) => voucher['isSelected'] == true)
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.approveTransferVouchers),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.select_all),
            onPressed: _toggleSelectAll,
            tooltip: 'تحديد الكل',
          ),
        ],
      ),
      body: Column(
        children: [
          // بطاقة البحث والفلترة
          Card(
            margin: const EdgeInsets.all(16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      labelText: 'البحث برقم السند أو المنشئ',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<String>(
                    value: _selectedType,
                    decoration: const InputDecoration(
                      labelText: 'نوع التحويل',
                      border: OutlineInputBorder(),
                    ),
                    items: [
                      const DropdownMenuItem(
                          value: null, child: Text('جميع الأنواع')),
                      ..._transferTypes.map((type) => DropdownMenuItem(
                            value: type['id'],
                            child: Text(type['name']!),
                          )),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedType = value;
                      });
                    },
                  ),
                ],
              ),
            ),
          ),

          // إحصائيات
          Card(
            margin: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatCard('المعلق', _pendingVouchers.length.toString(),
                      Colors.orange),
                  _buildStatCard('المحدد', _selectedVouchers.length.toString(),
                      Colors.blue),
                  _buildStatCard(
                      'العاجل',
                      _pendingVouchers
                          .where((v) => v['priority'] == 'عاجل')
                          .length
                          .toString(),
                      Colors.red),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // قائمة السندات
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _filteredVouchers.length,
              itemBuilder: (context, index) {
                final voucher = _filteredVouchers[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: CheckboxListTile(
                    value: voucher['isSelected'],
                    onChanged: (value) {
                      setState(() {
                        voucher['isSelected'] = value ?? false;
                      });
                    },
                    secondary: CircleAvatar(
                      backgroundColor: voucher['priority'] == 'عاجل'
                          ? Colors.red
                          : Colors.teal,
                      child: const Icon(Icons.swap_horiz, color: Colors.white),
                    ),
                    title: Text(voucher['number'],
                        style: const TextStyle(fontWeight: FontWeight.bold)),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('${voucher['type']} | ${voucher['amount']} ر.س'),
                        Text('من: ${voucher['from']} إلى: ${voucher['to']}'),
                        Text(
                            '${voucher['items']} عنصر | ${voucher['createdBy']}'),
                      ],
                    ),
                    isThreeLine: true,
                  ),
                );
              },
            ),
          ),
        ],
      ),
      bottomNavigationBar: _selectedVouchers.isNotEmpty
          ? Container(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _approveSelected,
                      icon: const Icon(Icons.check_circle),
                      label: Text('تعميد (${_selectedVouchers.length})'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _rejectSelected,
                      icon: const Icon(Icons.cancel),
                      label: Text('رفض (${_selectedVouchers.length})'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
            )
          : null,
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Column(
      children: [
        CircleAvatar(
          backgroundColor: color,
          child: Text(value,
              style: const TextStyle(
                  color: Colors.white, fontWeight: FontWeight.bold)),
        ),
        const SizedBox(height: 4),
        Text(title,
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold)),
      ],
    );
  }

  void _toggleSelectAll() {
    setState(() {
      _selectAll = !_selectAll;
      for (var voucher in _pendingVouchers) {
        voucher['isSelected'] = _selectAll;
      }
    });
  }

  void _approveSelected() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد التعميد'),
        content: Text(
            'هل أنت متأكد من تعميد ${_selectedVouchers.length} سند تحويل؟'),
        actions: [
          TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                    content:
                        Text('تم تعميد ${_selectedVouchers.length} سند تحويل'),
                    backgroundColor: Colors.green),
              );
              setState(() {
                _pendingVouchers
                    .removeWhere((voucher) => voucher['isSelected'] == true);
              });
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
            child: const Text('تعميد', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _rejectSelected() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الرفض'),
        content:
            Text('هل أنت متأكد من رفض ${_selectedVouchers.length} سند تحويل؟'),
        actions: [
          TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                    content:
                        Text('تم رفض ${_selectedVouchers.length} سند تحويل'),
                    backgroundColor: Colors.red),
              );
              setState(() {
                _pendingVouchers
                    .removeWhere((voucher) => voucher['isSelected'] == true);
              });
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('رفض', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
