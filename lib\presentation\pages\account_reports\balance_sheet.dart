import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة الميزانية العمومية
/// تعرض الأصول والخصوم وحقوق الملكية
class BalanceSheetPage extends StatefulWidget {
  const BalanceSheetPage({super.key});

  @override
  State<BalanceSheetPage> createState() => _BalanceSheetPageState();
}

class _BalanceSheetPageState extends State<BalanceSheetPage> {
  String _selectedDate = 'current_date';

  // بيانات تجريبية للميزانية العمومية
  final Map<String, dynamic> _balanceSheetData = {
    'assets': {
      'current_assets': [
        {'name': 'النقدية في الصندوق', 'amount': 15000.0},
        {'name': 'البنك', 'amount': 85000.0},
        {'name': 'العملاء', 'amount': 45000.0},
        {'name': 'المخزون', 'amount': 120000.0},
      ],
      'fixed_assets': [
        {'name': 'الأثاث والمعدات', 'amount': 50000.0},
        {'name': 'السيارات', 'amount': 80000.0},
        {'name': 'المباني', 'amount': 200000.0},
      ],
    },
    'liabilities': {
      'current_liabilities': [
        {'name': 'الموردين', 'amount': 25000.0},
        {'name': 'مصروفات مستحقة', 'amount': 8000.0},
        {'name': 'قروض قصيرة الأجل', 'amount': 15000.0},
      ],
      'long_term_liabilities': [
        {'name': 'قروض طويلة الأجل', 'amount': 100000.0},
        {'name': 'التزامات أخرى', 'amount': 12000.0},
      ],
    },
    'equity': [
      {'name': 'رأس المال', 'amount': 300000.0},
      {'name': 'الأرباح المحتجزة', 'amount': 135000.0},
    ],
  };

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.balanceSheet),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedDate,
                    decoration: const InputDecoration(
                      labelText: 'تاريخ الميزانية',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'current_date', child: Text('التاريخ الحالي')),
                      DropdownMenuItem(value: 'end_of_month', child: Text('نهاية الشهر')),
                      DropdownMenuItem(value: 'end_of_quarter', child: Text('نهاية الربع')),
                      DropdownMenuItem(value: 'end_of_year', child: Text('نهاية السنة')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedDate = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                _buildStatCard('إجمالي الأصول', '${_getTotalAssets()} ر.س', Colors.green),
                _buildStatCard('إجمالي الخصوم', '${_getTotalLiabilities()} ر.س', Colors.red),
                _buildStatCard('حقوق الملكية', '${_getTotalEquity()} ر.س', Colors.blue),
                _buildStatCard('التوازن', _isBalanced() ? 'متوازن' : 'غير متوازن', _isBalanced() ? Colors.green : Colors.red),
              ],
            ),
          ),

          // الميزانية العمومية
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              children: [
                // الأصول
                Card(
                  child: ExpansionTile(
                    leading: const CircleAvatar(
                      backgroundColor: Colors.green,
                      child: Icon(Icons.account_balance_wallet, color: Colors.white),
                    ),
                    title: const Text(
                      'الأصول',
                      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
                    ),
                    subtitle: Text('إجمالي: ${_getTotalAssets()} ر.س'),
                    children: [
                      // الأصول المتداولة
                      ExpansionTile(
                        title: const Text('الأصول المتداولة'),
                        subtitle: Text('${_getCurrentAssets()} ر.س'),
                        children: _balanceSheetData['assets']['current_assets'].map<Widget>((asset) {
                          return ListTile(
                            title: Text(asset['name']),
                            trailing: Text(
                              '${asset['amount'].toStringAsFixed(2)} ر.س',
                              style: const TextStyle(
                                color: Colors.green,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                      // الأصول الثابتة
                      ExpansionTile(
                        title: const Text('الأصول الثابتة'),
                        subtitle: Text('${_getFixedAssets()} ر.س'),
                        children: _balanceSheetData['assets']['fixed_assets'].map<Widget>((asset) {
                          return ListTile(
                            title: Text(asset['name']),
                            trailing: Text(
                              '${asset['amount'].toStringAsFixed(2)} ر.س',
                              style: const TextStyle(
                                color: Colors.green,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 8),

                // الخصوم
                Card(
                  child: ExpansionTile(
                    leading: const CircleAvatar(
                      backgroundColor: Colors.red,
                      child: Icon(Icons.credit_card, color: Colors.white),
                    ),
                    title: const Text(
                      'الخصوم',
                      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
                    ),
                    subtitle: Text('إجمالي: ${_getTotalLiabilities()} ر.س'),
                    children: [
                      // الخصوم المتداولة
                      ExpansionTile(
                        title: const Text('الخصوم المتداولة'),
                        subtitle: Text('${_getCurrentLiabilities()} ر.س'),
                        children: _balanceSheetData['liabilities']['current_liabilities'].map<Widget>((liability) {
                          return ListTile(
                            title: Text(liability['name']),
                            trailing: Text(
                              '${liability['amount'].toStringAsFixed(2)} ر.س',
                              style: const TextStyle(
                                color: Colors.red,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                      // الخصوم طويلة الأجل
                      ExpansionTile(
                        title: const Text('الخصوم طويلة الأجل'),
                        subtitle: Text('${_getLongTermLiabilities()} ر.س'),
                        children: _balanceSheetData['liabilities']['long_term_liabilities'].map<Widget>((liability) {
                          return ListTile(
                            title: Text(liability['name']),
                            trailing: Text(
                              '${liability['amount'].toStringAsFixed(2)} ر.س',
                              style: const TextStyle(
                                color: Colors.red,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 8),

                // حقوق الملكية
                Card(
                  child: ExpansionTile(
                    leading: const CircleAvatar(
                      backgroundColor: Colors.blue,
                      child: Icon(Icons.person, color: Colors.white),
                    ),
                    title: const Text(
                      'حقوق الملكية',
                      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
                    ),
                    subtitle: Text('إجمالي: ${_getTotalEquity()} ر.س'),
                    children: _balanceSheetData['equity'].map<Widget>((equity) {
                      return ListTile(
                        title: Text(equity['name']),
                        trailing: Text(
                          '${equity['amount'].toStringAsFixed(2)} ر.س',
                          style: const TextStyle(
                            color: Colors.blue,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),

                const SizedBox(height: 16),

                // معادلة الميزانية
                Card(
                  color: _isBalanced() ? Colors.green[50] : Colors.red[50],
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        Text(
                          'معادلة الميزانية',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: _isBalanced() ? Colors.green : Colors.red,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'الأصول = الخصوم + حقوق الملكية',
                          style: TextStyle(
                            fontSize: 16,
                            color: _isBalanced() ? Colors.green : Colors.red,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '${_getTotalAssets()} = ${_getTotalLiabilities()} + ${_getTotalEquity()}',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: _isBalanced() ? Colors.green : Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshData,
        backgroundColor: Colors.purple,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث البيانات'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  double _getCurrentAssets() {
    return _balanceSheetData['assets']['current_assets'].fold(0.0, (sum, asset) => sum + asset['amount']);
  }

  double _getFixedAssets() {
    return _balanceSheetData['assets']['fixed_assets'].fold(0.0, (sum, asset) => sum + asset['amount']);
  }

  double _getTotalAssets() {
    return _getCurrentAssets() + _getFixedAssets();
  }

  double _getCurrentLiabilities() {
    return _balanceSheetData['liabilities']['current_liabilities'].fold(0.0, (sum, liability) => sum + liability['amount']);
  }

  double _getLongTermLiabilities() {
    return _balanceSheetData['liabilities']['long_term_liabilities'].fold(0.0, (sum, liability) => sum + liability['amount']);
  }

  double _getTotalLiabilities() {
    return _getCurrentLiabilities() + _getLongTermLiabilities();
  }

  double _getTotalEquity() {
    return _balanceSheetData['equity'].fold(0.0, (sum, equity) => sum + equity['amount']);
  }

  bool _isBalanced() {
    return (_getTotalAssets() - (_getTotalLiabilities() + _getTotalEquity())).abs() < 0.01;
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة الميزانية العمومية')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير الميزانية العمومية')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث بيانات الميزانية العمومية'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
