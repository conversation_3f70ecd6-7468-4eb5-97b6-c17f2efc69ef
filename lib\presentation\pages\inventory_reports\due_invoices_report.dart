import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير الفواتير المستحقة
/// يعرض الفواتير المستحقة السداد مع تفاصيل العملاء والمبالغ
class DueInvoicesReportPage extends StatefulWidget {
  const DueInvoicesReportPage({super.key});

  @override
  State<DueInvoicesReportPage> createState() => _DueInvoicesReportPageState();
}

class _DueInvoicesReportPageState extends State<DueInvoicesReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedCustomer;
  String? _duePeriod = 'all';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الفواتير المستحقة'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.send),
            onPressed: _sendReminders,
            tooltip: 'إرسال تذكيرات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.red[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.fromDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, true),
                        controller: TextEditingController(
                          text: _startDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.toDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, false),
                        controller: TextEditingController(
                          text: _endDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'العميل',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedCustomer,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع العملاء')),
                          DropdownMenuItem(value: 'customer1', child: Text('شركة الأحمد للتجارة')),
                          DropdownMenuItem(value: 'customer2', child: Text('مؤسسة النور')),
                          DropdownMenuItem(value: 'customer3', child: Text('شركة الخليج')),
                        ],
                        onChanged: (value) => setState(() => _selectedCustomer = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'فترة الاستحقاق',
                          border: OutlineInputBorder(),
                        ),
                        value: _duePeriod,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الفترات')),
                          DropdownMenuItem(value: 'overdue', child: Text('متأخرة السداد')),
                          DropdownMenuItem(value: 'due_today', child: Text('مستحقة اليوم')),
                          DropdownMenuItem(value: 'due_week', child: Text('مستحقة خلال أسبوع')),
                          DropdownMenuItem(value: 'due_month', child: Text('مستحقة خلال شهر')),
                        ],
                        onChanged: (value) => setState(() => _duePeriod = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.search),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص الفواتير المستحقة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.receipt_long, color: Colors.red, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'ملخص الفواتير المستحقة',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSummaryCard('إجمالي الفواتير', '145', Colors.blue, Icons.receipt),
                              _buildSummaryCard('إجمالي المبلغ', '485,000 ر.س', Colors.red, Icons.monetization_on),
                              _buildSummaryCard('متأخرة السداد', '28', Colors.orange, Icons.warning),
                              _buildSummaryCard('مستحقة اليوم', '12', Colors.purple, Icons.today),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // تصنيف الفواتير حسب فترة الاستحقاق
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تصنيف الفواتير حسب فترة الاستحقاق',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildDuePeriodCard('متأخرة أكثر من 90 يوم', '8', '125,000 ر.س', Colors.red),
                              _buildDuePeriodCard('متأخرة 30-90 يوم', '15', '180,000 ر.س', Colors.orange),
                              _buildDuePeriodCard('متأخرة أقل من 30 يوم', '25', '95,000 ر.س', Colors.yellow),
                              _buildDuePeriodCard('مستحقة قريباً', '97', '85,000 ر.س', Colors.green),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول الفواتير المستحقة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل الفواتير المستحقة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('رقم الفاتورة')),
                                DataColumn(label: Text('العميل')),
                                DataColumn(label: Text('تاريخ الفاتورة')),
                                DataColumn(label: Text('تاريخ الاستحقاق')),
                                DataColumn(label: Text('المبلغ')),
                                DataColumn(label: Text('المبلغ المدفوع')),
                                DataColumn(label: Text('المبلغ المتبقي')),
                                DataColumn(label: Text('أيام التأخير')),
                                DataColumn(label: Text('الحالة')),
                                DataColumn(label: Text('الإجراء')),
                              ],
                              rows: _buildInvoiceRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // أكبر المدينين
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.people, color: Colors.orange, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'أكبر المدينين',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          ..._buildTopDebtorsList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _sendReminders,
                                  icon: const Icon(Icons.send),
                                  label: const Text('إرسال تذكيرات'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _generateStatements,
                                  icon: const Icon(Icons.description),
                                  label: const Text('كشف حساب'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _scheduleFollowUp,
                                  icon: const Icon(Icons.schedule),
                                  label: const Text('جدولة متابعة'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _applyLateFees,
                                  icon: const Icon(Icons.money_off),
                                  label: const Text('رسوم تأخير'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<DataRow> _buildInvoiceRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('INV-2024-001')),
        const DataCell(Text('شركة الأحمد للتجارة')),
        const DataCell(Text('2024-01-15')),
        const DataCell(Text('2024-02-14')),
        const DataCell(Text('25,000')),
        const DataCell(Text('15,000')),
        const DataCell(Text('10,000')),
        const DataCell(Text('45')),
        DataCell(_buildStatusBadge('متأخرة', Colors.red)),
        DataCell(ElevatedButton(
          onPressed: () => _followUpInvoice('INV-2024-001'),
          style: ElevatedButton.styleFrom(backgroundColor: Colors.orange, foregroundColor: Colors.white),
          child: const Text('متابعة'),
        )),
      ]),
      DataRow(cells: [
        const DataCell(Text('INV-2024-002')),
        const DataCell(Text('مؤسسة النور')),
        const DataCell(Text('2024-02-01')),
        const DataCell(Text('2024-03-03')),
        const DataCell(Text('18,500')),
        const DataCell(Text('0')),
        const DataCell(Text('18,500')),
        const DataCell(Text('0')),
        DataCell(_buildStatusBadge('مستحقة اليوم', Colors.orange)),
        DataCell(ElevatedButton(
          onPressed: () => _followUpInvoice('INV-2024-002'),
          style: ElevatedButton.styleFrom(backgroundColor: Colors.blue, foregroundColor: Colors.white),
          child: const Text('تذكير'),
        )),
      ]),
    ];
  }

  List<Widget> _buildTopDebtorsList() {
    final debtors = [
      {'name': 'شركة الأحمد للتجارة', 'amount': '125,000', 'invoices': '8'},
      {'name': 'مؤسسة النور', 'amount': '85,000', 'invoices': '5'},
      {'name': 'شركة الخليج', 'amount': '65,000', 'invoices': '3'},
      {'name': 'مؤسسة الشرق', 'amount': '45,000', 'invoices': '4'},
    ];

    return debtors.map((debtor) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.orange.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.orange.withOpacity(0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.person, color: Colors.orange, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  debtor['name']!,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${debtor['invoices']} فواتير',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Text(
            '${debtor['amount']} ر.س',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
        ],
      ),
    )).toList();
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDuePeriodCard(String title, String count, String amount, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                amount,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(status, style: const TextStyle(color: Colors.white, fontSize: 12)),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير الفواتير المستحقة بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _sendReminders() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إرسال تذكيرات للعملاء')),
    );
  }

  void _generateStatements() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء كشوف الحسابات')),
    );
  }

  void _scheduleFollowUp() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم جدولة متابعة العملاء')),
    );
  }

  void _applyLateFees() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تطبيق رسوم التأخير')),
    );
  }

  void _followUpInvoice(String invoiceNumber) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('متابعة الفاتورة $invoiceNumber')),
    );
  }
}
