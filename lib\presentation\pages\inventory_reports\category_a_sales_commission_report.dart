import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير عمولة المبيعات على التصنيف أ
/// يعرض تحليل عمولات المبيعات للتصنيف أ
class CategoryASalesCommissionReportPage extends StatefulWidget {
  const CategoryASalesCommissionReportPage({super.key});

  @override
  State<CategoryASalesCommissionReportPage> createState() => _CategoryASalesCommissionReportPageState();
}

class _CategoryASalesCommissionReportPageState extends State<CategoryASalesCommissionReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedSalesperson;
  String? _commissionType = 'all';
  String? _sortBy = 'commission_amount';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('عمولة المبيعات على التصنيف أ'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.calculate),
            onPressed: _calculateCommissions,
            tooltip: 'حساب العمولات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.green[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.fromDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, true),
                        controller: TextEditingController(
                          text: _startDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.toDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, false),
                        controller: TextEditingController(
                          text: _endDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'مندوب المبيعات',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedSalesperson,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع المندوبين')),
                          DropdownMenuItem(value: 'sales1', child: Text('أحمد محمد')),
                          DropdownMenuItem(value: 'sales2', child: Text('فاطمة أحمد')),
                          DropdownMenuItem(value: 'sales3', child: Text('محمد علي')),
                          DropdownMenuItem(value: 'sales4', child: Text('سارة خالد')),
                        ],
                        onChanged: (value) => setState(() => _selectedSalesperson = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'نوع العمولة',
                          border: OutlineInputBorder(),
                        ),
                        value: _commissionType,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الأنواع')),
                          DropdownMenuItem(value: 'percentage', child: Text('نسبة مئوية')),
                          DropdownMenuItem(value: 'fixed', child: Text('مبلغ ثابت')),
                          DropdownMenuItem(value: 'tiered', child: Text('متدرجة')),
                          DropdownMenuItem(value: 'bonus', child: Text('مكافآت')),
                        ],
                        onChanged: (value) => setState(() => _commissionType = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'ترتيب حسب',
                          border: OutlineInputBorder(),
                        ),
                        value: _sortBy,
                        items: const [
                          DropdownMenuItem(value: 'commission_amount', child: Text('مبلغ العمولة')),
                          DropdownMenuItem(value: 'sales_amount', child: Text('مبلغ المبيعات')),
                          DropdownMenuItem(value: 'commission_rate', child: Text('معدل العمولة')),
                          DropdownMenuItem(value: 'salesperson', child: Text('المندوب')),
                        ],
                        onChanged: (value) => setState(() => _sortBy = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _generateReport,
                        icon: const Icon(Icons.calculate),
                        label: Text(localizations.generateReport),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          minimumSize: const Size(double.infinity, 48),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص عمولات التصنيف أ
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.monetization_on, color: Colors.green, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'ملخص عمولات التصنيف أ',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSummaryCard('إجمالي العمولات', '285,000 ر.س', Colors.green, Icons.monetization_on),
                              _buildSummaryCard('مبيعات التصنيف أ', '2,850,000 ر.س', Colors.blue, Icons.trending_up),
                              _buildSummaryCard('متوسط العمولة', '10%', Colors.orange, Icons.percent),
                              _buildSummaryCard('عدد المندوبين', '12', Colors.purple, Icons.people),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // أفضل المندوبين
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'أفضل المندوبين (حسب العمولة)',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ..._buildTopSalespeopleList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول تفاصيل العمولات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل عمولات التصنيف أ',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('المندوب')),
                                DataColumn(label: Text('مبيعات التصنيف أ')),
                                DataColumn(label: Text('معدل العمولة')),
                                DataColumn(label: Text('مبلغ العمولة')),
                                DataColumn(label: Text('عدد الفواتير')),
                                DataColumn(label: Text('متوسط الفاتورة')),
                                DataColumn(label: Text('المكافآت')),
                                DataColumn(label: Text('إجمالي الاستحقاق')),
                                DataColumn(label: Text('الحالة')),
                              ],
                              rows: _buildCommissionRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // أفضل منتجات التصنيف أ
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.star, color: Colors.amber, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'أفضل منتجات التصنيف أ',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          ..._buildTopCategoryAProductsList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // تحليل معدلات العمولة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.analytics, color: Colors.indigo, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'تحليل معدلات العمولة',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildRateCard('5-8%', '3 مندوبين', Colors.red),
                              _buildRateCard('8-12%', '6 مندوبين', Colors.orange),
                              _buildRateCard('12-15%', '2 مندوبين', Colors.green),
                              _buildRateCard('15%+', '1 مندوب', Colors.blue),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _processPayments,
                                  icon: const Icon(Icons.payment),
                                  label: const Text('معالجة الدفعات'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _adjustRates,
                                  icon: const Icon(Icons.tune),
                                  label: const Text('تعديل المعدلات'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _sendStatements,
                                  icon: const Icon(Icons.email),
                                  label: const Text('إرسال كشوف'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _generateIncentives,
                                  icon: const Icon(Icons.card_giftcard),
                                  label: const Text('إنشاء حوافز'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildTopSalespeopleList() {
    final salespeople = [
      {'name': 'أحمد محمد', 'sales': '485,000 ر.س', 'commission': '48,500 ر.س', 'rate': '10%'},
      {'name': 'فاطمة أحمد', 'sales': '420,000 ر.س', 'commission': '50,400 ر.س', 'rate': '12%'},
      {'name': 'محمد علي', 'sales': '385,000 ر.س', 'commission': '38,500 ر.س', 'rate': '10%'},
      {'name': 'سارة خالد', 'sales': '320,000 ر.س', 'commission': '35,200 ر.س', 'rate': '11%'},
    ];

    return salespeople.map((person) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.green.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.green.withOpacity(0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.person, color: Colors.green, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  person['name']!,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'المبيعات: ${person['sales']} • المعدل: ${person['rate']}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Text(
            person['commission']!,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<Widget> _buildTopCategoryAProductsList() {
    final products = [
      {'product': 'لابتوب ديل XPS 13', 'sales': '485,000 ر.س', 'commission': '48,500 ر.س'},
      {'product': 'هاتف آيفون 15 برو', 'sales': '420,000 ر.س', 'commission': '42,000 ر.س'},
      {'product': 'طابعة HP LaserJet Pro', 'sales': '385,000 ر.س', 'commission': '38,500 ر.س'},
      {'product': 'ساعة آبل الذكية', 'sales': '320,000 ر.س', 'commission': '32,000 ر.س'},
    ];

    return products.map((product) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.amber.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.amber.withOpacity(0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.star, color: Colors.amber, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  product['product']!,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'المبيعات: ${product['sales']}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Text(
            product['commission']!,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.amber,
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildCommissionRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('أحمد محمد')),
        const DataCell(Text('485,000 ر.س')),
        const DataCell(Text('10%')),
        const DataCell(Text('48,500 ر.س')),
        const DataCell(Text('125')),
        const DataCell(Text('3,880 ر.س')),
        const DataCell(Text('5,000 ر.س')),
        const DataCell(Text('53,500 ر.س')),
        DataCell(_buildStatusBadge('مستحق', Colors.green)),
      ]),
      DataRow(cells: [
        const DataCell(Text('فاطمة أحمد')),
        const DataCell(Text('420,000 ر.س')),
        const DataCell(Text('12%')),
        const DataCell(Text('50,400 ر.س')),
        const DataCell(Text('95')),
        const DataCell(Text('4,421 ر.س')),
        const DataCell(Text('3,000 ر.س')),
        const DataCell(Text('53,400 ر.س')),
        DataCell(_buildStatusBadge('مدفوع', Colors.blue)),
      ]),
      DataRow(cells: [
        const DataCell(Text('محمد علي')),
        const DataCell(Text('385,000 ر.س')),
        const DataCell(Text('10%')),
        const DataCell(Text('38,500 ر.س')),
        const DataCell(Text('85')),
        const DataCell(Text('4,529 ر.س')),
        const DataCell(Text('2,000 ر.س')),
        const DataCell(Text('40,500 ر.س')),
        DataCell(_buildStatusBadge('قيد المعالجة', Colors.orange)),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRateCard(String range, String count, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                range,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                count,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        status,
        style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير عمولة المبيعات بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _calculateCommissions() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('حساب العمولات للفترة المحددة')),
    );
  }

  void _processPayments() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('معالجة دفعات العمولات')),
    );
  }

  void _adjustRates() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تعديل معدلات العمولة')),
    );
  }

  void _sendStatements() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إرسال كشوف العمولات للمندوبين')),
    );
  }

  void _generateIncentives() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء برنامج حوافز جديد')),
    );
  }
}
