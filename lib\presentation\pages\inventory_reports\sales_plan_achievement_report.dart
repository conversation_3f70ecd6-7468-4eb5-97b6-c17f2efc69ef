import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير نسبة المبيعات المحققة من الخطة
/// يعرض مقارنة المبيعات الفعلية مع الخطة المستهدفة
class SalesPlanAchievementReportPage extends StatefulWidget {
  const SalesPlanAchievementReportPage({super.key});

  @override
  State<SalesPlanAchievementReportPage> createState() =>
      _SalesPlanAchievementReportPageState();
}

class _SalesPlanAchievementReportPageState
    extends State<SalesPlanAchievementReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedPeriod = 'monthly';
  String? _selectedBranch;
  String? _selectedCategory;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('نسبة المبيعات المحققة من الخطة'),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.timeline),
            onPressed: _showTrends,
            tooltip: 'عرض الاتجاهات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.indigo[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.fromDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, true),
                        controller: TextEditingController(
                          text: _startDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.toDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, false),
                        controller: TextEditingController(
                          text: _endDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'فترة التقرير',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedPeriod,
                        items: const [
                          DropdownMenuItem(value: 'daily', child: Text('يومي')),
                          DropdownMenuItem(
                              value: 'weekly', child: Text('أسبوعي')),
                          DropdownMenuItem(
                              value: 'monthly', child: Text('شهري')),
                          DropdownMenuItem(
                              value: 'quarterly', child: Text('ربع سنوي')),
                          DropdownMenuItem(
                              value: 'yearly', child: Text('سنوي')),
                        ],
                        onChanged: (value) =>
                            setState(() => _selectedPeriod = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'الفرع',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedBranch,
                        items: const [
                          DropdownMenuItem(
                              value: 'all', child: Text('جميع الفروع')),
                          DropdownMenuItem(
                              value: 'main', child: Text('الفرع الرئيسي')),
                          DropdownMenuItem(
                              value: 'riyadh', child: Text('فرع الرياض')),
                          DropdownMenuItem(
                              value: 'jeddah', child: Text('فرع جدة')),
                          DropdownMenuItem(
                              value: 'dammam', child: Text('فرع الدمام')),
                        ],
                        onChanged: (value) =>
                            setState(() => _selectedBranch = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.category,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedCategory,
                        items: [
                          DropdownMenuItem(
                              value: 'all',
                              child: Text(localizations.allCategories)),
                          DropdownMenuItem(
                              value: 'electronics',
                              child: Text(localizations.electronics)),
                          DropdownMenuItem(
                              value: 'clothing',
                              child: Text(localizations.clothing)),
                          DropdownMenuItem(
                              value: 'food', child: Text(localizations.food)),
                        ],
                        onChanged: (value) =>
                            setState(() => _selectedCategory = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _generateReport,
                        icon: const Icon(Icons.analytics),
                        label: Text(localizations.generateReport),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.indigo,
                          foregroundColor: Colors.white,
                          minimumSize: const Size(double.infinity, 48),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص الإنجاز العام
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.track_changes,
                                  color: Colors.indigo, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'ملخص الإنجاز العام',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildAchievementCard('الخطة المستهدفة',
                                  '5,000,000 ر.س', Colors.blue, Icons.flag),
                              _buildAchievementCard(
                                  'المبيعات الفعلية',
                                  '4,250,000 ر.س',
                                  Colors.green,
                                  Icons.monetization_on),
                              _buildAchievementCard('نسبة الإنجاز', '85%',
                                  Colors.orange, Icons.percent),
                              _buildAchievementCard('المتبقي', '750,000 ر.س',
                                  Colors.red, Icons.trending_down),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // مؤشر الإنجاز البصري
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'مؤشر الإنجاز',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          _buildProgressIndicator(
                              'إجمالي الإنجاز', 85, Colors.indigo),
                          const SizedBox(height: 12),
                          _buildProgressIndicator(
                              'الإلكترونيات', 92, Colors.blue),
                          const SizedBox(height: 12),
                          _buildProgressIndicator('الملابس', 78, Colors.green),
                          const SizedBox(height: 12),
                          _buildProgressIndicator('الأغذية', 65, Colors.orange),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إنجاز الفروع
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إنجاز الفروع',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ..._buildBranchAchievementList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول تفاصيل الإنجاز
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل إنجاز الخطة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('الفترة')),
                                DataColumn(label: Text('الخطة المستهدفة')),
                                DataColumn(label: Text('المبيعات الفعلية')),
                                DataColumn(label: Text('نسبة الإنجاز')),
                                DataColumn(label: Text('الانحراف')),
                                DataColumn(label: Text('التقييم')),
                                DataColumn(label: Text('الاتجاه')),
                              ],
                              rows: _buildAchievementRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // تحليل الأداء
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.analytics,
                                  color: Colors.purple, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'تحليل الأداء',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildPerformanceCard('أفضل أداء', 'فرع الرياض',
                                  '95%', Colors.green, Icons.emoji_events),
                              _buildPerformanceCard('يحتاج تحسين', 'فرع الدمام',
                                  '65%', Colors.red, Icons.warning),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              _buildPerformanceCard('أسرع نمو', 'الإلكترونيات',
                                  '+15%', Colors.blue, Icons.trending_up),
                              _buildPerformanceCard('أبطأ نمو', 'الأغذية',
                                  '-5%', Colors.orange, Icons.trending_down),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _adjustTargets,
                                  icon: const Icon(Icons.tune),
                                  label: const Text('تعديل الأهداف'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _createActionPlan,
                                  icon: const Icon(Icons.assignment),
                                  label: const Text('خطة عمل'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _sendAlerts,
                                  icon: const Icon(Icons.notifications),
                                  label: const Text('إرسال تنبيهات'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _generateForecast,
                                  icon: const Icon(Icons.timeline),
                                  label: const Text('توقعات المستقبل'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildBranchAchievementList() {
    final branches = [
      {
        'name': 'فرع الرياض',
        'target': '1,500,000 ر.س',
        'actual': '1,425,000 ر.س',
        'percentage': '95%',
        'status': 'ممتاز'
      },
      {
        'name': 'فرع جدة',
        'target': '1,200,000 ر.س',
        'actual': '1,020,000 ر.س',
        'percentage': '85%',
        'status': 'جيد'
      },
      {
        'name': 'الفرع الرئيسي',
        'target': '1,800,000 ر.س',
        'actual': '1,530,000 ر.س',
        'percentage': '85%',
        'status': 'جيد'
      },
      {
        'name': 'فرع الدمام',
        'target': '500,000 ر.س',
        'actual': '325,000 ر.س',
        'percentage': '65%',
        'status': 'يحتاج تحسين'
      },
    ];

    return branches
        .map((branch) => Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(
                    color: _getStatusColor(branch['status']!).withOpacity(0.3)),
                borderRadius: BorderRadius.circular(8),
                color: _getStatusColor(branch['status']!).withOpacity(0.05),
              ),
              child: Row(
                children: [
                  Icon(_getStatusIcon(branch['status']!),
                      color: _getStatusColor(branch['status']!), size: 24),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          branch['name']!,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'الهدف: ${branch['target']} • الفعلي: ${branch['actual']}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color:
                          _getStatusColor(branch['status']!).withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      branch['percentage']!,
                      style: TextStyle(
                        color: _getStatusColor(branch['status']!),
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ))
        .toList();
  }

  List<DataRow> _buildAchievementRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('يناير 2024')),
        const DataCell(Text('1,200,000 ر.س')),
        const DataCell(Text('1,140,000 ر.س')),
        const DataCell(Text('95%')),
        const DataCell(Text('-60,000 ر.س')),
        DataCell(_buildStatusBadge('ممتاز', Colors.green)),
        DataCell(_buildTrendIcon(Colors.green, Icons.trending_up)),
      ]),
      DataRow(cells: [
        const DataCell(Text('فبراير 2024')),
        const DataCell(Text('1,300,000 ر.س')),
        const DataCell(Text('1,105,000 ر.س')),
        const DataCell(Text('85%')),
        const DataCell(Text('-195,000 ر.س')),
        DataCell(_buildStatusBadge('جيد', Colors.blue)),
        DataCell(_buildTrendIcon(Colors.orange, Icons.trending_down)),
      ]),
      DataRow(cells: [
        const DataCell(Text('مارس 2024')),
        const DataCell(Text('1,400,000 ر.س')),
        const DataCell(Text('1,190,000 ر.س')),
        const DataCell(Text('85%')),
        const DataCell(Text('-210,000 ر.س')),
        DataCell(_buildStatusBadge('جيد', Colors.blue)),
        DataCell(_buildTrendIcon(Colors.blue, Icons.trending_flat)),
      ]),
    ];
  }

  Widget _buildAchievementCard(
      String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProgressIndicator(String title, int percentage, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(title,
                style:
                    const TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
            Text('$percentage%',
                style: TextStyle(
                    fontSize: 14, color: color, fontWeight: FontWeight.bold)),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: percentage / 100,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(color),
          minHeight: 8,
        ),
      ],
    );
  }

  Widget _buildPerformanceCard(
      String title, String subtitle, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                title,
                style:
                    const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
                textAlign: TextAlign.center,
              ),
              Text(
                value,
                style: TextStyle(fontSize: 10, color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        status,
        style:
            TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildTrendIcon(Color color, IconData icon) {
    return Icon(icon, color: color, size: 20);
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'ممتاز':
        return Colors.green;
      case 'جيد':
        return Colors.blue;
      case 'يحتاج تحسين':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'ممتاز':
        return Icons.emoji_events;
      case 'جيد':
        return Icons.thumb_up;
      case 'يحتاج تحسين':
        return Icons.warning;
      default:
        return Icons.help;
    }
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير إنجاز الخطة بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showTrends() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض اتجاهات الأداء')),
    );
  }

  void _adjustTargets() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تعديل الأهداف والخطط')),
    );
  }

  void _createActionPlan() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء خطة عمل تحسينية')),
    );
  }

  void _sendAlerts() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إرسال تنبيهات للفرق')),
    );
  }

  void _generateForecast() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء توقعات الأداء المستقبلي')),
    );
  }
}
