import 'package:flutter/material.dart';

class EditPrintedReturnsButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String? tooltip;
  final bool isLoading;
  final bool isDisabled;

  const EditPrintedReturnsButton({
    super.key,
    this.onPressed,
    this.tooltip = 'Edit Printed Returns',
    this.isLoading = false,
    this.isDisabled = false,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip ?? 'Edit Printed Returns',
      child: ElevatedButton.icon(
        onPressed: isDisabled || isLoading ? null : onPressed,
        icon: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              )
            : const Icon(Icons.assignment_return),
        label: const Text('Edit Printed Returns'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.deepOrange.shade800,
          foregroundColor: Colors.white,
        ),
      ),
    );
  }
}
