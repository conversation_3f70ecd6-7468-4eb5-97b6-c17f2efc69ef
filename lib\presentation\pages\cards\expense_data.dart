import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة بيانات المصروفات
/// تتيح إدارة وعرض بيانات المصروفات المسجلة
class ExpenseDataPage extends StatefulWidget {
  const ExpenseDataPage({super.key});

  @override
  State<ExpenseDataPage> createState() => _ExpenseDataPageState();
}

class _ExpenseDataPageState extends State<ExpenseDataPage> {
  String _searchQuery = '';
  String _selectedStatus = 'all';

  // بيانات تجريبية للمصروفات
  final List<Map<String, dynamic>> _expenseData = [
    {
      'id': 'EXP-001',
      'description': 'راتب شهر يناير - أحمد محمد',
      'type': 'رواتب الموظفين',
      'amount': 8500.0,
      'date': '2024-01-31',
      'status': 'مدفوع',
      'approvedBy': 'مدير الموارد البشرية',
      'paymentMethod': 'تحويل بنكي',
      'reference': 'PAY-2024-001',
      'department': 'تقنية المعلومات',
      'employee': 'أحمد محمد',
      'category': 'مصروفات إدارية',
    },
    {
      'id': 'EXP-002',
      'description': 'إيجار مكتب الرياض - فبراير',
      'type': 'إيجار المكاتب',
      'amount': 15000.0,
      'date': '2024-02-01',
      'status': 'معلق',
      'approvedBy': null,
      'paymentMethod': null,
      'reference': 'RENT-2024-002',
      'department': 'الإدارة العامة',
      'employee': null,
      'category': 'مصروفات تشغيلية',
    },
    {
      'id': 'EXP-003',
      'description': 'تذكرة طيران - رحلة عمل دبي',
      'type': 'مصروفات السفر',
      'amount': 2800.0,
      'date': '2024-01-20',
      'status': 'مدفوع',
      'approvedBy': 'مدير المبيعات',
      'paymentMethod': 'بطاقة ائتمان',
      'reference': 'TRV-2024-003',
      'department': 'المبيعات',
      'employee': 'فاطمة أحمد',
      'category': 'مصروفات إدارية',
    },
    {
      'id': 'EXP-004',
      'description': 'صيانة أجهزة الكمبيوتر',
      'type': 'صيانة المعدات',
      'amount': 1200.0,
      'date': '2024-01-25',
      'status': 'مرفوض',
      'approvedBy': 'مدير تقنية المعلومات',
      'paymentMethod': null,
      'reference': 'MAINT-2024-004',
      'department': 'تقنية المعلومات',
      'employee': 'محمد علي',
      'category': 'مصروفات تشغيلية',
    },
    {
      'id': 'EXP-005',
      'description': 'حملة إعلانية - وسائل التواصل',
      'type': 'مصروفات التسويق',
      'amount': 5500.0,
      'date': '2024-02-05',
      'status': 'قيد المراجعة',
      'approvedBy': null,
      'paymentMethod': null,
      'reference': 'MKT-2024-005',
      'department': 'التسويق',
      'employee': 'سارة خالد',
      'category': 'مصروفات تسويقية',
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('بيانات المصروفات'),
        backgroundColor: Colors.blueGrey,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addExpense,
            tooltip: 'إضافة مصروف جديد',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportData,
            tooltip: 'تصدير البيانات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                TextField(
                  decoration: const InputDecoration(
                    hintText: 'البحث في المصروفات...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedStatus,
                        decoration: const InputDecoration(
                          labelText: 'حالة المصروف',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الحالات')),
                          DropdownMenuItem(value: 'مدفوع', child: Text('مدفوع')),
                          DropdownMenuItem(value: 'معلق', child: Text('معلق')),
                          DropdownMenuItem(value: 'قيد المراجعة', child: Text('قيد المراجعة')),
                          DropdownMenuItem(value: 'مرفوض', child: Text('مرفوض')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedStatus = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                _buildStatCard('المجموع', _expenseData.length.toString(), Colors.blue),
                _buildStatCard('مدفوع', _expenseData.where((e) => e['status'] == 'مدفوع').length.toString(), Colors.green),
                _buildStatCard('معلق', _expenseData.where((e) => e['status'] == 'معلق').length.toString(), Colors.orange),
                _buildStatCard('الإجمالي', '${_getTotalAmount()} ر.س', Colors.purple),
              ],
            ),
          ),

          // قائمة المصروفات
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _expenseData.length,
              itemBuilder: (context, index) {
                final expense = _expenseData[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: _getStatusColor(expense['status']),
                      child: Icon(
                        _getStatusIcon(expense['status']),
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    title: Text(
                      expense['description'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('النوع: ${expense['type']} | القسم: ${expense['department']}'),
                        Text('المبلغ: ${expense['amount']} ر.س | التاريخ: ${expense['date']}'),
                        Text('المرجع: ${expense['reference']}'),
                        if (expense['employee'] != null)
                          Text('الموظف: ${expense['employee']}'),
                        if (expense['approvedBy'] != null)
                          Text('معتمد من: ${expense['approvedBy']}'),
                      ],
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: _getStatusColor(expense['status']),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            expense['status'],
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        PopupMenuButton<String>(
                          onSelected: (value) => _handleAction(value, expense),
                          itemBuilder: (context) => [
                            const PopupMenuItem(
                              value: 'view',
                              child: ListTile(
                                leading: Icon(Icons.visibility),
                                title: Text('عرض التفاصيل'),
                              ),
                            ),
                            if (expense['status'] == 'معلق' || expense['status'] == 'قيد المراجعة')
                              const PopupMenuItem(
                                value: 'approve',
                                child: ListTile(
                                  leading: Icon(Icons.check, color: Colors.green),
                                  title: Text('اعتماد'),
                                ),
                              ),
                            if (expense['status'] == 'معلق' || expense['status'] == 'قيد المراجعة')
                              const PopupMenuItem(
                                value: 'reject',
                                child: ListTile(
                                  leading: Icon(Icons.close, color: Colors.red),
                                  title: Text('رفض'),
                                ),
                              ),
                            const PopupMenuItem(
                              value: 'edit',
                              child: ListTile(
                                leading: Icon(Icons.edit),
                                title: Text('تعديل'),
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'receipt',
                              child: ListTile(
                                leading: Icon(Icons.receipt),
                                title: Text('عرض الإيصال'),
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'delete',
                              child: ListTile(
                                leading: Icon(Icons.delete, color: Colors.red),
                                title: Text('حذف', style: TextStyle(color: Colors.red)),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    isThreeLine: true,
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addExpense,
        backgroundColor: Colors.blueGrey,
        icon: const Icon(Icons.add),
        label: const Text('إضافة مصروف'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'مدفوع':
        return Colors.green;
      case 'معلق':
        return Colors.orange;
      case 'قيد المراجعة':
        return Colors.blue;
      case 'مرفوض':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'مدفوع':
        return Icons.check_circle;
      case 'معلق':
        return Icons.hourglass_empty;
      case 'قيد المراجعة':
        return Icons.rate_review;
      case 'مرفوض':
        return Icons.cancel;
      default:
        return Icons.receipt;
    }
  }

  String _getTotalAmount() {
    double total = _expenseData.fold(0.0, (sum, expense) => sum + expense['amount']);
    return total.toStringAsFixed(0);
  }

  void _addExpense() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إضافة مصروف جديد')),
    );
  }

  void _exportData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير بيانات المصروفات')),
    );
  }

  void _handleAction(String action, Map<String, dynamic> expense) {
    switch (action) {
      case 'view':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('عرض تفاصيل ${expense['reference']}')),
        );
        break;
      case 'approve':
        setState(() {
          expense['status'] = 'مدفوع';
          expense['approvedBy'] = 'المدير المالي';
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم اعتماد المصروف ${expense['reference']}'),
            backgroundColor: Colors.green,
          ),
        );
        break;
      case 'reject':
        setState(() {
          expense['status'] = 'مرفوض';
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم رفض المصروف ${expense['reference']}'),
            backgroundColor: Colors.red,
          ),
        );
        break;
      case 'edit':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تعديل المصروف ${expense['reference']}')),
        );
        break;
      case 'receipt':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('عرض إيصال ${expense['reference']}')),
        );
        break;
      case 'delete':
        _showDeleteConfirmation(expense);
        break;
    }
  }

  void _showDeleteConfirmation(Map<String, dynamic> expense) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المصروف ${expense['reference']}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _expenseData.removeWhere((e) => e['id'] == expense['id']);
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف المصروف ${expense['reference']}'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
