# توثيق صفحة إدارة السنوات المالية

## نظرة عامة
تم تطوير صفحة إدارة السنوات المالية باستخدام نفس التقنيات المستخدمة في صفحة إدارة التصنيفات، مع استخدام اللون الأزرق كنظام ألوان جديد.

## هيكل الصفحة

### الملفات المنشأة:
- `lib/presentation/pages/general/fiscal_year/fiscal_year_page.dart` - الصفحة الرئيسية (تم نقلها من مجلد cards)
- `lib/presentation/pages/general/general_page.dart` - صفحة الإعدادات العامة مع زر السنة المالية

## المكونات الرئيسية

### 1. صفحة السنة المالية (FiscalYearPage)
- **التخطيط**: كرت واحد لعرض الجدول
- **اللون**: الأزرق (Colors.blue[700])
- **الوظائف**:
  - عرض قائمة السنوات المالية في جدول
  - إضافة سنة مالية جديدة عبر نافذة حوارية
  - البحث في السنوات المالية
  - تحديد الصفوف بالنقر

### 2. النافذة الحوارية للإضافة
- **الحقول المطلوبة**:
  - ID (تلقائي)
  - رقم الشركة (رقمي)
  - الاسم العربي (نصي)
  - الاسم الإنجليزي (نصي)
  - بداية السنة (تاريخ YYYY-MM-DD)
  - نهاية السنة (تاريخ YYYY-MM-DD)
  - استمرار الرقم التسلسلي (اختيار: نعم/لا)
  - تخطي آخر تسلسل (اختيار: نعم/لا)
  - رقم التخطي (رقمي، يظهر عند اختيار "نعم" للتخطي)

### 3. الأزرار المستخدمة
- **زر إضافة**: `GlobalButtons.newButton()` - يفتح النافذة الحوارية
- **زر إلغاء**: `GlobalButtons.cancelButton()` - في النافذة الحوارية
- **زر إضافة**: `GlobalButtons.newButton()` - في النافذة الحوارية لحفظ البيانات

## المميزات المطبقة

### 1. التصميم المتجاوب
- يتكيف مع أحجام الشاشات المختلفة
- أزرار متجاوبة حسب حجم الشاشة

### 2. التفاعل مع المستخدم
- تحديد الصفوف بالنقر
- تغيير لون الصف المحدد
- رسائل تأكيد عند الإضافة
- رسائل خطأ عند عدم ملء الحقول المطلوبة

### 3. حقول التاريخ
- منتقي التاريخ المدمج
- تنسيق التاريخ YYYY-MM-DD
- أيقونة التقويم للوصول السريع

### 4. الحقول الشرطية
- حقل "رقم التخطي" يظهر/يختفي حسب اختيار "تخطي آخر تسلسل"
- تفعيل/تعطيل الحقول حسب الحاجة

## التكامل مع النظام

### 1. صفحة الإعدادات
- تم ربط صفحة السنة المالية بصفحة الإعدادات الموجودة (settings_page.dart)
- زر "إدارة السنوات المالية" في قسم "إعدادات الشركة"
- تصميم متناسق مع باقي النظام

### 2. التنقل
- التنقل من الصفحة الرئيسية → القائمة الجانبية → الإعدادات → إعدادات الشركة → إدارة السنوات المالية
- إمكانية العودة عبر زر الرجوع في شريط التطبيق

## البيانات التجريبية
تم إضافة بيانات تجريبية لسنتين ماليتين:
1. السنة المالية 2024 (مع استمرار التسلسل)
2. السنة المالية 2023 (مع تخطي التسلسل برقم 1000)

## التحسينات المستقبلية المقترحة
1. إضافة وظائف التعديل والحذف
2. إضافة التصدير والطباعة
3. إضافة التحقق من صحة التواريخ
4. إضافة إعدادات متقدمة للسنة المالية
5. ربط البيانات بقاعدة البيانات

## ملاحظات التطوير
- تم استخدام نفس النمط المعماري للصفحات الأخرى
- الكود منظم ومعلق باللغة العربية
- تم اتباع معايير Flutter للتطوير
- استخدام GlobalButtons للحفاظ على التناسق
- تم نقل الملفات من مجلد cards إلى مجلد general حسب الطلب

## آخر تحديث
تاريخ الإنشاء: اليوم
المطور: Augment Agent
الحالة: مكتمل ويعمل بنجاح - تم نقل الملفات للمكان الصحيح
