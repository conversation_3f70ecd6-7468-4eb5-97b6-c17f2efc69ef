import 'package:flutter/material.dart';

/// صفحة صافي الربح خلال فترة
/// تعرض صافي الربح والخسارة خلال فترة محددة
class NetProfitPeriodPage extends StatefulWidget {
  const NetProfitPeriodPage({super.key});

  @override
  State<NetProfitPeriodPage> createState() => _NetProfitPeriodPageState();
}

class _NetProfitPeriodPageState extends State<NetProfitPeriodPage> {
  String _selectedPeriod = 'current_month';

  // بيانات تجريبية لصافي الربح حسب الفترات
  final Map<String, Map<String, dynamic>> _profitData = {
    'current_month': {
      'period': 'الشهر الحالي',
      'revenues': 250000.0,
      'expenses': 180000.0,
      'netProfit': 70000.0,
      'profitMargin': 28.0,
      'details': [
        {'category': 'إيرادات المبيعات', 'amount': 200000.0, 'type': 'revenue'},
        {'category': 'إيرادات الخدمات', 'amount': 35000.0, 'type': 'revenue'},
        {'category': 'إيرادات أخرى', 'amount': 15000.0, 'type': 'revenue'},
        {'category': 'تكلفة البضاعة المباعة', 'amount': 120000.0, 'type': 'expense'},
        {'category': 'مصروفات الرواتب', 'amount': 35000.0, 'type': 'expense'},
        {'category': 'مصروفات التشغيل', 'amount': 25000.0, 'type': 'expense'},
      ],
    },
    'current_quarter': {
      'period': 'الربع الحالي',
      'revenues': 720000.0,
      'expenses': 520000.0,
      'netProfit': 200000.0,
      'profitMargin': 27.8,
      'details': [
        {'category': 'إيرادات المبيعات', 'amount': 580000.0, 'type': 'revenue'},
        {'category': 'إيرادات الخدمات', 'amount': 95000.0, 'type': 'revenue'},
        {'category': 'إيرادات أخرى', 'amount': 45000.0, 'type': 'revenue'},
        {'category': 'تكلفة البضاعة المباعة', 'amount': 350000.0, 'type': 'expense'},
        {'category': 'مصروفات الرواتب', 'amount': 105000.0, 'type': 'expense'},
        {'category': 'مصروفات التشغيل', 'amount': 65000.0, 'type': 'expense'},
      ],
    },
    'current_year': {
      'period': 'السنة الحالية',
      'revenues': 2800000.0,
      'expenses': 2100000.0,
      'netProfit': 700000.0,
      'profitMargin': 25.0,
      'details': [
        {'category': 'إيرادات المبيعات', 'amount': 2300000.0, 'type': 'revenue'},
        {'category': 'إيرادات الخدمات', 'amount': 350000.0, 'type': 'revenue'},
        {'category': 'إيرادات أخرى', 'amount': 150000.0, 'type': 'revenue'},
        {'category': 'تكلفة البضاعة المباعة', 'amount': 1400000.0, 'type': 'expense'},
        {'category': 'مصروفات الرواتب', 'amount': 420000.0, 'type': 'expense'},
        {'category': 'مصروفات التشغيل', 'amount': 280000.0, 'type': 'expense'},
      ],
    },
  };

  @override
  Widget build(BuildContext context) {
    final currentData = _profitData[_selectedPeriod]!;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('صافي الربح خلال فترة'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedPeriod,
                    decoration: const InputDecoration(
                      labelText: 'الفترة',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'current_month', child: Text('الشهر الحالي')),
                      DropdownMenuItem(value: 'current_quarter', child: Text('الربع الحالي')),
                      DropdownMenuItem(value: 'current_year', child: Text('السنة الحالية')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedPeriod = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
          ),

          // ملخص صافي الربح
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              color: currentData['netProfit'] >= 0 ? Colors.green[50] : Colors.red[50],
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  children: [
                    Text(
                      currentData['period'],
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      currentData['netProfit'] >= 0 ? 'صافي الربح' : 'صافي الخسارة',
                      style: TextStyle(
                        fontSize: 16,
                        color: currentData['netProfit'] >= 0 ? Colors.green : Colors.red,
                      ),
                    ),
                    Text(
                      '${currentData['netProfit'].abs().toStringAsFixed(2)} ر.س',
                      style: TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: currentData['netProfit'] >= 0 ? Colors.green : Colors.red,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'هامش الربح: ${currentData['profitMargin']}%',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                _buildStatCard('إجمالي الإيرادات', '${currentData['revenues']} ر.س', Colors.green),
                _buildStatCard('إجمالي المصروفات', '${currentData['expenses']} ر.س', Colors.red),
                _buildStatCard('هامش الربح', '${currentData['profitMargin']}%', Colors.blue),
                _buildStatCard('نسبة المصروفات', '${((currentData['expenses'] / currentData['revenues']) * 100).toStringAsFixed(1)}%', Colors.orange),
              ],
            ),
          ),

          // تفاصيل الإيرادات والمصروفات
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(16.0),
              children: [
                // الإيرادات
                Card(
                  child: ExpansionTile(
                    leading: const CircleAvatar(
                      backgroundColor: Colors.green,
                      child: Icon(Icons.trending_up, color: Colors.white),
                    ),
                    title: const Text(
                      'الإيرادات',
                      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
                    ),
                    subtitle: Text('إجمالي: ${currentData['revenues']} ر.س'),
                    children: currentData['details']
                        .where((item) => item['type'] == 'revenue')
                        .map<Widget>((item) {
                      double percentage = (item['amount'] / currentData['revenues']) * 100;
                      return ListTile(
                        title: Text(item['category']),
                        subtitle: LinearProgressIndicator(
                          value: percentage / 100,
                          backgroundColor: Colors.grey[300],
                          valueColor: const AlwaysStoppedAnimation<Color>(Colors.green),
                        ),
                        trailing: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              '${item['amount'].toStringAsFixed(2)} ر.س',
                              style: const TextStyle(
                                color: Colors.green,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              '${percentage.toStringAsFixed(1)}%',
                              style: const TextStyle(fontSize: 12),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
                ),

                const SizedBox(height: 8),

                // المصروفات
                Card(
                  child: ExpansionTile(
                    leading: const CircleAvatar(
                      backgroundColor: Colors.red,
                      child: Icon(Icons.trending_down, color: Colors.white),
                    ),
                    title: const Text(
                      'المصروفات',
                      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
                    ),
                    subtitle: Text('إجمالي: ${currentData['expenses']} ر.س'),
                    children: currentData['details']
                        .where((item) => item['type'] == 'expense')
                        .map<Widget>((item) {
                      double percentage = (item['amount'] / currentData['expenses']) * 100;
                      return ListTile(
                        title: Text(item['category']),
                        subtitle: LinearProgressIndicator(
                          value: percentage / 100,
                          backgroundColor: Colors.grey[300],
                          valueColor: const AlwaysStoppedAnimation<Color>(Colors.red),
                        ),
                        trailing: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              '${item['amount'].toStringAsFixed(2)} ر.س',
                              style: const TextStyle(
                                color: Colors.red,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              '${percentage.toStringAsFixed(1)}%',
                              style: const TextStyle(fontSize: 12),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
                ),

                const SizedBox(height: 16),

                // مقارنة سريعة
                Card(
                  color: Colors.blue[50],
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'تحليل الأداء',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        _buildAnalysisRow('نسبة الربحية', '${currentData['profitMargin']}%', _getProfitabilityColor(currentData['profitMargin'])),
                        _buildAnalysisRow('كفاءة التكلفة', '${(100 - (currentData['expenses'] / currentData['revenues']) * 100).toStringAsFixed(1)}%', Colors.blue),
                        _buildAnalysisRow('العائد على الإيرادات', '${((currentData['netProfit'] / currentData['revenues']) * 100).toStringAsFixed(1)}%', Colors.green),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshData,
        backgroundColor: Colors.teal,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث البيانات'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAnalysisRow(String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Color _getProfitabilityColor(double margin) {
    if (margin >= 25) return Colors.green;
    if (margin >= 15) return Colors.orange;
    return Colors.red;
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة تقرير صافي الربح')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير تقرير صافي الربح')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث بيانات صافي الربح'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
