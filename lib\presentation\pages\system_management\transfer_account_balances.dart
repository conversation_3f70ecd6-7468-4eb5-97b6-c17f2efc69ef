import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة نقل أرصدة الحسابات
/// تتيح نقل الأرصدة بين الحسابات المختلفة
class TransferAccountBalancesPage extends StatefulWidget {
  const TransferAccountBalancesPage({super.key});

  @override
  State<TransferAccountBalancesPage> createState() =>
      _TransferAccountBalancesPageState();
}

class _TransferAccountBalancesPageState
    extends State<TransferAccountBalancesPage> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();

  String? _fromAccount;
  String? _toAccount;
  DateTime _transferDate = DateTime.now();
  bool _isProcessing = false;

  final List<Map<String, String>> _accounts = [
    {'id': '1', 'name': 'الصندوق الرئيسي', 'balance': '50,000.00'},
    {'id': '2', 'name': 'البنك الأهلي', 'balance': '125,000.00'},
    {'id': '3', 'name': 'بنك الراجحي', 'balance': '75,000.00'},
    {'id': '4', 'name': 'حساب العملاء', 'balance': '25,000.00'},
    {'id': '5', 'name': 'حساب الموردين', 'balance': '15,000.00'},
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.transferAccountBalances),
        backgroundColor: Colors.cyan,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: _showTransferHistory,
            tooltip: 'سجل التحويلات',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // بطاقة معلومات الحسابات
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'أرصدة الحسابات الحالية',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.cyan,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ...(_accounts.map((account) => Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(account['name']!),
                              Text(
                                '${account['balance']} ر.س',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.green,
                                ),
                              ),
                            ],
                          ),
                        ))),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة عملية النقل
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'تفاصيل عملية النقل',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.cyan,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // الحساب المرسل
                    DropdownButtonFormField<String>(
                      value: _fromAccount,
                      decoration: const InputDecoration(
                        labelText: 'من الحساب',
                        prefixIcon: Icon(Icons.account_balance_wallet),
                        border: OutlineInputBorder(),
                      ),
                      items: _accounts.map((account) {
                        return DropdownMenuItem(
                          value: account['id'],
                          child: Text(
                              '${account['name']} (${account['balance']} ر.س)'),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _fromAccount = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار الحساب المرسل';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // الحساب المستقبل
                    DropdownButtonFormField<String>(
                      value: _toAccount,
                      decoration: const InputDecoration(
                        labelText: 'إلى الحساب',
                        prefixIcon: Icon(Icons.account_balance),
                        border: OutlineInputBorder(),
                      ),
                      items: _accounts
                          .where((account) => account['id'] != _fromAccount)
                          .map((account) {
                        return DropdownMenuItem(
                          value: account['id'],
                          child: Text(
                              '${account['name']} (${account['balance']} ر.س)'),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _toAccount = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار الحساب المستقبل';
                        }
                        if (value == _fromAccount) {
                          return 'لا يمكن النقل إلى نفس الحساب';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // المبلغ
                    TextFormField(
                      controller: _amountController,
                      decoration: const InputDecoration(
                        labelText: 'المبلغ المراد نقله',
                        hintText: '0.00',
                        prefixIcon: Icon(Icons.attach_money),
                        border: OutlineInputBorder(),
                        suffixText: 'ر.س',
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال المبلغ';
                        }
                        final amount = double.tryParse(value);
                        if (amount == null || amount <= 0) {
                          return 'يرجى إدخال مبلغ صحيح';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // تاريخ التحويل
                    ListTile(
                      leading: const Icon(Icons.calendar_today),
                      title: const Text('تاريخ التحويل'),
                      subtitle: Text(
                          '${_transferDate.day}/${_transferDate.month}/${_transferDate.year}'),
                      trailing: const Icon(Icons.edit),
                      onTap: _selectDate,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                        side: BorderSide(color: Colors.grey.shade300),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // ملاحظات
                    TextFormField(
                      controller: _notesController,
                      decoration: const InputDecoration(
                        labelText: 'ملاحظات (اختياري)',
                        hintText: 'أدخل أي ملاحظات حول عملية النقل',
                        prefixIcon: Icon(Icons.note),
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // معاينة العملية
            if (_fromAccount != null &&
                _toAccount != null &&
                _amountController.text.isNotEmpty)
              Card(
                color: Colors.blue.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'معاينة عملية النقل',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildPreviewRow('من:', _getAccountName(_fromAccount!)),
                      _buildPreviewRow('إلى:', _getAccountName(_toAccount!)),
                      _buildPreviewRow(
                          'المبلغ:', '${_amountController.text} ر.س'),
                      _buildPreviewRow('التاريخ:',
                          '${_transferDate.day}/${_transferDate.month}/${_transferDate.year}'),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 24),

            // أزرار العمليات
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isProcessing ? null : _processTransfer,
                    icon: _isProcessing
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.transfer_within_a_station),
                    label: Text(
                        _isProcessing ? 'جاري النقل...' : 'تنفيذ عملية النقل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.cyan,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _resetForm,
                    icon: const Icon(Icons.refresh),
                    label: const Text('إعادة تعيين'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 60,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  String _getAccountName(String accountId) {
    final account = _accounts.firstWhere((acc) => acc['id'] == accountId);
    return account['name']!;
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _transferDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _transferDate) {
      setState(() {
        _transferDate = picked;
      });
    }
  }

  Future<void> _processTransfer() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isProcessing = true;
      });

      // محاكاة عملية النقل
      await Future.delayed(const Duration(seconds: 2));

      setState(() {
        _isProcessing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تنفيذ عملية نقل الأرصدة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        _resetForm();
      }
    }
  }

  void _resetForm() {
    setState(() {
      _fromAccount = null;
      _toAccount = null;
      _amountController.clear();
      _notesController.clear();
      _transferDate = DateTime.now();
    });
  }

  void _showTransferHistory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض سجل التحويلات')),
    );
  }

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
