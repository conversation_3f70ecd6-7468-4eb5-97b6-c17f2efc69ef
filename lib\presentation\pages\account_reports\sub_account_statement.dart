import 'package:flutter/material.dart';

/// صفحة كشف الحساب الفرعي
/// تعرض كشف حساب فرعي مفصل
class SubAccountStatementPage extends StatefulWidget {
  const SubAccountStatementPage({super.key});

  @override
  State<SubAccountStatementPage> createState() =>
      _SubAccountStatementPageState();
}

class _SubAccountStatementPageState extends State<SubAccountStatementPage> {
  String _selectedMainAccount = '1000';
  String _selectedSubAccount = '1100';

  // بيانات تجريبية للحسابات الفرعية
  final Map<String, List<Map<String, dynamic>>> _subAccountsData = {
    '1000': [
      {'code': '1100', 'name': 'الأصول المتداولة', 'balance': 265000.0},
      {'code': '1200', 'name': 'الأصول الثابتة', 'balance': 130000.0},
    ],
    '2000': [
      {'code': '2100', 'name': 'الخصوم المتداولة', 'balance': 48000.0},
      {'code': '2200', 'name': 'الخصوم طويلة الأجل', 'balance': 112000.0},
    ],
  };

  // بيانات تجريبية لحركة الحساب الفرعي
  final List<Map<String, dynamic>> _accountMovements = [
    {
      'date': '2024-01-01',
      'description': 'رصيد أول المدة',
      'reference': 'OB-001',
      'debit': 50000.0,
      'credit': 0.0,
      'balance': 50000.0,
    },
    {
      'date': '2024-01-15',
      'description': 'إيداع نقدي',
      'reference': 'JE-001',
      'debit': 15000.0,
      'credit': 0.0,
      'balance': 65000.0,
    },
    {
      'date': '2024-01-20',
      'description': 'تحويل من البنك',
      'reference': 'JE-002',
      'debit': 25000.0,
      'credit': 0.0,
      'balance': 90000.0,
    },
    {
      'date': '2024-01-25',
      'description': 'سحب نقدي',
      'reference': 'JE-003',
      'debit': 0.0,
      'credit': 5000.0,
      'balance': 85000.0,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('كشف الحساب الفرعي'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedMainAccount,
                        decoration: const InputDecoration(
                          labelText: 'الحساب الرئيسي',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(
                              value: '1000', child: Text('1000 - الأصول')),
                          DropdownMenuItem(
                              value: '2000', child: Text('2000 - الخصوم')),
                          DropdownMenuItem(
                              value: '3000',
                              child: Text('3000 - حقوق الملكية')),
                          DropdownMenuItem(
                              value: '4000', child: Text('4000 - الإيرادات')),
                          DropdownMenuItem(
                              value: '5000', child: Text('5000 - المصروفات')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedMainAccount = value!;
                            _selectedSubAccount =
                                _subAccountsData[value]?.first['code'] ?? '';
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedSubAccount,
                        decoration: const InputDecoration(
                          labelText: 'الحساب الفرعي',
                          border: OutlineInputBorder(),
                        ),
                        items: _subAccountsData[_selectedMainAccount]
                                ?.map<DropdownMenuItem<String>>((account) {
                              return DropdownMenuItem<String>(
                                value: account['code'],
                                child: Text(
                                    '${account['code']} - ${account['name']}'),
                              );
                            }).toList() ??
                            [],
                        onChanged: (value) {
                          setState(() {
                            _selectedSubAccount = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // معلومات الحساب الفرعي
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              color: Colors.deepPurple[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      _getSelectedAccountName(),
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Column(
                          children: [
                            const Text('رصيد أول المدة'),
                            Text(
                              '${_getOpeningBalance()} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('الرصيد الحالي'),
                            Text(
                              '${_getCurrentBalance()} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('عدد الحركات'),
                            Text(
                              _accountMovements.length.toString(),
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.orange,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                _buildStatCard(
                    'إجمالي المدين', '${_getTotalDebit()} ر.س', Colors.green),
                _buildStatCard(
                    'إجمالي الدائن', '${_getTotalCredit()} ر.س', Colors.red),
                _buildStatCard(
                    'صافي الحركة', '${_getNetMovement()} ر.س', Colors.blue),
                _buildStatCard('متوسط الحركة', '${_getAverageMovement()} ر.س',
                    Colors.purple),
              ],
            ),
          ),

          // حركة الحساب الفرعي
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: _accountMovements.length,
              itemBuilder: (context, index) {
                final movement = _accountMovements[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor:
                          movement['debit'] > 0 ? Colors.green : Colors.red,
                      child: Icon(
                        movement['debit'] > 0 ? Icons.add : Icons.remove,
                        color: Colors.white,
                      ),
                    ),
                    title: Text(
                      movement['description'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('التاريخ: ${movement['date']}'),
                        Text('المرجع: ${movement['reference']}'),
                        Row(
                          children: [
                            if (movement['debit'] > 0)
                              Text(
                                'مدين: ${movement['debit'].toStringAsFixed(2)} ر.س',
                                style: const TextStyle(
                                  color: Colors.green,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            if (movement['credit'] > 0)
                              Text(
                                'دائن: ${movement['credit'].toStringAsFixed(2)} ر.س',
                                style: const TextStyle(
                                  color: Colors.red,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                    trailing: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        const Text(
                          'الرصيد',
                          style: TextStyle(fontSize: 10),
                        ),
                        Text(
                          '${movement['balance'].toStringAsFixed(2)} ر.س',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                    isThreeLine: true,
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshData,
        backgroundColor: Colors.deepPurple,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث البيانات'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getSelectedAccountName() {
    final subAccounts = _subAccountsData[_selectedMainAccount] ?? [];
    final account = subAccounts.firstWhere(
      (acc) => acc['code'] == _selectedSubAccount,
      orElse: () => {'code': '', 'name': 'غير محدد'},
    );
    return '${account['code']} - ${account['name']}';
  }

  double _getOpeningBalance() {
    if (_accountMovements.isEmpty) return 0.0;
    final firstMovement = _accountMovements.first;
    return firstMovement['balance'] -
        firstMovement['debit'] +
        firstMovement['credit'];
  }

  double _getCurrentBalance() {
    if (_accountMovements.isEmpty) return 0.0;
    return _accountMovements.last['balance'];
  }

  double _getTotalDebit() {
    return _accountMovements.fold(
        0.0, (sum, movement) => sum + movement['debit']);
  }

  double _getTotalCredit() {
    return _accountMovements.fold(
        0.0, (sum, movement) => sum + movement['credit']);
  }

  double _getNetMovement() {
    return _getTotalDebit() - _getTotalCredit();
  }

  String _getAverageMovement() {
    if (_accountMovements.isEmpty) return '0.00';
    double totalMovement = _accountMovements.fold(0.0, (sum, movement) {
      return sum +
          (movement['debit'] > 0 ? movement['debit'] : movement['credit']);
    });
    return (totalMovement / _accountMovements.length).toStringAsFixed(2);
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة كشف الحساب الفرعي')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير كشف الحساب الفرعي')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث بيانات كشف الحساب الفرعي'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
