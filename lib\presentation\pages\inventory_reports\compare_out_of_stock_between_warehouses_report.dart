import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير مقارنة الأصناف منتهية الكميات بين مستودعين
/// يقارن الأصناف منتهية الكميات بين مستودعين مختلفين
class CompareOutOfStockBetweenWarehousesReportPage extends StatefulWidget {
  const CompareOutOfStockBetweenWarehousesReportPage({super.key});

  @override
  State<CompareOutOfStockBetweenWarehousesReportPage> createState() => _CompareOutOfStockBetweenWarehousesReportPageState();
}

class _CompareOutOfStockBetweenWarehousesReportPageState extends State<CompareOutOfStockBetweenWarehousesReportPage> {
  String? _warehouse1;
  String? _warehouse2;
  String? _selectedCategory;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('مقارنة الأصناف منتهية الكميات بين مستودعين'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.orange[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'المستودع الأول',
                          border: OutlineInputBorder(),
                        ),
                        value: _warehouse1,
                        items: const [
                          DropdownMenuItem(value: 'main', child: Text('المستودع الرئيسي')),
                          DropdownMenuItem(value: 'branch1', child: Text('مستودع الفرع الأول')),
                          DropdownMenuItem(value: 'branch2', child: Text('مستودع الفرع الثاني')),
                          DropdownMenuItem(value: 'branch3', child: Text('مستودع الفرع الثالث')),
                        ],
                        onChanged: (value) => setState(() => _warehouse1 = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'المستودع الثاني',
                          border: OutlineInputBorder(),
                        ),
                        value: _warehouse2,
                        items: const [
                          DropdownMenuItem(value: 'main', child: Text('المستودع الرئيسي')),
                          DropdownMenuItem(value: 'branch1', child: Text('مستودع الفرع الأول')),
                          DropdownMenuItem(value: 'branch2', child: Text('مستودع الفرع الثاني')),
                          DropdownMenuItem(value: 'branch3', child: Text('مستودع الفرع الثالث')),
                        ],
                        onChanged: (value) => setState(() => _warehouse2 = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.category,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedCategory,
                        items: [
                          DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                          DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                          DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                          DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                        ],
                        onChanged: (value) => setState(() => _selectedCategory = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _generateReport,
                        icon: const Icon(Icons.compare),
                        label: const Text('مقارنة المستودعين'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                          minimumSize: const Size(double.infinity, 48),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص المقارنة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.compare_arrows, color: Colors.orange, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'ملخص مقارنة الأصناف منتهية الكميات',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildComparisonCard('المستودع الأول', '25 صنف', Colors.blue, Icons.warehouse),
                              _buildComparisonCard('المستودع الثاني', '18 صنف', Colors.green, Icons.store),
                              _buildComparisonCard('مشتركة', '12 صنف', Colors.red, Icons.warning),
                              _buildComparisonCard('الفرق', '7 أصناف', Colors.purple, Icons.difference),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول المقارنة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل مقارنة الأصناف منتهية الكميات',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('كود الصنف')),
                                DataColumn(label: Text('اسم الصنف')),
                                DataColumn(label: Text('التصنيف')),
                                DataColumn(label: Text('المستودع الأول')),
                                DataColumn(label: Text('المستودع الثاني')),
                                DataColumn(label: Text('الحالة')),
                                DataColumn(label: Text('الإجراء المقترح')),
                              ],
                              rows: _buildComparisonRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // توصيات إعادة التوزيع
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.lightbulb, color: Colors.amber, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'توصيات إعادة التوزيع',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          _buildRecommendationItem(
                            'نقل من المستودع الرئيسي',
                            'نقل 15 صنف من المستودع الرئيسي إلى الفرع الأول',
                            Icons.arrow_forward,
                            Colors.blue,
                          ),
                          _buildRecommendationItem(
                            'طلب شراء عاجل',
                            'إنشاء طلب شراء عاجل للأصناف منتهية الكميات في كلا المستودعين',
                            Icons.shopping_cart,
                            Colors.red,
                          ),
                          _buildRecommendationItem(
                            'إعادة توزيع المخزون',
                            'إعادة توزيع المخزون بين المستودعات لتحقيق التوازن',
                            Icons.balance,
                            Colors.green,
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _createTransferOrder,
                                  icon: const Icon(Icons.swap_horiz),
                                  label: const Text('إنشاء أمر نقل'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _createPurchaseOrder,
                                  icon: const Icon(Icons.add_shopping_cart),
                                  label: const Text('إنشاء طلب شراء'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<DataRow> _buildComparisonRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('ITEM-001')),
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('0')),
        const DataCell(Text('5')),
        DataCell(_buildStatusBadge('متوفر في الثاني', Colors.green)),
        DataCell(_buildActionBadge('نقل', Colors.blue)),
      ]),
      DataRow(cells: [
        const DataCell(Text('ITEM-002')),
        const DataCell(Text('طابعة HP LaserJet')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('0')),
        const DataCell(Text('0')),
        DataCell(_buildStatusBadge('منتهي في الاثنين', Colors.red)),
        DataCell(_buildActionBadge('طلب شراء', Colors.red)),
      ]),
      DataRow(cells: [
        const DataCell(Text('ITEM-003')),
        const DataCell(Text('ماوس لوجيتك')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('3')),
        const DataCell(Text('0')),
        DataCell(_buildStatusBadge('متوفر في الأول', Colors.orange)),
        DataCell(_buildActionBadge('نقل', Colors.blue)),
      ]),
    ];
  }

  Widget _buildComparisonCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecommendationItem(String title, String description, IconData icon, Color color) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: color.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: color.withOpacity(0.05),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(status, style: const TextStyle(color: Colors.white, fontSize: 12)),
    );
  }

  Widget _buildActionBadge(String action, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(action, style: const TextStyle(color: Colors.white, fontSize: 12)),
    );
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء مقارنة الأصناف منتهية الكميات بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _createTransferOrder() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء أمر نقل بين المستودعين')),
    );
  }

  void _createPurchaseOrder() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء طلب شراء للأصناف المطلوبة')),
    );
  }
}
