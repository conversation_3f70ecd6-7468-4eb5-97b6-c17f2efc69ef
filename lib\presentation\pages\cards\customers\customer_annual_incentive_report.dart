import 'package:flutter/material.dart';

/// صفحة تقرير الحافز السنوي للعميل
/// تعرض تفاصيل الحوافز المستحقة للعميل على أساس سنوي
class CustomerAnnualIncentiveReportPage extends StatefulWidget {
  const CustomerAnnualIncentiveReportPage({super.key});

  @override
  State<CustomerAnnualIncentiveReportPage> createState() => _CustomerAnnualIncentiveReportPageState();
}

class _CustomerAnnualIncentiveReportPageState extends State<CustomerAnnualIncentiveReportPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير الحافز السنوي للعميل'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        elevation: 2,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.analytics_outlined,
              size: 80,
              color: Colors.blue,
            ),
            SizedBox(height: 24),
            Text(
              'تقرير الحافز السنوي للعميل',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            SizedBox(height: 16),
            Text(
              'هذه الصفحة فارغة وجاهزة للتطوير',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'سيتم تعبئتها لاحقاً بالمحتوى المطلوب',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
