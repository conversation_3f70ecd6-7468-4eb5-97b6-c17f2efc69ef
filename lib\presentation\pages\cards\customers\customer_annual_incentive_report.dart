import 'package:flutter/material.dart';

/// نموذج بيانات تقرير الحافز السنوي للعميل
class CustomerIncentiveData {
  final int sequence;
  final String accountNumber;
  final String customerName;
  final double totalSales;
  final double returns;
  final double discountNotifications;
  final double discountedInvoices;
  final double netSales;
  final double paidWithin90Days;
  final double incentive;

  CustomerIncentiveData({
    required this.sequence,
    required this.accountNumber,
    required this.customerName,
    required this.totalSales,
    required this.returns,
    required this.discountNotifications,
    required this.discountedInvoices,
    required this.netSales,
    required this.paidWithin90Days,
    required this.incentive,
  });
}

/// صفحة تقرير الحافز السنوي للعميل
/// تعرض تفاصيل الحوافز المستحقة لعميل واحد على أساس سنوي
class CustomerAnnualIncentiveReportPage extends StatefulWidget {
  final String? customerId; // معرف العميل (اختياري للمستقبل)
  final String? customerName; // اسم العميل (اختياري للمستقبل)

  const CustomerAnnualIncentiveReportPage({
    super.key,
    this.customerId,
    this.customerName,
  });

  @override
  State<CustomerAnnualIncentiveReportPage> createState() =>
      _CustomerAnnualIncentiveReportPageState();
}

class _CustomerAnnualIncentiveReportPageState
    extends State<CustomerAnnualIncentiveReportPage> {
  // بيانات العميل الحالي (افتراضية - سيتم تحديدها لاحقاً)
  late String _currentCustomerId;
  late String _currentCustomerName;
  late String _currentAccountNumber;

  @override
  void initState() {
    super.initState();
    // تحديد العميل الحالي (افتراضي أو من المعاملات)
    _currentCustomerId = widget.customerId ?? 'CUST001';
    _currentCustomerName = widget.customerName ?? 'شركة الأمل للتجارة';
    _currentAccountNumber = 'CUST001';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تقرير الحافز السنوي - $_currentCustomerName'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        elevation: 2,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان البيانات
            Text(
              'بيانات العميل',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.blue[700],
              ),
            ),

            const SizedBox(height: 20),

            // بطاقة البيانات
            _buildCustomerDataCard(),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة بيانات العميل
  Widget _buildCustomerDataCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 2,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: Colors.blue[100]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رقم التسلسل واسم العميل
          Row(
            children: [
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.blue[700],
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Text(
                  '1',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'شركة الأمل للتجارة',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'رقم الحساب: CUST001',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // البيانات المالية
          _buildFinancialDataGrid(),
        ],
      ),
    );
  }

  /// بناء شبكة البيانات المالية
  Widget _buildFinancialDataGrid() {
    return Column(
      children: [
        // الصف الأول
        Row(
          children: [
            Expanded(
                child: _buildDataItem(
                    'إجمالي المبيعات', '850000.00', 'ر.س', Colors.green)),
            const SizedBox(width: 16),
            Expanded(
                child:
                    _buildDataItem('المرتجعات', '25000.00', 'ر.س', Colors.red)),
          ],
        ),

        const SizedBox(height: 16),

        // الصف الثاني
        Row(
          children: [
            Expanded(
                child: _buildDataItem(
                    'إشعارات الخصم', '15000.00', 'ر.س', Colors.orange)),
            const SizedBox(width: 16),
            Expanded(
                child: _buildDataItem(
                    'الفواتير المخفضة', '35000.00', 'ر.س', Colors.purple)),
          ],
        ),

        const SizedBox(height: 16),

        // الصف الثالث
        Row(
          children: [
            Expanded(
                child: _buildDataItem(
                    'صافي المبيعات', '775000.00', 'ر.س', Colors.blue)),
            const SizedBox(width: 16),
            Expanded(
                child: _buildDataItem(
                    'المسدد خلال 90 يوم', '720000.00', 'ر.س', Colors.teal)),
          ],
        ),

        const SizedBox(height: 20),

        // الحافز (مميز)
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.green[400]!, Colors.green[600]!],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              const Text(
                'الحافز',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '38750.00 ر.س',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء عنصر بيانات واحد
  Widget _buildDataItem(
      String title, String value, String currency, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color.withValues(alpha: 0.8),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  value,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ),
              Text(
                currency,
                style: TextStyle(
                  fontSize: 12,
                  color: color.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// طباعة التقرير
  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري طباعة تقرير الحافز السنوي للعميل...'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// تصدير التقرير
  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تصدير تقرير الحافز السنوي للعميل...'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
