import 'package:flutter/material.dart';

/// صفحة تقرير الحافز السنوي للعميل
class CustomerAnnualIncentiveReportPage extends StatelessWidget {
  final String? customerId;
  final String? customerName;

  const CustomerAnnualIncentiveReportPage({
    super.key,
    this.customerId,
    this.customerName,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير الحافز السنوي للعميل'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        elevation: 2,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: () => _printReport(context),
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: () => _exportReport(context),
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Container(
        color: Colors.grey[50],
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // بطاقة العميل الرئيسية
              _buildCustomerCard(),

              const SizedBox(height: 20),

              // شبكة البيانات المالية
              Expanded(
                child: _buildFinancialDataGrid(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء بطاقة معلومات العميل
  Widget _buildCustomerCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 2,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // رقم التسلسل
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: Colors.blue[600],
              shape: BoxShape.circle,
            ),
            child: const Center(
              child: Text(
                '1',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),

          const SizedBox(width: 20),

          // معلومات العميل
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'شركة الأمل للتجارة',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'رقم الحساب: CUST001',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء شبكة البيانات المالية
  Widget _buildFinancialDataGrid() {
    return Column(
      children: [
        // الصف الأول
        Row(
          children: [
            Expanded(
              child: _buildDataCard(
                'المرتجعات',
                '25000.00',
                Colors.red[100]!,
                Colors.red[600]!,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDataCard(
                'إجمالي المبيعات',
                '850000.00',
                Colors.green[100]!,
                Colors.green[600]!,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // الصف الثاني
        Row(
          children: [
            Expanded(
              child: _buildDataCard(
                'الفواتير المخفضة',
                '35000.00',
                Colors.purple[100]!,
                Colors.purple[600]!,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDataCard(
                'إشعارات الخصم',
                '15000.00',
                Colors.orange[100]!,
                Colors.orange[600]!,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // الصف الثالث
        Row(
          children: [
            Expanded(
              child: _buildDataCard(
                'المسدد خلال 90 يوم',
                '720000.00',
                Colors.teal[100]!,
                Colors.teal[600]!,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDataCard(
                'صافي المبيعات',
                '775000.00',
                Colors.blue[100]!,
                Colors.blue[600]!,
              ),
            ),
          ],
        ),

        const SizedBox(height: 20),

        // بطاقة الحافز المميزة
        _buildIncentiveCard(),
      ],
    );
  }

  /// بناء بطاقة بيانات واحدة
  Widget _buildDataCard(
      String title, String amount, Color bgColor, Color textColor) {
    return Container(
      height: 100,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              color: textColor,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.right,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                'ر.س',
                style: TextStyle(
                  fontSize: 12,
                  color: textColor.withValues(alpha: 0.7),
                ),
              ),
              const SizedBox(width: 4),
              Text(
                amount,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: textColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة الحافز المميزة
  Widget _buildIncentiveCard() {
    return Container(
      width: double.infinity,
      height: 80,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.green[400]!, Colors.green[600]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const Text(
            'الحافز',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          Row(
            children: [
              const Text(
                'ر.س',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                ),
              ),
              const SizedBox(width: 8),
              const Text(
                '38750.00',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// طباعة التقرير
  void _printReport(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري طباعة تقرير الحافز السنوي للعميل...'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// تصدير التقرير
  void _exportReport(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تصدير تقرير الحافز السنوي للعميل...'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
