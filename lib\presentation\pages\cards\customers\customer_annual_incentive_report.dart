import 'package:flutter/material.dart';

/// نموذج بيانات تقرير الحافز السنوي للعميل
class CustomerIncentiveData {
  final int sequence;
  final String accountNumber;
  final String customerName;
  final double totalSales;
  final double returns;
  final double discountNotifications;
  final double discountedInvoices;
  final double netSales;
  final double paidWithin90Days;
  final double incentive;

  CustomerIncentiveData({
    required this.sequence,
    required this.accountNumber,
    required this.customerName,
    required this.totalSales,
    required this.returns,
    required this.discountNotifications,
    required this.discountedInvoices,
    required this.netSales,
    required this.paidWithin90Days,
    required this.incentive,
  });
}

/// صفحة تقرير الحافز السنوي للعميل
/// تعرض تفاصيل الحوافز المستحقة لعميل واحد على أساس سنوي
class CustomerAnnualIncentiveReportPage extends StatefulWidget {
  final String? customerId; // معرف العميل (اختياري للمستقبل)
  final String? customerName; // اسم العميل (اختياري للمستقبل)

  const CustomerAnnualIncentiveReportPage({
    super.key,
    this.customerId,
    this.customerName,
  });

  @override
  State<CustomerAnnualIncentiveReportPage> createState() =>
      _CustomerAnnualIncentiveReportPageState();
}

class _CustomerAnnualIncentiveReportPageState
    extends State<CustomerAnnualIncentiveReportPage> {
  String _selectedYear = 'الكل';
  String _sortBy = 'sequence';
  bool _sortAscending = true;

  final List<String> _years = ['الكل', '2024', '2023', '2022', '2021', '2020'];

  // بيانات العميل الحالي (افتراضية - سيتم تحديدها لاحقاً)
  late String _currentCustomerId;
  late String _currentCustomerName;
  late String _currentAccountNumber;

  @override
  void initState() {
    super.initState();
    // تحديد العميل الحالي (افتراضي أو من المعاملات)
    _currentCustomerId = widget.customerId ?? 'CUST001';
    _currentCustomerName = widget.customerName ?? 'شركة الأمل للتجارة';
    _currentAccountNumber = 'CUST001';
  }

  // بيانات تجريبية لتقرير الحافز السنوي لعميل واحد (حسب السنوات)
  List<CustomerIncentiveData> get _incentiveData {
    return [
      CustomerIncentiveData(
        sequence: 1,
        accountNumber: _currentAccountNumber,
        customerName: _currentCustomerName,
        totalSales: 850000.0,
        returns: 25000.0,
        discountNotifications: 15000.0,
        discountedInvoices: 35000.0,
        netSales: 775000.0,
        paidWithin90Days: 720000.0,
        incentive: 38750.0,
      ),
      CustomerIncentiveData(
        sequence: 2,
        accountNumber: _currentAccountNumber,
        customerName: _currentCustomerName,
        totalSales: 920000.0,
        returns: 32000.0,
        discountNotifications: 18000.0,
        discountedInvoices: 42000.0,
        netSales: 828000.0,
        paidWithin90Days: 800000.0,
        incentive: 41400.0,
      ),
      CustomerIncentiveData(
        sequence: 3,
        accountNumber: _currentAccountNumber,
        customerName: _currentCustomerName,
        totalSales: 750000.0,
        returns: 22000.0,
        discountNotifications: 14000.0,
        discountedInvoices: 30000.0,
        netSales: 684000.0,
        paidWithin90Days: 670000.0,
        incentive: 34200.0,
      ),
      CustomerIncentiveData(
        sequence: 4,
        accountNumber: _currentAccountNumber,
        customerName: _currentCustomerName,
        totalSales: 1200000.0,
        returns: 45000.0,
        discountNotifications: 25000.0,
        discountedInvoices: 55000.0,
        netSales: 1075000.0,
        paidWithin90Days: 1050000.0,
        incentive: 53750.0,
      ),
      CustomerIncentiveData(
        sequence: 5,
        accountNumber: _currentAccountNumber,
        customerName: _currentCustomerName,
        totalSales: 650000.0,
        returns: 18000.0,
        discountNotifications: 12000.0,
        discountedInvoices: 28000.0,
        netSales: 592000.0,
        paidWithin90Days: 580000.0,
        incentive: 29600.0,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تقرير الحافز السنوي - $_currentCustomerName'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        elevation: 2,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          _buildFilterSection(),

          // ملخص التقرير
          _buildSummarySection(),

          // جدول البيانات
          Expanded(
            child: _buildDataTable(),
          ),
        ],
      ),
    );
  }

  /// بناء قسم الفلترة
  Widget _buildFilterSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.person,
                color: Colors.blue[700],
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'العميل: $_currentCustomerName',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[700],
                ),
              ),
              const Spacer(),
              Text(
                'رقم الحساب: $_currentAccountNumber',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                flex: 2,
                child: _buildDropdownField(
                  'السنة',
                  _selectedYear,
                  _years,
                  (value) => setState(() => _selectedYear = value!),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                flex: 2,
                child: _buildDropdownField(
                  'ترتيب حسب',
                  _sortBy,
                  ['sequence', 'totalSales', 'incentive'],
                  (value) => setState(() => _sortBy = value!),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () =>
                      setState(() => _sortAscending = !_sortAscending),
                  icon: Icon(_sortAscending
                      ? Icons.arrow_upward
                      : Icons.arrow_downward),
                  label: Text(_sortAscending ? 'تصاعدي' : 'تنازلي'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue[100],
                    foregroundColor: Colors.blue[700],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء قسم الملخص
  Widget _buildSummarySection() {
    final filteredData = _getFilteredData();
    final totalSales =
        filteredData.fold(0.0, (sum, item) => sum + item.totalSales);
    final totalIncentives =
        filteredData.fold(0.0, (sum, item) => sum + item.incentive);
    final totalNetSales =
        filteredData.fold(0.0, (sum, item) => sum + item.netSales);
    final totalReturns =
        filteredData.fold(0.0, (sum, item) => sum + item.returns);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryCard(
              'إجمالي المبيعات',
              _formatCurrency(totalSales),
              Icons.trending_up,
              Colors.green,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildSummaryCard(
              'إجمالي المرتجعات',
              _formatCurrency(totalReturns),
              Icons.keyboard_return,
              Colors.red,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildSummaryCard(
              'صافي المبيعات',
              _formatCurrency(totalNetSales),
              Icons.account_balance,
              Colors.blue,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildSummaryCard(
              'إجمالي الحوافز',
              _formatCurrency(totalIncentives),
              Icons.card_giftcard,
              Colors.orange,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء جدول البيانات
  Widget _buildDataTable() {
    final filteredData = _getFilteredData();

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        child: DataTable(
          columnSpacing: 20,
          headingRowColor: WidgetStateProperty.all(Colors.blue[100]),
          columns: const [
            DataColumn(
              label: Text(
                'التسلسل',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            DataColumn(
              label: Text(
                'رقم الحساب',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            DataColumn(
              label: Text(
                'اسم العميل',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            DataColumn(
              label: Text(
                'إجمالي المبيعات',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              numeric: true,
            ),
            DataColumn(
              label: Text(
                'المرتجعات',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              numeric: true,
            ),
            DataColumn(
              label: Text(
                'إشعارات الخصم',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              numeric: true,
            ),
            DataColumn(
              label: Text(
                'الفواتير المخفضة',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              numeric: true,
            ),
            DataColumn(
              label: Text(
                'صافي المبيعات',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              numeric: true,
            ),
            DataColumn(
              label: Text(
                'المسدد خلال 90 يوم',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              numeric: true,
            ),
            DataColumn(
              label: Text(
                'الحافز',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              numeric: true,
            ),
          ],
          rows: filteredData.map((data) {
            return DataRow(
              cells: [
                DataCell(Text('${data.sequence}')),
                DataCell(Text(data.accountNumber)),
                DataCell(
                  SizedBox(
                    width: 200,
                    child: Text(
                      data.customerName,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                DataCell(Text(_formatCurrency(data.totalSales))),
                DataCell(Text(_formatCurrency(data.returns))),
                DataCell(Text(_formatCurrency(data.discountNotifications))),
                DataCell(Text(_formatCurrency(data.discountedInvoices))),
                DataCell(Text(_formatCurrency(data.netSales))),
                DataCell(Text(_formatCurrency(data.paidWithin90Days))),
                DataCell(
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green[100],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _formatCurrency(data.incentive),
                      style: TextStyle(
                        color: Colors.green[800],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }

  /// بناء حقل قائمة منسدلة
  Widget _buildDropdownField(
    String label,
    String value,
    List<String> items,
    ValueChanged<String?> onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: items.contains(value) ? value : items.first,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: items.map((String item) {
            return DropdownMenuItem<String>(
              value: item,
              child: Text(item),
            );
          }).toList(),
          onChanged: onChanged,
        ),
      ],
    );
  }

  /// بناء بطاقة ملخص
  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const Spacer(),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على البيانات المفلترة
  List<CustomerIncentiveData> _getFilteredData() {
    List<CustomerIncentiveData> filtered = List.from(_incentiveData);

    // فلترة حسب السنة (إذا لم تكن "الكل")
    if (_selectedYear != 'الكل') {
      // هنا يمكن إضافة منطق فلترة السنة في المستقبل
      // حالياً نعرض جميع البيانات
    }

    // ترتيب البيانات
    filtered.sort((a, b) {
      switch (_sortBy) {
        case 'totalSales':
          return _sortAscending
              ? a.totalSales.compareTo(b.totalSales)
              : b.totalSales.compareTo(a.totalSales);
        case 'incentive':
          return _sortAscending
              ? a.incentive.compareTo(b.incentive)
              : b.incentive.compareTo(a.incentive);
        default: // sequence
          return _sortAscending
              ? a.sequence.compareTo(b.sequence)
              : b.sequence.compareTo(a.sequence);
      }
    });

    return filtered;
  }

  /// تنسيق العملة
  String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(2)} ر.س';
  }

  /// طباعة التقرير
  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري طباعة تقرير الحافز السنوي للعميل...'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// تصدير التقرير
  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تصدير تقرير الحافز السنوي للعميل...'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
