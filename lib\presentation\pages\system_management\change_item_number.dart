import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تغير رقم الصنف
/// تتيح تغيير رقم الصنف مع الحفاظ على جميع البيانات المرتبطة
class ChangeItemNumberPage extends StatefulWidget {
  const ChangeItemNumberPage({super.key});

  @override
  State<ChangeItemNumberPage> createState() => _ChangeItemNumberPageState();
}

class _ChangeItemNumberPageState extends State<ChangeItemNumberPage> {
  final _formKey = GlobalKey<FormState>();
  final _searchController = TextEditingController();
  final _oldNumberController = TextEditingController();
  final _newNumberController = TextEditingController();

  String? _selectedItem;
  bool _isProcessing = false;
  bool _updateAllReferences = true;
  bool _createBackup = true;
  String _searchQuery = '';

  final List<Map<String, dynamic>> _items = [
    {
      'id': 'item1',
      'number': 'A001',
      'name': 'لابتوب ديل',
      'category': 'إلكترونيات',
      'price': 2500.0,
      'stock': 15,
      'references': 45, // عدد المراجع في النظام
    },
    {
      'id': 'item2',
      'number': 'B002',
      'name': 'طابعة HP',
      'category': 'إلكترونيات',
      'price': 450.0,
      'stock': 8,
      'references': 23,
    },
    {
      'id': 'item3',
      'number': 'C003',
      'name': 'كرسي مكتب',
      'category': 'أثاث',
      'price': 350.0,
      'stock': 12,
      'references': 18,
    },
    {
      'id': 'item4',
      'number': 'D004',
      'name': 'شاشة سامسونج',
      'category': 'إلكترونيات',
      'price': 800.0,
      'stock': 6,
      'references': 31,
    },
    {
      'id': 'item5',
      'number': 'E005',
      'name': 'مكتب خشبي',
      'category': 'أثاث',
      'price': 1200.0,
      'stock': 4,
      'references': 12,
    },
  ];

  List<Map<String, dynamic>> get _filteredItems {
    if (_searchQuery.isEmpty) return _items;
    return _items
        .where((item) =>
            item['name']
                .toString()
                .toLowerCase()
                .contains(_searchQuery.toLowerCase()) ||
            item['number']
                .toString()
                .toLowerCase()
                .contains(_searchQuery.toLowerCase()) ||
            item['category']
                .toString()
                .toLowerCase()
                .contains(_searchQuery.toLowerCase()))
        .toList();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _oldNumberController.dispose();
    _newNumberController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.changeItemNumber),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: _showChangeHistory,
            tooltip: 'سجل التغييرات',
          ),
          IconButton(
            icon: const Icon(Icons.backup),
            onPressed: _showBackupOptions,
            tooltip: 'خيارات النسخ الاحتياطي',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // بطاقة البحث عن الصنف
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'البحث عن الصنف',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.indigo,
                      ),
                    ),
                    const SizedBox(height: 16),

                    TextField(
                      controller: _searchController,
                      decoration: const InputDecoration(
                        labelText: 'البحث بالاسم أو الرقم',
                        prefixIcon: Icon(Icons.search),
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                      ),
                      onChanged: (value) {
                        setState(() {
                          _searchQuery = value;
                        });
                      },
                    ),

                    const SizedBox(height: 16),

                    // قائمة الأصناف
                    Container(
                      height: 200,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: ListView.builder(
                        itemCount: _filteredItems.length,
                        itemBuilder: (context, index) {
                          final item = _filteredItems[index];
                          final isSelected = _selectedItem == item['id'];

                          return ListTile(
                            selected: isSelected,
                            leading: CircleAvatar(
                              backgroundColor:
                                  isSelected ? Colors.indigo : Colors.grey,
                              child: Text(
                                item['number'].substring(0, 1),
                                style: const TextStyle(color: Colors.white),
                              ),
                            ),
                            title: Text('${item['number']} - ${item['name']}'),
                            subtitle: Text(
                                '${item['category']} | المخزون: ${item['stock']} | المراجع: ${item['references']}'),
                            onTap: () {
                              setState(() {
                                _selectedItem = item['id'];
                                _oldNumberController.text = item['number'];
                              });
                            },
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة تغيير الرقم
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'تغيير رقم الصنف',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.indigo,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        // الرقم الحالي
                        Expanded(
                          child: TextFormField(
                            controller: _oldNumberController,
                            decoration: const InputDecoration(
                              labelText: 'الرقم الحالي',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 8),
                            ),
                            readOnly: true,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى اختيار صنف';
                              }
                              return null;
                            },
                          ),
                        ),

                        const SizedBox(width: 16),

                        // الرقم الجديد
                        Expanded(
                          child: TextFormField(
                            controller: _newNumberController,
                            decoration: const InputDecoration(
                              labelText: 'الرقم الجديد',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 8),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال الرقم الجديد';
                              }
                              if (value == _oldNumberController.text) {
                                return 'الرقم الجديد يجب أن يكون مختلف';
                              }
                              if (_isNumberExists(value)) {
                                return 'هذا الرقم موجود بالفعل';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة الخيارات
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'خيارات التغيير',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.indigo,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SwitchListTile(
                      title: const Text('تحديث جميع المراجع'),
                      subtitle:
                          const Text('تحديث الرقم في جميع الفواتير والمستندات'),
                      value: _updateAllReferences,
                      onChanged: (value) {
                        setState(() {
                          _updateAllReferences = value;
                        });
                      },
                    ),
                    SwitchListTile(
                      title: const Text('إنشاء نسخة احتياطية'),
                      subtitle: const Text('إنشاء نسخة احتياطية قبل التغيير'),
                      value: _createBackup,
                      onChanged: (value) {
                        setState(() {
                          _createBackup = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // معاينة التغيير
            if (_selectedItem != null && _newNumberController.text.isNotEmpty)
              Card(
                color: Colors.indigo.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'معاينة التغيير',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.indigo,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildPreviewRow('الصنف:', _getSelectedItemName()),
                      _buildPreviewRow(
                          'الرقم الحالي:', _oldNumberController.text),
                      _buildPreviewRow(
                          'الرقم الجديد:', _newNumberController.text),
                      _buildPreviewRow('عدد المراجع:',
                          '${_getSelectedItemReferences()} مرجع'),
                      _buildPreviewRow('تحديث المراجع:',
                          _updateAllReferences ? 'نعم' : 'لا'),
                      _buildPreviewRow(
                          'نسخة احتياطية:', _createBackup ? 'نعم' : 'لا'),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 16),

            // تحذير
            if (_selectedItem != null && _getSelectedItemReferences() > 20)
              Card(
                color: Colors.orange.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      const Icon(Icons.warning, color: Colors.orange),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'تحذير',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.orange,
                              ),
                            ),
                            Text(
                              'هذا الصنف له ${_getSelectedItemReferences()} مرجع في النظام. قد تستغرق العملية وقت أطول.',
                              style: const TextStyle(fontSize: 12),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 24),

            // أزرار العمليات
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isProcessing ? null : _changeItemNumber,
                    icon: _isProcessing
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.edit),
                    label:
                        Text(_isProcessing ? 'جاري التغيير...' : 'تغيير الرقم'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.indigo,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _resetForm,
                    icon: const Icon(Icons.refresh),
                    label: const Text('إعادة تعيين'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  String _getSelectedItemName() {
    if (_selectedItem == null) return 'غير محدد';
    final item = _items.firstWhere((i) => i['id'] == _selectedItem);
    return item['name'];
  }

  int _getSelectedItemReferences() {
    if (_selectedItem == null) return 0;
    final item = _items.firstWhere((i) => i['id'] == _selectedItem);
    return item['references'];
  }

  bool _isNumberExists(String number) {
    return _items.any((item) =>
        item['number'].toString().toLowerCase() == number.toLowerCase() &&
        item['id'] != _selectedItem);
  }

  Future<void> _changeItemNumber() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isProcessing = true;
      });

      // محاكاة عملية التغيير
      await Future.delayed(const Duration(seconds: 3));

      setState(() {
        _isProcessing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'تم تغيير رقم الصنف من ${_oldNumberController.text} إلى ${_newNumberController.text} بنجاح'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
        _resetForm();
      }
    }
  }

  void _resetForm() {
    setState(() {
      _selectedItem = null;
      _oldNumberController.clear();
      _newNumberController.clear();
      _searchController.clear();
      _searchQuery = '';
      _updateAllReferences = true;
      _createBackup = true;
    });
  }

  void _showChangeHistory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض سجل تغييرات أرقام الأصناف')),
    );
  }

  void _showBackupOptions() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض خيارات النسخ الاحتياطي')),
    );
  }
}
