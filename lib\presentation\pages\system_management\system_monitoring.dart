import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة مراقبة النظام
/// تعرض حالة النظام والأداء والإحصائيات
class SystemMonitoringPage extends StatelessWidget {
  const SystemMonitoringPage({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.systemMonitoring),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // بطاقات حالة النظام
            Row(
              children: [
                Expanded(
                  child: _buildStatusCard(
                    title: 'حالة الخادم',
                    value: 'متصل',
                    icon: Icons.cloud_done,
                    color: Colors.green,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatusCard(
                    title: 'استخدام المعالج',
                    value: '45%',
                    icon: Icons.memory,
                    color: Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatusCard(
                    title: 'استخدام الذاكرة',
                    value: '2.1 GB',
                    icon: Icons.storage,
                    color: Colors.blue,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatusCard(
                    title: 'المستخدمين النشطين',
                    value: '12',
                    icon: Icons.people,
                    color: Colors.purple,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // قائمة أدوات المراقبة
            Expanded(
              child: ListView(
                children: [
                  _buildMonitoringTile(
                    title: 'سجل الأحداث',
                    subtitle: 'عرض سجل أحداث النظام',
                    icon: Icons.list_alt,
                    onTap: () => _showEventLog(context),
                  ),
                  _buildMonitoringTile(
                    title: 'المستخدمين المتصلين',
                    subtitle: 'عرض المستخدمين النشطين حالياً',
                    icon: Icons.people_outline,
                    onTap: () => _showActiveUsers(context),
                  ),
                  _buildMonitoringTile(
                    title: 'إحصائيات الأداء',
                    subtitle: 'تفاصيل أداء النظام',
                    icon: Icons.analytics,
                    onTap: () => _showPerformanceStats(context),
                  ),
                  _buildMonitoringTile(
                    title: 'تنبيهات النظام',
                    subtitle: 'عرض التنبيهات والتحذيرات',
                    icon: Icons.warning,
                    onTap: () => _showSystemAlerts(context),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMonitoringTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        leading: Icon(icon, color: Colors.indigo),
        title: Text(title),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: onTap,
      ),
    );
  }

  void _showEventLog(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض سجل الأحداث')),
    );
  }

  void _showActiveUsers(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض المستخدمين النشطين')),
    );
  }

  void _showPerformanceStats(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض إحصائيات الأداء')),
    );
  }

  void _showSystemAlerts(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تنبيهات النظام')),
    );
  }
}
