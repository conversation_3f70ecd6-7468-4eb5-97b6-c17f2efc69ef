import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير فواتير مشتريات الموردين حسب التصنيفات
/// يعرض تفاصيل فواتير المشتريات مصنفة حسب فئات الأصناف
class SupplierPurchaseInvoicesByCategoryReportPage extends StatefulWidget {
  const SupplierPurchaseInvoicesByCategoryReportPage({super.key});

  @override
  State<SupplierPurchaseInvoicesByCategoryReportPage> createState() => _SupplierPurchaseInvoicesByCategoryReportPageState();
}

class _SupplierPurchaseInvoicesByCategoryReportPageState extends State<SupplierPurchaseInvoicesByCategoryReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedSupplier;
  String? _selectedCategory;
  final String _sortBy = 'invoice_date';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('فواتير مشتريات الموردين حسب التصنيفات'),
        backgroundColor: Colors.deepOrange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showCategoryAnalysis,
            tooltip: 'تحليل التصنيفات',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilterSection(localizations),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildCategoryBreakdownSection(),
                  const SizedBox(height: 16),
                  _buildInvoicesTableSection(),
                  const SizedBox(height: 16),
                  _buildTopSuppliersSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.deepOrange[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.fromDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, true),
                  controller: TextEditingController(
                    text: _startDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.toDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, false),
                  controller: TextEditingController(
                    text: _endDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'المورد',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedSupplier,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الموردين')),
                    DropdownMenuItem(value: 'supplier1', child: Text('شركة التقنية المتقدمة')),
                    DropdownMenuItem(value: 'supplier2', child: Text('مؤسسة الإلكترونيات')),
                    DropdownMenuItem(value: 'supplier3', child: Text('شركة الأجهزة الذكية')),
                  ],
                  onChanged: (value) => setState(() => _selectedSupplier = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: localizations.category,
                    border: const OutlineInputBorder(),
                  ),
                  value: _selectedCategory,
                  items: [
                    DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                    DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                    DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                    DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                  ],
                  onChanged: (value) => setState(() => _selectedCategory = value),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.receipt_long, color: Colors.deepOrange, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص فواتير المشتريات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي الفواتير', '485', Colors.deepOrange, Icons.receipt),
                _buildSummaryCard('إجمالي المبلغ', '1,850,000 ر.س', Colors.green, Icons.monetization_on),
                _buildSummaryCard('متوسط الفاتورة', '3,814 ر.س', Colors.blue, Icons.calculate),
                _buildSummaryCard('عدد الموردين', '25', Colors.purple, Icons.business),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryBreakdownSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.pie_chart, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'توزيع المشتريات حسب التصنيفات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildCategoryCard('إلكترونيات', '925,000 ر.س', '50%', Colors.blue),
                _buildCategoryCard('ملابس', '462,500 ر.س', '25%', Colors.green),
                _buildCategoryCard('أغذية', '277,500 ر.س', '15%', Colors.orange),
                _buildCategoryCard('أخرى', '185,000 ر.س', '10%', Colors.purple),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoicesTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل فواتير المشتريات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('رقم الفاتورة')),
                  DataColumn(label: Text('التاريخ')),
                  DataColumn(label: Text('المورد')),
                  DataColumn(label: Text('التصنيف')),
                  DataColumn(label: Text('المبلغ')),
                  DataColumn(label: Text('الضريبة')),
                  DataColumn(label: Text('الإجمالي')),
                  DataColumn(label: Text('طريقة الدفع')),
                  DataColumn(label: Text('الحالة')),
                ],
                rows: _buildInvoicesRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopSuppliersSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.star, color: Colors.amber, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'أهم الموردين حسب التصنيف',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildTopSuppliersList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _negotiateSupplierPrices,
                    icon: const Icon(Icons.handshake),
                    label: const Text('تفاوض الأسعار'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _optimizePurchasing,
                    icon: const Icon(Icons.tune),
                    label: const Text('تحسين المشتريات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _evaluateSuppliers,
                    icon: const Icon(Icons.assessment),
                    label: const Text('تقييم الموردين'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _createPurchasePlan,
                    icon: const Icon(Icons.schedule),
                    label: const Text('خطة المشتريات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildTopSuppliersList() {
    final topSuppliers = [
      {'name': 'شركة التقنية المتقدمة', 'category': 'إلكترونيات', 'amount': '485,000 ر.س', 'invoices': '125'},
      {'name': 'مؤسسة الإلكترونيات', 'category': 'إلكترونيات', 'amount': '385,000 ر.س', 'invoices': '95'},
      {'name': 'شركة الأجهزة الذكية', 'category': 'إلكترونيات', 'amount': '285,000 ر.س', 'invoices': '75'},
      {'name': 'مصنع الملابس الحديث', 'category': 'ملابس', 'amount': '185,000 ر.س', 'invoices': '65'},
    ];

    return topSuppliers.map((supplier) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.amber.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.amber.withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.star, color: Colors.amber, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${supplier['name']} - ${supplier['category']}',
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  'مبلغ: ${supplier['amount']} • فواتير: ${supplier['invoices']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildInvoicesRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('PUR-001')),
        const DataCell(Text('2024-01-15')),
        const DataCell(Text('شركة التقنية المتقدمة')),
        DataCell(_buildCategoryBadge('إلكترونيات', Colors.blue)),
        const DataCell(Text('25,000 ر.س')),
        const DataCell(Text('3,750 ر.س')),
        const DataCell(Text('28,750 ر.س')),
        DataCell(_buildPaymentBadge('آجل', Colors.orange)),
        DataCell(_buildStatusBadge('مؤكد', Colors.green)),
      ]),
      DataRow(cells: [
        const DataCell(Text('PUR-002')),
        const DataCell(Text('2024-01-18')),
        const DataCell(Text('مؤسسة الإلكترونيات')),
        DataCell(_buildCategoryBadge('إلكترونيات', Colors.blue)),
        const DataCell(Text('15,000 ر.س')),
        const DataCell(Text('2,250 ر.س')),
        const DataCell(Text('17,250 ر.س')),
        DataCell(_buildPaymentBadge('نقداً', Colors.green)),
        DataCell(_buildStatusBadge('مكتمل', Colors.green)),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryCard(String category, String amount, String percentage, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                amount,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(category, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
              const SizedBox(height: 4),
              Text(
                percentage,
                style: TextStyle(fontSize: 10, color: color, fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryBadge(String category, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(category, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Widget _buildPaymentBadge(String payment, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(payment, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(status, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showCategoryAnalysis() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تحليل التصنيفات المتقدم')),
    );
  }

  void _negotiateSupplierPrices() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('بدء تفاوض أسعار الموردين')),
    );
  }

  void _optimizePurchasing() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحسين استراتيجية المشتريات')),
    );
  }

  void _evaluateSuppliers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تقييم أداء الموردين')),
    );
  }

  void _createPurchasePlan() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء خطة المشتريات')),
    );
  }
}
