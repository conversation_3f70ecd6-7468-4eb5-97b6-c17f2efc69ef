import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير حركة مبيعات العملاء بالتصنيفات
/// يعرض مبيعات العملاء مقسمة حسب تصنيفات الأصناف
class CustomerSalesByCategoriesReportPage extends StatefulWidget {
  const CustomerSalesByCategoriesReportPage({super.key});

  @override
  State<CustomerSalesByCategoriesReportPage> createState() => _CustomerSalesByCategoriesReportPageState();
}

class _CustomerSalesByCategoriesReportPageState extends State<CustomerSalesByCategoriesReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedCustomer;
  String? _selectedCategory;
  String? _sortBy = 'sales_amount';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('حركة مبيعات العملاء بالتصنيفات'),
        backgroundColor: Colors.cyan,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showAnalytics,
            tooltip: 'تحليلات متقدمة',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          _buildFilterSection(localizations),
          
          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildCategoriesSection(),
                  const SizedBox(height: 16),
                  _buildCustomerSalesTableSection(),
                  const SizedBox(height: 16),
                  _buildTopCustomersSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.cyan[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.fromDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, true),
                  controller: TextEditingController(
                    text: _startDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.toDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, false),
                  controller: TextEditingController(
                    text: _endDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'العميل',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedCustomer,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع العملاء')),
                    DropdownMenuItem(value: 'customer1', child: Text('أحمد محمد')),
                    DropdownMenuItem(value: 'customer2', child: Text('فاطمة علي')),
                    DropdownMenuItem(value: 'customer3', child: Text('محمد سالم')),
                    DropdownMenuItem(value: 'customer4', child: Text('نورا أحمد')),
                  ],
                  onChanged: (value) => setState(() => _selectedCustomer = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: localizations.category,
                    border: const OutlineInputBorder(),
                  ),
                  value: _selectedCategory,
                  items: [
                    DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                    DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                    DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                    DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                  ],
                  onChanged: (value) => setState(() => _selectedCategory = value),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'ترتيب حسب',
                    border: OutlineInputBorder(),
                  ),
                  value: _sortBy,
                  items: const [
                    DropdownMenuItem(value: 'sales_amount', child: Text('مبلغ المبيعات')),
                    DropdownMenuItem(value: 'quantity', child: Text('الكمية')),
                    DropdownMenuItem(value: 'customer_name', child: Text('اسم العميل')),
                    DropdownMenuItem(value: 'category', child: Text('التصنيف')),
                  ],
                  onChanged: (value) => setState(() => _sortBy = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.analytics),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.cyan,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.people, color: Colors.cyan, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص مبيعات العملاء بالتصنيفات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي العملاء', '285', Colors.cyan, Icons.people),
                _buildSummaryCard('إجمالي المبيعات', '1,250,000 ر.س', Colors.green, Icons.monetization_on),
                _buildSummaryCard('متوسط المبيعات', '4,385 ر.س', Colors.blue, Icons.calculate),
                _buildSummaryCard('أعلى مبيعات', '85,000 ر.س', Colors.orange, Icons.star),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoriesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.category, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'توزيع المبيعات حسب التصنيفات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildCategoryCard('إلكترونيات', '485,000 ر.س', Colors.blue),
                _buildCategoryCard('ملابس', '325,000 ر.س', Colors.green),
                _buildCategoryCard('أغذية', '285,000 ر.س', Colors.orange),
                _buildCategoryCard('أدوات منزلية', '155,000 ر.س', Colors.purple),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerSalesTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل مبيعات العملاء بالتصنيفات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('اسم العميل')),
                  DataColumn(label: Text('التصنيف')),
                  DataColumn(label: Text('عدد الأصناف')),
                  DataColumn(label: Text('الكمية')),
                  DataColumn(label: Text('مبلغ المبيعات')),
                  DataColumn(label: Text('متوسط السعر')),
                  DataColumn(label: Text('عدد المعاملات')),
                  DataColumn(label: Text('آخر شراء')),
                  DataColumn(label: Text('تقييم العميل')),
                ],
                rows: _buildCustomerSalesRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopCustomersSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.star, color: Colors.amber, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'أفضل العملاء حسب التصنيفات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildTopCustomersList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _createTargetedCampaign,
                    icon: const Icon(Icons.campaign),
                    label: const Text('حملة تسويقية مستهدفة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _customerSegmentation,
                    icon: const Icon(Icons.group),
                    label: const Text('تقسيم العملاء'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _personalizedOffers,
                    icon: const Icon(Icons.local_offer),
                    label: const Text('عروض شخصية'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _loyaltyProgram,
                    icon: const Icon(Icons.card_giftcard),
                    label: const Text('برنامج الولاء'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildTopCustomersList() {
    final topCustomers = [
      {'name': 'أحمد محمد', 'category': 'إلكترونيات', 'sales': '85,000 ر.س', 'transactions': '45', 'rank': '1'},
      {'name': 'فاطمة علي', 'category': 'ملابس', 'sales': '65,000 ر.س', 'transactions': '38', 'rank': '2'},
      {'name': 'محمد سالم', 'category': 'أغذية', 'sales': '55,000 ر.س', 'transactions': '52', 'rank': '3'},
      {'name': 'نورا أحمد', 'category': 'أدوات منزلية', 'sales': '45,000 ر.س', 'transactions': '28', 'rank': '4'},
    ];

    return topCustomers.map((customer) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: _getRankColor(customer['rank']!).withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: _getRankColor(customer['rank']!).withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: _getRankColor(customer['rank']!),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Center(
              child: Text(
                customer['rank']!,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  customer['name']!,
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  '${customer['category']} • ${customer['sales']} • ${customer['transactions']} معاملة',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          const Icon(Icons.star, color: Colors.amber, size: 20),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildCustomerSalesRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('أحمد محمد')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('25')),
        const DataCell(Text('125')),
        const DataCell(Text('85,000 ر.س')),
        const DataCell(Text('680 ر.س')),
        const DataCell(Text('45')),
        const DataCell(Text('2024-01-20')),
        DataCell(_buildRatingBadge('ممتاز', Colors.green)),
      ]),
      DataRow(cells: [
        const DataCell(Text('فاطمة علي')),
        const DataCell(Text('ملابس')),
        const DataCell(Text('35')),
        const DataCell(Text('185')),
        const DataCell(Text('65,000 ر.س')),
        const DataCell(Text('351 ر.س')),
        const DataCell(Text('38')),
        const DataCell(Text('2024-01-18')),
        DataCell(_buildRatingBadge('جيد جداً', Colors.blue)),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryCard(String category, String sales, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                sales,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(category, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRatingBadge(String rating, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(rating, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Color _getRankColor(String rank) {
    switch (rank) {
      case '1':
        return Colors.amber;
      case '2':
        return Colors.grey;
      case '3':
        return Colors.brown;
      case '4':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير مبيعات العملاء بالتصنيفات بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showAnalytics() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض التحليلات المتقدمة')),
    );
  }

  void _createTargetedCampaign() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء حملة تسويقية مستهدفة')),
    );
  }

  void _customerSegmentation() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تقسيم العملاء حسب التصنيفات')),
    );
  }

  void _personalizedOffers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء عروض شخصية للعملاء')),
    );
  }

  void _loyaltyProgram() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إدارة برنامج الولاء')),
    );
  }
}
