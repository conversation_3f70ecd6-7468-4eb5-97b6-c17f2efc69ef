import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة طباعة باركود لأصناف معينة
/// تتيح اختيار عدة أصناف وطباعة باركود لها دفعة واحدة
class PrintBarcodeSpecificItemsPage extends StatefulWidget {
  const PrintBarcodeSpecificItemsPage({super.key});

  @override
  State<PrintBarcodeSpecificItemsPage> createState() =>
      _PrintBarcodeSpecificItemsPageState();
}

class _PrintBarcodeSpecificItemsPageState
    extends State<PrintBarcodeSpecificItemsPage> {
  final _searchController = TextEditingController();

  String _searchQuery = '';
  String? _selectedCategory;
  String? _selectedBarcodeType;
  String? _selectedLabelSize;
  String? _selectedPrinter;
  bool _includeItemName = true;
  bool _includePrice = false;
  bool _includeDate = false;

  final List<String> _categories = [
    'إلكترونيات',
    'أثاث',
    'ملابس',
    'أدوات منزلية',
    'كتب ومجلات',
    'رياضة وترفيه',
  ];

  final List<Map<String, String>> _barcodeTypes = [
    {'id': 'code128', 'name': 'Code 128'},
    {'id': 'code39', 'name': 'Code 39'},
    {'id': 'ean13', 'name': 'EAN-13'},
    {'id': 'qr', 'name': 'QR Code'},
  ];

  final List<Map<String, String>> _labelSizes = [
    {'id': 'small', 'name': '2.5 × 1.5 سم'},
    {'id': 'medium', 'name': '4 × 2 سم'},
    {'id': 'large', 'name': '6 × 3 سم'},
  ];

  final List<Map<String, String>> _printers = [
    {'id': 'zebra1', 'name': 'Zebra ZD420'},
    {'id': 'brother1', 'name': 'Brother QL-800'},
    {'id': 'default', 'name': 'الطابعة الافتراضية'},
    {'id': 'pdf', 'name': 'حفظ كـ PDF'},
  ];

  final List<Map<String, dynamic>> _items = [
    {
      'id': 'item1',
      'code': 'A001',
      'name': 'لابتوب ديل XPS 13',
      'barcode': '1234567890123',
      'price': 2500.0,
      'category': 'إلكترونيات',
      'isSelected': false,
      'quantity': 1,
    },
    {
      'id': 'item2',
      'code': 'B002',
      'name': 'طابعة HP LaserJet Pro',
      'barcode': '2345678901234',
      'price': 450.0,
      'category': 'إلكترونيات',
      'isSelected': false,
      'quantity': 1,
    },
    {
      'id': 'item3',
      'code': 'C003',
      'name': 'كرسي مكتب جلد طبيعي',
      'barcode': '3456789012345',
      'price': 350.0,
      'category': 'أثاث',
      'isSelected': false,
      'quantity': 1,
    },
    {
      'id': 'item4',
      'code': 'D004',
      'name': 'شاشة سامسونج 27 بوصة',
      'barcode': '4567890123456',
      'price': 800.0,
      'category': 'إلكترونيات',
      'isSelected': false,
      'quantity': 1,
    },
    {
      'id': 'item5',
      'code': 'E005',
      'name': 'مكتب خشبي كلاسيكي',
      'barcode': '5678901234567',
      'price': 1200.0,
      'category': 'أثاث',
      'isSelected': false,
      'quantity': 1,
    },
    {
      'id': 'item6',
      'code': 'F006',
      'name': 'ماوس لاسلكي لوجيتك',
      'barcode': '6789012345678',
      'price': 85.0,
      'category': 'إلكترونيات',
      'isSelected': false,
      'quantity': 1,
    },
  ];

  List<Map<String, dynamic>> get _filteredItems {
    List<Map<String, dynamic>> filtered = _items;

    // فلتر الفئة
    if (_selectedCategory != null) {
      filtered = filtered
          .where((item) => item['category'] == _selectedCategory)
          .toList();
    }

    // البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered
          .where((item) =>
              item['name']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              item['code']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              item['barcode'].toString().contains(_searchQuery))
          .toList();
    }

    return filtered;
  }

  List<Map<String, dynamic>> get _selectedItems {
    return _items.where((item) => item['isSelected'] == true).toList();
  }

  int get _totalLabels {
    return _selectedItems.fold(
        0, (sum, item) => sum + (item['quantity'] as int));
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.printBarcodeSpecificItems),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.select_all),
            onPressed: _selectAllItems,
            tooltip: 'تحديد الكل',
          ),
          IconButton(
            icon: const Icon(Icons.clear_all),
            onPressed: _clearAllSelections,
            tooltip: 'إلغاء التحديد',
          ),
        ],
      ),
      body: Column(
        children: [
          // بطاقة البحث والفلترة
          Card(
            margin: const EdgeInsets.all(16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // شريط البحث
                  TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      labelText: 'البحث في الأصناف',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),

                  const SizedBox(height: 16),

                  // فلتر الفئة
                  DropdownButtonFormField<String>(
                    value: _selectedCategory,
                    decoration: const InputDecoration(
                      labelText: 'فلتر حسب الفئة',
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    ),
                    items: [
                      const DropdownMenuItem(
                          value: null, child: Text('جميع الفئات')),
                      ..._categories.map((category) => DropdownMenuItem(
                            value: category,
                            child: Text(category),
                          )),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedCategory = value;
                      });
                    },
                  ),
                ],
              ),
            ),
          ),

          // إحصائيات التحديد
          Card(
            margin: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatCard(
                      'المجموع', _filteredItems.length.toString(), Colors.blue),
                  _buildStatCard(
                      'المحدد', _selectedItems.length.toString(), Colors.green),
                  _buildStatCard(
                      'الملصقات', _totalLabels.toString(), Colors.purple),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // قائمة الأصناف
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _filteredItems.length,
              itemBuilder: (context, index) {
                final item = _filteredItems[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: Checkbox(
                      value: item['isSelected'],
                      onChanged: (value) {
                        setState(() {
                          item['isSelected'] = value ?? false;
                        });
                      },
                    ),
                    title: Text(
                      '${item['code']} - ${item['name']}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                            'الفئة: ${item['category']} | السعر: ${item['price']} ر.س'),
                        Text('باركود: ${item['barcode']}',
                            style: const TextStyle(fontSize: 12)),
                      ],
                    ),
                    trailing: item['isSelected']
                        ? Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.green.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '${item['quantity']} ملصق',
                              style: const TextStyle(
                                color: Colors.green,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          )
                        : null,
                    children: item['isSelected']
                        ? [
                            Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Row(
                                children: [
                                  const Text('عدد الملصقات:',
                                      style: TextStyle(
                                          fontWeight: FontWeight.bold)),
                                  const SizedBox(width: 16),
                                  SizedBox(
                                    width: 100,
                                    child: TextFormField(
                                      initialValue: item['quantity'].toString(),
                                      decoration: const InputDecoration(
                                        border: OutlineInputBorder(),
                                        contentPadding: EdgeInsets.symmetric(
                                            horizontal: 8, vertical: 8),
                                      ),
                                      keyboardType: TextInputType.number,
                                      onChanged: (value) {
                                        setState(() {
                                          item['quantity'] =
                                              int.tryParse(value) ?? 1;
                                        });
                                      },
                                    ),
                                  ),
                                  const Spacer(),
                                  ElevatedButton.icon(
                                    onPressed: () => _previewSingleItem(item),
                                    icon: const Icon(Icons.preview, size: 16),
                                    label: const Text('معاينة'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.blue,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ]
                        : [],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      bottomNavigationBar: _selectedItems.isNotEmpty
          ? Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.3),
                    spreadRadius: 1,
                    blurRadius: 5,
                    offset: const Offset(0, -3),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // إعدادات الطباعة
                  Row(
                    children: [
                      // نوع الباركود
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedBarcodeType,
                          decoration: const InputDecoration(
                            labelText: 'نوع الباركود',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 8),
                          ),
                          items: _barcodeTypes
                              .map<DropdownMenuItem<String>>((type) {
                            return DropdownMenuItem<String>(
                              value: type['id'],
                              child: Text(
                                type['name']!,
                                overflow: TextOverflow.ellipsis,
                              ),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedBarcodeType = value;
                            });
                          },
                        ),
                      ),

                      const SizedBox(width: 16),

                      // حجم الملصق
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedLabelSize,
                          decoration: const InputDecoration(
                            labelText: 'حجم الملصق',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 8),
                          ),
                          items:
                              _labelSizes.map<DropdownMenuItem<String>>((size) {
                            return DropdownMenuItem<String>(
                              value: size['id'],
                              child: Text(
                                size['name']!,
                                overflow: TextOverflow.ellipsis,
                              ),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedLabelSize = value;
                            });
                          },
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // الطابعة والخيارات
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedPrinter,
                          decoration: const InputDecoration(
                            labelText: 'الطابعة',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 8),
                          ),
                          items: _printers
                              .map<DropdownMenuItem<String>>((printer) {
                            return DropdownMenuItem<String>(
                              value: printer['id'],
                              child: Text(
                                printer['name']!,
                                overflow: TextOverflow.ellipsis,
                              ),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedPrinter = value;
                            });
                          },
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // خيارات إضافية
                  Wrap(
                    spacing: 8,
                    children: [
                      FilterChip(
                        label: const Text('اسم الصنف'),
                        selected: _includeItemName,
                        onSelected: (value) {
                          setState(() {
                            _includeItemName = value;
                          });
                        },
                      ),
                      FilterChip(
                        label: const Text('السعر'),
                        selected: _includePrice,
                        onSelected: (value) {
                          setState(() {
                            _includePrice = value;
                          });
                        },
                      ),
                      FilterChip(
                        label: const Text('التاريخ'),
                        selected: _includeDate,
                        onSelected: (value) {
                          setState(() {
                            _includeDate = value;
                          });
                        },
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // أزرار العمليات
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _previewAllBarcodes,
                          icon: const Icon(Icons.preview),
                          label: const Text('معاينة الكل'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _printAllBarcodes,
                          icon: const Icon(Icons.print),
                          label: Text('طباعة ($_totalLabels)'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.purple,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            )
          : null,
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Column(
      children: [
        CircleAvatar(
          backgroundColor: color,
          child: Text(
            value,
            style: const TextStyle(
                color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  void _selectAllItems() {
    setState(() {
      for (var item in _filteredItems) {
        item['isSelected'] = true;
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم تحديد ${_filteredItems.length} صنف')),
    );
  }

  void _clearAllSelections() {
    setState(() {
      for (var item in _items) {
        item['isSelected'] = false;
        item['quantity'] = 1;
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إلغاء جميع التحديدات')),
    );
  }

  void _previewSingleItem(Map<String, dynamic> item) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('معاينة باركود ${item['name']}')),
    );
  }

  void _previewAllBarcodes() {
    if (_selectedBarcodeType == null || _selectedLabelSize == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار نوع الباركود وحجم الملصق'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('معاينة $_totalLabels ملصق باركود')),
    );
  }

  void _printAllBarcodes() {
    if (_selectedBarcodeType == null ||
        _selectedLabelSize == null ||
        _selectedPrinter == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إكمال جميع الإعدادات المطلوبة'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الطباعة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('عدد الأصناف: ${_selectedItems.length}'),
            Text('إجمالي الملصقات: $_totalLabels'),
            Text(
                'نوع الباركود: ${_barcodeTypes.firstWhere((t) => t['id'] == _selectedBarcodeType)['name']}'),
            Text(
                'حجم الملصق: ${_labelSizes.firstWhere((s) => s['id'] == _selectedLabelSize)['name']}'),
            Text(
                'الطابعة: ${_printers.firstWhere((p) => p['id'] == _selectedPrinter)['name']}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إرسال الطباعة بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
              _clearAllSelections();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.purple),
            child: const Text('طباعة', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
