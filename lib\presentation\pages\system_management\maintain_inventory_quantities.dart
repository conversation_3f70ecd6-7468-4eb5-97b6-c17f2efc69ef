import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة صيانة كميات أصناف المخزون
/// تتيح فحص وإصلاح مشاكل الكميات في المخزون
class MaintainInventoryQuantitiesPage extends StatefulWidget {
  const MaintainInventoryQuantitiesPage({super.key});

  @override
  State<MaintainInventoryQuantitiesPage> createState() =>
      _MaintainInventoryQuantitiesPageState();
}

class _MaintainInventoryQuantitiesPageState
    extends State<MaintainInventoryQuantitiesPage> {
  final _formKey = GlobalKey<FormState>();

  String? _selectedWarehouse;
  String? _selectedCategory;
  String? _maintenanceType;
  bool _isProcessing = false;
  bool _autoFix = false;
  bool _createReport = true;

  final List<Map<String, String>> _warehouses = [
    {'id': 'all', 'name': 'جميع المخازن'},
    {'id': 'main', 'name': 'المخزن الرئيسي'},
    {'id': 'jeddah', 'name': 'مخزن جدة'},
    {'id': 'dammam', 'name': 'مخزن الدمام'},
    {'id': 'returns', 'name': 'مخزن المرتجعات'},
  ];

  final List<Map<String, String>> _categories = [
    {'id': 'all', 'name': 'جميع الفئات'},
    {'id': 'electronics', 'name': 'الإلكترونيات'},
    {'id': 'furniture', 'name': 'الأثاث المكتبي'},
    {'id': 'stationery', 'name': 'القرطاسية'},
    {'id': 'equipment', 'name': 'المعدات'},
  ];

  final List<Map<String, String>> _maintenanceTypes = [
    {
      'id': 'negative',
      'name': 'الكميات السالبة',
      'description': 'فحص الأصناف ذات الكميات السالبة'
    },
    {
      'id': 'zero',
      'name': 'الكميات الصفرية',
      'description': 'فحص الأصناف ذات الكميات الصفرية'
    },
    {
      'id': 'mismatch',
      'name': 'عدم تطابق الكميات',
      'description': 'مقارنة الكميات النظرية مع الفعلية'
    },
    {
      'id': 'duplicates',
      'name': 'الأصناف المكررة',
      'description': 'البحث عن أصناف مكررة في النظام'
    },
    {
      'id': 'orphaned',
      'name': 'الحركات المعلقة',
      'description': 'حركات بدون مستندات مرجعية'
    },
    {
      'id': 'all',
      'name': 'فحص شامل',
      'description': 'فحص جميع المشاكل المحتملة'
    },
  ];

  final List<Map<String, dynamic>> _detectedIssues = [
    {
      'id': 1,
      'type': 'negative',
      'item': 'لابتوب ديل XPS',
      'warehouse': 'المخزن الرئيسي',
      'issue': 'كمية سالبة: -5',
      'severity': 'high',
      'suggestion': 'تصحيح الكمية إلى 0'
    },
    {
      'id': 2,
      'type': 'mismatch',
      'item': 'طابعة HP',
      'warehouse': 'مخزن جدة',
      'issue': 'عدم تطابق: نظري 10، فعلي 8',
      'severity': 'medium',
      'suggestion': 'تحديث الكمية النظرية'
    },
    {
      'id': 3,
      'type': 'duplicates',
      'item': 'ماوس لاسلكي',
      'warehouse': 'متعدد',
      'issue': 'صنف مكرر في 3 مواقع',
      'severity': 'low',
      'suggestion': 'دمج الأصناف المكررة'
    },
    {
      'id': 4,
      'type': 'orphaned',
      'item': 'كيبورد ميكانيكي',
      'warehouse': 'مخزن الدمام',
      'issue': 'حركة بدون مستند مرجعي',
      'severity': 'medium',
      'suggestion': 'ربط الحركة بمستند أو حذفها'
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.maintainInventoryQuantities),
        backgroundColor: Colors.grey,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showAnalyticsReport,
            tooltip: 'تقرير التحليل',
          ),
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: _showMaintenanceHistory,
            tooltip: 'سجل الصيانة',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // بطاقة معايير الفحص
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'معايير الفحص',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 16),

                    Row(
                      children: [
                        // المخزن
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedWarehouse,
                            decoration: const InputDecoration(
                              labelText: 'المخزن',
                              prefixIcon: Icon(Icons.warehouse),
                              border: OutlineInputBorder(),
                            ),
                            items: _warehouses
                                .map<DropdownMenuItem<String>>((warehouse) {
                              return DropdownMenuItem<String>(
                                value: warehouse['id'],
                                child: Text(warehouse['name']!),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedWarehouse = value;
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'يرجى اختيار المخزن';
                              }
                              return null;
                            },
                          ),
                        ),

                        const SizedBox(width: 16),

                        // الفئة
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedCategory,
                            decoration: const InputDecoration(
                              labelText: 'فئة الأصناف',
                              prefixIcon: Icon(Icons.category),
                              border: OutlineInputBorder(),
                            ),
                            items: _categories
                                .map<DropdownMenuItem<String>>((category) {
                              return DropdownMenuItem<String>(
                                value: category['id'],
                                child: Text(category['name']!),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedCategory = value;
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'يرجى اختيار الفئة';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // نوع الصيانة
                    DropdownButtonFormField<String>(
                      value: _maintenanceType,
                      decoration: const InputDecoration(
                        labelText: 'نوع الصيانة',
                        prefixIcon: Icon(Icons.build),
                        border: OutlineInputBorder(),
                      ),
                      items: _maintenanceTypes
                          .map<DropdownMenuItem<String>>((type) {
                        return DropdownMenuItem<String>(
                          value: type['id'],
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(type['name']!),
                              Text(
                                type['description']!,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _maintenanceType = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار نوع الصيانة';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة خيارات الصيانة
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'خيارات الصيانة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SwitchListTile(
                      title: const Text('الإصلاح التلقائي'),
                      subtitle: const Text('إصلاح المشاكل البسيطة تلقائياً'),
                      value: _autoFix,
                      onChanged: (value) {
                        setState(() {
                          _autoFix = value;
                        });
                      },
                    ),
                    SwitchListTile(
                      title: const Text('إنشاء تقرير مفصل'),
                      subtitle:
                          const Text('إنشاء تقرير بجميع المشاكل المكتشفة'),
                      value: _createReport,
                      onChanged: (value) {
                        setState(() {
                          _createReport = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة المشاكل المكتشفة
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'المشاكل المكتشفة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 16),
                    if (_detectedIssues.isEmpty)
                      const Center(
                        child: Text(
                          'لم يتم اكتشاف أي مشاكل بعد\nقم بتشغيل الفحص أولاً',
                          textAlign: TextAlign.center,
                          style: TextStyle(color: Colors.grey),
                        ),
                      )
                    else
                      ...(_detectedIssues.map((issue) => Card(
                            margin: const EdgeInsets.symmetric(vertical: 4),
                            child: ListTile(
                              leading: CircleAvatar(
                                backgroundColor:
                                    _getSeverityColor(issue['severity']),
                                child: Icon(
                                  _getSeverityIcon(issue['severity']),
                                  color: Colors.white,
                                ),
                              ),
                              title: Text(issue['item']),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('المخزن: ${issue['warehouse']}'),
                                  Text('المشكلة: ${issue['issue']}'),
                                  Text(
                                    'الحل المقترح: ${issue['suggestion']}',
                                    style: const TextStyle(color: Colors.blue),
                                  ),
                                ],
                              ),
                              trailing: IconButton(
                                icon: const Icon(Icons.build,
                                    color: Colors.orange),
                                onPressed: () => _fixIssue(issue['id']),
                                tooltip: 'إصلاح المشكلة',
                              ),
                            ),
                          ))),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // معاينة العملية
            if (_selectedWarehouse != null &&
                _selectedCategory != null &&
                _maintenanceType != null)
              Card(
                color: Colors.grey.shade100,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'معاينة عملية الصيانة',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildPreviewRow('المخزن:', _getWarehouseName()),
                      _buildPreviewRow('الفئة:', _getCategoryName()),
                      _buildPreviewRow(
                          'نوع الصيانة:', _getMaintenanceTypeName()),
                      _buildPreviewRow(
                          'الإصلاح التلقائي:', _autoFix ? 'مفعل' : 'معطل'),
                      _buildPreviewRow(
                          'إنشاء تقرير:', _createReport ? 'نعم' : 'لا'),
                      _buildPreviewRow('المشاكل المكتشفة:',
                          '${_detectedIssues.length} مشكلة'),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 24),

            // أزرار العمليات
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isProcessing ? null : _startMaintenance,
                    icon: _isProcessing
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.search),
                    label: Text(_isProcessing ? 'جاري الفحص...' : 'بدء الفحص'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _detectedIssues.isEmpty ? null : _fixAllIssues,
                    icon: const Icon(Icons.build),
                    label: const Text('إصلاح الكل'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Color _getSeverityColor(String severity) {
    switch (severity) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.yellow;
      default:
        return Colors.grey;
    }
  }

  IconData _getSeverityIcon(String severity) {
    switch (severity) {
      case 'high':
        return Icons.error;
      case 'medium':
        return Icons.warning;
      case 'low':
        return Icons.info;
      default:
        return Icons.help;
    }
  }

  String _getWarehouseName() {
    if (_selectedWarehouse == null) return 'غير محدد';
    final warehouse =
        _warehouses.firstWhere((w) => w['id'] == _selectedWarehouse);
    return warehouse['name']!;
  }

  String _getCategoryName() {
    if (_selectedCategory == null) return 'غير محدد';
    final category =
        _categories.firstWhere((c) => c['id'] == _selectedCategory);
    return category['name']!;
  }

  String _getMaintenanceTypeName() {
    if (_maintenanceType == null) return 'غير محدد';
    final type =
        _maintenanceTypes.firstWhere((t) => t['id'] == _maintenanceType);
    return type['name']!;
  }

  Future<void> _startMaintenance() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isProcessing = true;
      });

      // محاكاة عملية الفحص
      await Future.delayed(const Duration(seconds: 3));

      setState(() {
        _isProcessing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('تم اكتشاف ${_detectedIssues.length} مشكلة في المخزون'),
            backgroundColor:
                _detectedIssues.isEmpty ? Colors.green : Colors.orange,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  void _fixIssue(int issueId) {
    setState(() {
      _detectedIssues.removeWhere((issue) => issue['id'] == issueId);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إصلاح المشكلة رقم $issueId'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _fixAllIssues() {
    final issueCount = _detectedIssues.length;
    setState(() {
      _detectedIssues.clear();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إصلاح جميع المشاكل ($issueCount مشكلة)'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _showAnalyticsReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تقرير التحليل')),
    );
  }

  void _showMaintenanceHistory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض سجل الصيانة')),
    );
  }
}
