import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير أصناف تمت إضافتها خلال فترة
/// تعرض الأصناف الجديدة التي تم إضافتها للنظام خلال فترة زمنية محددة
class ItemsAddedDuringPeriodReportPage extends StatefulWidget {
  const ItemsAddedDuringPeriodReportPage({super.key});

  @override
  State<ItemsAddedDuringPeriodReportPage> createState() =>
      _ItemsAddedDuringPeriodReportPageState();
}

class _ItemsAddedDuringPeriodReportPageState
    extends State<ItemsAddedDuringPeriodReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedCategory;
  String? _selectedUser;
  String? _sortBy = 'date';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.itemsAddedDuringPeriod),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.green[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.fromDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, true),
                        controller: TextEditingController(
                          text: _startDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.toDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, false),
                        controller: TextEditingController(
                          text: _endDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.category,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedCategory,
                        items: [
                          DropdownMenuItem(
                              value: 'all',
                              child: Text(localizations.allCategories)),
                          DropdownMenuItem(
                              value: 'electronics',
                              child: Text(localizations.electronics)),
                          DropdownMenuItem(
                              value: 'clothing',
                              child: Text(localizations.clothing)),
                          DropdownMenuItem(
                              value: 'food', child: Text(localizations.food)),
                        ],
                        onChanged: (value) =>
                            setState(() => _selectedCategory = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'ترتيب حسب',
                          border: OutlineInputBorder(),
                        ),
                        value: _sortBy,
                        items: const [
                          DropdownMenuItem(
                              value: 'date', child: Text('تاريخ الإضافة')),
                          DropdownMenuItem(
                              value: 'name', child: Text('اسم الصنف')),
                          DropdownMenuItem(
                              value: 'category', child: Text('التصنيف')),
                          DropdownMenuItem(
                              value: 'user', child: Text('المستخدم')),
                        ],
                        onChanged: (value) => setState(() => _sortBy = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.search),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // إحصائيات الأصناف الجديدة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إحصائيات الأصناف الجديدة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildStatCard(
                                  'إجمالي الأصناف الجديدة', '25', Colors.green),
                              _buildStatCard(
                                  'أصناف إلكترونيات', '12', Colors.blue),
                              _buildStatCard('أصناف ملابس', '8', Colors.purple),
                              _buildStatCard('أصناف أخرى', '5', Colors.orange),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول الأصناف الجديدة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل الأصناف المضافة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: [
                                DataColumn(label: Text(localizations.itemCode)),
                                DataColumn(label: Text(localizations.itemName)),
                                DataColumn(label: Text(localizations.category)),
                                DataColumn(label: Text('تاريخ الإضافة')),
                                DataColumn(label: Text('المستخدم')),
                                DataColumn(
                                    label: Text(localizations.costPrice)),
                                DataColumn(
                                    label: Text(localizations.sellingPrice)),
                                DataColumn(label: Text('الكمية الأولية')),
                                DataColumn(label: Text(localizations.status)),
                              ],
                              rows: _buildNewItemsRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // تحليل الإضافات حسب الفترة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تحليل الإضافات حسب الفترة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('الأسبوع')),
                                DataColumn(label: Text('عدد الأصناف')),
                                DataColumn(label: Text('إلكترونيات')),
                                DataColumn(label: Text('ملابس')),
                                DataColumn(label: Text('مواد غذائية')),
                                DataColumn(label: Text('أخرى')),
                              ],
                              rows: _buildWeeklyAnalysisRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // أكثر المستخدمين إضافة للأصناف
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'أكثر المستخدمين إضافة للأصناف',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildUserCard(
                                  'أحمد محمد', '8 أصناف', CustomColors.gold),
                              _buildUserCard(
                                  'سارة أحمد', '6 أصناف', CustomColors.silver),
                              _buildUserCard(
                                  'محمد علي', '5 أصناف', CustomColors.bronze),
                              _buildUserCard(
                                  'فاطمة خالد', '4 أصناف', Colors.grey),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<DataRow> _buildNewItemsRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('001')),
        const DataCell(Text('جهاز كمبيوتر محمول جديد')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('2024-01-15')),
        const DataCell(Text('أحمد محمد')),
        const DataCell(Text('2,500.00')),
        const DataCell(Text('3,200.00')),
        const DataCell(Text('10')),
        DataCell(_buildStatusBadge('نشط', Colors.green)),
      ]),
      DataRow(cells: [
        const DataCell(Text('002')),
        const DataCell(Text('قميص قطني')),
        const DataCell(Text('ملابس')),
        const DataCell(Text('2024-01-16')),
        const DataCell(Text('سارة أحمد')),
        const DataCell(Text('45.00')),
        const DataCell(Text('75.00')),
        const DataCell(Text('50')),
        DataCell(_buildStatusBadge('نشط', Colors.green)),
      ]),
      DataRow(cells: [
        const DataCell(Text('003')),
        const DataCell(Text('شاشة LED 24 بوصة')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('2024-01-17')),
        const DataCell(Text('محمد علي')),
        const DataCell(Text('800.00')),
        const DataCell(Text('1,200.00')),
        const DataCell(Text('15')),
        DataCell(_buildStatusBadge('نشط', Colors.green)),
      ]),
    ];
  }

  List<DataRow> _buildWeeklyAnalysisRows() {
    return [
      const DataRow(cells: [
        DataCell(Text('الأسبوع الأول')),
        DataCell(Text('8')),
        DataCell(Text('4')),
        DataCell(Text('2')),
        DataCell(Text('1')),
        DataCell(Text('1')),
      ]),
      const DataRow(cells: [
        DataCell(Text('الأسبوع الثاني')),
        DataCell(Text('6')),
        DataCell(Text('3')),
        DataCell(Text('2')),
        DataCell(Text('1')),
        DataCell(Text('0')),
      ]),
      const DataRow(cells: [
        DataCell(Text('الأسبوع الثالث')),
        DataCell(Text('7')),
        DataCell(Text('3')),
        DataCell(Text('2')),
        DataCell(Text('1')),
        DataCell(Text('1')),
      ]),
      const DataRow(cells: [
        DataCell(Text('الأسبوع الرابع')),
        DataCell(Text('4')),
        DataCell(Text('2')),
        DataCell(Text('2')),
        DataCell(Text('0')),
        DataCell(Text('0')),
      ]),
    ];
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUserCard(String name, String count, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(Icons.person, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                name,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                count,
                style: TextStyle(
                  fontSize: 12,
                  color: color,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(status,
          style: const TextStyle(color: Colors.white, fontSize: 12)),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء التقرير بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }
}

// ألوان مخصصة للمراكز
class CustomColors {
  static const Color gold = Color(0xFFFFD700);
  static const Color silver = Color(0xFFC0C0C0);
  static const Color bronze = Color(0xFFCD7F32);
}
