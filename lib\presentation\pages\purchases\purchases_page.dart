import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة المشتريات الرئيسية
/// تعرض ملخص المشتريات والإحصائيات السريعة
class PurchasesPage extends StatelessWidget {
  const PurchasesPage({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.purchases),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان الصفحة
            Text(
              localizations.purchases,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),

            // بطاقات الإحصائيات السريعة
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    title: localizations.totalPurchasesToday,
                    value: '8,750 ر.س',
                    icon: Icons.trending_down,
                    color: Colors.orange,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    title: localizations.invoiceCount,
                    value: '12',
                    icon: Icons.receipt,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // قائمة العمليات السريعة
            Text(
              localizations.quickOperations,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  _buildActionCard(
                    title: localizations.purchaseInvoice,
                    icon: Icons.receipt,
                    color: Colors.orange,
                    onTap: () => _navigateToPage(context, 'purchase_invoice'),
                  ),
                  _buildActionCard(
                    title: localizations.purchaseOrder,
                    icon: Icons.shopping_cart,
                    color: Colors.green,
                    onTap: () => _navigateToPage(context, 'purchase_order'),
                  ),
                  _buildActionCard(
                    title: localizations.requestPriceQuotes,
                    icon: Icons.request_quote,
                    color: Colors.purple,
                    onTap: () =>
                        _navigateToPage(context, 'request_price_quotes'),
                  ),
                  _buildActionCard(
                    title: localizations.purchaseReturnInvoice,
                    icon: Icons.assignment_return,
                    color: Colors.teal,
                    onTap: () =>
                        _navigateToPage(context, 'purchase_return_invoice'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const Spacer(),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة عملية
  Widget _buildActionCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 40,
                color: color,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// التنقل إلى صفحة معينة
  void _navigateToPage(BuildContext context, String page) {
    final localizations = AppLocalizations.of(context);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('${localizations.navigateTo} $page')),
    );
  }
}
