import 'package:flutter/material.dart';

class SupplyFromOutboundButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String? tooltip;
  final bool isLoading;
  final bool isDisabled;

  const SupplyFromOutboundButton({
    super.key,
    this.onPressed,
    this.tooltip = 'Supply From Outbound',
    this.isLoading = false,
    this.isDisabled = false,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip ?? 'Supply From Outbound',
      child: ElevatedButton.icon(
        onPressed: isDisabled || isLoading ? null : onPressed,
        icon: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              )
            : const Icon(Icons.outbound),
        label: const Text('Supply From Outbound'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.deepOrange.shade700,
          foregroundColor: Colors.white,
        ),
      ),
    );
  }
}
