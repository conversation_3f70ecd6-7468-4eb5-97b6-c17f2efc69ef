import 'package:flutter/material.dart';

/// صفحة إعدادات ضريبة القيمة المضافة
/// تتيح تكوين جميع إعدادات الضريبة في النظام
class VatTaxSettingsPage extends StatefulWidget {
  const VatTaxSettingsPage({super.key});

  @override
  State<VatTaxSettingsPage> createState() => _VatTaxSettingsPageState();
}

class _VatTaxSettingsPageState extends State<VatTaxSettingsPage> {
  final _formKey = GlobalKey<FormState>();
  final _vatRateController = TextEditingController(text: '15');
  final _vatNumberController = TextEditingController();
  final _companyNameController = TextEditingController();
  
  bool _isVatEnabled = true;
  bool _includeVatInPrice = false;
  bool _showVatOnInvoices = true;
  String _vatCalculationMethod = 'exclusive';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات ضريبة القيمة المضافة'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveVatSettings,
            tooltip: 'حفظ الإعدادات',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // بطاقة الحالة العامة
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'حالة ضريبة القيمة المضافة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    Row(
                      children: [
                        Expanded(
                          child: _buildStatusCard(
                            'حالة الضريبة',
                            _isVatEnabled ? 'مفعلة' : 'معطلة',
                            Icons.receipt,
                            _isVatEnabled ? Colors.green : Colors.red,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildStatusCard(
                            'نسبة الضريبة',
                            '${_vatRateController.text}%',
                            Icons.percent,
                            Colors.blue,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // بطاقة الإعدادات الأساسية
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'الإعدادات الأساسية',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // تفعيل الضريبة
                    SwitchListTile(
                      title: const Text('تفعيل ضريبة القيمة المضافة'),
                      subtitle: const Text('تطبيق الضريبة على جميع المعاملات'),
                      value: _isVatEnabled,
                      onChanged: (value) {
                        setState(() {
                          _isVatEnabled = value;
                        });
                      },
                    ),
                    
                    const Divider(),
                    
                    // نسبة الضريبة
                    TextFormField(
                      controller: _vatRateController,
                      decoration: const InputDecoration(
                        labelText: 'نسبة ضريبة القيمة المضافة (%)',
                        hintText: '15',
                        prefixIcon: Icon(Icons.percent),
                        border: OutlineInputBorder(),
                        helperText: 'النسبة المعتمدة في المملكة العربية السعودية: 15%',
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال نسبة الضريبة';
                        }
                        final rate = double.tryParse(value);
                        if (rate == null || rate < 0 || rate > 100) {
                          return 'يرجى إدخال نسبة صحيحة (0-100)';
                        }
                        return null;
                      },
                      onChanged: (value) {
                        setState(() {});
                      },
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // الرقم الضريبي
                    TextFormField(
                      controller: _vatNumberController,
                      decoration: const InputDecoration(
                        labelText: 'الرقم الضريبي للشركة',
                        hintText: '123456789012345',
                        prefixIcon: Icon(Icons.numbers),
                        border: OutlineInputBorder(),
                        helperText: 'الرقم الضريبي المسجل لدى هيئة الزكاة والضريبة والجمارك',
                      ),
                      validator: (value) {
                        if (_isVatEnabled && (value == null || value.isEmpty)) {
                          return 'يرجى إدخال الرقم الضريبي';
                        }
                        if (value != null && value.isNotEmpty && value.length != 15) {
                          return 'الرقم الضريبي يجب أن يكون 15 رقم';
                        }
                        return null;
                      },
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // اسم الشركة
                    TextFormField(
                      controller: _companyNameController,
                      decoration: const InputDecoration(
                        labelText: 'اسم الشركة المسجل',
                        hintText: 'شركة المثال للتجارة',
                        prefixIcon: Icon(Icons.business),
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // بطاقة طريقة الحساب
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'طريقة حساب الضريبة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // تضمين الضريبة في السعر
                    SwitchListTile(
                      title: const Text('تضمين الضريبة في السعر'),
                      subtitle: const Text('السعر المعروض يشمل الضريبة'),
                      value: _includeVatInPrice,
                      onChanged: (value) {
                        setState(() {
                          _includeVatInPrice = value;
                        });
                      },
                    ),
                    
                    // إظهار الضريبة في الفواتير
                    SwitchListTile(
                      title: const Text('إظهار الضريبة في الفواتير'),
                      subtitle: const Text('عرض تفاصيل الضريبة في الفواتير المطبوعة'),
                      value: _showVatOnInvoices,
                      onChanged: (value) {
                        setState(() {
                          _showVatOnInvoices = value;
                        });
                      },
                    ),
                    
                    const Divider(),
                    
                    // طريقة الحساب
                    const Text(
                      'طريقة الحساب:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    RadioListTile<String>(
                      title: const Text('حساب منفصل (السعر + الضريبة)'),
                      subtitle: const Text('مثال: 100 ر.س + 15 ر.س ضريبة = 115 ر.س'),
                      value: 'exclusive',
                      groupValue: _vatCalculationMethod,
                      onChanged: (value) {
                        setState(() {
                          _vatCalculationMethod = value!;
                        });
                      },
                    ),
                    RadioListTile<String>(
                      title: const Text('حساب مدمج (السعر يشمل الضريبة)'),
                      subtitle: const Text('مثال: 115 ر.س (يشمل 15 ر.س ضريبة)'),
                      value: 'inclusive',
                      groupValue: _vatCalculationMethod,
                      onChanged: (value) {
                        setState(() {
                          _vatCalculationMethod = value!;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // بطاقة معاينة الحساب
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'معاينة حساب الضريبة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildVatCalculationPreview(),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // أزرار الحفظ والإعادة
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _saveVatSettings,
                    icon: const Icon(Icons.save),
                    label: const Text('حفظ إعدادات الضريبة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _resetVatSettings,
                    icon: const Icon(Icons.refresh),
                    label: const Text('إعادة تعيين'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildVatCalculationPreview() {
    if (!_isVatEnabled) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: Text(
            'الضريبة غير مفعلة',
            style: TextStyle(color: Colors.grey),
          ),
        ),
      );
    }

    final basePrice = 100.0;
    final vatRate = double.tryParse(_vatRateController.text) ?? 15.0;
    
    double vatAmount;
    double totalPrice;
    double priceBeforeVat;
    
    if (_vatCalculationMethod == 'inclusive') {
      totalPrice = basePrice;
      vatAmount = basePrice * vatRate / (100 + vatRate);
      priceBeforeVat = basePrice - vatAmount;
    } else {
      priceBeforeVat = basePrice;
      vatAmount = basePrice * vatRate / 100;
      totalPrice = basePrice + vatAmount;
    }
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.withOpacity(0.2)),
      ),
      child: Column(
        children: [
          const Text(
            'مثال على فاتورة بقيمة 100 ر.س:',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('السعر قبل الضريبة:'),
              Text('${priceBeforeVat.toStringAsFixed(2)} ر.س'),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('ضريبة القيمة المضافة (${vatRate.toStringAsFixed(1)}%):'),
              Text('${vatAmount.toStringAsFixed(2)} ر.س'),
            ],
          ),
          const Divider(),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'الإجمالي شامل الضريبة:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(
                '${totalPrice.toStringAsFixed(2)} ر.س',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _saveVatSettings() {
    if (_formKey.currentState!.validate()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حفظ إعدادات ضريبة القيمة المضافة بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _resetVatSettings() {
    setState(() {
      _vatRateController.text = '15';
      _vatNumberController.clear();
      _companyNameController.clear();
      _isVatEnabled = true;
      _includeVatInPrice = false;
      _showVatOnInvoices = true;
      _vatCalculationMethod = 'exclusive';
    });
  }

  @override
  void dispose() {
    _vatRateController.dispose();
    _vatNumberController.dispose();
    _companyNameController.dispose();
    super.dispose();
  }
}
