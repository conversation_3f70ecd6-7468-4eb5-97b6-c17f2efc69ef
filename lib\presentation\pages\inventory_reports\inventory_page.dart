import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

// استيراد جميع صفحات التقارير
import 'item_movement_report.dart';
import 'item_movement_during_period_report.dart';
import 'item_movement_during_period_previous_year.dart';
import 'items_operations_report.dart';
import 'items_in_specific_location_report.dart';
import 'monthly_sales_average_report.dart';
import 'items_profit_report.dart';
import 'items_sold_below_cost_report.dart';
import 'items_added_during_period_report.dart';
import 'out_of_stock_items_report.dart';
import 'low_stock_items_report.dart';
import 'purchase_orders_memo_report.dart';
import 'inventory_valuation_report.dart';
import 'inventory_count_report.dart';
import 'branch_inventory_count_report.dart';
import 'compare_out_of_stock_between_warehouses_report.dart';
import 'due_invoices_report.dart';
import 'deferred_invoices_report.dart';
import 'print_item_labels_report.dart';
import 'customer_items_report.dart';
import 'beginning_inventory_balance_report.dart';
import 'inventory_shortage_report.dart';
import 'item_price_changes_report.dart';
import 'item_location_changes_report.dart';
import 'customer_sales_movement_report.dart';
import 'customer_debt_aging_report.dart';
import 'supplier_sales_movement_report.dart';
import 'supplier_purchases_movement_report.dart';
import 'print_clearances_report.dart';
import 'print_warehouse_invoices_report.dart';
import 'print_clearance_stickers_report.dart';
import 'selective_item_printing_report.dart';
import 'purchase_items_monitoring_report.dart';
import 'purchase_items_monitoring_previous_year_report.dart';
import 'item_requests_during_period_report.dart';
import 'item_categories_tree_report.dart';
import 'items_below_cost_forecast_report.dart';
import 'branch_sales_movement_report.dart';
import 'category_a_sales_commission_report.dart';
import 'sales_plan_achievement_report.dart';
import 'detailed_items_report.dart';
import 'external_item_requirements_report.dart';
import 'items_sold_below_price_report.dart';
import 'items_sold_above_price_report.dart';
import 'available_reservations_report.dart';
import 'available_quantities_report.dart';
import 'item_reservations_report.dart';
import 'reservation_voucher_sales_report.dart';
import 'item_reservation_voucher_movement_report.dart';
import 'damaged_item_exit_vouchers_report.dart';
import 'item_classification_changes_report.dart';
import 'daily_sales_purchases_details_report.dart';
import 'average_selling_prices_report.dart';
import 'purchased_items_profit_report.dart';
import 'purchase_orders_remaining_quantities_report.dart';
import 'customer_sales_by_categories_report.dart';
import 'items_with_images_report.dart';
import 'items_without_images_report.dart';
import 'items_expiry_dates_report.dart';
import 'specific_items_quantities_report.dart';
import 'cost_selling_price_deviation_report.dart';
import 'sales_representatives_evaluation_report.dart';
import 'suppliers_detailed_movement_report.dart';
import 'items_exit_vouchers_detailed_report.dart';
import 'inventory_aging_report.dart';
import 'customer_duplicate_items_report.dart';
import 'net_sales_period_report.dart';
import 'customer_movement_period_report.dart';
import 'unlinked_payments_period_report.dart';
import 'branch_sales_movement_2_report.dart';
import 'items_sales_period_report.dart';
import 'supplier_purchase_invoices_by_category_report.dart';

/// كلاس لتمثيل عنصر التقرير
class ReportItem {
  final int number;
  final String title;
  final String description;
  final String route;
  final String category;

  ReportItem(
      this.number, this.title, this.description, this.route, this.category);
}

/// صفحة تقارير المخزون الرئيسية
/// تعرض جميع التقارير المتعلقة بالمخزون والأصناف
class InventoryPage extends StatefulWidget {
  const InventoryPage({super.key});

  @override
  State<InventoryPage> createState() => _InventoryPageState();
}

class _InventoryPageState extends State<InventoryPage> {
  String _searchQuery = '';
  String _selectedCategory = 'all';
  List<ReportItem> _filteredReports = [];
  List<ReportItem> _allReports = [];
  bool _showAdvancedFilter = false;
  String _selectedPriority = 'all';
  String _selectedStatus = 'all';

  @override
  void initState() {
    super.initState();
    _initializeReports();
    _filteredReports = _allReports;
  }

  void _initializeReports() {
    _allReports = [
      ReportItem(
          1,
          'تقرير حركة الأصناف',
          'تقرير شامل لحركة جميع الأصناف في المخزون',
          'items_movement_report',
          'movement'),
      ReportItem(
          2,
          'تقرير حركة صنف خلال فترة',
          'تقرير مفصل لحركة صنف معين خلال فترة محددة',
          'item_movement_during_period_report',
          'movement'),
      ReportItem(
          3,
          'تقرير حركة صنف خلال فترة لسنة سابقة',
          'تقرير حركة صنف معين للسنة السابقة',
          'item_movement_during_period_previous_year',
          'movement'),
      ReportItem(
          4,
          'تقرير عمليات الأصناف',
          'تقرير شامل لجميع العمليات على الأصناف',
          'items_operations_report',
          'movement'),
      ReportItem(
          5,
          'تقرير بأصناف في موقع معين',
          'الأصناف الموجودة في موقع أو مستودع معين',
          'items_in_specific_location_report',
          'balance'),
      ReportItem(
          6,
          'تقرير المعدل الشهري لمبيعات الأصناف',
          'متوسط المبيعات الشهرية للأصناف',
          'monthly_sales_average_report',
          'analysis'),
      ReportItem(7, 'تقرير أرباح الأصناف', 'تقرير الأرباح المحققة من الأصناف',
          'items_profit_report', 'financial'),
      ReportItem(
          8,
          'تقرير أصناف بيعت أقل من التكلفة',
          'الأصناف التي تم بيعها بأقل من سعر التكلفة',
          'items_sold_below_cost_report',
          'analysis'),
      ReportItem(
          9,
          'تقرير أصناف تمت إضافتها خلال فترة',
          'الأصناف التي تم إضافتها للنظام خلال فترة زمنية محددة',
          'items_added_during_period_report',
          'movement'),
      ReportItem(
          10,
          'تقرير أصناف منتهية الكميات',
          'الأصناف التي انتهت كمياتها من المخزون',
          'out_of_stock_items_report',
          'balance'),
      ReportItem(
          11,
          'تقرير الأصناف ناقصة الكميات',
          'الأصناف التي وصلت للحد الأدنى من الكمية',
          'low_stock_items_report',
          'balance'),
      ReportItem(12, 'مذكرة الطلبات', 'مذكرة بالطلبات المطلوبة للأصناف',
          'purchase_orders_memo_report', 'movement'),
      ReportItem(13, 'تقييم المخزون', 'تقييم قيمة المخزون بطرق مختلفة',
          'inventory_valuation_report', 'operational'),
      ReportItem(14, 'قوائم الجرد', 'قوائم الجرد الدورية والاستثنائية للمخزون',
          'inventory_count_report', 'operational'),
      ReportItem(15, 'جرد مستودعات فرع معين', 'تفاصيل جرد مستودعات فرع محدد',
          'branch_inventory_count_report', 'operational'),
      ReportItem(
          16,
          'مقارنة الأصناف منتهية الكميات بين مستودعين',
          'مقارنة الأصناف منتهية الكميات بين مستودعين مختلفين',
          'compare_out_of_stock_between_warehouses_report',
          'analysis'),
      ReportItem(
          17,
          'الفواتير المستحقة',
          'تقرير الفواتير المستحقة السداد مع تفاصيل العملاء والمبالغ',
          'due_invoices_report',
          'financial'),
      ReportItem(
          18,
          'الفواتير الآجلة',
          'تقرير الفواتير المؤجلة السداد مع تفاصيل الدفع المؤجل',
          'deferred_invoices_report',
          'financial'),
      ReportItem(
          19,
          'طباعة ملصقات الأصناف',
          'طباعة ملصقات للأصناف مع الباركود والمعلومات الأساسية',
          'print_item_labels_report',
          'printing'),
      ReportItem(
          20,
          'تقرير أصناف العملاء',
          'الأصناف المرتبطة بعملاء معينين وتفضيلاتهم الشرائية',
          'customer_items_report',
          'analysis'),
      ReportItem(
          21,
          'باقي البضاعة أول المدة',
          'تقرير بأرصدة البضاعة المتبقية من بداية الفترة المحاسبية',
          'beginning_inventory_balance_report',
          'balance'),
      ReportItem(
          22,
          'كشف نواقص بضاعة',
          'تقرير بالأصناف التي تعاني من نقص في الكميات',
          'inventory_shortage_report',
          'balance'),
      ReportItem(
          23,
          'تغير أسعار الأصناف',
          'تقرير بتغييرات أسعار الأصناف خلال فترة معينة',
          'item_price_changes_report',
          'movement'),
      ReportItem(
          24,
          'تغير مواقع الأصناف',
          'تقرير بتغييرات مواقع الأصناف في المستودعات',
          'item_location_changes_report',
          'movement'),
      ReportItem(
          25,
          'حركة مبيعات العملاء',
          'تقرير تفصيلي بحركة مبيعات العملاء والأصناف المباعة',
          'customer_sales_movement_report',
          'movement'),
      ReportItem(
          26,
          'تقرير متوسط أعمار الديون للعملاء',
          'تحليل أعمار الديون المستحقة على العملاء',
          'customer_debt_aging_report',
          'analysis'),
      ReportItem(
          27,
          'حركة مبيعات الموردين',
          'تحليل مبيعات الأصناف حسب الموردين',
          'supplier_sales_movement_report',
          'movement'),
      ReportItem(28, 'حركة مشتريات الموردين', 'تحليل المشتريات من الموردين',
          'supplier_purchases_movement_report', 'movement'),
      ReportItem(29, 'طباعة الفسوحات', 'طباعة فسوحات البضائع الجمركية',
          'print_clearances_report', 'printing'),
      ReportItem(30, 'طباعة الفواتير من المستودع', 'طباعة فواتير المستودع',
          'print_warehouse_invoices_report', 'printing'),
      ReportItem(31, 'طباعة استكر لفسوحات', 'طباعة ملصقات الفسوحات الجمركية',
          'print_clearance_stickers_report', 'printing'),
      ReportItem(32, 'طباعة إختيارية للأصناف', 'طباعة قوائم مخصصة للأصناف',
          'selective_item_printing_report', 'printing'),
      ReportItem(33, 'مراقبة أصناف المشتريات', 'تحليل ومراقبة أصناف المشتريات',
          'purchase_items_monitoring_report', 'monitoring'),
      ReportItem(
          34,
          'مراقبة أصناف المشتريات لسنة سابقة',
          'تحليل ومقارنة أصناف المشتريات للسنة السابقة',
          'purchase_items_monitoring_previous_year_report',
          'monitoring'),
      ReportItem(
          35,
          'طلب الأصناف خلال فترة',
          'تقرير طلبات الأصناف خلال فترة محددة',
          'item_requests_during_period_report',
          'movement'),
      ReportItem(
          36,
          'تقرير شجرة تصنيفات الأصناف',
          'عرض التصنيفات في شكل شجرة هرمية',
          'item_categories_tree_report',
          'analysis'),
      ReportItem(
          37,
          'تقرير أصناف بيعت أقل من التكلفة المتوقعة',
          'الأصناف التي بيعت بأسعار أقل من التكلفة المتوقعة',
          'items_below_cost_forecast_report',
          'analysis'),
      ReportItem(
          38,
          'حركة مبيعات الفروع',
          'تقرير تفصيلي بحركة مبيعات جميع الفروع',
          'branch_sales_movement_report',
          'movement'),
      ReportItem(
          39,
          'تقرير عمولة مبيعات الفئة أ',
          'تقرير عمولات المبيعات للأصناف من الفئة أ',
          'category_a_sales_commission_report',
          'analysis'),
      ReportItem(
          40,
          'تقرير إنجاز خطة المبيعات',
          'مقارنة المبيعات الفعلية مع الخطة المحددة',
          'sales_plan_achievement_report',
          'analysis'),
      ReportItem(
          41,
          'تقرير مفصل للأصناف',
          'تقرير شامل ومفصل لجميع بيانات الأصناف',
          'detailed_items_report',
          'analysis'),
      ReportItem(
          42,
          'تقرير بالكميات المطلوبة خارجي لصنف',
          'الكميات المطلوبة من مصادر خارجية لصنف معين',
          'external_item_requirements_report',
          'movement'),
      ReportItem(
          43,
          'تقرير أصناف بيعت أقل من سعر البيع',
          'الأصناف التي تم بيعها بأقل من سعر البيع المحدد',
          'items_sold_below_price_report',
          'analysis'),
      ReportItem(
          44,
          'تقرير أصناف بيعت أكبر من سعر البيع',
          'الأصناف التي تم بيعها بأعلى من سعر البيع المحدد',
          'items_sold_above_price_report',
          'analysis'),
      ReportItem(
          45,
          'تقرير بالحجوزات المتاحة للبيع',
          'الحجوزات المتاحة والقابلة للبيع حالياً',
          'available_reservations_report',
          'balance'),
      ReportItem(
          46,
          'تقرير بالكميات المتاحة',
          'الكميات المتاحة للبيع في جميع المستودعات',
          'available_quantities_report',
          'balance'),
      ReportItem(
          47,
          'تقرير بحجوزات الأصناف',
          'تفاصيل حجوزات الأصناف والكميات المحجوزة',
          'item_reservations_report',
          'balance'),
      ReportItem(48, 'تقرير مبيعات سند حجز', 'المبيعات المرتبطة بسندات الحجز',
          'reservation_voucher_sales_report', 'movement'),
      ReportItem(
          49,
          'حركة سندات الحجز لصنف',
          'تفاصيل حركة سندات الحجز لصنف معين',
          'item_reservation_voucher_movement_report',
          'movement'),
      ReportItem(
          50,
          'تقرير سندات الإخراج لصنف تالف',
          'سندات إخراج الأصناف التالفة من المخزون',
          'damaged_item_exit_vouchers_report',
          'movement'),
      ReportItem(51, 'تغير تصنيفات الأصناف', 'تقرير بتغييرات تصنيفات الأصناف',
          'item_classification_changes_report', 'movement'),
      ReportItem(
          52,
          'تقرير تفاصيل المبيعات والمشتريات اليومية',
          'تفاصيل شاملة للمبيعات والمشتريات اليومية',
          'daily_sales_purchases_details_report',
          'movement'),
      ReportItem(
          53,
          'متوسط أسعار البيع للأصناف',
          'تقرير بمتوسط أسعار البيع لجميع الأصناف',
          'average_selling_prices_report',
          'analysis'),
      ReportItem(
          54,
          'أرباح الأصناف المشتراة خلال فترة',
          'تحليل أرباح الأصناف التي تم شراؤها خلال فترة محددة',
          'purchased_items_profit_report',
          'financial'),
      ReportItem(
          55,
          'متابعة الكميات المتبقية لطلبات الشراء',
          'الكميات المتبقية من طلبات الشراء المعلقة',
          'purchase_orders_remaining_quantities_report',
          'movement'),
      ReportItem(
          56,
          'حركة مبيعات العملاء بالتصنيفات',
          'تحليل مبيعات العملاء مقسمة حسب تصنيفات الأصناف',
          'customer_sales_by_categories_report',
          'analysis'),
      ReportItem(
          57,
          'الأصناف التي لها صور',
          'قائمة بالأصناف التي تحتوي على صور في النظام',
          'items_with_images_report',
          'analysis'),
      ReportItem(
          58,
          'الأصناف التي ليس لها صور',
          'قائمة بالأصناف التي لا تحتوي على صور في النظام',
          'items_without_images_report',
          'analysis'),
      ReportItem(
          59,
          'تقرير بتاريخ صلاحية الأصناف',
          'الأصناف مع تواريخ انتهاء الصلاحية',
          'items_expiry_dates_report',
          'balance'),
      ReportItem(
          60,
          'تقرير بالكميات لأصناف معينة',
          'كميات أصناف محددة في المستودعات',
          'specific_items_quantities_report',
          'balance'),
      ReportItem(
          61,
          'تقرير تقييم انحراف التكلفة وسعر البيع',
          'تحليل الانحراف بين التكلفة الفعلية وسعر البيع',
          'cost_selling_price_deviation_report',
          'analysis'),
      ReportItem(
          62,
          'تقرير تقييم المندوبين',
          'تقييم أداء المندوبين في المبيعات',
          'sales_representatives_evaluation_report',
          'analysis'),
      ReportItem(
          63,
          'تقرير تفصيلي بحركة الموردين خلال فترة',
          'حركة تفصيلية للموردين والمشتريات خلال فترة محددة',
          'suppliers_detailed_movement_report',
          'movement'),
      ReportItem(
          64,
          'حركة تفصيلية للأصناف بسندات الإخراج',
          'تفاصيل حركة الأصناف في سندات الإخراج',
          'items_exit_vouchers_detailed_report',
          'movement'),
      ReportItem(65, 'تقرير أعمار المخزون', 'تحليل أعمار الأصناف في المخزون',
          'inventory_aging_report', 'analysis'),
      ReportItem(
          66,
          'تقرير بالأصناف المكررة للعميل',
          'الأصناف المكررة في طلبات العميل',
          'customer_duplicate_items_report',
          'analysis'),
      ReportItem(
          67,
          'تقرير صافي المبيعات خلال فترة',
          'صافي المبيعات بعد خصم المرتجعات والخصومات',
          'net_sales_period_report',
          'analysis'),
      ReportItem(
          68,
          'تقرير حركة العملاء خلال فترة',
          'تفاصيل حركة العملاء والمعاملات خلال فترة محددة',
          'customer_movement_period_report',
          'movement'),
      ReportItem(
          69,
          'تقرير السدادات الغير مرتبطة بفاتورة خلال فترة',
          'السدادات التي لم يتم ربطها بفواتير محددة',
          'unlinked_payments_period_report',
          'movement'),
      ReportItem(70, 'حركة مبيعات الفروع 2', 'تقرير إضافي لحركة مبيعات الفروع',
          'branch_sales_movement_2_report', 'movement'),
      ReportItem(
          71,
          'تقرير مبيعات الأصناف خلال فترة',
          'تفاصيل مبيعات الأصناف خلال فترة زمنية محددة',
          'items_sales_period_report',
          'movement'),
      ReportItem(
          72,
          'تقرير فواتير مشتريات الموردين حسب التصنيفات',
          'فواتير المشتريات من الموردين مقسمة حسب التصنيفات',
          'supplier_purchase_invoices_by_category_report',
          'movement'),
    ];
  }

  void _filterReports() {
    setState(() {
      _filteredReports = _allReports.where((report) {
        // فلترة البحث
        final matchesSearch =
            report.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                report.description
                    .toLowerCase()
                    .contains(_searchQuery.toLowerCase()) ||
                report.number.toString().contains(_searchQuery);

        // فلترة الفئة
        final matchesCategory =
            _selectedCategory == 'all' || report.category == _selectedCategory;

        // فلترة النوع (متقدمة)
        final matchesPriority = _selectedPriority == 'all' ||
            _getReportType(report) == _selectedPriority;

        // فلترة الحالة (متقدمة)
        final matchesStatus = _selectedStatus == 'all' ||
            _getReportStatus(report) == _selectedStatus;

        return matchesSearch &&
            matchesCategory &&
            matchesPriority &&
            matchesStatus;
      }).toList();
    });
  }

  /// تحديد نوع التقرير للفلترة المتقدمة
  String _getReportType(ReportItem report) {
    // تصنيف التقارير حسب النوع
    if (report.title.contains('يومية') || report.title.contains('اليومي')) {
      return 'daily';
    } else if (report.title.contains('شهري') ||
        report.title.contains('الشهري')) {
      return 'monthly';
    } else if (report.title.contains('سنة') || report.title.contains('سنوي')) {
      return 'yearly';
    } else if (report.title.contains('مخصص') ||
        report.title.contains('اختياري')) {
      return 'custom';
    }
    return 'all';
  }

  /// تحديد حالة التقرير للفلترة المتقدمة
  String _getReportStatus(ReportItem report) {
    // تصنيف التقارير حسب الحالة
    if (report.number <= 20) {
      return 'popular'; // التقارير الأكثر استخداماً
    } else if (report.number >= 60) {
      return 'new'; // التقارير الجديدة
    } else if (report.category == 'analysis') {
      return 'updated'; // تقارير التحليل محدثة
    }
    return 'active'; // باقي التقارير نشطة
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.inventoryReports),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          // شريط البحث والفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.teal.shade50,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                // مربع البحث
                TextField(
                  decoration: InputDecoration(
                    hintText: localizations.searchReports,
                    prefixIcon: const Icon(Icons.search),
                    border: const OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                    _filterReports();
                  },
                ),
                const SizedBox(height: 12),
                // قائمة الفلترة الأساسية
                Row(
                  children: [
                    Text('${localizations.filterByCategory}: '),
                    Expanded(
                      child: DropdownButton<String>(
                        value: _selectedCategory,
                        isExpanded: true,
                        items: [
                          DropdownMenuItem(
                              value: 'all',
                              child: Text(localizations.allReports)),
                          DropdownMenuItem(
                              value: 'movement',
                              child: Text(localizations.movementReports)),
                          DropdownMenuItem(
                              value: 'balance',
                              child: Text(localizations.balanceReports)),
                          DropdownMenuItem(
                              value: 'analysis',
                              child: Text(localizations.analysisReports)),
                          DropdownMenuItem(
                              value: 'printing',
                              child: Text(localizations.printingReports)),
                          DropdownMenuItem(
                              value: 'monitoring',
                              child: Text(localizations.monitoringReports)),
                          DropdownMenuItem(
                              value: 'financial',
                              child: Text(localizations.financialReports)),
                          DropdownMenuItem(
                              value: 'operational',
                              child: Text(localizations.operationalReports)),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedCategory = value ?? 'all';
                          });
                          _filterReports();
                        },
                      ),
                    ),
                    // زر الفلتر المتقدم
                    IconButton(
                      icon: Icon(
                        _showAdvancedFilter
                            ? Icons.expand_less
                            : Icons.expand_more,
                        color: Colors.teal,
                      ),
                      onPressed: () {
                        setState(() {
                          _showAdvancedFilter = !_showAdvancedFilter;
                        });
                      },
                      tooltip: localizations.advancedFilter,
                    ),
                  ],
                ),
                // الفلتر المتقدم
                if (_showAdvancedFilter) ...[
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          localizations.advancedFilter,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 8),
                        // فلترة حسب النوع
                        Row(
                          children: [
                            Text('${localizations.reportType}: '),
                            Expanded(
                              child: DropdownButton<String>(
                                value: _selectedPriority,
                                isExpanded: true,
                                items: [
                                  DropdownMenuItem(
                                      value: 'all',
                                      child: Text(localizations.allTypes)),
                                  DropdownMenuItem(
                                      value: 'daily',
                                      child: Text(localizations.dailyReports)),
                                  DropdownMenuItem(
                                      value: 'monthly',
                                      child:
                                          Text(localizations.monthlyReports)),
                                  DropdownMenuItem(
                                      value: 'yearly',
                                      child: Text(localizations.yearlyReports)),
                                  DropdownMenuItem(
                                      value: 'custom',
                                      child: Text(localizations.customReports)),
                                ],
                                onChanged: (value) {
                                  setState(() {
                                    _selectedPriority = value ?? 'all';
                                  });
                                  _filterReports();
                                },
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        // فلترة حسب الحالة
                        Row(
                          children: [
                            Text('${localizations.reportStatus}: '),
                            Expanded(
                              child: DropdownButton<String>(
                                value: _selectedStatus,
                                isExpanded: true,
                                items: [
                                  DropdownMenuItem(
                                      value: 'all',
                                      child: Text(localizations.allStatuses)),
                                  DropdownMenuItem(
                                      value: 'active',
                                      child: Text(localizations.activeStatus)),
                                  DropdownMenuItem(
                                      value: 'popular',
                                      child: Text(localizations.popularStatus)),
                                  DropdownMenuItem(
                                      value: 'new',
                                      child: Text(localizations.newStatus)),
                                  DropdownMenuItem(
                                      value: 'updated',
                                      child: Text(localizations.updatedStatus)),
                                ],
                                onChanged: (value) {
                                  setState(() {
                                    _selectedStatus = value ?? 'all';
                                  });
                                  _filterReports();
                                },
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        // أزرار الفلتر
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            ElevatedButton.icon(
                              onPressed: () {
                                setState(() {
                                  _selectedCategory = 'all';
                                  _selectedPriority = 'all';
                                  _selectedStatus = 'all';
                                  _searchQuery = '';
                                });
                                _filterReports();
                              },
                              icon: const Icon(Icons.clear),
                              label: Text(localizations.clearFilter),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.grey.shade200,
                                foregroundColor: Colors.black87,
                              ),
                            ),
                            ElevatedButton.icon(
                              onPressed: _filterReports,
                              icon: const Icon(Icons.filter_list),
                              label: Text(localizations.applyFilter),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.teal,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
          // قائمة التقارير
          Expanded(
            child: _filteredReports.isEmpty
                ? Center(
                    child: Text(
                      localizations.noReportsFound,
                      style: const TextStyle(fontSize: 16, color: Colors.grey),
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    itemCount: _filteredReports.length,
                    itemBuilder: (context, index) {
                      final report = _filteredReports[index];
                      return _buildNumberedReportItem(
                        number: report.number,
                        title: report.title,
                        description: report.description,
                        icon: _getIconForCategory(report.category),
                        color: _getColorForCategory(report.category),
                        onTap: () {
                          try {
                            print(
                                '🎯 تم الضغط على التقرير رقم ${report.number}: ${report.title}');
                            print('🔗 المسار: ${report.route}');
                            _navigateToReport(context, report.route);
                          } catch (e) {
                            print('❌ خطأ عند الضغط على التقرير: $e');
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                    '${localizations.reportNotAvailable}: $e'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        },
                      );
                    },
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _generateCustomReport,
        backgroundColor: Colors.teal,
        icon: const Icon(Icons.add_chart),
        label: Text(localizations.customReport),
      ),
    );
  }

  /// دالة لإرجاع الأيقونة المناسبة حسب الفئة
  IconData _getIconForCategory(String category) {
    switch (category) {
      case 'movement':
        return Icons.trending_up;
      case 'balance':
        return Icons.inventory;
      case 'analysis':
        return Icons.analytics;
      case 'printing':
        return Icons.print;
      case 'monitoring':
        return Icons.monitor;
      case 'financial':
        return Icons.attach_money;
      case 'operational':
        return Icons.settings;
      default:
        return Icons.description;
    }
  }

  /// دالة لإرجاع اللون المناسب حسب الفئة
  Color _getColorForCategory(String category) {
    switch (category) {
      case 'movement':
        return Colors.blue;
      case 'balance':
        return Colors.green;
      case 'analysis':
        return Colors.orange;
      case 'printing':
        return Colors.purple;
      case 'monitoring':
        return Colors.red;
      case 'financial':
        return Colors.teal;
      case 'operational':
        return Colors.indigo;
      default:
        return Colors.grey;
    }
  }

  /// بناء عنصر تقرير مرقم
  Widget _buildNumberedReportItem({
    required int number,
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      elevation: 2,
      child: ListTile(
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '$number',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Icon(icon, color: color, size: 20),
            ],
          ),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
        subtitle: Text(
          description,
          style: const TextStyle(fontSize: 12),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          color: color,
          size: 16,
        ),
        onTap: onTap,
      ),
    );
  }

  /// التنقل إلى صفحة التقرير
  void _navigateToReport(BuildContext context, String report) {
    final localizations = AppLocalizations.of(context);

    // إضافة رسالة تشخيصية
    print('🔍 محاولة التنقل إلى التقرير: $report');

    // إظهار رسالة مؤقتة للمستخدم
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${localizations.openingReport}: $report'),
        duration: const Duration(seconds: 1),
      ),
    );

    Widget targetPage;

    switch (report) {
      case 'items_movement_report':
        targetPage = const ItemMovementReport();
        break;
      case 'item_movement_during_period_report':
        targetPage = const ItemMovementDuringPeriodReport();
        break;
      case 'item_movement_during_period_previous_year':
        targetPage = const ItemMovementDuringPeriodPreviousYearPage();
        break;
      case 'items_operations_report':
        targetPage = const ItemsOperationsReportPage();
        break;
      case 'items_in_specific_location_report':
        targetPage = const ItemsInSpecificLocationReportPage();
        break;
      case 'monthly_sales_average_report':
        targetPage = const MonthlySalesAverageReportPage();
        break;
      case 'items_profit_report':
        targetPage = const ItemsProfitReportPage();
        break;
      case 'items_sold_below_cost_report':
        targetPage = const ItemsSoldBelowCostReportPage();
        break;
      case 'items_added_during_period_report':
        targetPage = const ItemsAddedDuringPeriodReportPage();
        break;
      case 'out_of_stock_items_report':
        targetPage = const OutOfStockItemsReportPage();
        break;
      case 'low_stock_items_report':
        targetPage = const LowStockItemsReportPage();
        break;
      case 'purchase_orders_memo_report':
        targetPage = const PurchaseOrdersMemoReportPage();
        break;
      case 'inventory_valuation_report':
        targetPage = const InventoryValuationReportPage();
        break;
      case 'inventory_count_report':
        targetPage = const InventoryCountReportPage();
        break;
      case 'branch_inventory_count_report':
        targetPage = const BranchInventoryCountReportPage();
        break;
      case 'compare_out_of_stock_between_warehouses_report':
        targetPage = const CompareOutOfStockBetweenWarehousesReportPage();
        break;
      case 'due_invoices_report':
        targetPage = const DueInvoicesReportPage();
        break;
      case 'deferred_invoices_report':
        targetPage = const DeferredInvoicesReportPage();
        break;
      case 'print_item_labels_report':
        targetPage = const PrintItemLabelsReportPage();
        break;
      case 'customer_items_report':
        targetPage = const CustomerItemsReportPage();
        break;
      case 'beginning_inventory_balance_report':
        targetPage = const BeginningInventoryBalanceReportPage();
        break;
      case 'inventory_shortage_report':
        targetPage = const InventoryShortageReportPage();
        break;
      case 'item_price_changes_report':
        targetPage = const ItemPriceChangesReportPage();
        break;
      case 'item_location_changes_report':
        targetPage = const ItemLocationChangesReportPage();
        break;
      case 'customer_sales_movement_report':
        targetPage = const CustomerSalesMovementReportPage();
        break;
      case 'customer_debt_aging_report':
        targetPage = const CustomerDebtAgingReportPage();
        break;
      case 'supplier_sales_movement_report':
        targetPage = const SupplierSalesMovementReportPage();
        break;
      case 'supplier_purchases_movement_report':
        targetPage = const SupplierPurchasesMovementReportPage();
        break;
      case 'print_clearances_report':
        targetPage = const PrintClearancesReportPage();
        break;
      case 'print_warehouse_invoices_report':
        targetPage = const PrintWarehouseInvoicesReportPage();
        break;
      case 'print_clearance_stickers_report':
        targetPage = const PrintClearanceStickersReportPage();
        break;
      case 'selective_item_printing_report':
        targetPage = const SelectiveItemPrintingReportPage();
        break;
      case 'purchase_items_monitoring_report':
        targetPage = const PurchaseItemsMonitoringReportPage();
        break;
      case 'purchase_items_monitoring_previous_year_report':
        targetPage = const PurchaseItemsMonitoringPreviousYearReportPage();
        break;
      case 'item_requests_during_period_report':
        targetPage = const ItemRequestsDuringPeriodReportPage();
        break;
      case 'item_categories_tree_report':
        targetPage = const ItemCategoriesTreeReportPage();
        break;
      // التقارير الإضافية 37-72
      case 'detailed_items_report':
        targetPage = const DetailedItemsReportPage();
        break;
      case 'items_with_images_report':
        targetPage = const ItemsWithImagesReportPage();
        break;
      case 'items_without_images_report':
        targetPage = const ItemsWithoutImagesReportPage();
        break;
      case 'available_quantities_report':
        targetPage = const AvailableQuantitiesReportPage();
        break;
      case 'available_reservations_report':
        targetPage = const AvailableReservationsReportPage();
        break;
      case 'items_expiry_dates_report':
        targetPage = const ItemsExpiryDatesReportPage();
        break;
      case 'specific_items_quantities_report':
        targetPage = const SpecificItemsQuantitiesReportPage();
        break;
      case 'average_selling_prices_report':
        targetPage = const AverageSellingPricesReportPage();
        break;
      case 'purchased_items_profit_report':
        targetPage = const PurchasedItemsProfitReportPage();
        break;
      case 'customer_sales_by_categories_report':
        targetPage = const CustomerSalesByCategoriesReportPage();
        break;
      case 'items_below_cost_forecast_report':
        targetPage = const ItemsBelowCostForecastReportPage();
        break;
      case 'branch_sales_movement_report':
        targetPage = const BranchSalesMovementReportPage();
        break;
      case 'category_a_sales_commission_report':
        targetPage = const CategoryASalesCommissionReportPage();
        break;
      case 'sales_plan_achievement_report':
        targetPage = const SalesPlanAchievementReportPage();
        break;
      case 'external_item_requirements_report':
        targetPage = const ExternalItemRequirementsReportPage();
        break;
      case 'items_sold_below_price_report':
        targetPage = const ItemsSoldBelowPriceReportPage();
        break;
      case 'items_sold_above_price_report':
        targetPage = const ItemsSoldAbovePriceReportPage();
        break;
      case 'item_reservations_report':
        targetPage = const ItemReservationsReportPage();
        break;
      case 'reservation_voucher_sales_report':
        targetPage = const ReservationVoucherSalesReportPage();
        break;
      case 'item_reservation_voucher_movement_report':
        targetPage = const ItemReservationVoucherMovementReportPage();
        break;
      case 'damaged_item_exit_vouchers_report':
        targetPage = const DamagedItemExitVouchersReportPage();
        break;
      case 'item_classification_changes_report':
        targetPage = const ItemClassificationChangesReportPage();
        break;
      case 'daily_sales_purchases_details_report':
        targetPage = const DailySalesPurchasesDetailsReportPage();
        break;
      case 'purchase_orders_remaining_quantities_report':
        targetPage = const PurchaseOrdersRemainingQuantitiesReportPage();
        break;
      case 'cost_selling_price_deviation_report':
        targetPage = const CostSellingPriceDeviationReportPage();
        break;
      case 'sales_representatives_evaluation_report':
        targetPage = const SalesRepresentativesEvaluationReportPage();
        break;
      case 'suppliers_detailed_movement_report':
        targetPage = const SuppliersDetailedMovementReportPage();
        break;
      case 'items_exit_vouchers_detailed_report':
        targetPage = const ItemsExitVouchersDetailedReportPage();
        break;
      case 'inventory_aging_report':
        targetPage = const InventoryAgingReportPage();
        break;
      case 'items_sales_period_report':
        targetPage = const ItemsSalesPeriodReportPage();
        break;
      case 'supplier_purchase_invoices_by_category_report':
        targetPage = const SupplierPurchaseInvoicesByCategoryReportPage();
        break;
      case 'branch_sales_movement_2_report':
        targetPage = const BranchSalesMovement2ReportPage();
        break;
      case 'unlinked_payments_period_report':
        targetPage = const UnlinkedPaymentsPeriodReportPage();
        break;
      case 'customer_movement_period_report':
        targetPage = const CustomerMovementPeriodReportPage();
        break;
      case 'customer_duplicate_items_report':
        targetPage = const CustomerDuplicateItemsReportPage();
        break;
      case 'net_sales_period_report':
        targetPage = const NetSalesPeriodReportPage();
        break;

      default:
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(localizations.reportNotAvailable)),
        );
        return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => targetPage),
    );
  }

  /// إنشاء تقرير مخصص
  void _generateCustomReport() {
    final localizations = AppLocalizations.of(context);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(localizations.customReportFeature)),
    );
  }
}
