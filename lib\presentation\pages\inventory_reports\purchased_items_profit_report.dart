import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير أرباح الأصناف المشتراة خلال فترة
/// يعرض أرباح الأصناف التي تم شراؤها خلال فترة معينة
class PurchasedItemsProfitReportPage extends StatefulWidget {
  const PurchasedItemsProfitReportPage({super.key});

  @override
  State<PurchasedItemsProfitReportPage> createState() =>
      _PurchasedItemsProfitReportPageState();
}

class _PurchasedItemsProfitReportPageState
    extends State<PurchasedItemsProfitReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedCategory;
  String? _profitRange = 'all';
  String? _selectedSupplier;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('أرباح الأصناف المشتراة خلال فترة'),
        backgroundColor: Colors.amber,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.insights),
            onPressed: _showProfitInsights,
            tooltip: 'رؤى الأرباح',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          _buildFilterSection(localizations),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildProfitRangesSection(),
                  const SizedBox(height: 16),
                  _buildProfitTableSection(),
                  const SizedBox(height: 16),
                  _buildSupplierAnalysisSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.amber[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.fromDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, true),
                  controller: TextEditingController(
                    text: _startDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.toDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, false),
                  controller: TextEditingController(
                    text: _endDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: localizations.category,
                    border: const OutlineInputBorder(),
                  ),
                  value: _selectedCategory,
                  items: [
                    DropdownMenuItem(
                        value: 'all', child: Text(localizations.allCategories)),
                    DropdownMenuItem(
                        value: 'electronics',
                        child: Text(localizations.electronics)),
                    DropdownMenuItem(
                        value: 'clothing', child: Text(localizations.clothing)),
                    DropdownMenuItem(
                        value: 'food', child: Text(localizations.food)),
                  ],
                  onChanged: (value) =>
                      setState(() => _selectedCategory = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'نطاق الربح',
                    border: OutlineInputBorder(),
                  ),
                  value: _profitRange,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الأرباح')),
                    DropdownMenuItem(
                        value: 'high', child: Text('ربح عالي (+30%)')),
                    DropdownMenuItem(
                        value: 'medium', child: Text('ربح متوسط (10-30%)')),
                    DropdownMenuItem(
                        value: 'low', child: Text('ربح منخفض (<10%)')),
                    DropdownMenuItem(value: 'loss', child: Text('خسارة')),
                  ],
                  onChanged: (value) => setState(() => _profitRange = value),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'المورد',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedSupplier,
                  items: const [
                    DropdownMenuItem(
                        value: 'all', child: Text('جميع الموردين')),
                    DropdownMenuItem(
                        value: 'supplier1',
                        child: Text('شركة التقنية المتقدمة')),
                    DropdownMenuItem(
                        value: 'supplier2', child: Text('مؤسسة الإلكترونيات')),
                    DropdownMenuItem(
                        value: 'supplier3', child: Text('شركة الأجهزة الذكية')),
                  ],
                  onChanged: (value) =>
                      setState(() => _selectedSupplier = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.analytics),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.amber,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.trending_up, color: Colors.amber, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص أرباح الأصناف المشتراة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard(
                    'إجمالي الأصناف', '485', Colors.amber, Icons.inventory),
                _buildSummaryCard('إجمالي الأرباح', '285,000 ر.س', Colors.green,
                    Icons.monetization_on),
                _buildSummaryCard(
                    'متوسط هامش الربح', '32.5%', Colors.blue, Icons.percent),
                _buildSummaryCard(
                    'أعلى ربح', '85,000 ر.س', Colors.orange, Icons.star),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfitRangesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.pie_chart, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'توزيع نطاقات الأرباح',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildProfitRangeCard('ربح عالي', '125 صنف', Colors.green),
                _buildProfitRangeCard('ربح متوسط', '235 صنف', Colors.blue),
                _buildProfitRangeCard('ربح منخفض', '105 صنف', Colors.orange),
                _buildProfitRangeCard('خسارة', '20 صنف', Colors.red),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfitTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل أرباح الأصناف المشتراة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('اسم الصنف')),
                  DataColumn(label: Text('المورد')),
                  DataColumn(label: Text('تكلفة الشراء')),
                  DataColumn(label: Text('سعر البيع')),
                  DataColumn(label: Text('الربح')),
                  DataColumn(label: Text('هامش الربح')),
                  DataColumn(label: Text('الكمية المباعة')),
                  DataColumn(label: Text('إجمالي الربح')),
                  DataColumn(label: Text('تاريخ الشراء')),
                ],
                rows: _buildProfitRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSupplierAnalysisSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.business, color: Colors.teal, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'تحليل أداء الموردين',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildSupplierAnalysisList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _optimizePurchasing,
                    icon: const Icon(Icons.shopping_cart),
                    label: const Text('تحسين الشراء'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _negotiateWithSuppliers,
                    icon: const Icon(Icons.handshake),
                    label: const Text('التفاوض مع الموردين'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _identifyBestItems,
                    icon: const Icon(Icons.star),
                    label: const Text('تحديد أفضل الأصناف'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _createProfitStrategy,
                    icon: const Icon(Icons.analytics),
                    label: const Text('استراتيجية الربح'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildSupplierAnalysisList() {
    final suppliers = [
      {
        'name': 'شركة التقنية المتقدمة',
        'items': '125 صنف',
        'profit': '125,000 ر.س',
        'margin': '35%',
        'rating': 'ممتاز'
      },
      {
        'name': 'مؤسسة الإلكترونيات',
        'items': '95 صنف',
        'profit': '85,000 ر.س',
        'margin': '28%',
        'rating': 'جيد جداً'
      },
      {
        'name': 'شركة الأجهزة الذكية',
        'items': '75 صنف',
        'profit': '55,000 ر.س',
        'margin': '22%',
        'rating': 'جيد'
      },
      {
        'name': 'مؤسسة الأدوات المنزلية',
        'items': '65 صنف',
        'profit': '35,000 ر.س',
        'margin': '18%',
        'rating': 'مقبول'
      },
    ];

    return suppliers
        .map((supplier) => Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(
                    color: _getRatingColor(supplier['rating']!)
                        .withValues(alpha: 0.3)),
                borderRadius: BorderRadius.circular(8),
                color: _getRatingColor(supplier['rating']!)
                    .withValues(alpha: 0.05),
              ),
              child: Row(
                children: [
                  const Icon(Icons.business, color: Colors.teal, size: 24),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          supplier['name']!,
                          style: const TextStyle(
                              fontSize: 14, fontWeight: FontWeight.bold),
                        ),
                        Text(
                          '${supplier['items']} • ربح: ${supplier['profit']} • هامش: ${supplier['margin']}',
                          style:
                              TextStyle(fontSize: 12, color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getRatingColor(supplier['rating']!)
                          .withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      supplier['rating']!,
                      style: TextStyle(
                        color: _getRatingColor(supplier['rating']!),
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ))
        .toList();
  }

  List<DataRow> _buildProfitRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('شركة التقنية المتقدمة')),
        const DataCell(Text('3,200 ر.س')),
        const DataCell(Text('4,500 ر.س')),
        const DataCell(Text('1,300 ر.س')),
        DataCell(_buildProfitMarginBadge('40.6%', Colors.green)),
        const DataCell(Text('25')),
        const DataCell(Text('32,500 ر.س')),
        const DataCell(Text('2024-01-10')),
      ]),
      DataRow(cells: [
        const DataCell(Text('هاتف آيفون 15')),
        const DataCell(Text('مؤسسة الإلكترونيات')),
        const DataCell(Text('2,800 ر.س')),
        const DataCell(Text('3,800 ر.س')),
        const DataCell(Text('1,000 ر.س')),
        DataCell(_buildProfitMarginBadge('35.7%', Colors.green)),
        const DataCell(Text('18')),
        const DataCell(Text('18,000 ر.س')),
        const DataCell(Text('2024-01-12')),
      ]),
    ];
  }

  Widget _buildSummaryCard(
      String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                    fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title,
                  style: const TextStyle(fontSize: 12),
                  textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfitRangeCard(String range, String count, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(
                    fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(range,
                  style: const TextStyle(fontSize: 12),
                  textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfitMarginBadge(String margin, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(margin,
          style: TextStyle(
              color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Color _getRatingColor(String rating) {
    switch (rating) {
      case 'ممتاز':
        return Colors.green;
      case 'جيد جداً':
        return Colors.blue;
      case 'جيد':
        return Colors.orange;
      case 'مقبول':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
          content: Text('تم إنشاء تقرير أرباح الأصناف المشتراة بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showProfitInsights() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض رؤى الأرباح المتقدمة')),
    );
  }

  void _optimizePurchasing() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحسين استراتيجية الشراء')),
    );
  }

  void _negotiateWithSuppliers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('بدء التفاوض مع الموردين')),
    );
  }

  void _identifyBestItems() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحديد أفضل الأصناف للشراء')),
    );
  }

  void _createProfitStrategy() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء استراتيجية تحسين الأرباح')),
    );
  }
}
