import 'package:flutter/material.dart';

class ReportViewButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String? tooltip;
  final bool isLoading;
  final bool isDisabled;

  const ReportViewButton({
    super.key,
    this.onPressed,
    this.tooltip = 'Change Report View',
    this.isLoading = false,
    this.isDisabled = false,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip ?? 'Change Report View',
      child: ElevatedButton.icon(
        onPressed: isDisabled || isLoading ? null : onPressed,
        icon: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              )
            : const Icon(Icons.view_list),
        label: const Text('Change Report View'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.deepPurple.shade800,
          foregroundColor: Colors.white,
        ),
      ),
    );
  }
}
