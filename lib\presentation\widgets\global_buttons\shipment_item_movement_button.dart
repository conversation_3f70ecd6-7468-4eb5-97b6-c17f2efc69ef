import 'package:flutter/material.dart';

class ShipmentItemMovementButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String? tooltip;
  final bool isLoading;
  final bool isDisabled;

  const ShipmentItemMovementButton({
    super.key,
    this.onPressed,
    this.tooltip = 'Shipment Item Movement',
    this.isLoading = false,
    this.isDisabled = false,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip ?? 'Shipment Item Movement',
      child: ElevatedButton.icon(
        onPressed: isDisabled || isLoading ? null : onPressed,
        icon: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              )
            : const Icon(Icons.local_shipping),
        label: const Text('Shipment Item Movement'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.cyan.shade800,
          foregroundColor: Colors.white,
        ),
      ),
    );
  }
}
