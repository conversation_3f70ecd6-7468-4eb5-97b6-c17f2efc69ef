import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة الفروع
/// تتيح إدارة فروع الشركة ومعلوماتها
class BranchesPage extends StatefulWidget {
  const BranchesPage({super.key});

  @override
  State<BranchesPage> createState() => _BranchesPageState();
}

class _BranchesPageState extends State<BranchesPage> {
  String _searchQuery = '';
  String _selectedCity = 'all';

  // بيانات تجريبية للفروع
  final List<Map<String, dynamic>> _branches = [
    {
      'id': 'BR001',
      'name': 'الفرع الرئيسي',
      'code': 'MAIN',
      'city': 'الرياض',
      'address': 'شارع الملك فهد، حي العليا',
      'phone': '011-1234567',
      'manager': '<PERSON><PERSON>م<PERSON> محمد',
      'isActive': true,
      'openingDate': '2020-01-01',
      'area': 500.0,
      'employeeCount': 25,
    },
    {
      'id': 'BR002',
      'name': 'فرع جدة',
      'code': 'JED',
      'city': 'جدة',
      'address': 'طريق الملك عبدالعزيز، حي الروضة',
      'phone': '012-7654321',
      'manager': 'فاطمة أحمد',
      'isActive': true,
      'openingDate': '2021-03-15',
      'area': 350.0,
      'employeeCount': 18,
    },
    {
      'id': 'BR003',
      'name': 'فرع الدمام',
      'code': 'DAM',
      'city': 'الدمام',
      'address': 'شارع الأمير محمد بن فهد، حي الفيصلية',
      'phone': '013-9876543',
      'manager': 'محمد عبدالله',
      'isActive': false,
      'openingDate': '2022-06-20',
      'area': 280.0,
      'employeeCount': 12,
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.branches),
        backgroundColor: Colors.deepOrange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addBranch,
            tooltip: 'إضافة فرع جديد',
          ),
          IconButton(
            icon: const Icon(Icons.map),
            onPressed: _showBranchesMap,
            tooltip: 'عرض الخريطة',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                TextField(
                  decoration: const InputDecoration(
                    hintText: 'البحث في الفروع...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedCity,
                        decoration: const InputDecoration(
                          labelText: 'المدينة',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع المدن')),
                          DropdownMenuItem(value: 'الرياض', child: Text('الرياض')),
                          DropdownMenuItem(value: 'جدة', child: Text('جدة')),
                          DropdownMenuItem(value: 'الدمام', child: Text('الدمام')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedCity = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                _buildStatCard('المجموع', _branches.length.toString(), Colors.blue),
                _buildStatCard('النشطة', _branches.where((b) => b['isActive']).length.toString(), Colors.green),
                _buildStatCard('مغلقة', _branches.where((b) => !b['isActive']).length.toString(), Colors.red),
                _buildStatCard('الموظفين', _getTotalEmployees(), Colors.purple),
              ],
            ),
          ),

          // قائمة الفروع
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _branches.length,
              itemBuilder: (context, index) {
                final branch = _branches[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: branch['isActive'] ? Colors.green : Colors.red,
                      child: Icon(
                        Icons.store,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    title: Text(
                      branch['name'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('الكود: ${branch['code']} | المدينة: ${branch['city']}'),
                        Text('المدير: ${branch['manager']}'),
                        Text('الموظفين: ${branch['employeeCount']} | المساحة: ${branch['area']} م²'),
                      ],
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          branch['isActive'] ? Icons.check_circle : Icons.cancel,
                          color: branch['isActive'] ? Colors.green : Colors.red,
                        ),
                        PopupMenuButton<String>(
                          onSelected: (value) => _handleAction(value, branch),
                          itemBuilder: (context) => [
                            const PopupMenuItem(
                              value: 'edit',
                              child: ListTile(
                                leading: Icon(Icons.edit),
                                title: Text('تعديل'),
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'toggle',
                              child: ListTile(
                                leading: Icon(Icons.toggle_on),
                                title: Text('تغيير الحالة'),
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'employees',
                              child: ListTile(
                                leading: Icon(Icons.people),
                                title: Text('إدارة الموظفين'),
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'reports',
                              child: ListTile(
                                leading: Icon(Icons.analytics),
                                title: Text('التقارير'),
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'delete',
                              child: ListTile(
                                leading: Icon(Icons.delete, color: Colors.red),
                                title: Text('حذف', style: TextStyle(color: Colors.red)),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Icon(Icons.location_on, size: 16),
                                const SizedBox(width: 8),
                                Expanded(child: Text('العنوان: ${branch['address']}')),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                const Icon(Icons.phone, size: 16),
                                const SizedBox(width: 8),
                                Text('الهاتف: ${branch['phone']}'),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                const Icon(Icons.calendar_today, size: 16),
                                const SizedBox(width: 8),
                                Text('تاريخ الافتتاح: ${branch['openingDate']}'),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                const Icon(Icons.square_foot, size: 16),
                                const SizedBox(width: 8),
                                Text('المساحة: ${branch['area']} متر مربع'),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addBranch,
        backgroundColor: Colors.deepOrange,
        icon: const Icon(Icons.add),
        label: const Text('إضافة فرع'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getTotalEmployees() {
    int total = _branches.fold(0, (sum, branch) => sum + (branch['employeeCount'] as int));
    return total.toString();
  }

  void _addBranch() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إضافة فرع جديد')),
    );
  }

  void _showBranchesMap() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض الفروع على الخريطة')),
    );
  }

  void _handleAction(String action, Map<String, dynamic> branch) {
    switch (action) {
      case 'edit':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تعديل الفرع ${branch['name']}')),
        );
        break;
      case 'toggle':
        setState(() {
          branch['isActive'] = !branch['isActive'];
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم ${branch['isActive'] ? 'تفعيل' : 'إغلاق'} الفرع'),
          ),
        );
        break;
      case 'employees':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('إدارة موظفي فرع ${branch['name']}')),
        );
        break;
      case 'reports':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تقارير فرع ${branch['name']}')),
        );
        break;
      case 'delete':
        _showDeleteConfirmation(branch);
        break;
    }
  }

  void _showDeleteConfirmation(Map<String, dynamic> branch) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الفرع ${branch['name']}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _branches.removeWhere((b) => b['id'] == branch['id']);
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف الفرع ${branch['name']}'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
