import 'package:flutter/material.dart';
import 'approve_invoice_button.dart';
import 'approve_order_button.dart';
import 'approve_unapproved_transfer_vouchers_button.dart';
import 'auto_item_add_button.dart';
import 'bank_dues_report_button.dart';
import 'cancel_approval_button.dart';
import 'categorized_receipts_button.dart';
import 'change_payment_method_button.dart';
import 'change_view_button.dart';
import 'client_sales_invoices_button.dart';
import 'close_purchase_request_button.dart';
import 'compare_items_button.dart';
import 'convert_to_quote_button.dart';
import 'copy_from_button.dart';
import 'delete_button.dart';
import 'discount_button.dart';
import 'discount_points_button.dart';
import 'edit_invoice_prices_button.dart';
import 'edit_notes_button.dart';
import 'edit_prices_button.dart';
import 'edit_printed_invoices_button.dart';
import 'edit_printed_returns_button.dart';
import 'edit_purchase_request_button.dart';
import 'edit_received_request_button.dart';
import 'expired_items_button.dart';
import 'export_excel_button.dart';
import 'export_pdf_button.dart';
import 'fetch_account_invoices_button.dart';
import 'fetch_items_button.dart';
import 'import_from_file_button.dart';
import 'import_from_quote_button.dart';
import 'import_previous_entry_button.dart';
import 'invoice_settlement_movement_button.dart';
import 'new_button.dart';
import 'old_returns_button.dart';
import 'print_button.dart';
import 'print_cheque_button.dart';
import 'print_clearance_button.dart';
import 'print_from_warehouse_button.dart';
import 'print_preview_button.dart';
import 'purchase_invoice_filters_button.dart';
import 'purchase_items_button.dart';
import 'query_button.dart';
import 'receipt_list_button.dart';
import 'report_search_button.dart';
import 'report_view_button.dart';
import 'reports_button.dart';
import 'save_button.dart';
import 'send_sms_button.dart';
import 'send_whatsapp_button.dart';
import 'set_default_quantity_button.dart';
import 'settled_advances_button.dart';
import 'share_store_invoices_button.dart';
import 'shipment_item_movement_button.dart';
import 'sort_columns_button.dart';
import 'supply_from_outbound_button.dart';
import 'toggle_fields_button.dart';
import 'undo_button.dart';
import 'verify_items_button.dart';
import 'zoom_in_button.dart';
import 'zoom_out_button.dart';
import 'edit_button.dart';
import 'cancel_button.dart';

/// الفئة المركزية للأزرار العامة في التطبيق
/// تسمح بالوصول إلى جميع الأزرار من مكان واحد
class GlobalButtons {
  // أزرار عامة
  static Widget newButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      NewButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget queryButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      QueryButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget undoButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      UndoButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget saveButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      SaveButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget setDefaultQuantityButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      SetDefaultQuantityButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget sendSmsButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      SendSmsButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget exportExcelButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      ExportExcelButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget clientSalesInvoicesButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      ClientSalesInvoicesButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget approveInvoiceButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      ApproveInvoiceButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget sendWhatsappButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      SendWhatsappButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget copyFromButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      CopyFromButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget printClearanceButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      PrintClearanceButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget printButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      PrintButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget reportsButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      ReportsButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget printFromWarehouseButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      PrintFromWarehouseButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget editPricesButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      EditPricesButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget convertToQuoteButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      ConvertToQuoteButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget changePaymentMethodButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      ChangePaymentMethodButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget discountPointsButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      DiscountPointsButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget oldReturnsButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      OldReturnsButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget toggleFieldsButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      ToggleFieldsButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget sortColumnsButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      SortColumnsButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget changeViewButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      ChangeViewButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget importFromQuoteButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      ImportFromQuoteButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget deleteButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      DeleteButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget approveOrderButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      ApproveOrderButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget cancelApprovalButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      CancelApprovalButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget closePurchaseRequestButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      ClosePurchaseRequestButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget verifyItemsButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      VerifyItemsButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget editPurchaseRequestButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      EditPurchaseRequestButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget editReceivedRequestButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      EditReceivedRequestButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget compareItemsButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      CompareItemsButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget receiptListButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      ReceiptListButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget shipmentItemMovementButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      ShipmentItemMovementButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget expiredItemsButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      ExpiredItemsButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget bankDuesReportButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      BankDuesReportButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget categorizedReceiptsButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      CategorizedReceiptsButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget purchaseItemsButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      PurchaseItemsButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget purchaseInvoiceFiltersButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      PurchaseInvoiceFiltersButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget discountButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      DiscountButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget fetchItemsButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      FetchItemsButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget editInvoicePricesButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      EditInvoicePricesButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget zoomInButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      ZoomInButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget zoomOutButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      ZoomOutButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget printPreviewButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      PrintPreviewButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget exportPdfButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      ExportPdfButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget reportViewButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      ReportViewButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget reportSearchButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      ReportSearchButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget importPreviousEntryButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      ImportPreviousEntryButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget editNotesButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      EditNotesButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget importFromFileButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      ImportFromFileButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget fetchAccountInvoicesButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      FetchAccountInvoicesButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget printChequeButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      PrintChequeButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget invoiceSettlementMovementButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      InvoiceSettlementMovementButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget settledAdvancesButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      SettledAdvancesButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget supplyFromOutboundButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      SupplyFromOutboundButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget autoItemAddButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      AutoItemAddButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget editPrintedInvoicesButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      EditPrintedInvoicesButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget editPrintedReturnsButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      EditPrintedReturnsButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget approveUnapprovedTransferVouchersButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      ApproveUnapprovedTransferVouchersButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget shareStoreInvoicesButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      ShareStoreInvoicesButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget editButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      EditButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );

  static Widget cancelButton({
    VoidCallback? onPressed,
    String? tooltip,
    bool isLoading = false,
    bool isDisabled = false,
  }) =>
      CancelButton(
        onPressed: onPressed,
        tooltip: tooltip,
        isLoading: isLoading,
        isDisabled: isDisabled,
      );
}
