/// نموذج بيانات مركز التكلفة
class CostCenterData {
  String branchNumber;
  String centerCode;
  String arabicName;
  String englishName;
  String centerType;
  String freezeType;
  String currency;
  String securityLevel;
  String subCenterType;
  String balanceWarning;
  bool showNetMovement;
  bool isFolder;

  CostCenterData({
    this.branchNumber = '001',
    this.centerCode = '',
    this.arabicName = '',
    this.englishName = '',
    this.centerType = 'تشغيلي',
    this.freezeType = 'بدون',
    this.currency = 'ريال سعودي',
    this.securityLevel = 'عادي',
    this.subCenterType = '',
    this.balanceWarning = 'بدون',
    this.showNetMovement = false,
    this.isFolder = true,
  });

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'branchNumber': branchNumber,
      'centerCode': centerCode,
      'arabicName': arabicName,
      'englishName': englishName,
      'centerType': centerType,
      'freezeType': freezeType,
      'currency': currency,
      'securityLevel': securityLevel,
      'subCenterType': subCenterType,
      'balanceWarning': balanceWarning,
      'showNetMovement': showNetMovement,
      'isFolder': isFolder,
    };
  }

  /// إنشاء من Map
  factory CostCenterData.fromMap(Map<String, dynamic> map) {
    return CostCenterData(
      branchNumber: map['branchNumber'] ?? '001',
      centerCode: map['centerCode'] ?? '',
      arabicName: map['arabicName'] ?? '',
      englishName: map['englishName'] ?? '',
      centerType: map['centerType'] ?? 'تشغيلي',
      freezeType: map['freezeType'] ?? 'بدون',
      currency: map['currency'] ?? 'ريال سعودي',
      securityLevel: map['securityLevel'] ?? 'عادي',
      subCenterType: map['subCenterType'] ?? '',
      balanceWarning: map['balanceWarning'] ?? 'بدون',
      showNetMovement: map['showNetMovement'] ?? false,
      isFolder: map['isFolder'] ?? true,
    );
  }

  /// نسخ مع تعديل
  CostCenterData copyWith({
    String? branchNumber,
    String? centerCode,
    String? arabicName,
    String? englishName,
    String? centerType,
    String? freezeType,
    String? currency,
    String? securityLevel,
    String? subCenterType,
    String? balanceWarning,
    bool? showNetMovement,
    bool? isFolder,
  }) {
    return CostCenterData(
      branchNumber: branchNumber ?? this.branchNumber,
      centerCode: centerCode ?? this.centerCode,
      arabicName: arabicName ?? this.arabicName,
      englishName: englishName ?? this.englishName,
      centerType: centerType ?? this.centerType,
      freezeType: freezeType ?? this.freezeType,
      currency: currency ?? this.currency,
      securityLevel: securityLevel ?? this.securityLevel,
      subCenterType: subCenterType ?? this.subCenterType,
      balanceWarning: balanceWarning ?? this.balanceWarning,
      showNetMovement: showNetMovement ?? this.showNetMovement,
      isFolder: isFolder ?? this.isFolder,
    );
  }

  @override
  String toString() {
    return 'CostCenterData(centerCode: $centerCode, arabicName: $arabicName, englishName: $englishName, isFolder: $isFolder)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CostCenterData && other.centerCode == centerCode;
  }

  @override
  int get hashCode => centerCode.hashCode;
}
