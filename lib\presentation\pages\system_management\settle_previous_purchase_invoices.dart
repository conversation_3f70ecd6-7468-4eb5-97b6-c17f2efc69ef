import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تسديد فواتير المشتريات لسنوات سابقة
/// تتيح تسديد فواتير المشتريات المعلقة من السنوات السابقة
class SettlePreviousPurchaseInvoicesPage extends StatefulWidget {
  const SettlePreviousPurchaseInvoicesPage({super.key});

  @override
  State<SettlePreviousPurchaseInvoicesPage> createState() =>
      _SettlePreviousPurchaseInvoicesPageState();
}

class _SettlePreviousPurchaseInvoicesPageState
    extends State<SettlePreviousPurchaseInvoicesPage> {
  final _searchController = TextEditingController();

  String? _selectedYear;
  String? _selectedSupplier;
  String _searchQuery = '';
  final bool _isProcessing = false;
  bool _showPaidInvoices = false;

  final List<String> _years = ['2023', '2022', '2021', '2020', '2019'];

  final List<Map<String, String>> _suppliers = [
    {'id': 'supp1', 'name': 'شركة التوريدات المتقدمة', 'code': 'S001'},
    {'id': 'supp2', 'name': 'مؤسسة الإمداد الشامل', 'code': 'S002'},
    {'id': 'supp3', 'name': 'شركة المواد الأولية', 'code': 'S003'},
    {'id': 'supp4', 'name': 'مجموعة التجهيزات', 'code': 'S004'},
    {'id': 'supp5', 'name': 'شركة الخدمات اللوجستية', 'code': 'S005'},
  ];

  final List<Map<String, dynamic>> _invoices = [
    {
      'id': 'pinv1',
      'number': 'PINV-2023-045',
      'date': '2023/02/20',
      'supplier': 'شركة التوريدات المتقدمة',
      'supplierId': 'supp1',
      'amount': 28500.0,
      'paid': 15000.0,
      'remaining': 13500.0,
      'status': 'جزئي',
      'dueDate': '2023/03/20',
      'overdueDays': 310,
      'year': '2023',
      'category': 'مواد خام',
    },
    {
      'id': 'pinv2',
      'number': 'PINV-2022-189',
      'date': '2022/10/15',
      'supplier': 'مؤسسة الإمداد الشامل',
      'supplierId': 'supp2',
      'amount': 45600.0,
      'paid': 0.0,
      'remaining': 45600.0,
      'status': 'معلق',
      'dueDate': '2022/11/15',
      'overdueDays': 436,
      'year': '2022',
      'category': 'معدات',
    },
    {
      'id': 'pinv3',
      'number': 'PINV-2023-078',
      'date': '2023/06/12',
      'supplier': 'شركة المواد الأولية',
      'supplierId': 'supp3',
      'amount': 12800.0,
      'paid': 12800.0,
      'remaining': 0.0,
      'status': 'مسدد',
      'dueDate': '2023/07/12',
      'overdueDays': 0,
      'year': '2023',
      'category': 'مواد خام',
    },
    {
      'id': 'pinv4',
      'number': 'PINV-2021-156',
      'date': '2021/08/25',
      'supplier': 'مجموعة التجهيزات',
      'supplierId': 'supp4',
      'amount': 67200.0,
      'paid': 30000.0,
      'remaining': 37200.0,
      'status': 'جزئي',
      'dueDate': '2021/09/25',
      'overdueDays': 853,
      'year': '2021',
      'category': 'أجهزة',
    },
    {
      'id': 'pinv5',
      'number': 'PINV-2022-234',
      'date': '2022/04/08',
      'supplier': 'شركة الخدمات اللوجستية',
      'supplierId': 'supp5',
      'amount': 89400.0,
      'paid': 0.0,
      'remaining': 89400.0,
      'status': 'معلق',
      'dueDate': '2022/05/08',
      'overdueDays': 627,
      'year': '2022',
      'category': 'خدمات',
    },
  ];

  List<Map<String, dynamic>> get _filteredInvoices {
    List<Map<String, dynamic>> filtered = _invoices;

    // فلتر السنة
    if (_selectedYear != null) {
      filtered = filtered
          .where((invoice) => invoice['year'] == _selectedYear)
          .toList();
    }

    // فلتر المورد
    if (_selectedSupplier != null) {
      filtered = filtered
          .where((invoice) => invoice['supplierId'] == _selectedSupplier)
          .toList();
    }

    // فلتر الفواتير المسددة
    if (!_showPaidInvoices) {
      filtered = filtered.where((invoice) => invoice['remaining'] > 0).toList();
    }

    // البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered
          .where((invoice) =>
              invoice['number']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              invoice['supplier']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              invoice['category']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()))
          .toList();
    }

    return filtered;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.settlePreviousPurchaseInvoices),
        backgroundColor: Colors.brown,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.trending_down),
            onPressed: _showPayableAnalytics,
            tooltip: 'تحليلات المدفوعات',
          ),
          IconButton(
            icon: const Icon(Icons.schedule_send),
            onPressed: _schedulePayments,
            tooltip: 'جدولة المدفوعات',
          ),
        ],
      ),
      body: Column(
        children: [
          // بطاقة الفلاتر
          Card(
            margin: const EdgeInsets.all(16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // شريط البحث
                  TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      labelText: 'البحث برقم الفاتورة أو المورد أو الفئة',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),

                  const SizedBox(height: 16),

                  Row(
                    children: [
                      // فلتر السنة
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedYear,
                          decoration: const InputDecoration(
                            labelText: 'السنة',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 8),
                          ),
                          items: [
                            const DropdownMenuItem(
                                value: null, child: Text('جميع السنوات')),
                            ..._years.map((year) => DropdownMenuItem(
                                  value: year,
                                  child: Text(year),
                                )),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedYear = value;
                            });
                          },
                        ),
                      ),

                      const SizedBox(width: 16),

                      // فلتر المورد
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedSupplier,
                          decoration: const InputDecoration(
                            labelText: 'المورد',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 8),
                          ),
                          items: [
                            const DropdownMenuItem(
                                value: null, child: Text('جميع الموردين')),
                            ..._suppliers.map((supplier) => DropdownMenuItem(
                                  value: supplier['id'],
                                  child: Text(
                                    supplier['name']!,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                )),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedSupplier = value;
                            });
                          },
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // خيار عرض الفواتير المسددة
                  CheckboxListTile(
                    title: const Text('عرض الفواتير المسددة'),
                    value: _showPaidInvoices,
                    onChanged: (value) {
                      setState(() {
                        _showPaidInvoices = value ?? false;
                      });
                    },
                    dense: true,
                  ),
                ],
              ),
            ),
          ),

          // إحصائيات سريعة
          Card(
            margin: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildStatCard('المجموع',
                          _filteredInvoices.length.toString(), Colors.blue),
                      _buildStatCard(
                          'معلق',
                          _filteredInvoices
                              .where((i) => i['status'] == 'معلق')
                              .length
                              .toString(),
                          Colors.red),
                      _buildStatCard(
                          'جزئي',
                          _filteredInvoices
                              .where((i) => i['status'] == 'جزئي')
                              .length
                              .toString(),
                          Colors.orange),
                      _buildStatCard(
                          'مسدد',
                          _filteredInvoices
                              .where((i) => i['status'] == 'مسدد')
                              .length
                              .toString(),
                          Colors.green),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'إجمالي المبلغ المتبقي: ${_calculateTotalRemaining()} ر.س',
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, color: Colors.brown),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // قائمة الفواتير
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _filteredInvoices.length,
              itemBuilder: (context, index) {
                final invoice = _filteredInvoices[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: _getStatusColor(invoice['status']),
                      child: Icon(
                        _getStatusIcon(invoice['status']),
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    title: Text(
                      invoice['number'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(invoice['supplier']),
                        Text(
                          'الفئة: ${invoice['category']} | المبلغ: ${invoice['amount']} ر.س',
                          style: const TextStyle(fontSize: 12),
                        ),
                        Text(
                          'المتبقي: ${invoice['remaining']} ر.س',
                          style: TextStyle(
                            fontSize: 12,
                            color: invoice['remaining'] > 0
                                ? Colors.red
                                : Colors.green,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (invoice['overdueDays'] > 0)
                          Text(
                            'متأخر ${invoice['overdueDays']} يوم',
                            style: const TextStyle(
                                fontSize: 12, color: Colors.red),
                          ),
                      ],
                    ),
                    trailing: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color:
                            _getStatusColor(invoice['status']).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        invoice['status'],
                        style: TextStyle(
                          color: _getStatusColor(invoice['status']),
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            _buildDetailRow('تاريخ الفاتورة:', invoice['date']),
                            _buildDetailRow(
                                'تاريخ الاستحقاق:', invoice['dueDate']),
                            _buildDetailRow('الفئة:', invoice['category']),
                            _buildDetailRow(
                                'إجمالي المبلغ:', '${invoice['amount']} ر.س'),
                            _buildDetailRow(
                                'المدفوع:', '${invoice['paid']} ر.س'),
                            _buildDetailRow(
                                'المتبقي:', '${invoice['remaining']} ر.س'),

                            const SizedBox(height: 16),

                            // أزرار العمليات
                            if (invoice['remaining'] > 0) ...[
                              Row(
                                children: [
                                  Expanded(
                                    child: ElevatedButton.icon(
                                      onPressed: () => _payInvoice(invoice),
                                      icon: const Icon(Icons.payment, size: 16),
                                      label: const Text('دفع كامل'),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.green,
                                        foregroundColor: Colors.white,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: ElevatedButton.icon(
                                      onPressed: () => _partialPayment(invoice),
                                      icon:
                                          const Icon(Icons.payments, size: 16),
                                      label: const Text('دفع جزئي'),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.orange,
                                        foregroundColor: Colors.white,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Expanded(
                                    child: OutlinedButton.icon(
                                      onPressed: () =>
                                          _schedulePayment(invoice),
                                      icon:
                                          const Icon(Icons.schedule, size: 16),
                                      label: const Text('جدولة الدفع'),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: OutlinedButton.icon(
                                      onPressed: () =>
                                          _contactSupplier(invoice),
                                      icon: const Icon(Icons.phone, size: 16),
                                      label: const Text('اتصال'),
                                    ),
                                  ),
                                ],
                              ),
                            ] else ...[
                              Row(
                                children: [
                                  Expanded(
                                    child: ElevatedButton.icon(
                                      onPressed: () =>
                                          _viewInvoiceDetails(invoice),
                                      icon: const Icon(Icons.visibility,
                                          size: 16),
                                      label: const Text('عرض التفاصيل'),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.blue,
                                        foregroundColor: Colors.white,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: OutlinedButton.icon(
                                      onPressed: () => _printReceipt(invoice),
                                      icon: const Icon(Icons.receipt, size: 16),
                                      label: const Text('إيصال'),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _bulkPayment,
        backgroundColor: Colors.brown,
        icon: const Icon(Icons.account_balance_wallet),
        label: const Text('دفع جماعي'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Column(
      children: [
        CircleAvatar(
          backgroundColor: color,
          child: Text(
            value,
            style: const TextStyle(
                color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'مسدد':
        return Colors.green;
      case 'جزئي':
        return Colors.orange;
      case 'معلق':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'مسدد':
        return Icons.check_circle;
      case 'جزئي':
        return Icons.schedule;
      case 'معلق':
        return Icons.error;
      default:
        return Icons.help;
    }
  }

  String _calculateTotalRemaining() {
    double total = _filteredInvoices
        .where((invoice) => invoice['remaining'] > 0)
        .fold(0.0, (sum, invoice) => sum + invoice['remaining']);
    return total.toStringAsFixed(2);
  }

  void _payInvoice(Map<String, dynamic> invoice) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('دفع كامل للفاتورة ${invoice['number']}')),
    );
  }

  void _partialPayment(Map<String, dynamic> invoice) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('دفع جزئي للفاتورة ${invoice['number']}')),
    );
  }

  void _schedulePayment(Map<String, dynamic> invoice) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('جدولة دفع الفاتورة ${invoice['number']}')),
    );
  }

  void _contactSupplier(Map<String, dynamic> invoice) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('الاتصال بالمورد ${invoice['supplier']}')),
    );
  }

  void _viewInvoiceDetails(Map<String, dynamic> invoice) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض تفاصيل الفاتورة ${invoice['number']}')),
    );
  }

  void _printReceipt(Map<String, dynamic> invoice) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('طباعة إيصال الفاتورة ${invoice['number']}')),
    );
  }

  void _bulkPayment() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('دفع جماعي للفواتير المحددة')),
    );
  }

  void _showPayableAnalytics() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تحليلات المدفوعات والالتزامات')),
    );
  }

  void _schedulePayments() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جدولة المدفوعات المستقبلية')),
    );
  }
}
