import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير حركة مشتريات الموردين
/// يعرض تحليل المشتريات من الموردين
class SupplierPurchasesMovementReportPage extends StatefulWidget {
  const SupplierPurchasesMovementReportPage({super.key});

  @override
  State<SupplierPurchasesMovementReportPage> createState() => _SupplierPurchasesMovementReportPageState();
}

class _SupplierPurchasesMovementReportPageState extends State<SupplierPurchasesMovementReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedSupplier;
  String? _selectedCategory;
  String? _paymentStatus = 'all';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('حركة مشتريات الموردين'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showPurchaseAnalytics,
            tooltip: 'تحليل المشتريات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.deepPurple[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.fromDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, true),
                        controller: TextEditingController(
                          text: _startDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.toDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, false),
                        controller: TextEditingController(
                          text: _endDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'المورد',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedSupplier,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الموردين')),
                          DropdownMenuItem(value: 'supplier1', child: Text('شركة التقنية المتقدمة')),
                          DropdownMenuItem(value: 'supplier2', child: Text('مؤسسة الجودة العالمية')),
                          DropdownMenuItem(value: 'supplier3', child: Text('شركة الإمداد الشامل')),
                        ],
                        onChanged: (value) => setState(() => _selectedSupplier = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.category,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedCategory,
                        items: [
                          DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                          DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                          DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                          DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                        ],
                        onChanged: (value) => setState(() => _selectedCategory = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'حالة الدفع',
                          border: OutlineInputBorder(),
                        ),
                        value: _paymentStatus,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الحالات')),
                          DropdownMenuItem(value: 'paid', child: Text('مدفوعة')),
                          DropdownMenuItem(value: 'pending', child: Text('معلقة')),
                          DropdownMenuItem(value: 'overdue', child: Text('متأخرة')),
                        ],
                        onChanged: (value) => setState(() => _paymentStatus = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _generateReport,
                        icon: const Icon(Icons.search),
                        label: Text(localizations.generateReport),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.deepPurple,
                          foregroundColor: Colors.white,
                          minimumSize: const Size(double.infinity, 48),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص مشتريات الموردين
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.shopping_cart, color: Colors.deepPurple, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'ملخص مشتريات الموردين',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSummaryCard('إجمالي المشتريات', '2,850,000 ر.س', Colors.blue, Icons.monetization_on),
                              _buildSummaryCard('عدد الفواتير', '485', Colors.green, Icons.receipt),
                              _buildSummaryCard('عدد الموردين', '45', Colors.orange, Icons.business),
                              _buildSummaryCard('متوسط الفاتورة', '5,876 ر.س', Colors.purple, Icons.calculate),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // أكبر الموردين
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'أكبر الموردين (حسب قيمة المشتريات)',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSupplierCard('شركة التقنية المتقدمة', '1,150,000 ر.س', '40.4%', Colors.blue),
                              _buildSupplierCard('مؤسسة الجودة العالمية', '750,000 ر.س', '26.3%', Colors.green),
                              _buildSupplierCard('شركة الإمداد الشامل', '550,000 ر.س', '19.3%', Colors.orange),
                              _buildSupplierCard('موردين آخرين', '400,000 ر.س', '14.0%', Colors.grey),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول تفاصيل مشتريات الموردين
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل مشتريات الموردين',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('رقم الفاتورة')),
                                DataColumn(label: Text('المورد')),
                                DataColumn(label: Text('تاريخ الشراء')),
                                DataColumn(label: Text('قيمة الفاتورة')),
                                DataColumn(label: Text('المدفوع')),
                                DataColumn(label: Text('المتبقي')),
                                DataColumn(label: Text('تاريخ الاستحقاق')),
                                DataColumn(label: Text('حالة الدفع')),
                                DataColumn(label: Text('طريقة الدفع')),
                              ],
                              rows: _buildPurchaseRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // أكثر الأصناف شراءً
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.trending_up, color: Colors.green, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'أكثر الأصناف شراءً',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          ..._buildTopPurchasedItemsList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // تحليل الدفعات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.payment, color: Colors.indigo, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'تحليل حالة الدفعات',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildPaymentCard('مدفوعة', '1,850,000 ر.س', '65%', Colors.green),
                              _buildPaymentCard('معلقة', '650,000 ر.س', '23%', Colors.orange),
                              _buildPaymentCard('متأخرة', '350,000 ر.س', '12%', Colors.red),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _createPayment,
                                  icon: const Icon(Icons.payment),
                                  label: const Text('تسجيل دفعة'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _sendPaymentReminder,
                                  icon: const Icon(Icons.notifications),
                                  label: const Text('تذكير دفع'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _generateSupplierStatement,
                                  icon: const Icon(Icons.account_balance),
                                  label: const Text('كشف حساب'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _reconcileAccount,
                                  icon: const Icon(Icons.balance),
                                  label: const Text('تسوية حساب'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<DataRow> _buildPurchaseRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('PUR-2024-001')),
        const DataCell(Text('شركة التقنية المتقدمة')),
        const DataCell(Text('2024-02-15')),
        const DataCell(Text('125,000 ر.س')),
        const DataCell(Text('125,000 ر.س')),
        const DataCell(Text('0 ر.س')),
        const DataCell(Text('2024-03-15')),
        DataCell(_buildPaymentStatusBadge('مدفوعة', Colors.green)),
        const DataCell(Text('تحويل بنكي')),
      ]),
      DataRow(cells: [
        const DataCell(Text('PUR-2024-002')),
        const DataCell(Text('مؤسسة الجودة العالمية')),
        const DataCell(Text('2024-02-14')),
        const DataCell(Text('85,000 ر.س')),
        const DataCell(Text('50,000 ر.س')),
        const DataCell(Text('35,000 ر.س')),
        const DataCell(Text('2024-03-14')),
        DataCell(_buildPaymentStatusBadge('جزئية', Colors.orange)),
        const DataCell(Text('شيك')),
      ]),
      DataRow(cells: [
        const DataCell(Text('PUR-2024-003')),
        const DataCell(Text('شركة الإمداد الشامل')),
        const DataCell(Text('2024-02-10')),
        const DataCell(Text('65,000 ر.س')),
        const DataCell(Text('0 ر.س')),
        const DataCell(Text('65,000 ر.س')),
        const DataCell(Text('2024-02-25')),
        DataCell(_buildPaymentStatusBadge('متأخرة', Colors.red)),
        const DataCell(Text('آجل')),
      ]),
    ];
  }

  List<Widget> _buildTopPurchasedItemsList() {
    final topItems = [
      {'item': 'لابتوب ديل XPS 13', 'supplier': 'شركة التقنية المتقدمة', 'quantity': '125', 'value': '312,500 ر.س'},
      {'item': 'طابعة HP LaserJet', 'supplier': 'مؤسسة الجودة العالمية', 'quantity': '85', 'value': '255,000 ر.س'},
      {'item': 'هاتف آيفون 15', 'supplier': 'شركة الإمداد الشامل', 'quantity': '65', 'value': '195,000 ر.س'},
      {'item': 'ساعة ذكية', 'supplier': 'مؤسسة التوريد الشاملة', 'quantity': '95', 'value': '142,500 ر.س'},
    ];

    return topItems.map((item) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.deepPurple.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.deepPurple.withOpacity(0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.shopping_cart, color: Colors.deepPurple, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item['item']!,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${item['supplier']} • الكمية: ${item['quantity']}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Text(
            item['value']!,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.deepPurple,
            ),
          ),
        ],
      ),
    )).toList();
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSupplierCard(String supplier, String purchases, String percentage, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            children: [
              Text(
                purchases,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                percentage,
                style: TextStyle(
                  fontSize: 12,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                supplier,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentCard(String status, String amount, String percentage, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                amount,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                percentage,
                style: TextStyle(
                  fontSize: 12,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                status,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        status,
        style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير حركة مشتريات الموردين بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showPurchaseAnalytics() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تحليل مفصل للمشتريات')),
    );
  }

  void _createPayment() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تسجيل دفعة جديدة للمورد')),
    );
  }

  void _sendPaymentReminder() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إرسال تذكير دفع للمورد')),
    );
  }

  void _generateSupplierStatement() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء كشف حساب المورد')),
    );
  }

  void _reconcileAccount() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تسوية حساب المورد')),
    );
  }
}
