import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة صيانة كميات الطلبيات المثبتة
/// تتيح فحص وإصلاح مشاكل الطلبيات المثبتة والمحجوزة
class MaintainFixedOrdersPage extends StatefulWidget {
  const MaintainFixedOrdersPage({super.key});

  @override
  State<MaintainFixedOrdersPage> createState() =>
      _MaintainFixedOrdersPageState();
}

class _MaintainFixedOrdersPageState extends State<MaintainFixedOrdersPage> {
  final _formKey = GlobalKey<FormState>();

  String? _selectedOrderType;
  String? _selectedStatus;
  String? _selectedCustomer;
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  bool _isProcessing = false;
  bool _autoFix = false;
  bool _createReport = true;

  final List<Map<String, String>> _orderTypes = [
    {'id': 'all', 'name': 'جميع الأنواع'},
    {'id': 'sales', 'name': 'طلبيات المبيعات'},
    {'id': 'purchase', 'name': 'طلبيات المشتريات'},
    {'id': 'transfer', 'name': 'طلبيات التحويل'},
    {'id': 'production', 'name': 'طلبيات الإنتاج'},
  ];

  final List<Map<String, String>> _orderStatuses = [
    {'id': 'all', 'name': 'جميع الحالات'},
    {'id': 'pending', 'name': 'معلقة'},
    {'id': 'confirmed', 'name': 'مؤكدة'},
    {'id': 'partial', 'name': 'منفذة جزئياً'},
    {'id': 'completed', 'name': 'مكتملة'},
    {'id': 'cancelled', 'name': 'ملغية'},
  ];

  final List<Map<String, String>> _customers = [
    {'id': 'all', 'name': 'جميع العملاء'},
    {'id': 'customer1', 'name': 'شركة الأمل للتجارة'},
    {'id': 'customer2', 'name': 'مؤسسة النور'},
    {'id': 'customer3', 'name': 'شركة الفجر الجديد'},
    {'id': 'customer4', 'name': 'مجموعة الشروق'},
  ];

  final List<Map<String, dynamic>> _detectedIssues = [
    {
      'id': 1,
      'orderNumber': 'SO-2024-001',
      'customer': 'شركة الأمل للتجارة',
      'type': 'sales',
      'issue': 'كمية محجوزة أكبر من المتوفر',
      'details': 'محجوز: 50، متوفر: 30',
      'severity': 'high',
      'suggestion': 'تقليل الكمية المحجوزة إلى 30'
    },
    {
      'id': 2,
      'orderNumber': 'PO-2024-015',
      'customer': 'مؤسسة النور',
      'type': 'purchase',
      'issue': 'طلبية مكتملة لكن لم يتم إلغاء الحجز',
      'details': 'الحالة: مكتملة، الحجز: نشط',
      'severity': 'medium',
      'suggestion': 'إلغاء الحجز للطلبية المكتملة'
    },
    {
      'id': 3,
      'orderNumber': 'TO-2024-008',
      'customer': 'تحويل داخلي',
      'type': 'transfer',
      'issue': 'طلبية ملغية لكن الحجز ما زال نشط',
      'details': 'الحالة: ملغية، الحجز: نشط',
      'severity': 'medium',
      'suggestion': 'إلغاء الحجز للطلبية الملغية'
    },
    {
      'id': 4,
      'orderNumber': 'SO-2024-025',
      'customer': 'شركة الفجر الجديد',
      'type': 'sales',
      'issue': 'طلبية قديمة معلقة أكثر من 90 يوم',
      'details': 'تاريخ الإنشاء: 2023/10/15',
      'severity': 'low',
      'suggestion': 'مراجعة الطلبية أو إلغاؤها'
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.maintainFixedOrders),
        backgroundColor: Colors.blueGrey,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showOrdersReport,
            tooltip: 'تقرير الطلبيات',
          ),
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: _showMaintenanceHistory,
            tooltip: 'سجل الصيانة',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // بطاقة معايير البحث
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'معايير البحث',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.blueGrey,
                      ),
                    ),
                    const SizedBox(height: 16),

                    Row(
                      children: [
                        // نوع الطلبية
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedOrderType,
                            decoration: const InputDecoration(
                              labelText: 'نوع الطلبية',
                              prefixIcon: Icon(Icons.shopping_cart),
                              border: OutlineInputBorder(),
                            ),
                            items: _orderTypes
                                .map<DropdownMenuItem<String>>((type) {
                              return DropdownMenuItem<String>(
                                value: type['id'],
                                child: Text(type['name']!),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedOrderType = value;
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'يرجى اختيار نوع الطلبية';
                              }
                              return null;
                            },
                          ),
                        ),

                        const SizedBox(width: 16),

                        // حالة الطلبية
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedStatus,
                            decoration: const InputDecoration(
                              labelText: 'حالة الطلبية',
                              prefixIcon: Icon(Icons.flag),
                              border: OutlineInputBorder(),
                            ),
                            items: _orderStatuses
                                .map<DropdownMenuItem<String>>((status) {
                              return DropdownMenuItem<String>(
                                value: status['id'],
                                child: Text(status['name']!),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedStatus = value;
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'يرجى اختيار حالة الطلبية';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // العميل
                    DropdownButtonFormField<String>(
                      value: _selectedCustomer,
                      decoration: const InputDecoration(
                        labelText: 'العميل',
                        prefixIcon: Icon(Icons.person),
                        border: OutlineInputBorder(),
                      ),
                      items:
                          _customers.map<DropdownMenuItem<String>>((customer) {
                        return DropdownMenuItem<String>(
                          value: customer['id'],
                          child: Text(customer['name']!),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedCustomer = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار العميل';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة الفترة الزمنية
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'الفترة الزمنية',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.blueGrey,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        // تاريخ البداية
                        Expanded(
                          child: ListTile(
                            leading: const Icon(Icons.calendar_today),
                            title: const Text('من تاريخ'),
                            subtitle: Text(
                                '${_startDate.day}/${_startDate.month}/${_startDate.year}'),
                            onTap: () => _selectDate(true),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                              side: BorderSide(color: Colors.grey.shade300),
                            ),
                          ),
                        ),

                        const SizedBox(width: 16),

                        // تاريخ النهاية
                        Expanded(
                          child: ListTile(
                            leading: const Icon(Icons.event),
                            title: const Text('إلى تاريخ'),
                            subtitle: Text(
                                '${_endDate.day}/${_endDate.month}/${_endDate.year}'),
                            onTap: () => _selectDate(false),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                              side: BorderSide(color: Colors.grey.shade300),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة خيارات الصيانة
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'خيارات الصيانة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.blueGrey,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SwitchListTile(
                      title: const Text('الإصلاح التلقائي'),
                      subtitle: const Text('إصلاح المشاكل البسيطة تلقائياً'),
                      value: _autoFix,
                      onChanged: (value) {
                        setState(() {
                          _autoFix = value;
                        });
                      },
                    ),
                    SwitchListTile(
                      title: const Text('إنشاء تقرير مفصل'),
                      subtitle:
                          const Text('إنشاء تقرير بجميع المشاكل المكتشفة'),
                      value: _createReport,
                      onChanged: (value) {
                        setState(() {
                          _createReport = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة المشاكل المكتشفة
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'المشاكل المكتشفة في الطلبيات',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.blueGrey,
                      ),
                    ),
                    const SizedBox(height: 16),
                    if (_detectedIssues.isEmpty)
                      const Center(
                        child: Text(
                          'لم يتم اكتشاف أي مشاكل بعد\nقم بتشغيل الفحص أولاً',
                          textAlign: TextAlign.center,
                          style: TextStyle(color: Colors.grey),
                        ),
                      )
                    else
                      ...(_detectedIssues.map((issue) => Card(
                            margin: const EdgeInsets.symmetric(vertical: 4),
                            child: ExpansionTile(
                              leading: CircleAvatar(
                                backgroundColor:
                                    _getSeverityColor(issue['severity']),
                                child: Icon(
                                  _getOrderTypeIcon(issue['type']),
                                  color: Colors.white,
                                ),
                              ),
                              title: Text(
                                  '${issue['orderNumber']} - ${issue['customer']}'),
                              subtitle: Text(issue['issue']),
                              children: [
                                Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text('التفاصيل: ${issue['details']}'),
                                      const SizedBox(height: 8),
                                      Text(
                                        'الحل المقترح: ${issue['suggestion']}',
                                        style:
                                            const TextStyle(color: Colors.blue),
                                      ),
                                      const SizedBox(height: 16),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.end,
                                        children: [
                                          TextButton.icon(
                                            onPressed: () => _viewOrderDetails(
                                                issue['orderNumber']),
                                            icon: const Icon(Icons.visibility),
                                            label: const Text('عرض التفاصيل'),
                                          ),
                                          const SizedBox(width: 8),
                                          ElevatedButton.icon(
                                            onPressed: () =>
                                                _fixOrderIssue(issue['id']),
                                            icon: const Icon(Icons.build),
                                            label: const Text('إصلاح'),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.orange,
                                              foregroundColor: Colors.white,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ))),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // معاينة العملية
            if (_selectedOrderType != null &&
                _selectedStatus != null &&
                _selectedCustomer != null)
              Card(
                color: Colors.blueGrey.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'معاينة عملية الصيانة',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blueGrey,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildPreviewRow('نوع الطلبية:', _getOrderTypeName()),
                      _buildPreviewRow('الحالة:', _getStatusName()),
                      _buildPreviewRow('العميل:', _getCustomerName()),
                      _buildPreviewRow('الفترة:',
                          '${_startDate.day}/${_startDate.month}/${_startDate.year} - ${_endDate.day}/${_endDate.month}/${_endDate.year}'),
                      _buildPreviewRow(
                          'الإصلاح التلقائي:', _autoFix ? 'مفعل' : 'معطل'),
                      _buildPreviewRow(
                          'إنشاء تقرير:', _createReport ? 'نعم' : 'لا'),
                      _buildPreviewRow('المشاكل المكتشفة:',
                          '${_detectedIssues.length} مشكلة'),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 24),

            // أزرار العمليات
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isProcessing ? null : _startMaintenance,
                    icon: _isProcessing
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.search),
                    label: Text(
                        _isProcessing ? 'جاري الفحص...' : 'بدء فحص الطلبيات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blueGrey,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _detectedIssues.isEmpty ? null : _fixAllIssues,
                    icon: const Icon(Icons.build),
                    label: const Text('إصلاح جميع المشاكل'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Color _getSeverityColor(String severity) {
    switch (severity) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.yellow;
      default:
        return Colors.grey;
    }
  }

  IconData _getOrderTypeIcon(String type) {
    switch (type) {
      case 'sales':
        return Icons.sell;
      case 'purchase':
        return Icons.shopping_cart;
      case 'transfer':
        return Icons.swap_horiz;
      case 'production':
        return Icons.precision_manufacturing;
      default:
        return Icons.receipt;
    }
  }

  String _getOrderTypeName() {
    if (_selectedOrderType == null) return 'غير محدد';
    final type = _orderTypes.firstWhere((t) => t['id'] == _selectedOrderType);
    return type['name']!;
  }

  String _getStatusName() {
    if (_selectedStatus == null) return 'غير محدد';
    final status = _orderStatuses.firstWhere((s) => s['id'] == _selectedStatus);
    return status['name']!;
  }

  String _getCustomerName() {
    if (_selectedCustomer == null) return 'غير محدد';
    final customer = _customers.firstWhere((c) => c['id'] == _selectedCustomer);
    return customer['name']!;
  }

  Future<void> _selectDate(bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : _endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  Future<void> _startMaintenance() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isProcessing = true;
      });

      // محاكاة عملية الفحص
      await Future.delayed(const Duration(seconds: 3));

      setState(() {
        _isProcessing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('تم اكتشاف ${_detectedIssues.length} مشكلة في الطلبيات'),
            backgroundColor:
                _detectedIssues.isEmpty ? Colors.green : Colors.orange,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  void _fixOrderIssue(int issueId) {
    setState(() {
      _detectedIssues.removeWhere((issue) => issue['id'] == issueId);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إصلاح مشكلة الطلبية رقم $issueId'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _fixAllIssues() {
    final issueCount = _detectedIssues.length;
    setState(() {
      _detectedIssues.clear();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إصلاح جميع مشاكل الطلبيات ($issueCount مشكلة)'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _viewOrderDetails(String orderNumber) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض تفاصيل الطلبية $orderNumber')),
    );
  }

  void _showOrdersReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تقرير الطلبيات')),
    );
  }

  void _showMaintenanceHistory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض سجل صيانة الطلبيات')),
    );
  }
}
