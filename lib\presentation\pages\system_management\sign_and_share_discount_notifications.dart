import 'package:flutter/material.dart';

/// صفحة توقيع ومشاركة إشعارات الخصم
/// تتيح توقيع ومشاركة إشعارات الخصم مع العملاء
class SignAndShareDiscountNotificationsPage extends StatefulWidget {
  const SignAndShareDiscountNotificationsPage({super.key});

  @override
  State<SignAndShareDiscountNotificationsPage> createState() =>
      _SignAndShareDiscountNotificationsPageState();
}

class _SignAndShareDiscountNotificationsPageState
    extends State<SignAndShareDiscountNotificationsPage> {
  final _searchController = TextEditingController();

  String _searchQuery = '';
  String? _selectedType;
  String? _selectedStatus;
  bool _selectAll = false;

  final List<Map<String, String>> _notificationTypes = [
    {'id': 'seasonal', 'name': 'خصم موسمي'},
    {'id': 'quantity', 'name': 'خصم كمية'},
    {'id': 'loyalty', 'name': 'خصم عملاء مميزين'},
    {'id': 'clearance', 'name': 'خصم تصفية'},
    {'id': 'promotional', 'name': 'خصم ترويجي'},
  ];

  final List<Map<String, String>> _statuses = [
    {'id': 'draft', 'name': 'مسودة'},
    {'id': 'signed', 'name': 'موقع'},
    {'id': 'shared', 'name': 'مشارك'},
    {'id': 'expired', 'name': 'منتهي الصلاحية'},
  ];

  final List<Map<String, dynamic>> _notifications = [
    {
      'id': 'disc1',
      'title': 'خصم نهاية العام 2024',
      'type': 'خصم موسمي',
      'discount': '25%',
      'validFrom': '2024/01/20',
      'validTo': '2024/01/31',
      'customers': ['شركة الأمل', 'مؤسسة النور', 'شركة الفجر'],
      'status': 'مسودة',
      'isSelected': false,
      'signed': false,
      'shared': false,
      'signedBy': '',
      'signDate': '',
      'shareMethod': '',
      'shareDate': '',
    },
    {
      'id': 'disc2',
      'title': 'خصم الكمية للطلبات الكبيرة',
      'type': 'خصم كمية',
      'discount': '15%',
      'validFrom': '2024/01/15',
      'validTo': '2024/02/15',
      'customers': ['شركة التجارة الكبرى', 'مجموعة الأعمال'],
      'status': 'موقع',
      'isSelected': false,
      'signed': true,
      'shared': false,
      'signedBy': 'مدير المبيعات',
      'signDate': '2024/01/15 10:30',
      'shareMethod': '',
      'shareDate': '',
    },
    {
      'id': 'disc3',
      'title': 'خصم العملاء المميزين',
      'type': 'خصم عملاء مميزين',
      'discount': '20%',
      'validFrom': '2024/01/10',
      'validTo': '2024/03/10',
      'customers': ['شركة الأمل', 'مؤسسة الإبداع'],
      'status': 'مشارك',
      'isSelected': false,
      'signed': true,
      'shared': true,
      'signedBy': 'المدير العام',
      'signDate': '2024/01/10 14:20',
      'shareMethod': 'واتساب',
      'shareDate': '2024/01/10 15:00',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('توقيع ومشاركة إشعارات الخصم'),
        backgroundColor: Colors.amber,
        foregroundColor: Colors.black,
        actions: [
          IconButton(
            icon: const Icon(Icons.select_all),
            onPressed: _toggleSelectAll,
            tooltip: 'تحديد الكل',
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _createNewNotification,
            tooltip: 'إشعار جديد',
          ),
        ],
      ),
      body: Column(
        children: [
          // بطاقة البحث والفلترة
          Card(
            margin: const EdgeInsets.all(16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      labelText: 'البحث في إشعارات الخصم',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedType,
                          decoration: const InputDecoration(
                            labelText: 'نوع الخصم',
                            border: OutlineInputBorder(),
                          ),
                          items: [
                            const DropdownMenuItem(
                                value: null, child: Text('جميع الأنواع')),
                            ..._notificationTypes
                                .map((type) => DropdownMenuItem(
                                      value: type['id'],
                                      child: Text(type['name']!),
                                    )),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedType = value;
                            });
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedStatus,
                          decoration: const InputDecoration(
                            labelText: 'الحالة',
                            border: OutlineInputBorder(),
                          ),
                          items: [
                            const DropdownMenuItem(
                                value: null, child: Text('جميع الحالات')),
                            ..._statuses.map((status) => DropdownMenuItem(
                                  value: status['id'],
                                  child: Text(status['name']!),
                                )),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedStatus = value;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // إحصائيات سريعة
          Card(
            margin: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatCard(
                      'المجموع', _notifications.length.toString(), Colors.blue),
                  _buildStatCard(
                      'مسودة',
                      _notifications
                          .where((n) => n['status'] == 'مسودة')
                          .length
                          .toString(),
                      Colors.grey),
                  _buildStatCard(
                      'موقع',
                      _notifications
                          .where((n) => n['signed'] == true)
                          .length
                          .toString(),
                      Colors.green),
                  _buildStatCard(
                      'مشارك',
                      _notifications
                          .where((n) => n['shared'] == true)
                          .length
                          .toString(),
                      Colors.amber),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // قائمة الإشعارات
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _notifications.length,
              itemBuilder: (context, index) {
                final notification = _notifications[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: CheckboxListTile(
                    value: notification['isSelected'],
                    onChanged: (value) {
                      setState(() {
                        notification['isSelected'] = value ?? false;
                      });
                    },
                    secondary: CircleAvatar(
                      backgroundColor: _getStatusColor(notification['status']),
                      child: Icon(
                        _getStatusIcon(notification['status']),
                        color: Colors.white,
                      ),
                    ),
                    title: Text(
                      notification['title'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                            '${notification['type']} | خصم ${notification['discount']}'),
                        Text(
                            'صالح من ${notification['validFrom']} إلى ${notification['validTo']}'),
                        Text(
                            'العملاء: ${(notification['customers'] as List).join(', ')}'),
                        if (notification['signed'])
                          Text(
                              'موقع بواسطة ${notification['signedBy']} في ${notification['signDate']}',
                              style: const TextStyle(
                                  color: Colors.green, fontSize: 12)),
                        if (notification['shared'])
                          Text(
                              'مشارك عبر ${notification['shareMethod']} في ${notification['shareDate']}',
                              style: const TextStyle(
                                  color: Colors.amber, fontSize: 12)),
                      ],
                    ),
                    isThreeLine: true,
                  ),
                );
              },
            ),
          ),
        ],
      ),
      bottomNavigationBar: _getSelectedNotifications().isNotEmpty
          ? Container(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _signSelected,
                      icon: const Icon(Icons.edit),
                      label:
                          Text('توقيع (${_getSelectedNotifications().length})'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _shareSelected,
                      icon: const Icon(Icons.share),
                      label: Text(
                          'مشاركة (${_getSelectedNotifications().length})'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.amber,
                        foregroundColor: Colors.black,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
            )
          : null,
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Column(
      children: [
        CircleAvatar(
          backgroundColor: color,
          child: Text(value,
              style: const TextStyle(
                  color: Colors.white, fontWeight: FontWeight.bold)),
        ),
        const SizedBox(height: 4),
        Text(title,
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold)),
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'مسودة':
        return Colors.grey;
      case 'موقع':
        return Colors.green;
      case 'مشارك':
        return Colors.amber;
      case 'منتهي الصلاحية':
        return Colors.red;
      default:
        return Colors.blue;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'مسودة':
        return Icons.edit;
      case 'موقع':
        return Icons.verified;
      case 'مشارك':
        return Icons.share;
      case 'منتهي الصلاحية':
        return Icons.access_time;
      default:
        return Icons.notifications;
    }
  }

  List<Map<String, dynamic>> _getSelectedNotifications() {
    return _notifications
        .where((notification) => notification['isSelected'] == true)
        .toList();
  }

  void _toggleSelectAll() {
    setState(() {
      _selectAll = !_selectAll;
      for (var notification in _notifications) {
        notification['isSelected'] = _selectAll;
      }
    });
  }

  void _signSelected() {
    final selectedNotifications = _getSelectedNotifications();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد التوقيع'),
        content: Text(
            'هل أنت متأكد من توقيع ${selectedNotifications.length} إشعار خصم؟'),
        actions: [
          TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                for (var notification in selectedNotifications) {
                  notification['signed'] = true;
                  notification['signedBy'] = 'المدير العام';
                  notification['signDate'] =
                      '${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year} ${DateTime.now().hour}:${DateTime.now().minute}';
                  notification['status'] = 'موقع';
                  notification['isSelected'] = false;
                }
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                      'تم توقيع ${selectedNotifications.length} إشعار خصم'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
            child: const Text('توقيع', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _shareSelected() {
    final selectedNotifications = _getSelectedNotifications();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد المشاركة'),
        content: Text(
            'هل أنت متأكد من مشاركة ${selectedNotifications.length} إشعار خصم؟'),
        actions: [
          TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                for (var notification in selectedNotifications) {
                  notification['shared'] = true;
                  notification['shareMethod'] = 'واتساب';
                  notification['shareDate'] =
                      '${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year} ${DateTime.now().hour}:${DateTime.now().minute}';
                  notification['status'] = 'مشارك';
                  notification['isSelected'] = false;
                }
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                      'تم مشاركة ${selectedNotifications.length} إشعار خصم'),
                  backgroundColor: Colors.amber,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.amber),
            child: const Text('مشاركة', style: TextStyle(color: Colors.black)),
          ),
        ],
      ),
    );
  }

  void _createNewNotification() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء إشعار خصم جديد')),
    );
  }
}
