import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة الأدوات الرئيسية
/// تعرض الأدوات المساعدة والمرافق
class ToolsPage extends StatelessWidget {
  const ToolsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.tools),
        backgroundColor: Colors.grey.shade700,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقات الإحصائيات السريعة
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    title: localizations.savedNotes,
                    value: '25',
                    icon: Icons.note,
                    color: Colors.blue,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    title: localizations.contacts,
                    value: '156',
                    icon: Icons.phone,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    title: localizations.calculations,
                    value: '∞',
                    icon: Icons.calculate,
                    color: Colors.orange,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    title: localizations.availableTools,
                    value: '8',
                    icon: Icons.build,
                    color: Colors.purple,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // قائمة الأدوات المتاحة
            Text(
              localizations.availableToolsList,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  _buildToolCard(
                    title: localizations.calculator,
                    icon: Icons.calculate,
                    color: Colors.blue,
                    onTap: () => _navigateToTool(context, 'calculator'),
                  ),
                  _buildToolCard(
                    title: localizations.notes,
                    icon: Icons.note,
                    color: Colors.green,
                    onTap: () => _navigateToTool(context, 'notes'),
                  ),
                  _buildToolCard(
                    title: localizations.phoneDirectory,
                    icon: Icons.phone,
                    color: Colors.orange,
                    onTap: () => _navigateToTool(context, 'phone_directory'),
                  ),
                  _buildToolCard(
                    title: localizations.currencyConverter,
                    icon: Icons.currency_exchange,
                    color: Colors.purple,
                    onTap: () => _navigateToTool(context, 'currency_converter'),
                  ),
                  _buildToolCard(
                    title: localizations.barcodeGenerator,
                    icon: Icons.qr_code,
                    color: Colors.teal,
                    onTap: () => _navigateToTool(context, 'barcode_generator'),
                  ),
                  _buildToolCard(
                    title: localizations.calendar,
                    icon: Icons.calendar_today,
                    color: Colors.indigo,
                    onTap: () => _navigateToTool(context, 'calendar'),
                  ),
                  _buildToolCard(
                    title: localizations.timer,
                    icon: Icons.timer,
                    color: Colors.red,
                    onTap: () => _navigateToTool(context, 'timer'),
                  ),
                  _buildToolCard(
                    title: localizations.textTools,
                    icon: Icons.text_fields,
                    color: Colors.brown,
                    onTap: () => _navigateToTool(context, 'text_tools'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const Spacer(),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة أداة
  Widget _buildToolCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 40,
                color: color,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// التنقل إلى أداة معينة
  void _navigateToTool(BuildContext context, String tool) {
    final localizations = AppLocalizations.of(context);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('${localizations.navigateToTool}: $tool')),
    );
  }
}
