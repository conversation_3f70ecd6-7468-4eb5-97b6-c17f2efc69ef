import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة صيانة ملف أصناف الموردين
/// تتيح فحص وإصلاح مشاكل ربط الأصناف بالموردين
class MaintainSupplierItemsPage extends StatefulWidget {
  const MaintainSupplierItemsPage({super.key});

  @override
  State<MaintainSupplierItemsPage> createState() =>
      _MaintainSupplierItemsPageState();
}

class _MaintainSupplierItemsPageState extends State<MaintainSupplierItemsPage> {
  final _formKey = GlobalKey<FormState>();

  String? _selectedSupplier;
  String? _selectedCategory;
  String? _maintenanceType;
  bool _isProcessing = false;
  bool _autoFix = false;
  bool _createReport = true;
  bool _includeInactive = false;

  final List<Map<String, String>> _suppliers = [
    {'id': 'all', 'name': 'جميع الموردين'},
    {'id': 'supplier1', 'name': 'شركة التقنية المتقدمة'},
    {'id': 'supplier2', 'name': 'مؤسسة الأجهزة الذكية'},
    {'id': 'supplier3', 'name': 'شركة المكاتب الحديثة'},
    {'id': 'supplier4', 'name': 'مجموعة القرطاسية'},
  ];

  final List<Map<String, String>> _categories = [
    {'id': 'all', 'name': 'جميع الفئات'},
    {'id': 'electronics', 'name': 'الإلكترونيات'},
    {'id': 'furniture', 'name': 'الأثاث المكتبي'},
    {'id': 'stationery', 'name': 'القرطاسية'},
    {'id': 'equipment', 'name': 'المعدات'},
  ];

  final List<Map<String, String>> _maintenanceTypes = [
    {
      'id': 'orphaned_items',
      'name': 'أصناف بدون موردين',
      'description': 'أصناف غير مربوطة بأي مورد'
    },
    {
      'id': 'duplicate_links',
      'name': 'روابط مكررة',
      'description': 'نفس الصنف مربوط بنفس المورد أكثر من مرة'
    },
    {
      'id': 'invalid_prices',
      'name': 'أسعار غير صحيحة',
      'description': 'أسعار سالبة أو صفرية'
    },
    {
      'id': 'outdated_prices',
      'name': 'أسعار قديمة',
      'description': 'أسعار لم تُحدث لفترة طويلة'
    },
    {
      'id': 'missing_codes',
      'name': 'أكواد مفقودة',
      'description': 'أصناف بدون أكواد موردين'
    },
    {
      'id': 'inactive_suppliers',
      'name': 'موردين غير نشطين',
      'description': 'أصناف مربوطة بموردين غير نشطين'
    },
    {
      'id': 'all',
      'name': 'فحص شامل',
      'description': 'فحص جميع المشاكل المحتملة'
    },
  ];

  final List<Map<String, dynamic>> _detectedIssues = [
    {
      'id': 1,
      'itemCode': 'ITM001',
      'itemName': 'لابتوب ديل XPS 13',
      'supplierName': 'غير محدد',
      'type': 'orphaned_items',
      'issue': 'صنف غير مربوط بأي مورد',
      'details': 'الصنف موجود في المخزون لكن غير مربوط بمورد',
      'severity': 'high',
      'suggestion': 'ربط الصنف بالمورد المناسب'
    },
    {
      'id': 2,
      'itemCode': 'ITM002',
      'itemName': 'طابعة HP LaserJet',
      'supplierName': 'شركة التقنية المتقدمة',
      'type': 'duplicate_links',
      'issue': 'ربط مكرر مع نفس المورد',
      'details': 'الصنف مربوط 3 مرات مع نفس المورد',
      'severity': 'medium',
      'suggestion': 'حذف الروابط المكررة والاحتفاظ بواحد فقط'
    },
    {
      'id': 3,
      'itemCode': 'ITM003',
      'itemName': 'مكتب خشبي فاخر',
      'supplierName': 'شركة المكاتب الحديثة',
      'type': 'invalid_prices',
      'issue': 'سعر سالب: -150.00 ر.س',
      'details': 'سعر المورد سالب أو صفري',
      'severity': 'high',
      'suggestion': 'تصحيح السعر إلى قيمة موجبة'
    },
    {
      'id': 4,
      'itemCode': 'ITM004',
      'itemName': 'أقلام حبر جاف',
      'supplierName': 'مجموعة القرطاسية',
      'type': 'outdated_prices',
      'issue': 'لم يُحدث السعر منذ 6 أشهر',
      'details': 'آخر تحديث: 2023/07/15',
      'severity': 'low',
      'suggestion': 'مراجعة وتحديث السعر'
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.maintainSupplierItems),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.link),
            onPressed: _showSupplierItemsReport,
            tooltip: 'تقرير ربط الأصناف',
          ),
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: _showMaintenanceHistory,
            tooltip: 'سجل الصيانة',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // بطاقة معايير الفحص
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'معايير الفحص',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.teal,
                      ),
                    ),
                    const SizedBox(height: 16),

                    Row(
                      children: [
                        // المورد
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedSupplier,
                            decoration: const InputDecoration(
                              labelText: 'المورد',
                              prefixIcon: Icon(Icons.business),
                              border: OutlineInputBorder(),
                            ),
                            items: _suppliers
                                .map<DropdownMenuItem<String>>((supplier) {
                              return DropdownMenuItem<String>(
                                value: supplier['id'],
                                child: Text(
                                  supplier['name']!,
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedSupplier = value;
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'يرجى اختيار المورد';
                              }
                              return null;
                            },
                          ),
                        ),

                        const SizedBox(width: 16),

                        // الفئة
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedCategory,
                            decoration: const InputDecoration(
                              labelText: 'فئة الأصناف',
                              prefixIcon: Icon(Icons.category),
                              border: OutlineInputBorder(),
                            ),
                            items: _categories
                                .map<DropdownMenuItem<String>>((category) {
                              return DropdownMenuItem<String>(
                                value: category['id'],
                                child: Text(
                                  category['name']!,
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedCategory = value;
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'يرجى اختيار الفئة';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // نوع الصيانة
                    DropdownButtonFormField<String>(
                      value: _maintenanceType,
                      decoration: const InputDecoration(
                        labelText: 'نوع الصيانة',
                        prefixIcon: Icon(Icons.build),
                        border: OutlineInputBorder(),
                      ),
                      items: _maintenanceTypes
                          .map<DropdownMenuItem<String>>((type) {
                        return DropdownMenuItem<String>(
                          value: type['id'],
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                type['name']!,
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                              Text(
                                type['description']!,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _maintenanceType = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار نوع الصيانة';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة خيارات الصيانة
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'خيارات الصيانة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.teal,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SwitchListTile(
                      title: const Text('تضمين الموردين غير النشطين'),
                      subtitle:
                          const Text('فحص الأصناف المربوطة بموردين غير نشطين'),
                      value: _includeInactive,
                      onChanged: (value) {
                        setState(() {
                          _includeInactive = value;
                        });
                      },
                    ),
                    SwitchListTile(
                      title: const Text('الإصلاح التلقائي'),
                      subtitle: const Text('إصلاح المشاكل البسيطة تلقائياً'),
                      value: _autoFix,
                      onChanged: (value) {
                        setState(() {
                          _autoFix = value;
                        });
                      },
                    ),
                    SwitchListTile(
                      title: const Text('إنشاء تقرير مفصل'),
                      subtitle:
                          const Text('إنشاء تقرير بجميع المشاكل المكتشفة'),
                      value: _createReport,
                      onChanged: (value) {
                        setState(() {
                          _createReport = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة المشاكل المكتشفة
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'المشاكل المكتشفة في ربط الأصناف',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.teal,
                      ),
                    ),
                    const SizedBox(height: 16),
                    if (_detectedIssues.isEmpty)
                      const Center(
                        child: Text(
                          'لم يتم اكتشاف أي مشاكل بعد\nقم بتشغيل الفحص أولاً',
                          textAlign: TextAlign.center,
                          style: TextStyle(color: Colors.grey),
                        ),
                      )
                    else
                      ...(_detectedIssues.map((issue) => Card(
                            margin: const EdgeInsets.symmetric(vertical: 4),
                            child: ExpansionTile(
                              leading: CircleAvatar(
                                backgroundColor:
                                    _getSeverityColor(issue['severity']),
                                child: Icon(
                                  _getIssueTypeIcon(issue['type']),
                                  color: Colors.white,
                                ),
                              ),
                              title: Text(
                                  '${issue['itemCode']} - ${issue['itemName']}'),
                              subtitle:
                                  Text('المورد: ${issue['supplierName']}'),
                              children: [
                                Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text('المشكلة: ${issue['issue']}'),
                                      const SizedBox(height: 8),
                                      Text('التفاصيل: ${issue['details']}'),
                                      const SizedBox(height: 8),
                                      Text(
                                        'الحل المقترح: ${issue['suggestion']}',
                                        style:
                                            const TextStyle(color: Colors.blue),
                                      ),
                                      const SizedBox(height: 16),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.end,
                                        children: [
                                          TextButton.icon(
                                            onPressed: () => _viewItemDetails(
                                                issue['itemCode']),
                                            icon: const Icon(Icons.visibility),
                                            label: const Text('عرض الصنف'),
                                          ),
                                          const SizedBox(width: 8),
                                          TextButton.icon(
                                            onPressed: () => _linkToSupplier(
                                                issue['itemCode']),
                                            icon: const Icon(Icons.link),
                                            label: const Text('ربط بمورد'),
                                          ),
                                          const SizedBox(width: 8),
                                          ElevatedButton.icon(
                                            onPressed: () =>
                                                _fixSupplierItemIssue(
                                                    issue['id']),
                                            icon: const Icon(Icons.build),
                                            label: const Text('إصلاح'),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.orange,
                                              foregroundColor: Colors.white,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ))),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // معاينة العملية
            if (_selectedSupplier != null &&
                _selectedCategory != null &&
                _maintenanceType != null)
              Card(
                color: Colors.teal.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'معاينة عملية الصيانة',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.teal,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildPreviewRow('المورد:', _getSupplierName()),
                      _buildPreviewRow('الفئة:', _getCategoryName()),
                      _buildPreviewRow(
                          'نوع الصيانة:', _getMaintenanceTypeName()),
                      _buildPreviewRow('الموردين غير النشطين:',
                          _includeInactive ? 'مُضمنين' : 'مستبعدين'),
                      _buildPreviewRow(
                          'الإصلاح التلقائي:', _autoFix ? 'مفعل' : 'معطل'),
                      _buildPreviewRow('المشاكل المكتشفة:',
                          '${_detectedIssues.length} مشكلة'),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 24),

            // أزرار العمليات
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isProcessing ? null : _startMaintenance,
                    icon: _isProcessing
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.search),
                    label: Text(
                        _isProcessing ? 'جاري الفحص...' : 'بدء فحص الروابط'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.teal,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _detectedIssues.isEmpty ? null : _fixAllIssues,
                    icon: const Icon(Icons.build),
                    label: const Text('إصلاح جميع المشاكل'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Color _getSeverityColor(String severity) {
    switch (severity) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.yellow;
      default:
        return Colors.grey;
    }
  }

  IconData _getIssueTypeIcon(String type) {
    switch (type) {
      case 'orphaned_items':
        return Icons.link_off;
      case 'duplicate_links':
        return Icons.content_copy;
      case 'invalid_prices':
        return Icons.money_off;
      case 'outdated_prices':
        return Icons.schedule;
      case 'missing_codes':
        return Icons.qr_code_2;
      case 'inactive_suppliers':
        return Icons.business_center;
      default:
        return Icons.error;
    }
  }

  String _getSupplierName() {
    if (_selectedSupplier == null) return 'غير محدد';
    final supplier = _suppliers.firstWhere((s) => s['id'] == _selectedSupplier);
    return supplier['name']!;
  }

  String _getCategoryName() {
    if (_selectedCategory == null) return 'غير محدد';
    final category =
        _categories.firstWhere((c) => c['id'] == _selectedCategory);
    return category['name']!;
  }

  String _getMaintenanceTypeName() {
    if (_maintenanceType == null) return 'غير محدد';
    final type =
        _maintenanceTypes.firstWhere((t) => t['id'] == _maintenanceType);
    return type['name']!;
  }

  Future<void> _startMaintenance() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isProcessing = true;
      });

      // محاكاة عملية الفحص
      await Future.delayed(const Duration(seconds: 3));

      setState(() {
        _isProcessing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'تم اكتشاف ${_detectedIssues.length} مشكلة في ربط الأصناف بالموردين'),
            backgroundColor:
                _detectedIssues.isEmpty ? Colors.green : Colors.orange,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  void _fixSupplierItemIssue(int issueId) {
    setState(() {
      _detectedIssues.removeWhere((issue) => issue['id'] == issueId);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إصلاح مشكلة الربط رقم $issueId'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _fixAllIssues() {
    final issueCount = _detectedIssues.length;
    setState(() {
      _detectedIssues.clear();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إصلاح جميع مشاكل ربط الأصناف ($issueCount مشكلة)'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _viewItemDetails(String itemCode) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض تفاصيل الصنف $itemCode')),
    );
  }

  void _linkToSupplier(String itemCode) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('ربط الصنف $itemCode بمورد')),
    );
  }

  void _showSupplierItemsReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تقرير ربط الأصناف بالموردين')),
    );
  }

  void _showMaintenanceHistory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض سجل صيانة ربط الأصناف')),
    );
  }
}
