class AccountReportsLocalizations {
  static const Map<String, Map<String, String>> values = {
    'en': {
      // صفحات وحدة تقارير الحسابات
      'account_reports': 'Account Reports',
      'trial_balance': 'Trial Balance',
      'income_statement': 'Income Statement',
      'balance_sheet': 'Balance Sheet',
      'general_ledger': 'General Ledger',
      'daily_restriction_report': 'Daily Restriction Report',
      'continuous_account_statement': 'Continuous Account Statement',
      'main_account_statement': 'Main Account Statement',
      'print_reports': 'Print Reports',
      'export_reports': 'Export Reports',
      'search_reports': 'Search in reports...',
      'report_type': 'Report Type',
      'all_reports': 'All Reports',
      'financial_reports': 'Financial Reports',
      'accounting_reports': 'Accounting Reports',
      'analytical_reports': 'Analytical Reports',
      'reports_count': 'Reports',
      'accounts_count': 'Accounts',
      'balances': 'Balances',
      'entries': 'Entries',
      'custom_report': 'Custom Report',
      'navigate_to_report': 'Navigate to report',
      'sub_account_statement': 'Sub Account Statement',
      'account_group_balances': 'Account Group Balances',
      'branch_representatives_movement': 'Branch Representatives Movement',
      'representatives_ratios_by_categories':
          'Representatives Ratios by Categories',
      'net_profit_period': 'Net Profit Period',
      'daily_income_report': 'Daily Income Report',
      'bank_dues_statement': 'Bank Dues Statement',
      'proportional_debit_credit_statement':
          'Proportional Debit Credit Statement',
      'expense_monitoring_reports': 'Expense Monitoring Reports',
      'cost_center_expenses': 'Cost Center Expenses',
      'revenue_monitoring_report': 'Revenue Monitoring Report',
      'cost_center_revenue': 'Cost Center Revenue',
      'cost_center_evaluation': 'Cost Center Evaluation',
      'accounts_without_movement': 'Accounts Without Movement',
      'customers_without_movement': 'Customers Without Movement',
      'cash_flows': 'Cash Flows',
      'bank_statement': 'Bank Statement',
      'missing_sequences': 'Missing Sequences',
      'branch_debt_aging': 'Branch Debt Aging',
    },
    'ar': {
      // صفحات وحدة تقارير الحسابات
      'account_reports': 'تقارير الحسابات',
      'trial_balance': 'ميزان المراجعة',
      'income_statement': 'قائمة الدخل',
      'balance_sheet': 'الميزانية العمومية',
      'general_ledger': 'دفتر الأستاذ العام',
      'daily_restriction_report': 'تقرير القيود اليومية',
      'continuous_account_statement': 'كشف حساب مستمر',
      'main_account_statement': 'كشف حساب رئيسي',
      'print_reports': 'طباعة التقارير',
      'export_reports': 'تصدير التقارير',
      'search_reports': 'البحث في التقارير...',
      'report_type': 'نوع التقرير',
      'all_reports': 'جميع التقارير',
      'financial_reports': 'التقارير المالية',
      'accounting_reports': 'التقارير المحاسبية',
      'analytical_reports': 'التقارير التحليلية',
      'reports_count': 'التقارير',
      'accounts_count': 'الحسابات',
      'balances': 'الأرصدة',
      'entries': 'القيود',
      'custom_report': 'تقرير مخصص',
      'navigate_to_report': 'الانتقال إلى تقرير',
      'sub_account_statement': 'كشف حساب فرعي',
      'account_group_balances': 'أرصدة مجموعة حسابات',
      'branch_representatives_movement': 'حركة مندوبين فرع معين',
      'representatives_ratios_by_categories':
          'تقرير نسب المندوبين بأصناف معينة',
      'net_profit_period': 'صافي الربح خلال فترة',
      'daily_income_report': 'تقرير الدخل اليومي',
      'bank_dues_statement': 'كشف مستحقات البنوك',
      'proportional_debit_credit_statement': 'كشف حساب نسبي مدين دائن',
      'expense_monitoring_reports': 'تقارير مراقبة النفقات',
      'cost_center_expenses': 'تقرير نفقات مراكز التكلفة',
      'revenue_monitoring_report': 'تقرير مراقبة الإيرادات',
      'cost_center_revenue': 'تقرير إيرادات مركز التكلفة',
      'cost_center_evaluation': 'تقييم مركز التكلفة',
      'accounts_without_movement': 'حسابات بدون حركة',
      'customers_without_movement': 'عملاء بدون حركة',
      'cash_flows': 'التدفقات النقدية',
      'bank_statement': 'كشف البنك',
      'missing_sequences': 'التسلسلات المفقودة',
      'branch_debt_aging': 'تقرير أعمار ديون الفروع',
    },
  };
}
