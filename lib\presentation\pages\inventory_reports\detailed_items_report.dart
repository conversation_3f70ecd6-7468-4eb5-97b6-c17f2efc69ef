import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير مفصل للأصناف
/// يعرض تفاصيل شاملة لجميع الأصناف في المخزون
class DetailedItemsReportPage extends StatefulWidget {
  const DetailedItemsReportPage({super.key});

  @override
  State<DetailedItemsReportPage> createState() => _DetailedItemsReportPageState();
}

class _DetailedItemsReportPageState extends State<DetailedItemsReportPage> {
  String? _selectedCategory;
  String? _selectedSupplier;
  String? _stockStatus = 'all';
  String? _sortBy = 'name';
  String? _searchQuery = '';
  bool _showInactive = false;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير مفصل للأصناف'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
            tooltip: 'البحث المتقدم',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          _buildFilterSection(localizations),
          
          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildStockStatusSection(),
                  const SizedBox(height: 16),
                  _buildItemsTableSection(),
                  const SizedBox(height: 16),
                  _buildCategoryAnalysisSection(),
                  const SizedBox(height: 16),
                  _buildSupplierAnalysisSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.teal[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'البحث في الأصناف',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.search),
                  ),
                  onChanged: (value) => setState(() => _searchQuery = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: localizations.category,
                    border: const OutlineInputBorder(),
                  ),
                  value: _selectedCategory,
                  items: [
                    DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                    DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                    DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                    DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                    const DropdownMenuItem(value: 'home', child: Text('أدوات منزلية')),
                  ],
                  onChanged: (value) => setState(() => _selectedCategory = value),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'المورد',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedSupplier,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الموردين')),
                    DropdownMenuItem(value: 'supplier1', child: Text('شركة التقنية المتقدمة')),
                    DropdownMenuItem(value: 'supplier2', child: Text('مؤسسة الجودة العالمية')),
                    DropdownMenuItem(value: 'supplier3', child: Text('شركة الإمداد الشامل')),
                  ],
                  onChanged: (value) => setState(() => _selectedSupplier = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'حالة المخزون',
                    border: OutlineInputBorder(),
                  ),
                  value: _stockStatus,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الحالات')),
                    DropdownMenuItem(value: 'in_stock', child: Text('متوفر')),
                    DropdownMenuItem(value: 'low_stock', child: Text('مخزون منخفض')),
                    DropdownMenuItem(value: 'out_of_stock', child: Text('نفد المخزون')),
                    DropdownMenuItem(value: 'overstock', child: Text('مخزون زائد')),
                  ],
                  onChanged: (value) => setState(() => _stockStatus = value),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'ترتيب حسب',
                    border: OutlineInputBorder(),
                  ),
                  value: _sortBy,
                  items: const [
                    DropdownMenuItem(value: 'name', child: Text('الاسم')),
                    DropdownMenuItem(value: 'code', child: Text('الكود')),
                    DropdownMenuItem(value: 'category', child: Text('الفئة')),
                    DropdownMenuItem(value: 'stock', child: Text('الكمية')),
                    DropdownMenuItem(value: 'price', child: Text('السعر')),
                    DropdownMenuItem(value: 'cost', child: Text('التكلفة')),
                  ],
                  onChanged: (value) => setState(() => _sortBy = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: CheckboxListTile(
                  title: const Text('عرض الأصناف غير النشطة'),
                  value: _showInactive,
                  onChanged: (value) => setState(() => _showInactive = value!),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.inventory, color: Colors.teal, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص الأصناف',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي الأصناف', '1,245', Colors.teal, Icons.inventory),
                _buildSummaryCard('الأصناف النشطة', '1,180', Colors.green, Icons.check_circle),
                _buildSummaryCard('الأصناف غير النشطة', '65', Colors.red, Icons.cancel),
                _buildSummaryCard('إجمالي القيمة', '8,450,000 ر.س', Colors.blue, Icons.monetization_on),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStockStatusSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.assessment, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'حالة المخزون',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildStatusCard('متوفر', '985', Colors.green, Icons.check),
                _buildStatusCard('مخزون منخفض', '185', Colors.orange, Icons.warning),
                _buildStatusCard('نفد المخزون', '45', Colors.red, Icons.error),
                _buildStatusCard('مخزون زائد', '30', Colors.purple, Icons.trending_up),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل الأصناف',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('الكود')),
                  DataColumn(label: Text('اسم الصنف')),
                  DataColumn(label: Text('الفئة')),
                  DataColumn(label: Text('المورد')),
                  DataColumn(label: Text('الكمية')),
                  DataColumn(label: Text('سعر البيع')),
                  DataColumn(label: Text('التكلفة')),
                  DataColumn(label: Text('الربح')),
                  DataColumn(label: Text('الحالة')),
                  DataColumn(label: Text('إجراءات')),
                ],
                rows: _buildItemRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryAnalysisSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.category, color: Colors.purple, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'تحليل الفئات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildCategoryAnalysisList(),
          ],
        ),
      ),
    );
  }

  Widget _buildSupplierAnalysisSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.business, color: Colors.amber, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'تحليل الموردين',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildSupplierAnalysisList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _addNewItem,
                    icon: const Icon(Icons.add),
                    label: const Text('إضافة صنف جديد'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _bulkUpdate,
                    icon: const Icon(Icons.edit),
                    label: const Text('تحديث مجمع'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _generateBarcodes,
                    icon: const Icon(Icons.qr_code),
                    label: const Text('إنشاء باركود'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _stockAdjustment,
                    icon: const Icon(Icons.tune),
                    label: const Text('تسوية المخزون'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildCategoryAnalysisList() {
    final categories = [
      {'name': 'الإلكترونيات', 'count': '485', 'value': '3,250,000 ر.س', 'percentage': '38.5%'},
      {'name': 'الملابس', 'count': '320', 'value': '2,180,000 ر.س', 'percentage': '25.8%'},
      {'name': 'الأغذية', 'count': '285', 'value': '1,850,000 ر.س', 'percentage': '21.9%'},
      {'name': 'أدوات منزلية', 'count': '155', 'value': '1,170,000 ر.س', 'percentage': '13.8%'},
    ];

    return categories.map((category) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.purple.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.purple.withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.category, color: Colors.purple, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  category['name']!,
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  'العدد: ${category['count']} • القيمة: ${category['value']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.purple.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              category['percentage']!,
              style: const TextStyle(
                color: Colors.purple,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<Widget> _buildSupplierAnalysisList() {
    final suppliers = [
      {'name': 'شركة التقنية المتقدمة', 'count': '385', 'value': '2,850,000 ر.س'},
      {'name': 'مؤسسة الجودة العالمية', 'count': '285', 'value': '2,180,000 ر.س'},
      {'name': 'شركة الإمداد الشامل', 'count': '245', 'value': '1,950,000 ر.س'},
      {'name': 'موردين آخرين', 'count': '330', 'value': '1,470,000 ر.س'},
    ];

    return suppliers.map((supplier) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.amber.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.amber.withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.business, color: Colors.amber, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  supplier['name']!,
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  'الأصناف: ${supplier['count']} • القيمة: ${supplier['value']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildItemRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('ITM001')),
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('شركة التقنية')),
        const DataCell(Text('25')),
        const DataCell(Text('4,500 ر.س')),
        const DataCell(Text('3,200 ر.س')),
        const DataCell(Text('1,300 ر.س')),
        DataCell(_buildStatusBadge('متوفر', Colors.green)),
        DataCell(IconButton(
          icon: const Icon(Icons.edit, color: Colors.blue, size: 16),
          onPressed: () => _editItem('ITM001'),
        )),
      ]),
      DataRow(cells: [
        const DataCell(Text('ITM002')),
        const DataCell(Text('هاتف آيفون 15')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('مؤسسة الجودة')),
        const DataCell(Text('5')),
        const DataCell(Text('3,800 ر.س')),
        const DataCell(Text('2,900 ر.س')),
        const DataCell(Text('900 ر.س')),
        DataCell(_buildStatusBadge('مخزون منخفض', Colors.orange)),
        DataCell(IconButton(
          icon: const Icon(Icons.edit, color: Colors.blue, size: 16),
          onPressed: () => _editItem('ITM002'),
        )),
      ]),
      DataRow(cells: [
        const DataCell(Text('ITM003')),
        const DataCell(Text('بدلة رسمية')),
        const DataCell(Text('ملابس')),
        const DataCell(Text('شركة الإمداد')),
        const DataCell(Text('0')),
        const DataCell(Text('850 ر.س')),
        const DataCell(Text('450 ر.س')),
        const DataCell(Text('400 ر.س')),
        DataCell(_buildStatusBadge('نفد المخزون', Colors.red)),
        DataCell(IconButton(
          icon: const Icon(Icons.edit, color: Colors.blue, size: 16),
          onPressed: () => _editItem('ITM003'),
        )),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusCard(String title, String count, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                count,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(status, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  void _showSearchDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('فتح البحث المتقدم')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _addNewItem() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إضافة صنف جديد')),
    );
  }

  void _bulkUpdate() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحديث مجمع للأصناف')),
    );
  }

  void _generateBarcodes() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء باركود للأصناف')),
    );
  }

  void _stockAdjustment() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تسوية المخزون')),
    );
  }

  void _editItem(String itemCode) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تعديل الصنف: $itemCode')),
    );
  }
}
