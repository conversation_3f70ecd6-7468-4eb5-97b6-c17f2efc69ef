import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة طباعة الفسوحات
/// يعرض ويطبع فسوحات البضائع الجمركية
class PrintClearancesReportPage extends StatefulWidget {
  const PrintClearancesReportPage({super.key});

  @override
  State<PrintClearancesReportPage> createState() => _PrintClearancesReportPageState();
}

class _PrintClearancesReportPageState extends State<PrintClearancesReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _clearanceStatus = 'all';
  String? _customsOffice;
  String? _selectedSupplier;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('طباعة الفسوحات'),
        backgroundColor: Colors.brown,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printAllClearances,
            tooltip: 'طباعة جميع الفسوحات',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportClearances,
            tooltip: 'تصدير الفسوحات',
          ),
          IconButton(
            icon: const Icon(Icons.qr_code),
            onPressed: _generateQRCodes,
            tooltip: 'إنشاء رموز QR',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.brown[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.fromDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, true),
                        controller: TextEditingController(
                          text: _startDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.toDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, false),
                        controller: TextEditingController(
                          text: _endDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'حالة الفسح',
                          border: OutlineInputBorder(),
                        ),
                        value: _clearanceStatus,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الحالات')),
                          DropdownMenuItem(value: 'pending', child: Text('قيد الانتظار')),
                          DropdownMenuItem(value: 'approved', child: Text('مفسوحة')),
                          DropdownMenuItem(value: 'rejected', child: Text('مرفوضة')),
                          DropdownMenuItem(value: 'in_progress', child: Text('قيد المعالجة')),
                        ],
                        onChanged: (value) => setState(() => _clearanceStatus = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'مكتب الجمارك',
                          border: OutlineInputBorder(),
                        ),
                        value: _customsOffice,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع المكاتب')),
                          DropdownMenuItem(value: 'riyadh', child: Text('جمارك الرياض')),
                          DropdownMenuItem(value: 'jeddah', child: Text('جمارك جدة')),
                          DropdownMenuItem(value: 'dammam', child: Text('جمارك الدمام')),
                          DropdownMenuItem(value: 'khobar', child: Text('جمارك الخبر')),
                        ],
                        onChanged: (value) => setState(() => _customsOffice = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'المورد',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedSupplier,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الموردين')),
                          DropdownMenuItem(value: 'supplier1', child: Text('شركة التقنية المتقدمة')),
                          DropdownMenuItem(value: 'supplier2', child: Text('مؤسسة الجودة العالمية')),
                          DropdownMenuItem(value: 'supplier3', child: Text('شركة الإمداد الشامل')),
                        ],
                        onChanged: (value) => setState(() => _selectedSupplier = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _loadClearances,
                        icon: const Icon(Icons.search),
                        label: const Text('تحميل الفسوحات'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.brown,
                          foregroundColor: Colors.white,
                          minimumSize: const Size(double.infinity, 48),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // محتوى الفسوحات
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص الفسوحات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.assignment, color: Colors.brown, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'ملخص الفسوحات',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSummaryCard('إجمالي الفسوحات', '156', Colors.blue, Icons.assignment),
                              _buildSummaryCard('مفسوحة', '89', Colors.green, Icons.check_circle),
                              _buildSummaryCard('قيد المعالجة', '45', Colors.orange, Icons.hourglass_empty),
                              _buildSummaryCard('مرفوضة', '22', Colors.red, Icons.cancel),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // قائمة الفسوحات للطباعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'قائمة الفسوحات للطباعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ..._buildClearancesList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول تفاصيل الفسوحات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل الفسوحات',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('رقم الفسح')),
                                DataColumn(label: Text('تاريخ الفسح')),
                                DataColumn(label: Text('المورد')),
                                DataColumn(label: Text('مكتب الجمارك')),
                                DataColumn(label: Text('قيمة البضاعة')),
                                DataColumn(label: Text('الرسوم الجمركية')),
                                DataColumn(label: Text('الحالة')),
                                DataColumn(label: Text('تاريخ الانتهاء')),
                                DataColumn(label: Text('إجراءات')),
                              ],
                              rows: _buildClearanceRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إعدادات الطباعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.print_outlined, color: Colors.indigo, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'إعدادات الطباعة',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildPrintOptionCard('طباعة فردية', 'طباعة كل فسح منفصل', Icons.description, Colors.blue),
                              _buildPrintOptionCard('طباعة مجمعة', 'طباعة جميع الفسوحات معاً', Icons.library_books, Colors.green),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              _buildPrintOptionCard('مع الباركود', 'إضافة رمز شريطي لكل فسح', Icons.qr_code, Colors.orange),
                              _buildPrintOptionCard('مع التوقيع', 'إضافة مساحة للتوقيع', Icons.edit, Colors.purple),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _printSelected,
                                  icon: const Icon(Icons.print),
                                  label: const Text('طباعة المحدد'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _emailClearances,
                                  icon: const Icon(Icons.email),
                                  label: const Text('إرسال بالإيميل'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _archiveClearances,
                                  icon: const Icon(Icons.archive),
                                  label: const Text('أرشفة'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _trackStatus,
                                  icon: const Icon(Icons.track_changes),
                                  label: const Text('تتبع الحالة'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildClearancesList() {
    final clearances = [
      {'id': 'CLR-2024-001', 'supplier': 'شركة التقنية المتقدمة', 'value': '485,000 ر.س', 'status': 'مفسوحة'},
      {'id': 'CLR-2024-002', 'supplier': 'مؤسسة الجودة العالمية', 'value': '320,000 ر.س', 'status': 'قيد المعالجة'},
      {'id': 'CLR-2024-003', 'supplier': 'شركة الإمداد الشامل', 'value': '285,000 ر.س', 'status': 'مفسوحة'},
      {'id': 'CLR-2024-004', 'supplier': 'مؤسسة التوريد الشاملة', 'value': '185,000 ر.س', 'status': 'مرفوضة'},
    ];

    return clearances.map((clearance) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.brown.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.brown.withOpacity(0.05),
      ),
      child: Row(
        children: [
          Checkbox(
            value: false,
            onChanged: (value) {},
          ),
          const SizedBox(width: 12),
          const Icon(Icons.assignment, color: Colors.brown, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  clearance['id']!,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${clearance['supplier']} • ${clearance['value']}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          _buildStatusBadge(clearance['status']!),
          const SizedBox(width: 8),
          IconButton(
            icon: const Icon(Icons.print, color: Colors.blue),
            onPressed: () => _printSingleClearance(clearance['id']!),
            tooltip: 'طباعة',
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildClearanceRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('CLR-2024-001')),
        const DataCell(Text('2024-02-15')),
        const DataCell(Text('شركة التقنية المتقدمة')),
        const DataCell(Text('جمارك الرياض')),
        const DataCell(Text('485,000 ر.س')),
        const DataCell(Text('48,500 ر.س')),
        DataCell(_buildStatusBadge('مفسوحة')),
        const DataCell(Text('2024-03-15')),
        DataCell(Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.print, color: Colors.blue, size: 16),
              onPressed: () => _printSingleClearance('CLR-2024-001'),
            ),
            IconButton(
              icon: const Icon(Icons.edit, color: Colors.orange, size: 16),
              onPressed: () => _editClearance('CLR-2024-001'),
            ),
          ],
        )),
      ]),
      DataRow(cells: [
        const DataCell(Text('CLR-2024-002')),
        const DataCell(Text('2024-02-14')),
        const DataCell(Text('مؤسسة الجودة العالمية')),
        const DataCell(Text('جمارك جدة')),
        const DataCell(Text('320,000 ر.س')),
        const DataCell(Text('32,000 ر.س')),
        DataCell(_buildStatusBadge('قيد المعالجة')),
        const DataCell(Text('2024-03-14')),
        DataCell(Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.print, color: Colors.blue, size: 16),
              onPressed: () => _printSingleClearance('CLR-2024-002'),
            ),
            IconButton(
              icon: const Icon(Icons.edit, color: Colors.orange, size: 16),
              onPressed: () => _editClearance('CLR-2024-002'),
            ),
          ],
        )),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPrintOptionCard(String title, String description, IconData icon, Color color) {
    return Expanded(
      child: Card(
        child: InkWell(
          onTap: () => _selectPrintOption(title),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Icon(icon, color: color, size: 32),
                const SizedBox(height: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(fontSize: 10),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status) {
    Color color;
    switch (status) {
      case 'مفسوحة':
        color = Colors.green;
        break;
      case 'قيد المعالجة':
        color = Colors.orange;
        break;
      case 'مرفوضة':
        color = Colors.red;
        break;
      default:
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        status,
        style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _loadClearances() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تحميل الفسوحات بنجاح')),
    );
  }

  void _printAllClearances() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة جميع الفسوحات...')),
    );
  }

  void _exportClearances() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير الفسوحات...')),
    );
  }

  void _generateQRCodes() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء رموز QR للفسوحات')),
    );
  }

  void _printSelected() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة الفسوحات المحددة')),
    );
  }

  void _emailClearances() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إرسال الفسوحات بالإيميل')),
    );
  }

  void _archiveClearances() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('أرشفة الفسوحات المحددة')),
    );
  }

  void _trackStatus() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تتبع حالة الفسوحات')),
    );
  }

  void _printSingleClearance(String clearanceId) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('طباعة الفسح $clearanceId')),
    );
  }

  void _editClearance(String clearanceId) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تعديل الفسح $clearanceId')),
    );
  }

  void _selectPrintOption(String option) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم اختيار خيار: $option')),
    );
  }
}
