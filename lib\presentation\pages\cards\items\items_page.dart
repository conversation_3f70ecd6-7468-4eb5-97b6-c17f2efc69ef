import 'package:flutter/material.dart';
import '../../../../core/localization/app_localizations.dart';
import 'add_a_new_item.dart';
import 'view_items.dart';

/// صفحة إدارة الأصناف الرئيسية
class ItemsPage extends StatelessWidget {
  const ItemsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.items),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // العنوان الرئيسي
            const Text(
              'إدارة الأصناف',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.purple,
              ),
            ),
            const SizedBox(height: 32),

            // الأزرار الرئيسية
            Row(
              children: [
                // زر إضافة صنف جديد
                Expanded(
                  child: _buildMainButton(
                    title: 'إضافة صنف جديد',
                    icon: Icons.add_box,
                    color: Colors.green,
                    onPressed: () => _showAddItemDialog(context),
                  ),
                ),
                const SizedBox(width: 16),
                // زر عرض الأصناف
                Expanded(
                  child: _buildMainButton(
                    title: 'عرض الأصناف',
                    icon: Icons.list_alt,
                    color: Colors.blue,
                    onPressed: () => _showItemsListDialog(context),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر رئيسي
  Widget _buildMainButton({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              Icon(
                icon,
                size: 48,
                color: color,
              ),
              const SizedBox(height: 16),
              Text(
                title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// الانتقال إلى صفحة إضافة صنف جديد
  void _showAddItemDialog(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddNewItemPage(),
      ),
    );
  }

  /// الانتقال إلى صفحة عرض الأصناف
  void _showItemsListDialog(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ViewItemsPage(),
      ),
    );
  }
}
