import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة التسعير الآلي
/// تتيح إدارة قواعد التسعير التلقائي للأصناف
class AutomaticPricingPage extends StatefulWidget {
  const AutomaticPricingPage({super.key});

  @override
  State<AutomaticPricingPage> createState() => _AutomaticPricingPageState();
}

class _AutomaticPricingPageState extends State<AutomaticPricingPage> {
  String _searchQuery = '';
  String _selectedType = 'all';

  // بيانات تجريبية لقواعد التسعير
  final List<Map<String, dynamic>> _pricingRules = [
    {
      'id': 'PR001',
      'name': 'تسعير المواد الغذائية',
      'type': 'نسبة مئوية',
      'category': 'مواد غذائية',
      'costMultiplier': 1.25,
      'minProfit': 15.0,
      'maxProfit': 40.0,
      'isActive': true,
      'priority': 1,
      'description': 'قاعدة تسعير للمواد الغذائية بهامش ربح 25%',
    },
    {
      'id': 'PR002',
      'name': 'تسعير الإلكترونيات',
      'type': 'مبلغ ثابت',
      'category': 'إلكترونيات',
      'costMultiplier': 1.15,
      'minProfit': 10.0,
      'maxProfit': 30.0,
      'isActive': true,
      'priority': 2,
      'description': 'قاعدة تسعير للإلكترونيات بهامش ربح 15%',
    },
    {
      'id': 'PR003',
      'name': 'تسعير الملابس',
      'type': 'نسبة متدرجة',
      'category': 'ملابس',
      'costMultiplier': 2.0,
      'minProfit': 50.0,
      'maxProfit': 100.0,
      'isActive': false,
      'priority': 3,
      'description': 'قاعدة تسعير للملابس بهامش ربح متدرج',
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.automaticPricing),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addPricingRule,
            tooltip: 'إضافة قاعدة تسعير',
          ),
          IconButton(
            icon: const Icon(Icons.play_arrow),
            onPressed: _runAutoPricing,
            tooltip: 'تشغيل التسعير الآلي',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                TextField(
                  decoration: const InputDecoration(
                    hintText: 'البحث في قواعد التسعير...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedType,
                        decoration: const InputDecoration(
                          labelText: 'نوع التسعير',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الأنواع')),
                          DropdownMenuItem(value: 'نسبة مئوية', child: Text('نسبة مئوية')),
                          DropdownMenuItem(value: 'مبلغ ثابت', child: Text('مبلغ ثابت')),
                          DropdownMenuItem(value: 'نسبة متدرجة', child: Text('نسبة متدرجة')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedType = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                _buildStatCard('المجموع', _pricingRules.length.toString(), Colors.blue),
                _buildStatCard('النشطة', _pricingRules.where((r) => r['isActive']).length.toString(), Colors.green),
                _buildStatCard('معطلة', _pricingRules.where((r) => !r['isActive']).length.toString(), Colors.red),
                _buildStatCard('متوسط الربح', '${_getAverageProfit()}%', Colors.orange),
              ],
            ),
          ),

          // قائمة قواعد التسعير
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _pricingRules.length,
              itemBuilder: (context, index) {
                final rule = _pricingRules[index];
                
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: rule['isActive'] ? Colors.green : Colors.red,
                      child: Text(
                        rule['priority'].toString(),
                        style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                      ),
                    ),
                    title: Text(
                      rule['name'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('النوع: ${rule['type']} | الفئة: ${rule['category']}'),
                        Text('هامش الربح: ${rule['minProfit']}% - ${rule['maxProfit']}%'),
                        Text('معامل التكلفة: ${rule['costMultiplier']}x'),
                      ],
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          rule['isActive'] ? Icons.check_circle : Icons.cancel,
                          color: rule['isActive'] ? Colors.green : Colors.red,
                        ),
                        PopupMenuButton<String>(
                          onSelected: (value) => _handleAction(value, rule),
                          itemBuilder: (context) => [
                            const PopupMenuItem(
                              value: 'edit',
                              child: ListTile(
                                leading: Icon(Icons.edit),
                                title: Text('تعديل'),
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'toggle',
                              child: ListTile(
                                leading: Icon(Icons.toggle_on),
                                title: Text('تغيير الحالة'),
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'test',
                              child: ListTile(
                                leading: Icon(Icons.science),
                                title: Text('اختبار القاعدة'),
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'duplicate',
                              child: ListTile(
                                leading: Icon(Icons.copy),
                                title: Text('نسخ'),
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'delete',
                              child: ListTile(
                                leading: Icon(Icons.delete, color: Colors.red),
                                title: Text('حذف', style: TextStyle(color: Colors.red)),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('الوصف: ${rule['description']}'),
                            const SizedBox(height: 8),
                            Text('الأولوية: ${rule['priority']}'),
                            const SizedBox(height: 8),
                            Text('معامل التكلفة: ${rule['costMultiplier']}'),
                            const SizedBox(height: 8),
                            Text('نطاق الربح: ${rule['minProfit']}% - ${rule['maxProfit']}%'),
                            const SizedBox(height: 16),
                            Row(
                              children: [
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () => _testRule(rule),
                                    icon: const Icon(Icons.science),
                                    label: const Text('اختبار القاعدة'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.blue,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () => _applyRule(rule),
                                    icon: const Icon(Icons.play_arrow),
                                    label: const Text('تطبيق القاعدة'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.green,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addPricingRule,
        backgroundColor: Colors.purple,
        icon: const Icon(Icons.add),
        label: const Text('إضافة قاعدة'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getAverageProfit() {
    if (_pricingRules.isEmpty) return '0';
    double totalProfit = _pricingRules.fold(0.0, (sum, rule) => sum + rule['minProfit']);
    return (totalProfit / _pricingRules.length).toStringAsFixed(1);
  }

  void _addPricingRule() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إضافة قاعدة تسعير جديدة')),
    );
  }

  void _runAutoPricing() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تشغيل التسعير الآلي لجميع الأصناف'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _testRule(Map<String, dynamic> rule) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('اختبار قاعدة ${rule['name']}')),
    );
  }

  void _applyRule(Map<String, dynamic> rule) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تطبيق قاعدة ${rule['name']}'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _handleAction(String action, Map<String, dynamic> rule) {
    switch (action) {
      case 'edit':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تعديل قاعدة ${rule['name']}')),
        );
        break;
      case 'toggle':
        setState(() {
          rule['isActive'] = !rule['isActive'];
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم ${rule['isActive'] ? 'تفعيل' : 'تعطيل'} القاعدة'),
          ),
        );
        break;
      case 'test':
        _testRule(rule);
        break;
      case 'duplicate':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم نسخ قاعدة ${rule['name']}')),
        );
        break;
      case 'delete':
        _showDeleteConfirmation(rule);
        break;
    }
  }

  void _showDeleteConfirmation(Map<String, dynamic> rule) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف قاعدة ${rule['name']}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _pricingRules.removeWhere((r) => r['id'] == rule['id']);
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف قاعدة ${rule['name']}'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
