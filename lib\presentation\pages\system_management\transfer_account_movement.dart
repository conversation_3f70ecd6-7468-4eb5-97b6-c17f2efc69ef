import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة نقل حركة حساب الى حساب آخر
/// تتيح نقل الحركات المالية بين الحسابات
class TransferAccountMovementPage extends StatefulWidget {
  const TransferAccountMovementPage({super.key});

  @override
  State<TransferAccountMovementPage> createState() =>
      _TransferAccountMovementPageState();
}

class _TransferAccountMovementPageState
    extends State<TransferAccountMovementPage> {
  final _formKey = GlobalKey<FormState>();
  final _reasonController = TextEditingController();

  String? _fromAccount;
  String? _toAccount;
  DateTime? _fromDate;
  DateTime? _toDate;
  bool _selectAll = false;

  final List<Map<String, String>> _accounts = [
    {'id': 'acc1', 'name': 'المبيعات', 'code': '4001'},
    {'id': 'acc2', 'name': 'المشتريات', 'code': '5001'},
    {'id': 'acc3', 'name': 'المصروفات', 'code': '6001'},
    {'id': 'acc4', 'name': 'الإيرادات', 'code': '4002'},
    {'id': 'acc5', 'name': 'البنك', 'code': '1001'},
  ];

  final List<Map<String, dynamic>> _movements = [
    {
      'id': 'mov1',
      'date': '2024/01/25',
      'description': 'فاتورة مبيعات رقم INV-001',
      'debit': 15000.0,
      'credit': 0.0,
      'reference': 'INV-001',
      'isSelected': false,
    },
    {
      'id': 'mov2',
      'date': '2024/01/24',
      'description': 'دفعة نقدية من عميل',
      'debit': 0.0,
      'credit': 8500.0,
      'reference': 'REC-002',
      'isSelected': false,
    },
    {
      'id': 'mov3',
      'date': '2024/01/23',
      'description': 'مصروفات إدارية',
      'debit': 2300.0,
      'credit': 0.0,
      'reference': 'EXP-003',
      'isSelected': false,
    },
  ];

  @override
  void dispose() {
    _reasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.transferAccountMovement),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            // بطاقة إعدادات النقل
            Card(
              margin: const EdgeInsets.all(16.0),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'إعدادات النقل',
                      style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.purple),
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: _fromAccount,
                      decoration: const InputDecoration(
                        labelText: 'من حساب',
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                      ),
                      isExpanded: true,
                      items: _accounts
                          .map((account) => DropdownMenuItem(
                                value: account['id'],
                                child: Text(
                                  '${account['code']} - ${account['name']}',
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ))
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          _fromAccount = value;
                        });
                      },
                      validator: (value) =>
                          value == null ? 'يرجى اختيار الحساب المصدر' : null,
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: _toAccount,
                      decoration: const InputDecoration(
                        labelText: 'إلى حساب',
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                      ),
                      isExpanded: true,
                      items: _accounts
                          .map((account) => DropdownMenuItem(
                                value: account['id'],
                                child: Text(
                                  '${account['code']} - ${account['name']}',
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ))
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          _toAccount = value;
                        });
                      },
                      validator: (value) =>
                          value == null ? 'يرجى اختيار الحساب المستهدف' : null,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: InkWell(
                            onTap: () => _selectDate(true),
                            child: InputDecorator(
                              decoration: const InputDecoration(
                                labelText: 'من تاريخ',
                                border: OutlineInputBorder(),
                              ),
                              child: Text(_fromDate != null
                                  ? '${_fromDate!.day}/${_fromDate!.month}/${_fromDate!.year}'
                                  : 'اختر التاريخ'),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: InkWell(
                            onTap: () => _selectDate(false),
                            child: InputDecorator(
                              decoration: const InputDecoration(
                                labelText: 'إلى تاريخ',
                                border: OutlineInputBorder(),
                              ),
                              child: Text(_toDate != null
                                  ? '${_toDate!.day}/${_toDate!.month}/${_toDate!.year}'
                                  : 'اختر التاريخ'),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _reasonController,
                      decoration: const InputDecoration(
                        labelText: 'سبب النقل',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 2,
                      validator: (value) => value?.isEmpty == true
                          ? 'يرجى إدخال سبب النقل'
                          : null,
                    ),
                  ],
                ),
              ),
            ),

            // إحصائيات
            Card(
              margin: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildStatCard(
                        'المجموع', _movements.length.toString(), Colors.blue),
                    _buildStatCard(
                        'المحدد',
                        _getSelectedMovements().length.toString(),
                        Colors.purple),
                    _buildStatCard(
                        'المبلغ',
                        _calculateSelectedAmount().toStringAsFixed(2),
                        Colors.green),
                  ],
                ),
              ),
            ),

            // قائمة الحركات
            Expanded(
              child: Card(
                margin: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16.0),
                      decoration: BoxDecoration(
                        color: Colors.purple.shade50,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(8),
                          topRight: Radius.circular(8),
                        ),
                      ),
                      child: Row(
                        children: [
                          Checkbox(
                            value: _selectAll,
                            onChanged: _toggleSelectAll,
                          ),
                          const Text('حركات الحساب',
                              style: TextStyle(
                                  fontSize: 16, fontWeight: FontWeight.bold)),
                        ],
                      ),
                    ),
                    Expanded(
                      child: ListView.builder(
                        itemCount: _movements.length,
                        itemBuilder: (context, index) {
                          final movement = _movements[index];
                          return CheckboxListTile(
                            value: movement['isSelected'],
                            onChanged: (value) {
                              setState(() {
                                movement['isSelected'] = value ?? false;
                              });
                            },
                            title: Text(
                              movement['description'],
                              overflow: TextOverflow.ellipsis,
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '${movement['date']} | ${movement['reference']}',
                                  overflow: TextOverflow.ellipsis,
                                ),
                                Text(
                                  'مدين: ${movement['debit']} | دائن: ${movement['credit']}',
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                            isThreeLine: true,
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: _getSelectedMovements().isNotEmpty
          ? Container(
              padding: const EdgeInsets.all(16.0),
              child: ElevatedButton.icon(
                onPressed: _transferMovements,
                icon: const Icon(Icons.swap_horiz),
                label: Text('نقل (${_getSelectedMovements().length}) حركة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            )
          : null,
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Column(
      children: [
        CircleAvatar(
          backgroundColor: color,
          child: Text(value,
              style: const TextStyle(
                  color: Colors.white, fontWeight: FontWeight.bold)),
        ),
        const SizedBox(height: 4),
        Text(title,
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold)),
      ],
    );
  }

  List<Map<String, dynamic>> _getSelectedMovements() {
    return _movements
        .where((movement) => movement['isSelected'] == true)
        .toList();
  }

  double _calculateSelectedAmount() {
    return _getSelectedMovements().fold(
        0.0,
        (sum, movement) =>
            sum +
            (movement['debit'] as double) +
            (movement['credit'] as double));
  }

  Future<void> _selectDate(bool isFromDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      setState(() {
        if (isFromDate) {
          _fromDate = picked;
        } else {
          _toDate = picked;
        }
      });
    }
  }

  void _toggleSelectAll(bool? value) {
    setState(() {
      _selectAll = value ?? false;
      for (var movement in _movements) {
        movement['isSelected'] = _selectAll;
      }
    });
  }

  void _transferMovements() {
    if (_formKey.currentState!.validate()) {
      if (_fromAccount == _toAccount) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('لا يمكن النقل إلى نفس الحساب'),
              backgroundColor: Colors.red),
        );
        return;
      }

      final selectedMovements = _getSelectedMovements();
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('تأكيد النقل'),
          content: SizedBox(
            width: double.maxFinite,
            child: Text(
              'هل أنت متأكد من نقل ${selectedMovements.length} حركة من الحساب المصدر إلى الحساب المستهدف؟',
              softWrap: true,
            ),
          ),
          actions: [
            TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء')),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content:
                        Text('تم نقل ${selectedMovements.length} حركة بنجاح'),
                    backgroundColor: Colors.green,
                  ),
                );
                setState(() {
                  _movements.removeWhere(
                      (movement) => movement['isSelected'] == true);
                  _reasonController.clear();
                });
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.purple),
              child: const Text('نقل', style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      );
    }
  }
}
