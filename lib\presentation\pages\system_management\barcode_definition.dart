import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تعريف الباركود
/// تتيح إعداد وتكوين أنواع الباركود المختلفة
class BarcodeDefinitionPage extends StatefulWidget {
  const BarcodeDefinitionPage({super.key});

  @override
  State<BarcodeDefinitionPage> createState() => _BarcodeDefinitionPageState();
}

class _BarcodeDefinitionPageState extends State<BarcodeDefinitionPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _prefixController = TextEditingController();
  final _lengthController = TextEditingController();
  final _descriptionController = TextEditingController();

  String? _selectedType;
  String? _selectedFormat;
  bool _isActive = true;
  bool _autoGenerate = false;
  bool _includeCheckDigit = false;

  final List<Map<String, String>> _barcodeTypes = [
    {
      'id': 'code128',
      'name': 'Code 128',
      'description': 'الأكثر شيوعاً ومرونة'
    },
    {
      'id': 'code39',
      'name': 'Code 39',
      'description': 'متوافق مع الأنظمة القديمة'
    },
    {
      'id': 'ean13',
      'name': 'EAN-13',
      'description': 'للمنتجات التجارية العالمية'
    },
    {'id': 'ean8', 'name': 'EAN-8', 'description': 'للمنتجات الصغيرة'},
    {'id': 'upc', 'name': 'UPC-A', 'description': 'المعيار الأمريكي'},
    {'id': 'qr', 'name': 'QR Code', 'description': 'يحتوي على معلومات إضافية'},
    {
      'id': 'datamatrix',
      'name': 'Data Matrix',
      'description': 'للمساحات الصغيرة'
    },
  ];

  final List<Map<String, String>> _formats = [
    {'id': 'numeric', 'name': 'رقمي', 'pattern': '0-9'},
    {'id': 'alphanumeric', 'name': 'أرقام وحروف', 'pattern': 'A-Z, 0-9'},
    {'id': 'alpha', 'name': 'حروف فقط', 'pattern': 'A-Z'},
    {'id': 'custom', 'name': 'مخصص', 'pattern': 'حسب التعريف'},
  ];

  final List<Map<String, dynamic>> _definedBarcodes = [
    {
      'id': 'def1',
      'name': 'باركود المنتجات الرئيسية',
      'type': 'Code 128',
      'format': 'رقمي',
      'prefix': 'PRD',
      'length': 13,
      'isActive': true,
      'autoGenerate': true,
      'checkDigit': true,
      'itemsCount': 1250,
      'lastUsed': '2024/01/25',
    },
    {
      'id': 'def2',
      'name': 'باركود الخدمات',
      'type': 'Code 39',
      'format': 'أرقام وحروف',
      'prefix': 'SRV',
      'length': 10,
      'isActive': true,
      'autoGenerate': false,
      'checkDigit': false,
      'itemsCount': 89,
      'lastUsed': '2024/01/24',
    },
    {
      'id': 'def3',
      'name': 'باركود المواد الخام',
      'type': 'EAN-13',
      'format': 'رقمي',
      'prefix': 'RAW',
      'length': 13,
      'isActive': false,
      'autoGenerate': true,
      'checkDigit': true,
      'itemsCount': 456,
      'lastUsed': '2024/01/20',
    },
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _prefixController.dispose();
    _lengthController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.barcodeDefinition),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.qr_code_scanner),
            onPressed: _testBarcode,
            tooltip: 'اختبار الباركود',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _globalSettings,
            tooltip: 'الإعدادات العامة',
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          // بطاقة إضافة تعريف جديد
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'إضافة تعريف باركود جديد',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.indigo,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // اسم التعريف
                    TextFormField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        labelText: 'اسم التعريف',
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال اسم التعريف';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    Row(
                      children: [
                        // نوع الباركود
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedType,
                            decoration: const InputDecoration(
                              labelText: 'نوع الباركود',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 8),
                            ),
                            items: _barcodeTypes
                                .map<DropdownMenuItem<String>>((type) {
                              return DropdownMenuItem<String>(
                                value: type['id'],
                                child: Text(type['name']!),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedType = value;
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'يرجى اختيار النوع';
                              }
                              return null;
                            },
                          ),
                        ),

                        const SizedBox(width: 16),

                        // تنسيق البيانات
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedFormat,
                            decoration: const InputDecoration(
                              labelText: 'تنسيق البيانات',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 8),
                            ),
                            items: _formats
                                .map<DropdownMenuItem<String>>((format) {
                              return DropdownMenuItem<String>(
                                value: format['id'],
                                child: Text(format['name']!),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedFormat = value;
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'يرجى اختيار التنسيق';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    Row(
                      children: [
                        // البادئة
                        Expanded(
                          child: TextFormField(
                            controller: _prefixController,
                            decoration: const InputDecoration(
                              labelText: 'البادئة (اختياري)',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 8),
                            ),
                          ),
                        ),

                        const SizedBox(width: 16),

                        // الطول
                        Expanded(
                          child: TextFormField(
                            controller: _lengthController,
                            decoration: const InputDecoration(
                              labelText: 'الطول الإجمالي',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 8),
                            ),
                            keyboardType: TextInputType.number,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال الطول';
                              }
                              if (int.tryParse(value) == null ||
                                  int.parse(value) <= 0) {
                                return 'يرجى إدخال رقم صحيح';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // الوصف
                    TextFormField(
                      controller: _descriptionController,
                      decoration: const InputDecoration(
                        labelText: 'الوصف',
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                      ),
                      maxLines: 2,
                    ),

                    const SizedBox(height: 16),

                    // الخيارات
                    Wrap(
                      spacing: 16,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Checkbox(
                              value: _isActive,
                              onChanged: (value) {
                                setState(() {
                                  _isActive = value ?? true;
                                });
                              },
                            ),
                            const Text('نشط'),
                          ],
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Checkbox(
                              value: _autoGenerate,
                              onChanged: (value) {
                                setState(() {
                                  _autoGenerate = value ?? false;
                                });
                              },
                            ),
                            const Text('توليد تلقائي'),
                          ],
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Checkbox(
                              value: _includeCheckDigit,
                              onChanged: (value) {
                                setState(() {
                                  _includeCheckDigit = value ?? false;
                                });
                              },
                            ),
                            const Text('رقم التحقق'),
                          ],
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // أزرار العمليات
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _saveDefinition,
                            icon: const Icon(Icons.save),
                            label: const Text('حفظ التعريف'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.indigo,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: _resetForm,
                            icon: const Icon(Icons.refresh),
                            label: const Text('إعادة تعيين'),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // قائمة التعريفات الموجودة
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'التعريفات الموجودة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.indigo,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _definedBarcodes.length,
                    itemBuilder: (context, index) {
                      final definition = _definedBarcodes[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 8.0),
                        child: ExpansionTile(
                          leading: CircleAvatar(
                            backgroundColor: definition['isActive']
                                ? Colors.green
                                : Colors.grey,
                            child: Icon(
                              Icons.qr_code,
                              color: Colors.white,
                            ),
                          ),
                          title: Text(
                            definition['name'],
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          subtitle: Text(
                            '${definition['type']} | ${definition['format']} | ${definition['itemsCount']} عنصر',
                          ),
                          trailing: Switch(
                            value: definition['isActive'],
                            onChanged: (value) =>
                                _toggleDefinition(definition, value),
                          ),
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                children: [
                                  _buildDetailRow('النوع:', definition['type']),
                                  _buildDetailRow(
                                      'التنسيق:', definition['format']),
                                  _buildDetailRow(
                                      'البادئة:', definition['prefix']),
                                  _buildDetailRow(
                                      'الطول:', '${definition['length']} رقم'),
                                  _buildDetailRow(
                                      'توليد تلقائي:',
                                      definition['autoGenerate']
                                          ? 'نعم'
                                          : 'لا'),
                                  _buildDetailRow('رقم التحقق:',
                                      definition['checkDigit'] ? 'نعم' : 'لا'),
                                  _buildDetailRow('عدد العناصر:',
                                      '${definition['itemsCount']} عنصر'),
                                  _buildDetailRow(
                                      'آخر استخدام:', definition['lastUsed']),
                                  const SizedBox(height: 16),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceEvenly,
                                    children: [
                                      ElevatedButton.icon(
                                        onPressed: () =>
                                            _editDefinition(definition),
                                        icon: const Icon(Icons.edit, size: 16),
                                        label: const Text('تعديل'),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.blue,
                                          foregroundColor: Colors.white,
                                        ),
                                      ),
                                      ElevatedButton.icon(
                                        onPressed: () =>
                                            _duplicateDefinition(definition),
                                        icon: const Icon(Icons.copy, size: 16),
                                        label: const Text('نسخ'),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.green,
                                          foregroundColor: Colors.white,
                                        ),
                                      ),
                                      ElevatedButton.icon(
                                        onPressed: () =>
                                            _deleteDefinition(definition),
                                        icon:
                                            const Icon(Icons.delete, size: 16),
                                        label: const Text('حذف'),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.red,
                                          foregroundColor: Colors.white,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  void _saveDefinition() {
    if (_formKey.currentState!.validate()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حفظ تعريف الباركود بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
      _resetForm();
    }
  }

  void _resetForm() {
    setState(() {
      _nameController.clear();
      _prefixController.clear();
      _lengthController.clear();
      _descriptionController.clear();
      _selectedType = null;
      _selectedFormat = null;
      _isActive = true;
      _autoGenerate = false;
      _includeCheckDigit = false;
    });
  }

  void _toggleDefinition(Map<String, dynamic> definition, bool value) {
    setState(() {
      definition['isActive'] = value;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(value ? 'تم تفعيل التعريف' : 'تم إلغاء تفعيل التعريف'),
        backgroundColor: value ? Colors.green : Colors.orange,
      ),
    );
  }

  void _editDefinition(Map<String, dynamic> definition) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تعديل التعريف: ${definition['name']}')),
    );
  }

  void _duplicateDefinition(Map<String, dynamic> definition) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('نسخ التعريف: ${definition['name']}')),
    );
  }

  void _deleteDefinition(Map<String, dynamic> definition) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف التعريف "${definition['name']}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف التعريف: ${definition['name']}'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _testBarcode() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('اختبار قراءة الباركود')),
    );
  }

  void _globalSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('الإعدادات العامة للباركود')),
    );
  }
}
