import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';
import 'trial_balance.dart';
import 'income_statement.dart';
import 'balance_sheet.dart';
import 'general_ledger.dart';
import 'daily_restriction_report.dart';
import 'continuous_account_statement.dart';
import 'main_account_statement.dart';
import 'sub_account_statement.dart';
import 'account_group_balances.dart';
import 'branch_representatives_movement.dart';
import 'net_profit_period.dart';
import 'cash_flows.dart';
import 'representatives_ratios_by_categories.dart';
import 'daily_income_report.dart';
import 'bank_dues_statement.dart';
import 'accounts_without_movement.dart';
import 'proportional_debit_credit_statement.dart';
import 'bank_statement.dart';
import 'expense_monitoring_reports.dart';
import 'customers_without_movement.dart';
import 'missing_sequences.dart';
import 'cost_center_expenses.dart';
import 'revenue_monitoring_report.dart';
import 'cost_center_revenue.dart';
import 'cost_center_evaluation.dart';
import 'branch_debt_aging.dart';

/// صفحة تقارير الحسابات الرئيسية
/// تعرض جميع التقارير المحاسبية والمالية المتاحة
class AccountReportsPage extends StatelessWidget {
  const AccountReportsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.accountReports),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReports,
            tooltip: localizations.printReports,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReports,
            tooltip: localizations.exportReports,
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                TextField(
                  decoration: InputDecoration(
                    hintText: localizations.searchAccountReports,
                    prefixIcon: const Icon(Icons.search),
                    border: const OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    // تطبيق البحث
                  },
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.accountReportType,
                          border: const OutlineInputBorder(),
                        ),
                        items: [
                          DropdownMenuItem(
                              value: 'all',
                              child: Text(localizations.allReports)),
                          DropdownMenuItem(
                              value: 'financial',
                              child:
                                  Text(localizations.accountFinancialReports)),
                          DropdownMenuItem(
                              value: 'accounting',
                              child: Text(localizations.accountingReports)),
                          DropdownMenuItem(
                              value: 'analytical',
                              child: Text(localizations.analyticalReports)),
                        ],
                        onChanged: (value) {
                          // تطبيق الفلترة
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                _buildStatCard(localizations.reportsCount, '26', Colors.blue),
                _buildStatCard(localizations.accountsCount, '89', Colors.green),
                _buildStatCard(
                    localizations.balances, '125,750 ر.س', Colors.orange),
                _buildStatCard(localizations.entries, '245', Colors.purple),
              ],
            ),
          ),

          // قائمة التقارير
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              children: [
                // التقارير حسب الترتيب مع أرقام تسلسلية
                _buildNumberedReportItem(
                  number: 1,
                  title: 'كشف حساب مستمر',
                  description: 'كشف الحساب المستمر والمفصل',
                  icon: Icons.receipt_long,
                  color: Colors.blue,
                  onTap: () => _navigateToReport(
                      context, 'continuous_account_statement'),
                ),
                _buildNumberedReportItem(
                  number: 2,
                  title: 'كشف حساب رئيسي',
                  description: 'كشف حساب رئيسي مع التفاصيل',
                  icon: Icons.account_tree,
                  color: Colors.green,
                  onTap: () =>
                      _navigateToReport(context, 'main_account_statement'),
                ),
                _buildNumberedReportItem(
                  number: 3,
                  title: 'كشف حساب فرعي',
                  description: 'كشف حساب فرعي مفصل',
                  icon: Icons.account_box,
                  color: Colors.purple,
                  onTap: () =>
                      _navigateToReport(context, 'sub_account_statement'),
                ),
                _buildNumberedReportItem(
                  number: 4,
                  title: 'أرصدة مجموعة حسابات',
                  description: 'أرصدة مجموعة من الحسابات المحددة',
                  icon: Icons.group,
                  color: Colors.orange,
                  onTap: () =>
                      _navigateToReport(context, 'account_group_balances'),
                ),
                _buildNumberedReportItem(
                  number: 5,
                  title: 'حركة مندوبين فرع معين',
                  description: 'حركة المندوبين في فرع محدد',
                  icon: Icons.people_alt,
                  color: Colors.teal,
                  onTap: () => _navigateToReport(
                      context, 'branch_representatives_movement'),
                ),
                _buildNumberedReportItem(
                  number: 6,
                  title: 'تقرير نسب المندوبين بأصناف معينة',
                  description: 'نسب مبيعات المندوبين حسب فئات المنتجات',
                  icon: Icons.pie_chart,
                  color: Colors.red,
                  onTap: () => _navigateToReport(
                      context, 'representatives_ratios_by_categories'),
                ),
                _buildNumberedReportItem(
                  number: 7,
                  title: 'ميزان مراجعة',
                  description: 'عرض أرصدة جميع الحسابات في تاريخ محدد',
                  icon: Icons.balance,
                  color: Colors.brown,
                  onTap: () => _navigateToReport(context, 'trial_balance'),
                ),
                _buildNumberedReportItem(
                  number: 8,
                  title: 'صافي الربح خلال فترة',
                  description: 'صافي الربح والخسارة خلال فترة محددة',
                  icon: Icons.trending_up,
                  color: Colors.deepPurple,
                  onTap: () => _navigateToReport(context, 'net_profit_period'),
                ),
                _buildNumberedReportItem(
                  number: 9,
                  title: 'تقرير الدخل اليومي',
                  description: 'تقرير الدخل والإيرادات اليومية',
                  icon: Icons.today,
                  color: Colors.cyan,
                  onTap: () =>
                      _navigateToReport(context, 'daily_income_report'),
                ),
                _buildNumberedReportItem(
                  number: 10,
                  title: 'كشف مستحقات البنوك',
                  description: 'المستحقات والالتزامات البنكية',
                  icon: Icons.account_balance,
                  color: Colors.indigo,
                  onTap: () =>
                      _navigateToReport(context, 'bank_dues_statement'),
                ),
                _buildNumberedReportItem(
                  number: 11,
                  title: 'كشف حساب نسبي مدين دائن',
                  description: 'النسب المئوية للمدين والدائن في الحسابات',
                  icon: Icons.pie_chart_outline,
                  color: Colors.teal,
                  onTap: () => _navigateToReport(
                      context, 'proportional_debit_credit_statement'),
                ),
                _buildNumberedReportItem(
                  number: 12,
                  title: 'تقارير مراقبة النفقات',
                  description: 'مراقبة ومتابعة النفقات والمصروفات',
                  icon: Icons.monitor_heart,
                  color: Colors.lightBlue,
                  onTap: () =>
                      _navigateToReport(context, 'expense_monitoring_reports'),
                ),
                _buildNumberedReportItem(
                  number: 13,
                  title: 'تقرير نفقات مراكز التكلفة',
                  description: 'نفقات ومصروفات مراكز التكلفة المختلفة',
                  icon: Icons.business_center,
                  color: Colors.deepOrange,
                  onTap: () =>
                      _navigateToReport(context, 'cost_center_expenses'),
                ),
                _buildNumberedReportItem(
                  number: 14,
                  title: 'تقرير مراقبة الإيرادات',
                  description: 'مراقبة ومتابعة الإيرادات مقابل الأهداف',
                  icon: Icons.trending_up,
                  color: Colors.green,
                  onTap: () =>
                      _navigateToReport(context, 'revenue_monitoring_report'),
                ),
                _buildNumberedReportItem(
                  number: 15,
                  title: 'تقرير إيرادات مركز التكلفة',
                  description: 'إيرادات مراكز التكلفة المختلفة',
                  icon: Icons.monetization_on,
                  color: Colors.blueGrey,
                  onTap: () =>
                      _navigateToReport(context, 'cost_center_revenue'),
                ),
                _buildNumberedReportItem(
                  number: 16,
                  title: 'تقييم مركز التكلفة',
                  description: 'تقييم شامل لأداء مراكز التكلفة',
                  icon: Icons.assessment,
                  color: Colors.grey,
                  onTap: () =>
                      _navigateToReport(context, 'cost_center_evaluation'),
                ),
                _buildNumberedReportItem(
                  number: 17,
                  title: 'تقارير ختامية قائمة الدخل',
                  description: 'قائمة الدخل والإيرادات والمصروفات',
                  icon: Icons.attach_money,
                  color: Colors.amber,
                  onTap: () => _navigateToReport(context, 'income_statement'),
                ),
                _buildNumberedReportItem(
                  number: 18,
                  title: 'تقارير ختامية أرصدة حسابات الميزانية',
                  description: 'أرصدة حسابات الميزانية العمومية',
                  icon: Icons.account_balance_wallet,
                  color: Colors.lightGreen,
                  onTap: () => _navigateToReport(context, 'balance_sheet'),
                ),
                _buildNumberedReportItem(
                  number: 19,
                  title: 'تقارير ختامية الميزانية العمومية',
                  description: 'الميزانية العمومية للأصول والخصوم',
                  icon: Icons.account_balance,
                  color: Colors.redAccent,
                  onTap: () => _navigateToReport(context, 'balance_sheet'),
                ),
                _buildNumberedReportItem(
                  number: 20,
                  title: 'تقارير ختامية مقارنة قائمة الدخل',
                  description: 'مقارنة قوائم الدخل لفترات مختلفة',
                  icon: Icons.compare_arrows,
                  color: Colors.deepPurple,
                  onTap: () => _navigateToReport(context, 'income_statement'),
                ),
                _buildNumberedReportItem(
                  number: 21,
                  title: 'حسابات بدون حركة',
                  description: 'الحسابات التي لم تشهد حركة خلال فترة محددة',
                  icon: Icons.pause_circle,
                  color: Colors.orange,
                  onTap: () =>
                      _navigateToReport(context, 'accounts_without_movement'),
                ),
                _buildNumberedReportItem(
                  number: 22,
                  title: 'عملاء بدون حركة',
                  description:
                      'العملاء الذين لم يقوموا بمعاملات خلال فترة محددة',
                  icon: Icons.person_off,
                  color: Colors.teal,
                  onTap: () =>
                      _navigateToReport(context, 'customers_without_movement'),
                ),
                _buildNumberedReportItem(
                  number: 23,
                  title: 'التدفقات النقدية',
                  description: 'تقرير التدفقات النقدية التشغيلية والاستثمارية',
                  icon: Icons.water_drop,
                  color: Colors.green,
                  onTap: () => _navigateToReport(context, 'cash_flows'),
                ),
                _buildNumberedReportItem(
                  number: 24,
                  title: 'كشف البنك',
                  description: 'كشف حساب البنك مع جميع المعاملات',
                  icon: Icons.account_balance_wallet,
                  color: Colors.cyan,
                  onTap: () => _navigateToReport(context, 'bank_statement'),
                ),
                _buildNumberedReportItem(
                  number: 25,
                  title: 'التسلسلات المفقودة',
                  description: 'التسلسلات المفقودة في الفواتير والمستندات',
                  icon: Icons.find_in_page,
                  color: Colors.indigo,
                  onTap: () => _navigateToReport(context, 'missing_sequences'),
                ),
                _buildNumberedReportItem(
                  number: 26,
                  title: 'تقرير أعمار ديون الفروع',
                  description: 'أعمار الديون المستحقة على الفروع المختلفة',
                  icon: Icons.schedule,
                  color: Colors.brown,
                  onTap: () => _navigateToReport(context, 'branch_debt_aging'),
                ),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _generateCustomReport,
        backgroundColor: Colors.indigo,
        icon: const Icon(Icons.add_chart),
        label: Text(localizations.customReport),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء عنصر تقرير مرقم
  Widget _buildNumberedReportItem({
    required int number,
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              // رقم تسلسلي
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: color, width: 2),
                ),
                child: Center(
                  child: Text(
                    number.toString(),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),

              // أيقونة
              Icon(
                icon,
                size: 32,
                color: color,
              ),
              const SizedBox(width: 16),

              // العنوان والوصف
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // سهم
              Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey[400],
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _printReports() {
    // طباعة التقارير
  }

  void _exportReports() {
    // تصدير التقارير
  }

  void _generateCustomReport() {
    // إنشاء تقرير مخصص
  }

  /// التنقل إلى تقرير معين
  void _navigateToReport(BuildContext context, String report) {
    Widget? targetPage;

    switch (report) {
      case 'trial_balance':
        targetPage = const TrialBalancePage();
        break;
      case 'income_statement':
        targetPage = const IncomeStatementPage();
        break;
      case 'balance_sheet':
        targetPage = const BalanceSheetPage();
        break;
      case 'general_ledger':
        targetPage = const GeneralLedgerPage();
        break;
      case 'daily_restriction_report':
        targetPage = const DailyRestrictionReportPage();
        break;
      case 'continuous_account_statement':
        targetPage = const ContinuousAccountStatementPage();
        break;
      case 'main_account_statement':
        targetPage = const MainAccountStatementPage();
        break;
      case 'sub_account_statement':
        targetPage = const SubAccountStatementPage();
        break;
      case 'account_group_balances':
        targetPage = const AccountGroupBalancesPage();
        break;
      case 'branch_representatives_movement':
        targetPage = const BranchRepresentativesMovementPage();
        break;
      case 'net_profit_period':
        targetPage = const NetProfitPeriodPage();
        break;
      case 'cash_flows':
        targetPage = const CashFlowsPage();
        break;
      case 'representatives_ratios_by_categories':
        targetPage = const RepresentativesRatiosByCategoriesPage();
        break;
      case 'daily_income_report':
        targetPage = const DailyIncomeReportPage();
        break;
      case 'bank_dues_statement':
        targetPage = const BankDuesStatementPage();
        break;
      case 'accounts_without_movement':
        targetPage = const AccountsWithoutMovementPage();
        break;
      case 'proportional_debit_credit_statement':
        targetPage = const ProportionalDebitCreditStatementPage();
        break;
      case 'bank_statement':
        targetPage = const BankStatementPage();
        break;
      case 'expense_monitoring_reports':
        targetPage = const ExpenseMonitoringReportsPage();
        break;
      case 'customers_without_movement':
        targetPage = const CustomersWithoutMovementPage();
        break;
      case 'missing_sequences':
        targetPage = const MissingSequencesPage();
        break;
      case 'cost_center_expenses':
        targetPage = const CostCenterExpensesPage();
        break;
      case 'revenue_monitoring_report':
        targetPage = const RevenueMonitoringReportPage();
        break;
      case 'cost_center_revenue':
        targetPage = const CostCenterRevenuePage();
        break;
      case 'cost_center_evaluation':
        targetPage = const CostCenterEvaluationPage();
        break;
      case 'branch_debt_aging':
        targetPage = const BranchDebtAgingPage();
        break;
      default:
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('الانتقال إلى تقرير $report')),
        );
        return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => targetPage!,
      ),
    );
  }
}
