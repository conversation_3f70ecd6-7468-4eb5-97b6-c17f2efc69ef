/// نموذج عقدة شجرة مراكز التكلفة
class CostCenterTreeNode {
  final String code;
  final String name;
  final int level;
  final bool hasChildren;
  final List<CostCenterTreeNode> children;
  bool isExpanded;
  bool isSelected;

  CostCenterTreeNode({
    required this.code,
    required this.name,
    required this.level,
    required this.hasChildren,
    required this.children,
    this.isExpanded = false,
    this.isSelected = false,
  });

  /// نسخ مع تعديل
  CostCenterTreeNode copyWith({
    String? code,
    String? name,
    int? level,
    bool? hasChildren,
    List<CostCenterTreeNode>? children,
    bool? isExpanded,
    bool? isSelected,
  }) {
    return CostCenterTreeNode(
      code: code ?? this.code,
      name: name ?? this.name,
      level: level ?? this.level,
      hasChildren: hasChildren ?? this.hasChildren,
      children: children ?? this.children,
      isExpanded: isExpanded ?? this.isExpanded,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  /// البحث عن عقدة بالكود
  CostCenterTreeNode? findByCode(String searchCode) {
    if (code == searchCode) return this;
    
    for (final child in children) {
      final found = child.findByCode(searchCode);
      if (found != null) return found;
    }
    
    return null;
  }

  /// الحصول على جميع العقد الفرعية
  List<CostCenterTreeNode> getAllChildren() {
    List<CostCenterTreeNode> allChildren = [];
    
    for (final child in children) {
      allChildren.add(child);
      allChildren.addAll(child.getAllChildren());
    }
    
    return allChildren;
  }

  /// التحقق من وجود أطفال في مستوى معين
  bool hasChildrenAtLevel(int targetLevel) {
    if (level == targetLevel && children.isNotEmpty) return true;
    
    for (final child in children) {
      if (child.hasChildrenAtLevel(targetLevel)) return true;
    }
    
    return false;
  }

  /// الحصول على العمق الأقصى للشجرة
  int getMaxDepth() {
    if (children.isEmpty) return level;
    
    int maxChildDepth = level;
    for (final child in children) {
      final childDepth = child.getMaxDepth();
      if (childDepth > maxChildDepth) {
        maxChildDepth = childDepth;
      }
    }
    
    return maxChildDepth;
  }

  @override
  String toString() {
    return 'CostCenterTreeNode(code: $code, name: $name, level: $level, hasChildren: $hasChildren)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CostCenterTreeNode && other.code == code;
  }

  @override
  int get hashCode => code.hashCode;
}
