import 'package:flutter/material.dart';

/// صفحة حسابات بدون حركة
/// تعرض الحسابات التي لم تشهد أي حركة خلال فترة محددة
class AccountsWithoutMovementPage extends StatefulWidget {
  const AccountsWithoutMovementPage({super.key});

  @override
  State<AccountsWithoutMovementPage> createState() => _AccountsWithoutMovementPageState();
}

class _AccountsWithoutMovementPageState extends State<AccountsWithoutMovementPage> {
  String _selectedPeriod = 'last_3_months';
  String _selectedAccountType = 'all';

  // بيانات تجريبية للحسابات بدون حركة
  final List<Map<String, dynamic>> _inactiveAccounts = [
    {
      'accountCode': '1105',
      'accountName': 'حساب توفير احتياطي',
      'accountType': 'أصول',
      'lastMovementDate': '2023-08-15',
      'currentBalance': 25000.0,
      'daysSinceLastMovement': 158,
      'openingDate': '2022-01-01',
      'status': 'نشط',
      'category': 'حسابات بنكية',
    },
    {
      'accountCode': '1301',
      'accountName': 'مخزون قطع غيار قديمة',
      'accountType': 'أصول',
      'lastMovementDate': '2023-07-20',
      'currentBalance': 8500.0,
      'daysSinceLastMovement': 184,
      'openingDate': '2021-05-15',
      'status': 'نشط',
      'category': 'مخزون',
    },
    {
      'accountCode': '2105',
      'accountName': 'مورد معدات متوقف',
      'accountType': 'خصوم',
      'lastMovementDate': '2023-09-10',
      'currentBalance': 3200.0,
      'daysSinceLastMovement': 132,
      'openingDate': '2020-03-01',
      'status': 'معلق',
      'category': 'موردين',
    },
    {
      'accountCode': '1205',
      'accountName': 'عميل شركة متوقفة',
      'accountType': 'أصول',
      'lastMovementDate': '2023-06-05',
      'currentBalance': 15000.0,
      'daysSinceLastMovement': 229,
      'openingDate': '2019-12-01',
      'status': 'معلق',
      'category': 'عملاء',
    },
    {
      'accountCode': '5105',
      'accountName': 'مصروفات تدريب موقوفة',
      'accountType': 'مصروفات',
      'lastMovementDate': '2023-04-10',
      'currentBalance': 0.0,
      'daysSinceLastMovement': 285,
      'openingDate': '2022-01-01',
      'status': 'نشط',
      'category': 'مصروفات إدارية',
    },
    {
      'accountCode': '4105',
      'accountName': 'إيرادات خدمة ملغاة',
      'accountType': 'إيرادات',
      'lastMovementDate': '2023-05-15',
      'currentBalance': 0.0,
      'daysSinceLastMovement': 250,
      'openingDate': '2021-08-01',
      'status': 'معلق',
      'category': 'إيرادات خدمات',
    },
    {
      'accountCode': '1401',
      'accountName': 'معدات غير مستخدمة',
      'accountType': 'أصول',
      'lastMovementDate': '2023-03-20',
      'currentBalance': 45000.0,
      'daysSinceLastMovement': 306,
      'openingDate': '2020-06-01',
      'status': 'نشط',
      'category': 'أصول ثابتة',
    },
    {
      'accountCode': '3105',
      'accountName': 'احتياطي غير مستخدم',
      'accountType': 'حقوق ملكية',
      'lastMovementDate': '2023-02-28',
      'currentBalance': 12000.0,
      'daysSinceLastMovement': 326,
      'openingDate': '2021-01-01',
      'status': 'نشط',
      'category': 'احتياطيات',
    },
  ];

  @override
  Widget build(BuildContext context) {
    final filteredAccounts = _getFilteredAccounts();
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('حسابات بدون حركة'),
        backgroundColor: Colors.grey,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedPeriod,
                        decoration: const InputDecoration(
                          labelText: 'فترة عدم النشاط',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'last_month', child: Text('آخر شهر')),
                          DropdownMenuItem(value: 'last_3_months', child: Text('آخر 3 أشهر')),
                          DropdownMenuItem(value: 'last_6_months', child: Text('آخر 6 أشهر')),
                          DropdownMenuItem(value: 'last_year', child: Text('آخر سنة')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedPeriod = value!;
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedAccountType,
                        decoration: const InputDecoration(
                          labelText: 'نوع الحساب',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الأنواع')),
                          DropdownMenuItem(value: 'أصول', child: Text('أصول')),
                          DropdownMenuItem(value: 'خصوم', child: Text('خصوم')),
                          DropdownMenuItem(value: 'حقوق ملكية', child: Text('حقوق ملكية')),
                          DropdownMenuItem(value: 'إيرادات', child: Text('إيرادات')),
                          DropdownMenuItem(value: 'مصروفات', child: Text('مصروفات')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedAccountType = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // ملخص الحسابات غير النشطة
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              color: Colors.grey[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    const Text(
                      'ملخص الحسابات بدون حركة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Column(
                          children: [
                            const Text('عدد الحسابات'),
                            Text(
                              filteredAccounts.length.toString(),
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.red,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('إجمالي الأرصدة'),
                            Text(
                              '${_getTotalBalance(filteredAccounts)} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('متوسط عدم النشاط'),
                            Text(
                              '${_getAverageInactiveDays(filteredAccounts)} يوم',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.orange,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                _buildStatCard('الحسابات النشطة', _getActiveAccountsCount(filteredAccounts).toString(), Colors.green),
                _buildStatCard('الحسابات المعلقة', _getSuspendedAccountsCount(filteredAccounts).toString(), Colors.red),
                _buildStatCard('أطول فترة', '${_getLongestInactivePeriod(filteredAccounts)} يوم', Colors.purple),
                _buildStatCard('الأرصدة الصفرية', _getZeroBalanceCount(filteredAccounts).toString(), Colors.grey),
              ],
            ),
          ),

          // قائمة الحسابات
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: filteredAccounts.length,
              itemBuilder: (context, index) {
                final account = filteredAccounts[index];
                
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: _getInactivityColor(account['daysSinceLastMovement']),
                      child: Text(
                        account['daysSinceLastMovement'].toString(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        ),
                      ),
                    ),
                    title: Text(
                      '${account['accountCode']} - ${account['accountName']}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('آخر حركة: ${account['lastMovementDate']}'),
                        Text('عدم النشاط: ${account['daysSinceLastMovement']} يوم'),
                        Text('الرصيد: ${account['currentBalance'].toStringAsFixed(2)} ر.س'),
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: _getStatusColor(account['status']),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            account['status'],
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('نوع الحساب', account['accountType'], Icons.account_tree),
                                ),
                                Expanded(
                                  child: _buildDetailCard('الفئة', account['category'], Icons.category),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('تاريخ الفتح', account['openingDate'], Icons.calendar_today),
                                ),
                                Expanded(
                                  child: _buildDetailCard('الرصيد الحالي', '${account['currentBalance'].toStringAsFixed(2)} ر.س', Icons.account_balance),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('آخر حركة', account['lastMovementDate'], Icons.history),
                                ),
                                Expanded(
                                  child: _buildDetailCard('أيام عدم النشاط', '${account['daysSinceLastMovement']} يوم', Icons.timer_off),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Row(
                              children: [
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () => _viewAccountDetails(account),
                                    icon: const Icon(Icons.visibility),
                                    label: const Text('عرض التفاصيل'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.blue,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () => _reactivateAccount(account),
                                    icon: const Icon(Icons.play_arrow),
                                    label: const Text('تفعيل الحساب'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.green,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshData,
        backgroundColor: Colors.grey,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث البيانات'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailCard(String title, String value, IconData icon) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Icon(icon, size: 20, color: Colors.grey[600]),
            const SizedBox(height: 4),
            Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
            Text(
              title,
              style: const TextStyle(fontSize: 10),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Color _getInactivityColor(int days) {
    if (days >= 365) return Colors.red;
    if (days >= 180) return Colors.orange;
    if (days >= 90) return Colors.yellow[700]!;
    return Colors.grey;
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'نشط':
        return Colors.green;
      case 'معلق':
        return Colors.red;
      case 'مغلق':
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }

  List<Map<String, dynamic>> _getFilteredAccounts() {
    int periodDays = _getPeriodDays();
    
    return _inactiveAccounts.where((account) {
      bool periodMatch = account['daysSinceLastMovement'] >= periodDays;
      bool typeMatch = _selectedAccountType == 'all' || account['accountType'] == _selectedAccountType;
      return periodMatch && typeMatch;
    }).toList();
  }

  int _getPeriodDays() {
    switch (_selectedPeriod) {
      case 'last_month':
        return 30;
      case 'last_3_months':
        return 90;
      case 'last_6_months':
        return 180;
      case 'last_year':
        return 365;
      default:
        return 90;
    }
  }

  double _getTotalBalance(List<Map<String, dynamic>> accounts) {
    return accounts.fold(0.0, (sum, account) => sum + account['currentBalance']);
  }

  int _getAverageInactiveDays(List<Map<String, dynamic>> accounts) {
    if (accounts.isEmpty) return 0;
    int total = accounts.fold(0, (sum, account) => sum + account['daysSinceLastMovement'] as int);
    return (total / accounts.length).round();
  }

  int _getActiveAccountsCount(List<Map<String, dynamic>> accounts) {
    return accounts.where((account) => account['status'] == 'نشط').length;
  }

  int _getSuspendedAccountsCount(List<Map<String, dynamic>> accounts) {
    return accounts.where((account) => account['status'] == 'معلق').length;
  }

  int _getLongestInactivePeriod(List<Map<String, dynamic>> accounts) {
    if (accounts.isEmpty) return 0;
    return accounts.map((account) => account['daysSinceLastMovement'] as int).reduce((a, b) => a > b ? a : b);
  }

  int _getZeroBalanceCount(List<Map<String, dynamic>> accounts) {
    return accounts.where((account) => account['currentBalance'] == 0.0).length;
  }

  void _viewAccountDetails(Map<String, dynamic> account) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض تفاصيل ${account['accountName']}')),
    );
  }

  void _reactivateAccount(Map<String, dynamic> account) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تفعيل حساب ${account['accountName']}')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة تقرير الحسابات بدون حركة')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير تقرير الحسابات بدون حركة')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث بيانات الحسابات بدون حركة'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
