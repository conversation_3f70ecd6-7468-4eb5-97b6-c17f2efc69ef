import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة التعليمات الرئيسية
/// تعرض المساعدة والدعم والمعلومات
class HelpPage extends StatelessWidget {
  const HelpPage({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.help),
        backgroundColor: Colors.cyan,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقات الإحصائيات السريعة
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    title: localizations.appVersion,
                    value: 'v1.0.0',
                    icon: Icons.info,
                    color: Colors.blue,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    title: localizations.lastUpdate,
                    value: localizations.today,
                    icon: Icons.update,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    title: localizations.availableArticles,
                    value: '50+',
                    icon: Icons.article,
                    color: Colors.orange,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    title: localizations.quickTipsCount,
                    value: '25',
                    icon: Icons.lightbulb,
                    color: Colors.purple,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // قائمة المساعدة والدعم
            Text(
              localizations.helpAndSupport,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  _buildHelpCard(
                    title: localizations.quickTips,
                    icon: Icons.lightbulb,
                    color: Colors.yellow.shade700,
                    onTap: () => _navigateToHelp(context, 'quick_tips'),
                  ),
                  _buildHelpCard(
                    title: localizations.programContent,
                    icon: Icons.menu_book,
                    color: Colors.blue,
                    onTap: () => _navigateToHelp(context, 'program_content'),
                  ),
                  _buildHelpCard(
                    title: localizations.contactUs,
                    icon: Icons.contact_support,
                    color: Colors.green,
                    onTap: () => _navigateToHelp(context, 'contact_us'),
                  ),
                  _buildHelpCard(
                    title: localizations.aboutUs,
                    icon: Icons.info,
                    color: Colors.purple,
                    onTap: () => _navigateToHelp(context, 'about_us'),
                  ),
                  _buildHelpCard(
                    title: localizations.faq,
                    icon: Icons.help,
                    color: Colors.orange,
                    onTap: () => _navigateToHelp(context, 'faq'),
                  ),
                  _buildHelpCard(
                    title: localizations.userGuide,
                    icon: Icons.book,
                    color: Colors.teal,
                    onTap: () => _navigateToHelp(context, 'user_guide'),
                  ),
                  _buildHelpCard(
                    title: localizations.tutorialVideos,
                    icon: Icons.play_circle,
                    color: Colors.red,
                    onTap: () => _navigateToHelp(context, 'tutorial_videos'),
                  ),
                  _buildHelpCard(
                    title: localizations.technicalSupport,
                    icon: Icons.support_agent,
                    color: Colors.indigo,
                    onTap: () => _navigateToHelp(context, 'technical_support'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const Spacer(),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة مساعدة
  Widget _buildHelpCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 40,
                color: color,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// التنقل إلى قسم مساعدة معين
  void _navigateToHelp(BuildContext context, String help) {
    final localizations = AppLocalizations.of(context);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('${localizations.navigateTo} $help')),
    );
  }
}
