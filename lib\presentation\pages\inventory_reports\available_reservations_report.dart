import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير الحجوزات المتاحة للبيع
/// يعرض الحجوزات التي يمكن تحويلها إلى مبيعات
class AvailableReservationsReportPage extends StatefulWidget {
  const AvailableReservationsReportPage({super.key});

  @override
  State<AvailableReservationsReportPage> createState() => _AvailableReservationsReportPageState();
}

class _AvailableReservationsReportPageState extends State<AvailableReservationsReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedCategory;
  String? _reservationStatus = 'all';
  String? _selectedCustomer;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الحجوزات المتاحة للبيع'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: _sendReminders,
            tooltip: 'إرسال تذكيرات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          _buildFilterSection(localizations),
          
          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildStatusAnalysisSection(),
                  const SizedBox(height: 16),
                  _buildReservationsTableSection(),
                  const SizedBox(height: 16),
                  _buildExpiringReservationsSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.teal[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.fromDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, true),
                  controller: TextEditingController(
                    text: _startDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.toDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, false),
                  controller: TextEditingController(
                    text: _endDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: localizations.category,
                    border: const OutlineInputBorder(),
                  ),
                  value: _selectedCategory,
                  items: [
                    DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                    DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                    DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                    DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                  ],
                  onChanged: (value) => setState(() => _selectedCategory = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'حالة الحجز',
                    border: OutlineInputBorder(),
                  ),
                  value: _reservationStatus,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الحالات')),
                    DropdownMenuItem(value: 'active', child: Text('نشط')),
                    DropdownMenuItem(value: 'pending', child: Text('في الانتظار')),
                    DropdownMenuItem(value: 'expiring_soon', child: Text('ينتهي قريباً')),
                  ],
                  onChanged: (value) => setState(() => _reservationStatus = value),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'العميل',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedCustomer,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع العملاء')),
                    DropdownMenuItem(value: 'cust1', child: Text('أحمد محمد')),
                    DropdownMenuItem(value: 'cust2', child: Text('فاطمة علي')),
                    DropdownMenuItem(value: 'cust3', child: Text('محمد سالم')),
                  ],
                  onChanged: (value) => setState(() => _selectedCustomer = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.analytics),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.teal,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.bookmark, color: Colors.teal, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص الحجوزات المتاحة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي الحجوزات', '145', Colors.teal, Icons.bookmark_border),
                _buildSummaryCard('قيمة الحجوزات', '485,000 ر.س', Colors.blue, Icons.monetization_on),
                _buildSummaryCard('جاهزة للبيع', '95', Colors.green, Icons.check_circle),
                _buildSummaryCard('تنتهي خلال أسبوع', '25', Colors.orange, Icons.schedule),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusAnalysisSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.analytics, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'تحليل حالة الحجوزات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildStatusCard('نشطة', '95 حجز', Colors.green),
                _buildStatusCard('في الانتظار', '35 حجز', Colors.orange),
                _buildStatusCard('تحتاج متابعة', '15 حجز', Colors.red),
                _buildStatusCard('مكتملة', '285 حجز', Colors.blue),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReservationsTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل الحجوزات المتاحة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('رقم الحجز')),
                  DataColumn(label: Text('اسم الصنف')),
                  DataColumn(label: Text('العميل')),
                  DataColumn(label: Text('الكمية')),
                  DataColumn(label: Text('القيمة')),
                  DataColumn(label: Text('تاريخ الحجز')),
                  DataColumn(label: Text('تاريخ الانتهاء')),
                  DataColumn(label: Text('الحالة')),
                  DataColumn(label: Text('إجراءات')),
                ],
                rows: _buildReservationsRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExpiringReservationsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.schedule, color: Colors.orange, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'حجوزات تنتهي قريباً',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildExpiringReservationsList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _convertToSales,
                    icon: const Icon(Icons.shopping_cart),
                    label: const Text('تحويل لمبيعات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _extendReservations,
                    icon: const Icon(Icons.update),
                    label: const Text('تمديد الحجوزات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _cancelExpiredReservations,
                    icon: const Icon(Icons.cancel),
                    label: const Text('إلغاء المنتهية'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _generateFollowUpReport,
                    icon: const Icon(Icons.follow_the_signs),
                    label: const Text('تقرير متابعة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildExpiringReservationsList() {
    final expiringReservations = [
      {'id': 'RES001', 'item': 'لابتوب ديل XPS 13', 'customer': 'أحمد محمد', 'daysLeft': '3 أيام', 'value': '4,500 ر.س'},
      {'id': 'RES002', 'item': 'هاتف آيفون 15', 'customer': 'فاطمة علي', 'daysLeft': '5 أيام', 'value': '3,800 ر.س'},
      {'id': 'RES003', 'item': 'طابعة HP LaserJet', 'customer': 'محمد سالم', 'daysLeft': '7 أيام', 'value': '1,200 ر.س'},
      {'id': 'RES004', 'item': 'ساعة ذكية', 'customer': 'سارة أحمد', 'daysLeft': '2 أيام', 'value': '850 ر.س'},
    ];

    return expiringReservations.map((res) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: _getDaysLeftColor(res['daysLeft']!).withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: _getDaysLeftColor(res['daysLeft']!).withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.schedule, color: Colors.orange, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${res['id']} - ${res['item']}',
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  'العميل: ${res['customer']} • القيمة: ${res['value']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getDaysLeftColor(res['daysLeft']!).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              res['daysLeft']!,
              style: TextStyle(
                color: _getDaysLeftColor(res['daysLeft']!),
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildReservationsRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('RES001')),
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('أحمد محمد')),
        const DataCell(Text('2')),
        const DataCell(Text('9,000 ر.س')),
        const DataCell(Text('2024-01-10')),
        const DataCell(Text('2024-01-25')),
        DataCell(_buildStatusBadge('نشط', Colors.green)),
        DataCell(Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.shopping_cart, color: Colors.blue, size: 16),
              onPressed: () => _convertReservationToSale('RES001'),
            ),
            IconButton(
              icon: const Icon(Icons.edit, color: Colors.orange, size: 16),
              onPressed: () => _editReservation('RES001'),
            ),
          ],
        )),
      ]),
      DataRow(cells: [
        const DataCell(Text('RES002')),
        const DataCell(Text('هاتف آيفون 15')),
        const DataCell(Text('فاطمة علي')),
        const DataCell(Text('1')),
        const DataCell(Text('3,800 ر.س')),
        const DataCell(Text('2024-01-12')),
        const DataCell(Text('2024-01-27')),
        DataCell(_buildStatusBadge('في الانتظار', Colors.orange)),
        DataCell(Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.shopping_cart, color: Colors.blue, size: 16),
              onPressed: () => _convertReservationToSale('RES002'),
            ),
            IconButton(
              icon: const Icon(Icons.edit, color: Colors.orange, size: 16),
              onPressed: () => _editReservation('RES002'),
            ),
          ],
        )),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusCard(String status, String count, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(status, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(status, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Color _getDaysLeftColor(String daysLeft) {
    if (daysLeft.contains('2') || daysLeft.contains('3')) {
      return Colors.red;
    } else if (daysLeft.contains('5') || daysLeft.contains('7')) {
      return Colors.orange;
    }
    return Colors.green;
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير الحجوزات المتاحة بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _sendReminders() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إرسال تذكيرات للعملاء')),
    );
  }

  void _convertToSales() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحويل الحجوزات المحددة إلى مبيعات')),
    );
  }

  void _extendReservations() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تمديد فترة الحجوزات')),
    );
  }

  void _cancelExpiredReservations() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إلغاء الحجوزات المنتهية')),
    );
  }

  void _generateFollowUpReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء تقرير متابعة الحجوزات')),
    );
  }

  void _convertReservationToSale(String reservationId) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تحويل الحجز $reservationId إلى مبيعة')),
    );
  }

  void _editReservation(String reservationId) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تعديل الحجز $reservationId')),
    );
  }
}
