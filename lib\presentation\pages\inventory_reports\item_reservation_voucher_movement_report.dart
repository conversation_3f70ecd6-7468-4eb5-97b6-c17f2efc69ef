import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير حركة سندات الحجز لصنف
/// يعرض حركة سندات الحجز لصنف معين
class ItemReservationVoucherMovementReportPage extends StatefulWidget {
  const ItemReservationVoucherMovementReportPage({super.key});

  @override
  State<ItemReservationVoucherMovementReportPage> createState() => _ItemReservationVoucherMovementReportPageState();
}

class _ItemReservationVoucherMovementReportPageState extends State<ItemReservationVoucherMovementReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedItem;
  String? _movementType = 'all';
  String? _selectedWarehouse;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('حركة سندات الحجز لصنف'),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.timeline),
            onPressed: _showTimeline,
            tooltip: 'عرض الخط الزمني',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          _buildFilterSection(localizations),
          
          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildMovementTypeSection(),
                  const SizedBox(height: 16),
                  _buildMovementTableSection(),
                  const SizedBox(height: 16),
                  _buildTrendAnalysisSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.indigo[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.fromDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, true),
                  controller: TextEditingController(
                    text: _startDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.toDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, false),
                  controller: TextEditingController(
                    text: _endDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'الصنف',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedItem,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الأصناف')),
                    DropdownMenuItem(value: 'laptop', child: Text('لابتوب ديل XPS 13')),
                    DropdownMenuItem(value: 'phone', child: Text('هاتف آيفون 15')),
                    DropdownMenuItem(value: 'printer', child: Text('طابعة HP LaserJet')),
                    DropdownMenuItem(value: 'watch', child: Text('ساعة ذكية')),
                  ],
                  onChanged: (value) => setState(() => _selectedItem = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'نوع الحركة',
                    border: OutlineInputBorder(),
                  ),
                  value: _movementType,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الحركات')),
                    DropdownMenuItem(value: 'reservation', child: Text('حجز')),
                    DropdownMenuItem(value: 'release', child: Text('إلغاء حجز')),
                    DropdownMenuItem(value: 'sale', child: Text('بيع محجوز')),
                    DropdownMenuItem(value: 'transfer', child: Text('نقل حجز')),
                  ],
                  onChanged: (value) => setState(() => _movementType = value),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'المستودع',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedWarehouse,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع المستودعات')),
                    DropdownMenuItem(value: 'main', child: Text('المستودع الرئيسي')),
                    DropdownMenuItem(value: 'branch1', child: Text('مستودع الفرع الأول')),
                    DropdownMenuItem(value: 'branch2', child: Text('مستودع الفرع الثاني')),
                  ],
                  onChanged: (value) => setState(() => _selectedWarehouse = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.analytics),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.indigo,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.move_up, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص حركة سندات الحجز',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي الحركات', '285', Colors.indigo, Icons.swap_horiz),
                _buildSummaryCard('كمية محجوزة', '1,250', Colors.blue, Icons.lock),
                _buildSummaryCard('كمية محررة', '850', Colors.green, Icons.lock_open),
                _buildSummaryCard('كمية مباعة', '400', Colors.orange, Icons.shopping_cart),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMovementTypeSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.category, color: Colors.purple, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'توزيع أنواع الحركات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildMovementCard('حجز', '125 حركة', Colors.blue),
                _buildMovementCard('إلغاء حجز', '85 حركة', Colors.red),
                _buildMovementCard('بيع محجوز', '55 حركة', Colors.green),
                _buildMovementCard('نقل حجز', '20 حركة', Colors.orange),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMovementTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل حركة سندات الحجز',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('رقم السند')),
                  DataColumn(label: Text('اسم الصنف')),
                  DataColumn(label: Text('نوع الحركة')),
                  DataColumn(label: Text('الكمية')),
                  DataColumn(label: Text('المستودع')),
                  DataColumn(label: Text('العميل')),
                  DataColumn(label: Text('التاريخ')),
                  DataColumn(label: Text('الموظف')),
                  DataColumn(label: Text('ملاحظات')),
                ],
                rows: _buildMovementRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrendAnalysisSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.trending_up, color: Colors.teal, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'تحليل الاتجاهات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildTrendAnalysisList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _createReservation,
                    icon: const Icon(Icons.add),
                    label: const Text('إنشاء حجز'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _releaseReservations,
                    icon: const Icon(Icons.lock_open),
                    label: const Text('تحرير حجوزات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _transferReservations,
                    icon: const Icon(Icons.transfer_within_a_station),
                    label: const Text('نقل حجوزات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _optimizeReservations,
                    icon: const Icon(Icons.tune),
                    label: const Text('تحسين الحجوزات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildTrendAnalysisList() {
    final trends = [
      {'period': 'الأسبوع الماضي', 'reservations': '+15%', 'releases': '+8%', 'sales': '+12%', 'trend': 'صاعد'},
      {'period': 'الشهر الماضي', 'reservations': '+25%', 'releases': '+18%', 'sales': '+22%', 'trend': 'صاعد'},
      {'period': 'الربع الماضي', 'reservations': '+35%', 'releases': '+28%', 'sales': '+32%', 'trend': 'صاعد'},
      {'period': 'السنة الماضية', 'reservations': '+45%', 'releases': '+38%', 'sales': '+42%', 'trend': 'صاعد'},
    ];

    return trends.map((trend) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: _getTrendColor(trend['trend']!).withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: _getTrendColor(trend['trend']!).withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.trending_up, color: Colors.teal, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  trend['period']!,
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  'حجز: ${trend['reservations']} • تحرير: ${trend['releases']} • بيع: ${trend['sales']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getTrendColor(trend['trend']!).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              trend['trend']!,
              style: TextStyle(
                color: _getTrendColor(trend['trend']!),
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildMovementRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('RV001')),
        const DataCell(Text('لابتوب ديل XPS 13')),
        DataCell(_buildMovementTypeBadge('حجز', Colors.blue)),
        const DataCell(Text('5')),
        const DataCell(Text('المستودع الرئيسي')),
        const DataCell(Text('أحمد محمد')),
        const DataCell(Text('2024-01-15')),
        const DataCell(Text('فاطمة علي')),
        const DataCell(Text('طلب خاص')),
      ]),
      DataRow(cells: [
        const DataCell(Text('RV002')),
        const DataCell(Text('هاتف آيفون 15')),
        DataCell(_buildMovementTypeBadge('بيع محجوز', Colors.green)),
        const DataCell(Text('3')),
        const DataCell(Text('مستودع الفرع الأول')),
        const DataCell(Text('سارة أحمد')),
        const DataCell(Text('2024-01-18')),
        const DataCell(Text('محمد سالم')),
        const DataCell(Text('عميل مميز')),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMovementCard(String type, String count, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(type, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMovementTypeBadge(String type, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(type, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Color _getTrendColor(String trend) {
    switch (trend) {
      case 'صاعد':
        return Colors.green;
      case 'نازل':
        return Colors.red;
      case 'ثابت':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير حركة سندات الحجز بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showTimeline() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض الخط الزمني للحركات')),
    );
  }

  void _createReservation() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء حجز جديد')),
    );
  }

  void _releaseReservations() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحرير الحجوزات المحددة')),
    );
  }

  void _transferReservations() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('نقل الحجوزات بين المستودعات')),
    );
  }

  void _optimizeReservations() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحسين إدارة الحجوزات')),
    );
  }
}
