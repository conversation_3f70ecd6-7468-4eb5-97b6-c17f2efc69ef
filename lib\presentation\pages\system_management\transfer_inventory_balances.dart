import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة نقل أرصدة الجرد
/// تتيح نقل أرصدة الجرد بين المخازن المختلفة
class TransferInventoryBalancesPage extends StatefulWidget {
  const TransferInventoryBalancesPage({super.key});

  @override
  State<TransferInventoryBalancesPage> createState() =>
      _TransferInventoryBalancesPageState();
}

class _TransferInventoryBalancesPageState
    extends State<TransferInventoryBalancesPage> {
  final _formKey = GlobalKey<FormState>();
  final _quantityController = TextEditingController();
  final _notesController = TextEditingController();

  String? _selectedItem;
  String? _fromWarehouse;
  String? _toWarehouse;
  DateTime _transferDate = DateTime.now();
  bool _isProcessing = false;

  final List<Map<String, dynamic>> _items = [
    {'id': '1', 'name': 'لابتوب ديل', 'code': 'LAP001', 'unit': 'جهاز'},
    {'id': '2', 'name': 'ماوس لاسلكي', 'code': 'MOU001', 'unit': 'قطعة'},
    {'id': '3', 'name': 'كيبورد ميكانيكي', 'code': 'KEY001', 'unit': 'قطعة'},
    {'id': '4', 'name': 'شاشة 24 بوصة', 'code': 'MON001', 'unit': 'جهاز'},
  ];

  final List<Map<String, dynamic>> _warehouses = [
    {'id': '1', 'name': 'المخزن الرئيسي', 'location': 'الرياض'},
    {'id': '2', 'name': 'مخزن جدة', 'location': 'جدة'},
    {'id': '3', 'name': 'مخزن الدمام', 'location': 'الدمام'},
    {'id': '4', 'name': 'مخزن المرتجعات', 'location': 'الرياض'},
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.transferInventoryBalances),
        backgroundColor: Colors.lime,
        foregroundColor: Colors.black,
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: _showTransferHistory,
            tooltip: 'سجل نقل الجرد',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // بطاقة اختيار الصنف
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'اختيار الصنف',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.lime,
                      ),
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: _selectedItem,
                      decoration: const InputDecoration(
                        labelText: 'الصنف المراد نقله',
                        prefixIcon: Icon(Icons.inventory),
                        border: OutlineInputBorder(),
                      ),
                      items: _items.map<DropdownMenuItem<String>>((item) {
                        return DropdownMenuItem<String>(
                          value: item['id'],
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(item['name']),
                              Text(
                                'كود: ${item['code']} | الوحدة: ${item['unit']}',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedItem = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار الصنف';
                        }
                        return null;
                      },
                    ),
                    if (_selectedItem != null) ...[
                      const SizedBox(height: 16),
                      _buildItemDetails(),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة تفاصيل النقل
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'تفاصيل عملية النقل',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.lime,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // المخزن المرسل
                    DropdownButtonFormField<String>(
                      value: _fromWarehouse,
                      decoration: const InputDecoration(
                        labelText: 'من المخزن',
                        prefixIcon: Icon(Icons.warehouse),
                        border: OutlineInputBorder(),
                      ),
                      items: _warehouses
                          .map<DropdownMenuItem<String>>((warehouse) {
                        return DropdownMenuItem<String>(
                          value: warehouse['id'],
                          child: Text(
                              '${warehouse['name']} - ${warehouse['location']}'),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _fromWarehouse = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار المخزن المرسل';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // المخزن المستقبل
                    DropdownButtonFormField<String>(
                      value: _toWarehouse,
                      decoration: const InputDecoration(
                        labelText: 'إلى المخزن',
                        prefixIcon: Icon(Icons.store),
                        border: OutlineInputBorder(),
                      ),
                      items: _warehouses
                          .where(
                              (warehouse) => warehouse['id'] != _fromWarehouse)
                          .map<DropdownMenuItem<String>>((warehouse) {
                        return DropdownMenuItem<String>(
                          value: warehouse['id'],
                          child: Text(
                              '${warehouse['name']} - ${warehouse['location']}'),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _toWarehouse = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار المخزن المستقبل';
                        }
                        if (value == _fromWarehouse) {
                          return 'لا يمكن النقل إلى نفس المخزن';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // الكمية
                    TextFormField(
                      controller: _quantityController,
                      decoration: InputDecoration(
                        labelText: 'الكمية المراد نقلها',
                        hintText: '0',
                        prefixIcon: const Icon(Icons.numbers),
                        border: const OutlineInputBorder(),
                        suffixText: _selectedItem != null
                            ? _getItemUnit(_selectedItem!)
                            : '',
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال الكمية';
                        }
                        final quantity = double.tryParse(value);
                        if (quantity == null || quantity <= 0) {
                          return 'يرجى إدخال كمية صحيحة';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // تاريخ النقل
                    ListTile(
                      leading: const Icon(Icons.calendar_today),
                      title: const Text('تاريخ النقل'),
                      subtitle: Text(
                          '${_transferDate.day}/${_transferDate.month}/${_transferDate.year}'),
                      trailing: const Icon(Icons.edit),
                      onTap: _selectDate,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                        side: BorderSide(color: Colors.grey.shade300),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // ملاحظات
                    TextFormField(
                      controller: _notesController,
                      decoration: const InputDecoration(
                        labelText: 'ملاحظات (اختياري)',
                        hintText: 'أدخل أي ملاحظات حول عملية النقل',
                        prefixIcon: Icon(Icons.note),
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // معاينة العملية
            if (_selectedItem != null &&
                _fromWarehouse != null &&
                _toWarehouse != null &&
                _quantityController.text.isNotEmpty)
              Card(
                color: Colors.lime.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'معاينة عملية النقل',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildPreviewRow('الصنف:', _getItemName(_selectedItem!)),
                      _buildPreviewRow(
                          'من مخزن:', _getWarehouseName(_fromWarehouse!)),
                      _buildPreviewRow(
                          'إلى مخزن:', _getWarehouseName(_toWarehouse!)),
                      _buildPreviewRow('الكمية:',
                          '${_quantityController.text} ${_getItemUnit(_selectedItem!)}'),
                      _buildPreviewRow('التاريخ:',
                          '${_transferDate.day}/${_transferDate.month}/${_transferDate.year}'),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 24),

            // أزرار العمليات
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isProcessing ? null : _processTransfer,
                    icon: _isProcessing
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.transfer_within_a_station),
                    label: Text(
                        _isProcessing ? 'جاري النقل...' : 'تنفيذ عملية النقل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.lime,
                      foregroundColor: Colors.black,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _resetForm,
                    icon: const Icon(Icons.refresh),
                    label: const Text('إعادة تعيين'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemDetails() {
    final item = _items.firstWhere((item) => item['id'] == _selectedItem);
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تفاصيل الصنف المحدد:',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text('الاسم: ${item['name']}'),
          Text('الكود: ${item['code']}'),
          Text('الوحدة: ${item['unit']}'),
        ],
      ),
    );
  }

  Widget _buildPreviewRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  String _getItemName(String itemId) {
    final item = _items.firstWhere((item) => item['id'] == itemId);
    return item['name'];
  }

  String _getItemUnit(String itemId) {
    final item = _items.firstWhere((item) => item['id'] == itemId);
    return item['unit'];
  }

  String _getWarehouseName(String warehouseId) {
    final warehouse = _warehouses.firstWhere((wh) => wh['id'] == warehouseId);
    return '${warehouse['name']} - ${warehouse['location']}';
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _transferDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _transferDate) {
      setState(() {
        _transferDate = picked;
      });
    }
  }

  Future<void> _processTransfer() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isProcessing = true;
      });

      // محاكاة عملية النقل
      await Future.delayed(const Duration(seconds: 2));

      setState(() {
        _isProcessing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تنفيذ عملية نقل أرصدة الجرد بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        _resetForm();
      }
    }
  }

  void _resetForm() {
    setState(() {
      _selectedItem = null;
      _fromWarehouse = null;
      _toWarehouse = null;
      _quantityController.clear();
      _notesController.clear();
      _transferDate = DateTime.now();
    });
  }

  void _showTransferHistory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض سجل نقل الجرد')),
    );
  }

  @override
  void dispose() {
    _quantityController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
