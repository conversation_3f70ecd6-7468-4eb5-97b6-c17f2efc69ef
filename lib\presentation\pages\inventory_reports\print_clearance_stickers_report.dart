import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة طباعة استكر لفسوحات
/// يعرض ويطبع ملصقات الفسوحات الجمركية
class PrintClearanceStickersReportPage extends StatefulWidget {
  const PrintClearanceStickersReportPage({super.key});

  @override
  State<PrintClearanceStickersReportPage> createState() =>
      _PrintClearanceStickersReportPageState();
}

class _PrintClearanceStickersReportPageState
    extends State<PrintClearanceStickersReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _stickerSize = 'medium';
  String? _stickerLayout = 'standard';
  String? _selectedClearance;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('طباعة استكر لفسوحات'),
        backgroundColor: Colors.cyan,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printAllStickers,
            tooltip: 'طباعة جميع الملصقات',
          ),
          IconButton(
            icon: const Icon(Icons.preview),
            onPressed: _previewStickers,
            tooltip: 'معاينة الملصقات',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _stickerSettings,
            tooltip: 'إعدادات الملصقات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة والإعدادات
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.cyan[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.fromDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, true),
                        controller: TextEditingController(
                          text: _startDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.toDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, false),
                        controller: TextEditingController(
                          text: _endDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'حجم الملصق',
                          border: OutlineInputBorder(),
                        ),
                        value: _stickerSize,
                        items: const [
                          DropdownMenuItem(
                              value: 'small', child: Text('صغير (5x3 سم)')),
                          DropdownMenuItem(
                              value: 'medium', child: Text('متوسط (7x5 سم)')),
                          DropdownMenuItem(
                              value: 'large', child: Text('كبير (10x7 سم)')),
                          DropdownMenuItem(
                              value: 'custom', child: Text('مخصص')),
                        ],
                        onChanged: (value) =>
                            setState(() => _stickerSize = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'تخطيط الملصق',
                          border: OutlineInputBorder(),
                        ),
                        value: _stickerLayout,
                        items: const [
                          DropdownMenuItem(
                              value: 'standard', child: Text('قياسي')),
                          DropdownMenuItem(
                              value: 'compact', child: Text('مضغوط')),
                          DropdownMenuItem(
                              value: 'detailed', child: Text('مفصل')),
                          DropdownMenuItem(
                              value: 'barcode_only', child: Text('باركود فقط')),
                        ],
                        onChanged: (value) =>
                            setState(() => _stickerLayout = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'فسح محدد',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedClearance,
                        items: const [
                          DropdownMenuItem(
                              value: 'all', child: Text('جميع الفسوحات')),
                          DropdownMenuItem(
                              value: 'CLR-2024-001',
                              child: Text('CLR-2024-001')),
                          DropdownMenuItem(
                              value: 'CLR-2024-002',
                              child: Text('CLR-2024-002')),
                          DropdownMenuItem(
                              value: 'CLR-2024-003',
                              child: Text('CLR-2024-003')),
                        ],
                        onChanged: (value) =>
                            setState(() => _selectedClearance = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _loadClearanceStickers,
                        icon: const Icon(Icons.search),
                        label: const Text('تحميل الملصقات'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.cyan,
                          foregroundColor: Colors.white,
                          minimumSize: const Size(double.infinity, 48),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // محتوى الملصقات
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص الملصقات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.label,
                                  color: Colors.cyan, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'ملخص ملصقات الفسوحات',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSummaryCard('إجمالي الملصقات', '156',
                                  Colors.blue, Icons.label),
                              _buildSummaryCard('جاهزة للطباعة', '89',
                                  Colors.green, Icons.check_circle),
                              _buildSummaryCard('تحتاج مراجعة', '45',
                                  Colors.orange, Icons.warning),
                              _buildSummaryCard(
                                  'مطبوعة', '22', Colors.purple, Icons.print),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // معاينة تخطيط الملصق
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'معاينة تخطيط الملصق',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Center(
                            child: Container(
                              width: 200,
                              height: 140,
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.cyan),
                                borderRadius: BorderRadius.circular(8),
                                color: Colors.cyan.withOpacity(0.1),
                              ),
                              child: const Padding(
                                padding: EdgeInsets.all(8.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'CLR-2024-001',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    SizedBox(height: 4),
                                    Text(
                                      'شركة التقنية المتقدمة',
                                      style: TextStyle(fontSize: 10),
                                    ),
                                    Text(
                                      'جمارك الرياض',
                                      style: TextStyle(fontSize: 10),
                                    ),
                                    Text(
                                      '2024-02-15',
                                      style: TextStyle(fontSize: 10),
                                    ),
                                    SizedBox(height: 8),
                                    Center(
                                      child: Text(
                                        '||||| |||| |||||',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontFamily: 'monospace',
                                        ),
                                      ),
                                    ),
                                    Center(
                                      child: Text(
                                        'CLR2024001',
                                        style: TextStyle(fontSize: 8),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // قائمة الفسوحات للملصقات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Text(
                                'قائمة الفسوحات للملصقات',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const Spacer(),
                              Checkbox(
                                value: false,
                                onChanged: (value) => _selectAllStickers(),
                              ),
                              const Text('تحديد الكل'),
                            ],
                          ),
                          const SizedBox(height: 16),
                          ..._buildStickersList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إعدادات الطباعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.print_outlined,
                                  color: Colors.indigo, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'إعدادات طباعة الملصقات',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildPrintOptionCard(
                                  'ملصق واحد',
                                  'طباعة ملصق واحد في الصفحة',
                                  Icons.crop_portrait,
                                  Colors.blue),
                              _buildPrintOptionCard(
                                  'متعدد الملصقات',
                                  'طباعة عدة ملصقات في الصفحة',
                                  Icons.grid_view,
                                  Colors.green),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              _buildPrintOptionCard(
                                  'جودة عالية',
                                  'طباعة بجودة عالية للباركود',
                                  Icons.high_quality,
                                  Colors.orange),
                              _buildPrintOptionCard(
                                  'طباعة سريعة',
                                  'طباعة سريعة بجودة عادية',
                                  Icons.speed,
                                  Colors.purple),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // أحجام الملصقات المتاحة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.straighten,
                                  color: Colors.green, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'أحجام الملصقات المتاحة',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSizeCard('صغير', '5x3 سم', '20 ملصق/صفحة',
                                  Colors.blue),
                              _buildSizeCard('متوسط', '7x5 سم', '12 ملصق/صفحة',
                                  Colors.green),
                              _buildSizeCard('كبير', '10x7 سم', '6 ملصقات/صفحة',
                                  Colors.orange),
                              _buildSizeCard(
                                  'مخصص', 'حسب الطلب', 'متغير', Colors.purple),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _printSelectedStickers,
                                  icon: const Icon(Icons.print),
                                  label: const Text('طباعة المحدد'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _previewBeforePrint,
                                  icon: const Icon(Icons.preview),
                                  label: const Text('معاينة قبل الطباعة'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _exportStickers,
                                  icon: const Icon(Icons.file_download),
                                  label: const Text('تصدير كـ PDF'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _saveTemplate,
                                  icon: const Icon(Icons.save),
                                  label: const Text('حفظ كقالب'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildStickersList() {
    final stickers = [
      {
        'id': 'CLR-2024-001',
        'supplier': 'شركة التقنية المتقدمة',
        'customs': 'جمارك الرياض',
        'status': 'جاهز'
      },
      {
        'id': 'CLR-2024-002',
        'supplier': 'مؤسسة الجودة العالمية',
        'customs': 'جمارك جدة',
        'status': 'مراجعة'
      },
      {
        'id': 'CLR-2024-003',
        'supplier': 'شركة الإمداد الشامل',
        'customs': 'جمارك الدمام',
        'status': 'جاهز'
      },
      {
        'id': 'CLR-2024-004',
        'supplier': 'مؤسسة التوريد الشاملة',
        'customs': 'جمارك الخبر',
        'status': 'مطبوع'
      },
    ];

    return stickers
        .map((sticker) => Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.cyan.withOpacity(0.3)),
                borderRadius: BorderRadius.circular(8),
                color: Colors.cyan.withOpacity(0.05),
              ),
              child: Row(
                children: [
                  Checkbox(
                    value: false,
                    onChanged: (value) {},
                  ),
                  const SizedBox(width: 12),
                  const Icon(Icons.label, color: Colors.cyan, size: 24),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          sticker['id']!,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '${sticker['supplier']} • ${sticker['customs']}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildStatusBadge(sticker['status']!),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.print, color: Colors.blue),
                    onPressed: () => _printSingleSticker(sticker['id']!),
                    tooltip: 'طباعة ملصق',
                  ),
                ],
              ),
            ))
        .toList();
  }

  Widget _buildSummaryCard(
      String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPrintOptionCard(
      String title, String description, IconData icon, Color color) {
    return Expanded(
      child: Card(
        child: InkWell(
          onTap: () => _selectPrintOption(title),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Icon(icon, color: color, size: 32),
                const SizedBox(height: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(fontSize: 10),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSizeCard(
      String size, String dimensions, String perPage, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                size,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                dimensions,
                style: const TextStyle(fontSize: 12),
              ),
              Text(
                perPage,
                style: const TextStyle(fontSize: 10),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status) {
    Color color;
    switch (status) {
      case 'جاهز':
        color = Colors.green;
        break;
      case 'مراجعة':
        color = Colors.orange;
        break;
      case 'مطبوع':
        color = Colors.blue;
        break;
      default:
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        status,
        style:
            TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _loadClearanceStickers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تحميل ملصقات الفسوحات بنجاح')),
    );
  }

  void _printAllStickers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة جميع الملصقات...')),
    );
  }

  void _previewStickers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('معاينة الملصقات')),
    );
  }

  void _stickerSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('فتح إعدادات الملصقات')),
    );
  }

  void _selectAllStickers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تحديد جميع الملصقات')),
    );
  }

  void _printSelectedStickers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة الملصقات المحددة')),
    );
  }

  void _previewBeforePrint() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('معاينة قبل الطباعة')),
    );
  }

  void _exportStickers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير الملصقات كـ PDF')),
    );
  }

  void _saveTemplate() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('حفظ القالب بنجاح')),
    );
  }

  void _printSingleSticker(String stickerId) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('طباعة ملصق $stickerId')),
    );
  }

  void _selectPrintOption(String option) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم اختيار خيار: $option')),
    );
  }
}
