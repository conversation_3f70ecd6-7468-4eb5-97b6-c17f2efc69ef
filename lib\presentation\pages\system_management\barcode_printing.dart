import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة طباعة الباركود
/// تتيح إنشاء وطباعة الباركود للأصناف
class BarcodePrintingPage extends StatefulWidget {
  const BarcodePrintingPage({super.key});

  @override
  State<BarcodePrintingPage> createState() => _BarcodePrintingPageState();
}

class _BarcodePrintingPageState extends State<BarcodePrintingPage> {
  final _itemCodeController = TextEditingController();
  final _itemNameController = TextEditingController();
  final _priceController = TextEditingController();
  final _quantityController = TextEditingController(text: '1');

  String _selectedBarcodeType = 'CODE128';
  bool _includeName = true;
  bool _includePrice = true;
  bool _includeCode = true;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.printBarcode),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printBarcodes,
            tooltip: 'طباعة', // يمكن تركها كما هي أو إضافة مفتاح ترجمة
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // بطاقة معلومات الصنف
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'معلومات الصنف',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.deepPurple,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _itemCodeController,
                            decoration: const InputDecoration(
                              labelText: 'كود الصنف',
                              hintText: '12345',
                              prefixIcon: Icon(Icons.qr_code),
                              border: OutlineInputBorder(),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        ElevatedButton.icon(
                          onPressed: _searchItem,
                          icon: const Icon(Icons.search),
                          label: const Text('بحث'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _itemNameController,
                      decoration: const InputDecoration(
                        labelText: 'اسم الصنف',
                        hintText: 'أدخل اسم الصنف',
                        prefixIcon: Icon(Icons.inventory),
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _priceController,
                            decoration: const InputDecoration(
                              labelText: 'السعر',
                              hintText: '0.00',
                              prefixIcon: Icon(Icons.attach_money),
                              border: OutlineInputBorder(),
                              suffixText: 'ر.س',
                            ),
                            keyboardType: TextInputType.number,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _quantityController,
                            decoration: const InputDecoration(
                              labelText: 'عدد النسخ',
                              hintText: '1',
                              prefixIcon: Icon(Icons.copy),
                              border: OutlineInputBorder(),
                            ),
                            keyboardType: TextInputType.number,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة إعدادات الباركود
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'إعدادات الباركود',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.deepPurple,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // نوع الباركود
                    DropdownButtonFormField<String>(
                      value: _selectedBarcodeType,
                      decoration: const InputDecoration(
                        labelText: 'نوع الباركود',
                        prefixIcon: Icon(Icons.qr_code_scanner),
                        border: OutlineInputBorder(),
                      ),
                      items: const [
                        DropdownMenuItem(
                            value: 'CODE128', child: Text('CODE128')),
                        DropdownMenuItem(
                            value: 'CODE39', child: Text('CODE39')),
                        DropdownMenuItem(value: 'EAN13', child: Text('EAN13')),
                        DropdownMenuItem(value: 'QR', child: Text('QR Code')),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedBarcodeType = value!;
                        });
                      },
                    ),

                    const SizedBox(height: 16),

                    // خيارات العرض
                    const Text(
                      'عناصر العرض:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    CheckboxListTile(
                      title: const Text('عرض اسم الصنف'),
                      value: _includeName,
                      onChanged: (value) {
                        setState(() {
                          _includeName = value!;
                        });
                      },
                    ),
                    CheckboxListTile(
                      title: const Text('عرض السعر'),
                      value: _includePrice,
                      onChanged: (value) {
                        setState(() {
                          _includePrice = value!;
                        });
                      },
                    ),
                    CheckboxListTile(
                      title: const Text('عرض كود الصنف'),
                      value: _includeCode,
                      onChanged: (value) {
                        setState(() {
                          _includeCode = value!;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // معاينة الباركود
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'معاينة الباركود',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.deepPurple,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Expanded(
                        child: Center(
                          child: Container(
                            width: 200,
                            height: 150,
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                // محاكاة الباركود
                                Container(
                                  width: 150,
                                  height: 60,
                                  decoration: BoxDecoration(
                                    color: Colors.black,
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: const Center(
                                    child: Text(
                                      '||||| |||| |||||',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontFamily: 'monospace',
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 8),

                                // معلومات الصنف
                                if (_includeName &&
                                    _itemNameController.text.isNotEmpty)
                                  Text(
                                    _itemNameController.text,
                                    style: const TextStyle(fontSize: 12),
                                    textAlign: TextAlign.center,
                                  ),
                                if (_includeCode &&
                                    _itemCodeController.text.isNotEmpty)
                                  Text(
                                    _itemCodeController.text,
                                    style: const TextStyle(fontSize: 10),
                                  ),
                                if (_includePrice &&
                                    _priceController.text.isNotEmpty)
                                  Text(
                                    '${_priceController.text} ر.س',
                                    style: const TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // أزرار العمليات
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _printBarcodes,
                    icon: const Icon(Icons.print),
                    label: const Text('طباعة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.deepPurple,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _saveBarcodeTemplate,
                    icon: const Icon(Icons.save),
                    label: const Text('حفظ كقالب'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _searchItem() {
    // محاكاة البحث عن الصنف
    if (_itemCodeController.text.isNotEmpty) {
      setState(() {
        _itemNameController.text = 'صنف تجريبي';
        _priceController.text = '25.50';
      });
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم العثور على الصنف')),
      );
    }
  }

  void _printBarcodes() {
    if (_itemCodeController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال كود الصنف'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('طباعة ${_quantityController.text} نسخة من الباركود'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _saveBarcodeTemplate() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم حفظ قالب الباركود')),
    );
  }

  @override
  void dispose() {
    _itemCodeController.dispose();
    _itemNameController.dispose();
    _priceController.dispose();
    _quantityController.dispose();
    super.dispose();
  }
}
