import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقييم المخزون
/// تعرض تقييم قيمة المخزون بطرق مختلفة (FIFO, LIFO, متوسط مرجح)
class InventoryValuationReportPage extends StatefulWidget {
  const InventoryValuationReportPage({super.key});

  @override
  State<InventoryValuationReportPage> createState() => _InventoryValuationReportPageState();
}

class _InventoryValuationReportPageState extends State<InventoryValuationReportPage> {
  DateTime? _valuationDate;
  String? _selectedWarehouse;
  String? _valuationMethod = 'weighted_average';
  String? _selectedCategory;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.inventoryValuation),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.compare),
            onPressed: _compareValuations,
            tooltip: 'مقارنة طرق التقييم',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.deepPurple[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'تاريخ التقييم',
                          border: OutlineInputBorder(),
                          suffixIcon: Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context),
                        controller: TextEditingController(
                          text: _valuationDate?.toString().split(' ')[0] ?? DateTime.now().toString().split(' ')[0],
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'طريقة التقييم',
                          border: OutlineInputBorder(),
                        ),
                        value: _valuationMethod,
                        items: const [
                          DropdownMenuItem(value: 'weighted_average', child: Text('المتوسط المرجح')),
                          DropdownMenuItem(value: 'fifo', child: Text('الوارد أولاً صادر أولاً (FIFO)')),
                          DropdownMenuItem(value: 'lifo', child: Text('الوارد أخيراً صادر أولاً (LIFO)')),
                          DropdownMenuItem(value: 'standard_cost', child: Text('التكلفة المعيارية')),
                        ],
                        onChanged: (value) => setState(() => _valuationMethod = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.warehouse,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedWarehouse,
                        items: [
                          DropdownMenuItem(value: 'all', child: Text(localizations.allWarehouses)),
                          DropdownMenuItem(value: 'main', child: Text(localizations.mainWarehouse)),
                          DropdownMenuItem(value: 'branch1', child: Text('${localizations.branchWarehouse} الأول')),
                          DropdownMenuItem(value: 'branch2', child: Text('${localizations.branchWarehouse} الثاني')),
                        ],
                        onChanged: (value) => setState(() => _selectedWarehouse = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.category,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedCategory,
                        items: [
                          DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                          DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                          DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                          DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                        ],
                        onChanged: (value) => setState(() => _selectedCategory = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.calculate),
                  label: const Text('حساب التقييم'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.deepPurple,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص التقييم
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.assessment, color: Colors.deepPurple, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'ملخص تقييم المخزون',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildValuationCard('إجمالي الكمية', '2,450 قطعة', Colors.blue),
                              _buildValuationCard('إجمالي القيمة', '1,250,000 ر.س', Colors.green),
                              _buildValuationCard('متوسط التكلفة', '510.20 ر.س', Colors.orange),
                              _buildValuationCard('عدد الأصناف', '185', Colors.purple),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // مقارنة طرق التقييم
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'مقارنة طرق التقييم',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('طريقة التقييم')),
                                DataColumn(label: Text('إجمالي القيمة')),
                                DataColumn(label: Text('الفرق عن المتوسط')),
                                DataColumn(label: Text('النسبة %')),
                                DataColumn(label: Text('التوصية')),
                              ],
                              rows: _buildValuationComparisonRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // تفاصيل التقييم حسب التصنيف
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تقييم المخزون حسب التصنيف',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: [
                                DataColumn(label: Text(localizations.category)),
                                DataColumn(label: Text(localizations.quantity)),
                                DataColumn(label: Text('متوسط التكلفة')),
                                DataColumn(label: Text('إجمالي القيمة')),
                                DataColumn(label: Text('النسبة من الإجمالي')),
                                DataColumn(label: Text('الحالة')),
                              ],
                              rows: _buildCategoryValuationRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // تحليل الحركة والتقييم
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تحليل الحركة والتقييم',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildAnalysisCard('أصناف عالية القيمة', '25', Colors.red, Icons.trending_up),
                              _buildAnalysisCard('أصناف متوسطة القيمة', '85', Colors.amber, Icons.trending_flat),
                              _buildAnalysisCard('أصناف منخفضة القيمة', '75', Colors.green, Icons.trending_down),
                              _buildAnalysisCard('أصناف راكدة', '12', Colors.grey, Icons.warning),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // توصيات التحسين
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.lightbulb, color: Colors.amber, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'توصيات تحسين المخزون',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          _buildRecommendationItem(
                            'تحسين دوران المخزون',
                            'يُنصح بتقليل المخزون الراكد بنسبة 15% لتحسين التدفق النقدي',
                            Icons.refresh,
                            Colors.blue,
                          ),
                          _buildRecommendationItem(
                            'مراجعة طريقة التقييم',
                            'استخدام المتوسط المرجح يعطي تقييماً أكثر دقة للمخزون الحالي',
                            Icons.calculate,
                            Colors.green,
                          ),
                          _buildRecommendationItem(
                            'تحديث الأسعار',
                            'مراجعة أسعار التكلفة للأصناف التي لم تُحدث منذ أكثر من 6 أشهر',
                            Icons.price_change,
                            Colors.orange,
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _adjustValuation,
                                  icon: const Icon(Icons.tune),
                                  label: const Text('تعديل التقييم'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _updateCosts,
                                  icon: const Icon(Icons.update),
                                  label: const Text('تحديث التكاليف'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _generateAdjustmentEntry,
                                  icon: const Icon(Icons.receipt),
                                  label: const Text('قيد التسوية'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _scheduleRevaluation,
                                  icon: const Icon(Icons.schedule),
                                  label: const Text('جدولة إعادة التقييم'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<DataRow> _buildValuationComparisonRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('المتوسط المرجح')),
        const DataCell(Text('1,250,000 ر.س')),
        const DataCell(Text('0 ر.س')),
        const DataCell(Text('100%')),
        DataCell(_buildRecommendationBadge('مُوصى', Colors.green)),
      ]),
      DataRow(cells: [
        const DataCell(Text('FIFO')),
        const DataCell(Text('1,285,000 ر.س')),
        const DataCell(Text('+35,000 ر.س')),
        const DataCell(Text('102.8%')),
        DataCell(_buildRecommendationBadge('جيد', Colors.blue)),
      ]),
      DataRow(cells: [
        const DataCell(Text('LIFO')),
        const DataCell(Text('1,215,000 ر.س')),
        const DataCell(Text('-35,000 ر.س')),
        const DataCell(Text('97.2%')),
        DataCell(_buildRecommendationBadge('مقبول', Colors.orange)),
      ]),
      DataRow(cells: [
        const DataCell(Text('التكلفة المعيارية')),
        const DataCell(Text('1,300,000 ر.س')),
        const DataCell(Text('+50,000 ر.س')),
        const DataCell(Text('104%')),
        DataCell(_buildRecommendationBadge('مراجعة', Colors.red)),
      ]),
    ];
  }

  List<DataRow> _buildCategoryValuationRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('850')),
        const DataCell(Text('750.50')),
        const DataCell(Text('637,925 ر.س')),
        const DataCell(Text('51.0%')),
        DataCell(_buildStatusBadge('نشط', Colors.green)),
      ]),
      DataRow(cells: [
        const DataCell(Text('ملابس')),
        const DataCell(Text('1,200')),
        const DataCell(Text('185.25')),
        const DataCell(Text('222,300 ر.س')),
        const DataCell(Text('17.8%')),
        DataCell(_buildStatusBadge('نشط', Colors.green)),
      ]),
      DataRow(cells: [
        const DataCell(Text('مواد غذائية')),
        const DataCell(Text('400')),
        const DataCell(Text('97.50')),
        const DataCell(Text('39,000 ر.س')),
        const DataCell(Text('3.1%')),
        DataCell(_buildStatusBadge('متوسط', Colors.orange)),
      ]),
    ];
  }

  Widget _buildValuationCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAnalysisCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecommendationItem(String title, String description, IconData icon, Color color) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: color.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: color.withOpacity(0.05),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationBadge(String recommendation, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(recommendation, style: const TextStyle(color: Colors.white, fontSize: 12)),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(status, style: const TextStyle(color: Colors.white, fontSize: 12)),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        _valuationDate = picked;
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم حساب تقييم المخزون بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة تقرير التقييم...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير تقرير التقييم...')),
    );
  }

  void _compareValuations() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض مقارنة تفصيلية لطرق التقييم')),
    );
  }

  void _adjustValuation() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('فتح صفحة تعديل التقييم')),
    );
  }

  void _updateCosts() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تحديث تكاليف المخزون')),
    );
  }

  void _generateAdjustmentEntry() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء قيد التسوية المحاسبي')),
    );
  }

  void _scheduleRevaluation() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم جدولة إعادة التقييم الدوري')),
    );
  }
}
