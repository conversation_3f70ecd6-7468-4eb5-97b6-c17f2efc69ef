import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../widgets/global_buttons/global_buttons.dart';

/// صفحة إدارة الوحدات
class UnitsPage extends StatefulWidget {
  const UnitsPage({super.key});

  @override
  State<UnitsPage> createState() => _UnitsPageState();
}

class _UnitsPageState extends State<UnitsPage> {
  // متحكمات النصوص
  final TextEditingController _unitNumberController = TextEditingController();
  final TextEditingController _arabicNameController = TextEditingController();
  final TextEditingController _englishNameController = TextEditingController();
  final TextEditingController _maxDiscountController = TextEditingController();

  // متغيرات الحالة
  bool _isLoading = false;
  bool _isEditing = false;

  @override
  void dispose() {
    _unitNumberController.dispose();
    _arabicNameController.dispose();
    _englishNameController.dispose();
    _maxDiscountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الوحدات'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          // شريط الأزرار العلوي
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              border: Border(
                bottom: BorderSide(color: Colors.grey[300]!),
              ),
            ),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  // زر إضافة
                  GlobalButtons.newButton(
                    onPressed: _addNewUnit,
                    tooltip: 'إضافة وحدة جديدة',
                    isLoading: _isLoading,
                  ),
                  const SizedBox(width: 8),

                  // زر استعلام
                  GlobalButtons.queryButton(
                    onPressed: _queryUnit,
                    tooltip: 'استعلام عن وحدة',
                    isLoading: _isLoading,
                  ),
                  const SizedBox(width: 8),

                  // زر حذف
                  GlobalButtons.deleteButton(
                    onPressed: _deleteUnit,
                    tooltip: 'حذف الوحدة',
                    isLoading: _isLoading,
                    isDisabled: !_isEditing,
                  ),
                  const SizedBox(width: 16),

                  // زر تراجع
                  GlobalButtons.undoButton(
                    onPressed: _undoChanges,
                    tooltip: 'تراجع عن التغييرات',
                    isLoading: _isLoading,
                    isDisabled: !_isEditing,
                  ),
                  const SizedBox(width: 8),

                  // زر حفظ
                  GlobalButtons.saveButton(
                    onPressed: _saveUnit,
                    tooltip: 'حفظ الوحدة',
                    isLoading: _isLoading,
                    isDisabled: !_isEditing,
                  ),
                ],
              ),
            ),
          ),

          // محتوى الصفحة
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24.0),
              child: Center(
                child: Container(
                  constraints: const BoxConstraints(maxWidth: 800),
                  child: Card(
                    elevation: 4,
                    child: Padding(
                      padding: const EdgeInsets.all(32.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // عنوان الكرت
                          Row(
                            children: [
                              Icon(
                                Icons.straighten,
                                color: Colors.blue[700],
                                size: 28,
                              ),
                              const SizedBox(width: 12),
                              Text(
                                'بيانات الوحدة',
                                style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue[700],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 32),

                          // رقم الوحدة
                          _buildNumberField(
                            label: 'رقم الوحدة',
                            controller: _unitNumberController,
                            hint: 'ادخل رقم الوحدة',
                            icon: Icons.numbers,
                          ),
                          const SizedBox(height: 24),

                          // الاسم العربي
                          _buildTextField(
                            label: 'الاسم العربي',
                            controller: _arabicNameController,
                            hint: 'ادخل اسم الوحدة بالعربية',
                            icon: Icons.text_fields,
                          ),
                          const SizedBox(height: 24),

                          // الاسم الإنجليزي
                          _buildTextField(
                            label: 'الاسم الإنجليزي',
                            controller: _englishNameController,
                            hint: 'ادخل اسم الوحدة بالإنجليزية',
                            icon: Icons.translate,
                          ),
                          const SizedBox(height: 24),

                          // أقصى خصم للوحدة
                          _buildNumberField(
                            label: 'أقصى خصم للوحدة (%)',
                            controller: _maxDiscountController,
                            hint: 'ادخل أقصى نسبة خصم مسموحة',
                            icon: Icons.percent,
                            suffix: '%',
                          ),
                          const SizedBox(height: 32),

                          // معلومات إضافية
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.blue[50],
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.blue[200]!),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.info_outline,
                                  color: Colors.blue[700],
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    'تأكد من إدخال جميع البيانات المطلوبة قبل الحفظ',
                                    style: TextStyle(
                                      color: Colors.blue[700],
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حقل إدخال رقمي
  Widget _buildNumberField({
    required String label,
    required TextEditingController controller,
    required String hint,
    required IconData icon,
    String? suffix,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
          ],
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: Icon(icon, color: Colors.blue[600]),
            suffixText: suffix,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.blue[600]!, width: 2),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          onChanged: (value) {
            if (!_isEditing) {
              setState(() {
                _isEditing = true;
              });
            }
          },
        ),
      ],
    );
  }

  /// بناء حقل إدخال نصي
  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    required String hint,
    required IconData icon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: Icon(icon, color: Colors.blue[600]),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.blue[600]!, width: 2),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          onChanged: (value) {
            if (!_isEditing) {
              setState(() {
                _isEditing = true;
              });
            }
          },
        ),
      ],
    );
  }

  /// إضافة وحدة جديدة
  void _addNewUnit() {
    setState(() {
      _isEditing = true;
      _unitNumberController.clear();
      _arabicNameController.clear();
      _englishNameController.clear();
      _maxDiscountController.clear();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاهز لإدخال وحدة جديدة'),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// استعلام عن وحدة
  void _queryUnit() {
    // هنا يمكن إضافة نافذة بحث أو استعلام
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('استعلام عن وحدة'),
        content: const Text('سيتم تطوير وظيفة الاستعلام قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  /// حذف الوحدة
  void _deleteUnit() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذه الوحدة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _performDelete();
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  /// تنفيذ عملية الحذف
  void _performDelete() {
    setState(() {
      _isLoading = true;
    });

    // محاكاة عملية الحذف
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isEditing = false;
          _unitNumberController.clear();
          _arabicNameController.clear();
          _englishNameController.clear();
          _maxDiscountController.clear();
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف الوحدة بنجاح'),
            backgroundColor: Colors.red,
          ),
        );
      }
    });
  }

  /// تراجع عن التغييرات
  void _undoChanges() {
    setState(() {
      _isEditing = false;
      _unitNumberController.clear();
      _arabicNameController.clear();
      _englishNameController.clear();
      _maxDiscountController.clear();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم التراجع عن التغييرات'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  /// حفظ الوحدة
  void _saveUnit() {
    // التحقق من صحة البيانات
    if (_arabicNameController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال الاسم العربي للوحدة'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // محاكاة عملية الحفظ
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isEditing = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ الوحدة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    });
  }
}
