import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تفعيل المستخدمين
/// تتيح للمدير تفعيل وإلغاء تفعيل المستخدمين في النظام
class ActivateUsersPage extends StatefulWidget {
  const ActivateUsersPage({super.key});

  @override
  State<ActivateUsersPage> createState() => _ActivateUsersPageState();
}

class _ActivateUsersPageState extends State<ActivateUsersPage> {
  final _searchController = TextEditingController();

  String _searchQuery = '';
  String _selectedFilter = 'all';

  final List<Map<String, dynamic>> _users = [
    {
      'id': 'user1',
      'name': 'أحمد محمد',
      'email': '<EMAIL>',
      'role': 'مدير',
      'department': 'المبيعات',
      'isActive': true,
      'lastLogin': '2024/01/25 10:30',
      'createdDate': '2023/01/15',
      'loginAttempts': 0,
    },
    {
      'id': 'user2',
      'name': 'فاطمة علي',
      'email': '<EMAIL>',
      'role': 'محاسب',
      'department': 'المحاسبة',
      'isActive': false,
      'lastLogin': '2024/01/20 14:15',
      'createdDate': '2023/03/10',
      'loginAttempts': 3,
    },
    {
      'id': 'user3',
      'name': 'محمد سالم',
      'email': '<EMAIL>',
      'role': 'موظف',
      'department': 'المخازن',
      'isActive': true,
      'lastLogin': '2024/01/24 09:45',
      'createdDate': '2023/06/20',
      'loginAttempts': 0,
    },
    {
      'id': 'user4',
      'name': 'نورا أحمد',
      'email': '<EMAIL>',
      'role': 'مشرف',
      'department': 'المشتريات',
      'isActive': false,
      'lastLogin': '2024/01/18 16:20',
      'createdDate': '2023/08/05',
      'loginAttempts': 5,
    },
    {
      'id': 'user5',
      'name': 'خالد عبدالله',
      'email': '<EMAIL>',
      'role': 'موظف',
      'department': 'المبيعات',
      'isActive': true,
      'lastLogin': '2024/01/25 11:00',
      'createdDate': '2023/11/12',
      'loginAttempts': 1,
    },
  ];

  List<Map<String, dynamic>> get _filteredUsers {
    List<Map<String, dynamic>> filtered = _users;

    // تطبيق فلتر الحالة
    if (_selectedFilter == 'active') {
      filtered = filtered.where((user) => user['isActive'] == true).toList();
    } else if (_selectedFilter == 'inactive') {
      filtered = filtered.where((user) => user['isActive'] == false).toList();
    } else if (_selectedFilter == 'blocked') {
      filtered = filtered.where((user) => user['loginAttempts'] >= 3).toList();
    }

    // تطبيق البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered
          .where((user) =>
              user['name']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              user['email']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              user['role']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              user['department']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()))
          .toList();
    }

    return filtered;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.activateUsers),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add_circle),
            onPressed: _addNewUser,
            tooltip: localizations.addNewUser,
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showUserSettings,
            tooltip: localizations.userSettings,
          ),
        ],
      ),
      body: Column(
        children: [
          // بطاقة البحث والفلترة
          Card(
            margin: const EdgeInsets.all(16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // شريط البحث
                  TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      labelText: localizations.searchUser,
                      prefixIcon: const Icon(Icons.search, size: 20),
                      border: const OutlineInputBorder(),
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 8),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),

                  const SizedBox(height: 16),

                  // فلاتر الحالة
                  Row(
                    children: [
                      Text('${localizations.filter}:',
                          style: const TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(width: 16),
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedFilter,
                          decoration: const InputDecoration(
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 8),
                          ),
                          items: [
                            DropdownMenuItem(
                                value: 'all',
                                child: Text(localizations.allUsers)),
                            DropdownMenuItem(
                                value: 'active',
                                child: Text(localizations.activeUsers)),
                            DropdownMenuItem(
                                value: 'inactive',
                                child: Text(localizations.inactiveUsers)),
                            DropdownMenuItem(
                                value: 'blocked',
                                child: Text(localizations.blockedUsers)),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedFilter = value!;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // إحصائيات سريعة
          Card(
            margin: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatCard(localizations.total, _users.length.toString(),
                      Colors.blue),
                  _buildStatCard(
                      localizations.active,
                      _users.where((u) => u['isActive']).length.toString(),
                      Colors.green),
                  _buildStatCard(
                      localizations.inactive,
                      _users.where((u) => !u['isActive']).length.toString(),
                      Colors.orange),
                  _buildStatCard(
                      localizations.blocked,
                      _users
                          .where((u) => u['loginAttempts'] >= 3)
                          .length
                          .toString(),
                      Colors.red),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // قائمة المستخدمين
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _filteredUsers.length,
              itemBuilder: (context, index) {
                final user = _filteredUsers[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor:
                          user['isActive'] ? Colors.green : Colors.grey,
                      child: Icon(
                        user['isActive'] ? Icons.person : Icons.person_off,
                        color: Colors.white,
                      ),
                    ),
                    title: Text(
                      user['name'],
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: user['isActive'] ? Colors.black : Colors.grey,
                      ),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('${user['role']} - ${user['department']}'),
                        Text('البريد: ${user['email']}',
                            style: const TextStyle(fontSize: 12)),
                        Text('آخر دخول: ${user['lastLogin']}',
                            style: const TextStyle(fontSize: 12)),
                        if (user['loginAttempts'] > 0)
                          Text(
                            'محاولات دخول فاشلة: ${user['loginAttempts']}',
                            style: const TextStyle(
                                fontSize: 12, color: Colors.red),
                          ),
                      ],
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // زر التفعيل/إلغاء التفعيل
                        IconButton(
                          icon: Icon(
                            user['isActive']
                                ? Icons.toggle_on
                                : Icons.toggle_off,
                            color:
                                user['isActive'] ? Colors.green : Colors.grey,
                            size: 32,
                          ),
                          onPressed: () => _toggleUserStatus(user['id']),
                          tooltip: user['isActive'] ? 'إلغاء التفعيل' : 'تفعيل',
                        ),

                        // زر الخيارات
                        PopupMenuButton<String>(
                          onSelected: (value) => _handleUserAction(value, user),
                          itemBuilder: (context) => [
                            PopupMenuItem(
                              value: 'edit',
                              child: ListTile(
                                leading: const Icon(Icons.edit),
                                title: Text(localizations.edit),
                              ),
                            ),
                            PopupMenuItem(
                              value: 'reset_password',
                              child: ListTile(
                                leading: const Icon(Icons.lock_reset),
                                title: Text(localizations.resetPassword),
                              ),
                            ),
                            PopupMenuItem(
                              value: 'reset_attempts',
                              child: ListTile(
                                leading: const Icon(Icons.refresh),
                                title: Text(localizations.resetAttempts),
                              ),
                            ),
                            PopupMenuItem(
                              value: 'delete',
                              child: ListTile(
                                leading:
                                    const Icon(Icons.delete, color: Colors.red),
                                title: Text(localizations.delete,
                                    style: const TextStyle(color: Colors.red)),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    isThreeLine: true,
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _bulkActions(localizations),
        backgroundColor: Colors.indigo,
        icon: const Icon(Icons.settings),
        label: Text(localizations.bulkOperations),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Column(
      children: [
        CircleAvatar(
          backgroundColor: color,
          child: Text(
            value,
            style: const TextStyle(
                color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  void _toggleUserStatus(String userId) {
    setState(() {
      final userIndex = _users.indexWhere((user) => user['id'] == userId);
      if (userIndex != -1) {
        _users[userIndex]['isActive'] = !_users[userIndex]['isActive'];

        // إعادة تعيين محاولات الدخول عند التفعيل
        if (_users[userIndex]['isActive']) {
          _users[userIndex]['loginAttempts'] = 0;
        }
      }
    });

    final user = _users.firstWhere((user) => user['id'] == userId);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          user['isActive']
              ? 'تم تفعيل المستخدم ${user['name']}'
              : 'تم إلغاء تفعيل المستخدم ${user['name']}',
        ),
        backgroundColor: user['isActive'] ? Colors.green : Colors.orange,
      ),
    );
  }

  void _handleUserAction(String action, Map<String, dynamic> user) {
    switch (action) {
      case 'edit':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تعديل بيانات المستخدم ${user['name']}')),
        );
        break;
      case 'reset_password':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('إعادة تعيين كلمة مرور المستخدم ${user['name']}')),
        );
        break;
      case 'reset_attempts':
        setState(() {
          final userIndex = _users.indexWhere((u) => u['id'] == user['id']);
          if (userIndex != -1) {
            _users[userIndex]['loginAttempts'] = 0;
          }
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('تم إعادة تعيين محاولات الدخول للمستخدم ${user['name']}'),
            backgroundColor: Colors.green,
          ),
        );
        break;
      case 'delete':
        _showDeleteConfirmation(user);
        break;
    }
  }

  void _showDeleteConfirmation(Map<String, dynamic> user) {
    final localizations = AppLocalizations.of(context);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.confirmDelete),
        content: Text(
            'هل أنت متأكد من حذف المستخدم ${user['name']}؟\nهذا الإجراء لا يمكن التراجع عنه.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(localizations.cancel),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _users.removeWhere((u) => u['id'] == user['id']);
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف المستخدم ${user['name']}'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(localizations.delete,
                style: const TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _bulkActions(AppLocalizations localizations) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              localizations.bulkOperations,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.toggle_on, color: Colors.green),
              title: Text(localizations.activateAllUsers),
              onTap: () {
                _bulkToggleUsers(true);
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.toggle_off, color: Colors.grey),
              title: Text(localizations.deactivateAllUsers),
              onTap: () {
                _bulkToggleUsers(false);
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.refresh, color: Colors.blue),
              title: Text(localizations.resetAllLoginAttempts),
              onTap: () {
                _resetAllLoginAttempts();
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.file_download, color: Colors.indigo),
              title: Text(localizations.exportUsersList),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(localizations.exportUsersList)),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _bulkToggleUsers(bool activate) {
    setState(() {
      for (var user in _users) {
        user['isActive'] = activate;
        if (activate) {
          user['loginAttempts'] = 0;
        }
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(activate
            ? 'تم تفعيل جميع المستخدمين'
            : 'تم إلغاء تفعيل جميع المستخدمين'),
        backgroundColor: activate ? Colors.green : Colors.orange,
      ),
    );
  }

  void _resetAllLoginAttempts() {
    setState(() {
      for (var user in _users) {
        user['loginAttempts'] = 0;
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إعادة تعيين جميع محاولات الدخول'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _addNewUser() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إضافة مستخدم جديد')),
    );
  }

  void _showUserSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إعدادات المستخدمين')),
    );
  }
}
