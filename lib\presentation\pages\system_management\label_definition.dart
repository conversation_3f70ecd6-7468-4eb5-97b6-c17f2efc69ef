import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تعريف الملصقات
/// تتيح إعداد وتكوين قوالب الملصقات المختلفة
class LabelDefinitionPage extends StatefulWidget {
  const LabelDefinitionPage({super.key});

  @override
  State<LabelDefinitionPage> createState() => _LabelDefinitionPageState();
}

class _LabelDefinitionPageState extends State<LabelDefinitionPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _widthController = TextEditingController();
  final _heightController = TextEditingController();
  final _descriptionController = TextEditingController();

  String? _selectedType;
  String? _selectedSize;
  String? _selectedOrientation;
  bool _isActive = true;
  bool _includeBarcode = true;
  bool _includePrice = true;
  bool _includeImage = false;

  final List<Map<String, String>> _labelTypes = [
    {'id': 'product', 'name': 'ملصق منتج', 'description': 'للمنتجات العادية'},
    {'id': 'price', 'name': 'ملصق سعر', 'description': 'لعرض الأسعار فقط'},
    {'id': 'promotion', 'name': 'ملصق عرض', 'description': 'للعروض والخصومات'},
    {'id': 'inventory', 'name': 'ملصق جرد', 'description': 'لعمليات الجرد'},
    {'id': 'shipping', 'name': 'ملصق شحن', 'description': 'للشحن والتوصيل'},
    {'id': 'custom', 'name': 'ملصق مخصص', 'description': 'تصميم مخصص'},
  ];

  final List<Map<String, String>> _standardSizes = [
    {
      'id': 'small',
      'name': 'صغير',
      'width': '2.5',
      'height': '1.5',
      'unit': 'سم'
    },
    {
      'id': 'medium',
      'name': 'متوسط',
      'width': '4.0',
      'height': '2.5',
      'unit': 'سم'
    },
    {
      'id': 'large',
      'name': 'كبير',
      'width': '6.0',
      'height': '4.0',
      'unit': 'سم'
    },
    {
      'id': 'xlarge',
      'name': 'كبير جداً',
      'width': '8.0',
      'height': '6.0',
      'unit': 'سم'
    },
    {'id': 'custom', 'name': 'مخصص', 'width': '', 'height': '', 'unit': 'سم'},
  ];

  final List<Map<String, String>> _orientations = [
    {'id': 'portrait', 'name': 'عمودي', 'icon': 'portrait'},
    {'id': 'landscape', 'name': 'أفقي', 'icon': 'landscape'},
  ];

  final List<Map<String, dynamic>> _definedLabels = [
    {
      'id': 'label1',
      'name': 'ملصق المنتجات الأساسي',
      'type': 'ملصق منتج',
      'size': 'متوسط',
      'width': 4.0,
      'height': 2.5,
      'orientation': 'أفقي',
      'isActive': true,
      'includeBarcode': true,
      'includePrice': true,
      'includeImage': false,
      'usageCount': 1250,
      'lastUsed': '2024/01/25',
    },
    {
      'id': 'label2',
      'name': 'ملصق الأسعار',
      'type': 'ملصق سعر',
      'size': 'صغير',
      'width': 2.5,
      'height': 1.5,
      'orientation': 'أفقي',
      'isActive': true,
      'includeBarcode': false,
      'includePrice': true,
      'includeImage': false,
      'usageCount': 890,
      'lastUsed': '2024/01/24',
    },
    {
      'id': 'label3',
      'name': 'ملصق العروض الخاصة',
      'type': 'ملصق عرض',
      'size': 'كبير',
      'width': 6.0,
      'height': 4.0,
      'orientation': 'عمودي',
      'isActive': false,
      'includeBarcode': true,
      'includePrice': true,
      'includeImage': true,
      'usageCount': 156,
      'lastUsed': '2024/01/20',
    },
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _widthController.dispose();
    _heightController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.labelDefinition),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.preview),
            onPressed: _previewLabel,
            tooltip: 'معاينة الملصق',
          ),
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printTest,
            tooltip: 'طباعة تجريبية',
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          // بطاقة إضافة تعريف جديد
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'إضافة تعريف ملصق جديد',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.deepPurple,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // اسم التعريف
                    TextFormField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        labelText: 'اسم الملصق',
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال اسم الملصق';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    Row(
                      children: [
                        // نوع الملصق
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedType,
                            decoration: const InputDecoration(
                              labelText: 'نوع الملصق',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 8),
                            ),
                            items: _labelTypes
                                .map<DropdownMenuItem<String>>((type) {
                              return DropdownMenuItem<String>(
                                value: type['id'],
                                child: Text(type['name']!),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedType = value;
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'يرجى اختيار النوع';
                              }
                              return null;
                            },
                          ),
                        ),

                        const SizedBox(width: 16),

                        // الحجم القياسي
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedSize,
                            decoration: const InputDecoration(
                              labelText: 'الحجم',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 8),
                            ),
                            items: _standardSizes
                                .map<DropdownMenuItem<String>>((size) {
                              return DropdownMenuItem<String>(
                                value: size['id'],
                                child: Text(
                                    '${size['name']} ${size['width']?.isNotEmpty == true ? '(${size['width']}×${size['height']} ${size['unit']})' : ''}'),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedSize = value;
                                if (value != 'custom') {
                                  final size = _standardSizes
                                      .firstWhere((s) => s['id'] == value);
                                  _widthController.text = size['width']!;
                                  _heightController.text = size['height']!;
                                }
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'يرجى اختيار الحجم';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // الأبعاد المخصصة
                    if (_selectedSize == 'custom')
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _widthController,
                              decoration: const InputDecoration(
                                labelText: 'العرض (سم)',
                                border: OutlineInputBorder(),
                                contentPadding: EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 8),
                              ),
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (_selectedSize == 'custom' &&
                                    (value == null || value.isEmpty)) {
                                  return 'يرجى إدخال العرض';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: TextFormField(
                              controller: _heightController,
                              decoration: const InputDecoration(
                                labelText: 'الارتفاع (سم)',
                                border: OutlineInputBorder(),
                                contentPadding: EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 8),
                              ),
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (_selectedSize == 'custom' &&
                                    (value == null || value.isEmpty)) {
                                  return 'يرجى إدخال الارتفاع';
                                }
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),

                    const SizedBox(height: 16),

                    // الاتجاه
                    DropdownButtonFormField<String>(
                      value: _selectedOrientation,
                      decoration: const InputDecoration(
                        labelText: 'اتجاه الملصق',
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                      ),
                      items: _orientations
                          .map<DropdownMenuItem<String>>((orientation) {
                        return DropdownMenuItem<String>(
                          value: orientation['id'],
                          child: Text(orientation['name']!),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedOrientation = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار الاتجاه';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // الوصف
                    TextFormField(
                      controller: _descriptionController,
                      decoration: const InputDecoration(
                        labelText: 'الوصف',
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                      ),
                      maxLines: 2,
                    ),

                    const SizedBox(height: 16),

                    // خيارات المحتوى
                    const Text(
                      'محتوى الملصق:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),

                    Wrap(
                      spacing: 16,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Checkbox(
                              value: _isActive,
                              onChanged: (value) {
                                setState(() {
                                  _isActive = value ?? true;
                                });
                              },
                            ),
                            const Text('نشط'),
                          ],
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Checkbox(
                              value: _includeBarcode,
                              onChanged: (value) {
                                setState(() {
                                  _includeBarcode = value ?? false;
                                });
                              },
                            ),
                            const Text('باركود'),
                          ],
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Checkbox(
                              value: _includePrice,
                              onChanged: (value) {
                                setState(() {
                                  _includePrice = value ?? false;
                                });
                              },
                            ),
                            const Text('السعر'),
                          ],
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Checkbox(
                              value: _includeImage,
                              onChanged: (value) {
                                setState(() {
                                  _includeImage = value ?? false;
                                });
                              },
                            ),
                            const Text('صورة'),
                          ],
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // أزرار العمليات
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _saveDefinition,
                            icon: const Icon(Icons.save),
                            label: const Text('حفظ التعريف'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.deepPurple,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: _resetForm,
                            icon: const Icon(Icons.refresh),
                            label: const Text('إعادة تعيين'),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // قائمة التعريفات الموجودة
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'الملصقات المعرفة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.deepPurple,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _definedLabels.length,
                    itemBuilder: (context, index) {
                      final label = _definedLabels[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 8.0),
                        child: ExpansionTile(
                          leading: CircleAvatar(
                            backgroundColor: label['isActive']
                                ? Colors.deepPurple
                                : Colors.grey,
                            child: const Icon(
                              Icons.label,
                              color: Colors.white,
                            ),
                          ),
                          title: Text(
                            label['name'],
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          subtitle: Text(
                            '${label['type']} | ${label['width']}×${label['height']} سم | ${label['usageCount']} استخدام',
                          ),
                          trailing: Switch(
                            value: label['isActive'],
                            onChanged: (value) => _toggleLabel(label, value),
                          ),
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                children: [
                                  _buildDetailRow('النوع:', label['type']),
                                  _buildDetailRow('الحجم:',
                                      '${label['size']} (${label['width']}×${label['height']} سم)'),
                                  _buildDetailRow(
                                      'الاتجاه:', label['orientation']),
                                  _buildDetailRow('باركود:',
                                      label['includeBarcode'] ? 'نعم' : 'لا'),
                                  _buildDetailRow('السعر:',
                                      label['includePrice'] ? 'نعم' : 'لا'),
                                  _buildDetailRow('الصورة:',
                                      label['includeImage'] ? 'نعم' : 'لا'),
                                  _buildDetailRow('عدد الاستخدامات:',
                                      '${label['usageCount']} مرة'),
                                  _buildDetailRow(
                                      'آخر استخدام:', label['lastUsed']),
                                  const SizedBox(height: 16),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceEvenly,
                                    children: [
                                      ElevatedButton.icon(
                                        onPressed: () => _editLabel(label),
                                        icon: const Icon(Icons.edit, size: 16),
                                        label: const Text('تعديل'),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.blue,
                                          foregroundColor: Colors.white,
                                        ),
                                      ),
                                      ElevatedButton.icon(
                                        onPressed: () =>
                                            _previewSpecificLabel(label),
                                        icon:
                                            const Icon(Icons.preview, size: 16),
                                        label: const Text('معاينة'),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.green,
                                          foregroundColor: Colors.white,
                                        ),
                                      ),
                                      ElevatedButton.icon(
                                        onPressed: () => _deleteLabel(label),
                                        icon:
                                            const Icon(Icons.delete, size: 16),
                                        label: const Text('حذف'),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.red,
                                          foregroundColor: Colors.white,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  void _saveDefinition() {
    if (_formKey.currentState!.validate()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حفظ تعريف الملصق بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
      _resetForm();
    }
  }

  void _resetForm() {
    setState(() {
      _nameController.clear();
      _widthController.clear();
      _heightController.clear();
      _descriptionController.clear();
      _selectedType = null;
      _selectedSize = null;
      _selectedOrientation = null;
      _isActive = true;
      _includeBarcode = true;
      _includePrice = true;
      _includeImage = false;
    });
  }

  void _toggleLabel(Map<String, dynamic> label, bool value) {
    setState(() {
      label['isActive'] = value;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(value ? 'تم تفعيل الملصق' : 'تم إلغاء تفعيل الملصق'),
        backgroundColor: value ? Colors.green : Colors.orange,
      ),
    );
  }

  void _editLabel(Map<String, dynamic> label) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تعديل الملصق: ${label['name']}')),
    );
  }

  void _deleteLabel(Map<String, dynamic> label) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الملصق "${label['name']}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف الملصق: ${label['name']}'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _previewLabel() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('معاينة الملصق الحالي')),
    );
  }

  void _previewSpecificLabel(Map<String, dynamic> label) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('معاينة الملصق: ${label['name']}')),
    );
  }

  void _printTest() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة تجريبية للملصق')),
    );
  }
}
