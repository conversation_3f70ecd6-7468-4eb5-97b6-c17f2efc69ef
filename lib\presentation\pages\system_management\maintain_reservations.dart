import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة صيانة ملف الحجوزات
/// تتيح فحص وإصلاح مشاكل الحجوزات والطلبيات المحجوزة
class MaintainReservationsPage extends StatefulWidget {
  const MaintainReservationsPage({super.key});

  @override
  State<MaintainReservationsPage> createState() =>
      _MaintainReservationsPageState();
}

class _MaintainReservationsPageState extends State<MaintainReservationsPage> {
  final _formKey = GlobalKey<FormState>();

  String? _selectedReservationType;
  String? _selectedStatus;
  String? _maintenanceType;
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  bool _isProcessing = false;
  bool _autoFix = false;
  bool _createReport = true;
  bool _includeExpired = false;

  final List<Map<String, String>> _reservationTypes = [
    {'id': 'all', 'name': 'جميع الأنواع'},
    {'id': 'sales', 'name': 'المبيعات'},
    {'id': 'purchase', 'name': 'المشتريات'},
    {'id': 'transfer', 'name': 'التحويل'},
    {'id': 'production', 'name': 'الإنتاج'},
    {'id': 'maintenance', 'name': 'الصيانة'},
  ];

  final List<Map<String, String>> _reservationStatuses = [
    {'id': 'all', 'name': 'جميع الحالات'},
    {'id': 'active', 'name': 'نشطة'},
    {'id': 'expired', 'name': 'منتهية الصلاحية'},
    {'id': 'cancelled', 'name': 'ملغية'},
    {'id': 'completed', 'name': 'مكتملة'},
    {'id': 'pending', 'name': 'معلقة'},
  ];

  final List<Map<String, String>> _maintenanceTypes = [
    {
      'id': 'expired_reservations',
      'name': 'الحجوزات المنتهية',
      'description': 'حجوزات تجاوزت تاريخ انتهائها'
    },
    {
      'id': 'duplicate_reservations',
      'name': 'الحجوزات المكررة',
      'description': 'نفس الصنف محجوز أكثر من مرة'
    },
    {
      'id': 'invalid_quantities',
      'name': 'كميات غير صحيحة',
      'description': 'كميات محجوزة أكبر من المتوفر'
    },
    {
      'id': 'orphaned_reservations',
      'name': 'حجوزات معلقة',
      'description': 'حجوزات بدون مستندات مرجعية'
    },
    {
      'id': 'cancelled_not_released',
      'name': 'ملغية غير محررة',
      'description': 'حجوزات ملغية لكن لم يتم تحرير الكمية'
    },
    {
      'id': 'completed_not_released',
      'name': 'مكتملة غير محررة',
      'description': 'حجوزات مكتملة لكن لم يتم تحرير الكمية'
    },
    {
      'id': 'all',
      'name': 'فحص شامل',
      'description': 'فحص جميع المشاكل المحتملة'
    },
  ];

  final List<Map<String, dynamic>> _detectedIssues = [
    {
      'id': 1,
      'reservationNumber': 'RES-2024-001',
      'itemName': 'لابتوب ديل XPS 13',
      'customer': 'شركة الأمل للتجارة',
      'type': 'expired_reservations',
      'issue': 'حجز منتهي الصلاحية منذ 15 يوم',
      'details': 'تاريخ الانتهاء: 2024/01/10، الكمية المحجوزة: 5',
      'severity': 'high',
      'suggestion': 'تحرير الكمية المحجوزة أو تجديد الحجز'
    },
    {
      'id': 2,
      'reservationNumber': 'RES-2024-002',
      'itemName': 'طابعة HP LaserJet',
      'customer': 'مؤسسة النور التقنية',
      'type': 'invalid_quantities',
      'issue': 'كمية محجوزة أكبر من المتوفر',
      'details': 'محجوز: 10، متوفر: 7',
      'severity': 'high',
      'suggestion': 'تقليل الكمية المحجوزة إلى 7 أو إلغاء الحجز'
    },
    {
      'id': 3,
      'reservationNumber': 'RES-2024-003',
      'itemName': 'مكتب خشبي فاخر',
      'customer': 'شركة الفجر الجديد',
      'type': 'duplicate_reservations',
      'issue': 'حجز مكرر لنفس الصنف',
      'details': 'يوجد 3 حجوزات منفصلة لنفس الصنف',
      'severity': 'medium',
      'suggestion': 'دمج الحجوزات في حجز واحد'
    },
    {
      'id': 4,
      'reservationNumber': 'RES-2024-004',
      'itemName': 'أقلام حبر جاف',
      'customer': 'مجموعة الشروق',
      'type': 'cancelled_not_released',
      'issue': 'حجز ملغي لكن الكمية لم تُحرر',
      'details': 'تاريخ الإلغاء: 2024/01/20، الكمية: 50',
      'severity': 'medium',
      'suggestion': 'تحرير الكمية المحجوزة'
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.maintainReservations),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.book_online),
            onPressed: _showReservationsReport,
            tooltip: 'تقرير الحجوزات',
          ),
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: _showMaintenanceHistory,
            tooltip: 'سجل الصيانة',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // بطاقة معايير الفحص
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'معايير الفحص',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.purple,
                      ),
                    ),
                    const SizedBox(height: 16),

                    Row(
                      children: [
                        // نوع الحجز
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedReservationType,
                            decoration: const InputDecoration(
                              labelText: 'نوع الحجز',
                              prefixIcon: Icon(Icons.book_online, size: 20),
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 8),
                            ),
                            items: _reservationTypes
                                .map<DropdownMenuItem<String>>((type) {
                              return DropdownMenuItem<String>(
                                value: type['id'],
                                child: Text(type['name']!),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedReservationType = value;
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'يرجى اختيار نوع الحجز';
                              }
                              return null;
                            },
                          ),
                        ),

                        const SizedBox(width: 16),

                        // حالة الحجز
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedStatus,
                            decoration: const InputDecoration(
                              labelText: 'حالة الحجز',
                              prefixIcon: Icon(Icons.flag, size: 20),
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 8),
                            ),
                            items: _reservationStatuses
                                .map<DropdownMenuItem<String>>((status) {
                              return DropdownMenuItem<String>(
                                value: status['id'],
                                child: Text(status['name']!),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedStatus = value;
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'يرجى اختيار حالة الحجز';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // نوع الصيانة
                    DropdownButtonFormField<String>(
                      value: _maintenanceType,
                      decoration: const InputDecoration(
                        labelText: 'نوع الصيانة',
                        prefixIcon: Icon(Icons.build, size: 20),
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                      ),
                      items: _maintenanceTypes
                          .map<DropdownMenuItem<String>>((type) {
                        return DropdownMenuItem<String>(
                          value: type['id'],
                          child: Text(
                            type['name']!,
                            style: const TextStyle(fontSize: 14),
                            overflow: TextOverflow.ellipsis,
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _maintenanceType = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار نوع الصيانة';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة الفترة الزمنية
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'الفترة الزمنية للفحص',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.purple,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        // تاريخ البداية
                        Expanded(
                          child: ListTile(
                            leading: const Icon(Icons.calendar_today),
                            title: const Text('من تاريخ'),
                            subtitle: Text(
                                '${_startDate.day}/${_startDate.month}/${_startDate.year}'),
                            onTap: () => _selectDate(true),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                              side: BorderSide(color: Colors.grey.shade300),
                            ),
                          ),
                        ),

                        const SizedBox(width: 16),

                        // تاريخ النهاية
                        Expanded(
                          child: ListTile(
                            leading: const Icon(Icons.event),
                            title: const Text('إلى تاريخ'),
                            subtitle: Text(
                                '${_endDate.day}/${_endDate.month}/${_endDate.year}'),
                            onTap: () => _selectDate(false),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                              side: BorderSide(color: Colors.grey.shade300),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة خيارات الصيانة
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'خيارات الصيانة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.purple,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SwitchListTile(
                      title: const Text('تضمين الحجوزات المنتهية'),
                      subtitle: const Text('فحص الحجوزات منتهية الصلاحية'),
                      value: _includeExpired,
                      onChanged: (value) {
                        setState(() {
                          _includeExpired = value;
                        });
                      },
                    ),
                    SwitchListTile(
                      title: const Text('الإصلاح التلقائي'),
                      subtitle: const Text('إصلاح المشاكل البسيطة تلقائياً'),
                      value: _autoFix,
                      onChanged: (value) {
                        setState(() {
                          _autoFix = value;
                        });
                      },
                    ),
                    SwitchListTile(
                      title: const Text('إنشاء تقرير مفصل'),
                      subtitle:
                          const Text('إنشاء تقرير بجميع المشاكل المكتشفة'),
                      value: _createReport,
                      onChanged: (value) {
                        setState(() {
                          _createReport = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة المشاكل المكتشفة
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'المشاكل المكتشفة في الحجوزات',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.purple,
                      ),
                    ),
                    const SizedBox(height: 16),
                    if (_detectedIssues.isEmpty)
                      const Center(
                        child: Text(
                          'لم يتم اكتشاف أي مشاكل بعد\nقم بتشغيل الفحص أولاً',
                          textAlign: TextAlign.center,
                          style: TextStyle(color: Colors.grey),
                        ),
                      )
                    else
                      ...(_detectedIssues.map((issue) => Card(
                            margin: const EdgeInsets.symmetric(vertical: 4),
                            child: ExpansionTile(
                              leading: CircleAvatar(
                                backgroundColor:
                                    _getSeverityColor(issue['severity']),
                                child: Icon(
                                  _getIssueTypeIcon(issue['type']),
                                  color: Colors.white,
                                ),
                              ),
                              title: Text(
                                  '${issue['reservationNumber']} - ${issue['itemName']}'),
                              subtitle: Text('العميل: ${issue['customer']}'),
                              children: [
                                Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text('المشكلة: ${issue['issue']}'),
                                      const SizedBox(height: 8),
                                      Text('التفاصيل: ${issue['details']}'),
                                      const SizedBox(height: 8),
                                      Text(
                                        'الحل المقترح: ${issue['suggestion']}',
                                        style:
                                            const TextStyle(color: Colors.blue),
                                      ),
                                      const SizedBox(height: 16),
                                      Wrap(
                                        alignment: WrapAlignment.end,
                                        spacing: 4,
                                        runSpacing: 4,
                                        children: [
                                          TextButton.icon(
                                            onPressed: () =>
                                                _viewReservationDetails(
                                                    issue['reservationNumber']),
                                            icon: const Icon(Icons.visibility,
                                                size: 16),
                                            label: const Text('عرض',
                                                style: TextStyle(fontSize: 12)),
                                            style: TextButton.styleFrom(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 8,
                                                      vertical: 4),
                                            ),
                                          ),
                                          TextButton.icon(
                                            onPressed: () =>
                                                _releaseReservation(
                                                    issue['reservationNumber']),
                                            icon: const Icon(Icons.lock_open,
                                                size: 16),
                                            label: const Text('تحرير',
                                                style: TextStyle(fontSize: 12)),
                                            style: TextButton.styleFrom(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 8,
                                                      vertical: 4),
                                            ),
                                          ),
                                          ElevatedButton.icon(
                                            onPressed: () =>
                                                _fixReservationIssue(
                                                    issue['id']),
                                            icon: const Icon(Icons.build,
                                                size: 16),
                                            label: const Text('إصلاح',
                                                style: TextStyle(fontSize: 12)),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.orange,
                                              foregroundColor: Colors.white,
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 8,
                                                      vertical: 4),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ))),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // معاينة العملية
            if (_selectedReservationType != null &&
                _selectedStatus != null &&
                _maintenanceType != null)
              Card(
                color: Colors.purple.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'معاينة عملية الصيانة',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.purple,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildPreviewRow('نوع الحجز:', _getReservationTypeName()),
                      _buildPreviewRow('الحالة:', _getStatusName()),
                      _buildPreviewRow(
                          'نوع الصيانة:', _getMaintenanceTypeName()),
                      _buildPreviewRow('الفترة:',
                          '${_startDate.day}/${_startDate.month}/${_startDate.year} - ${_endDate.day}/${_endDate.month}/${_endDate.year}'),
                      _buildPreviewRow('الحجوزات المنتهية:',
                          _includeExpired ? 'مُضمنة' : 'مستبعدة'),
                      _buildPreviewRow(
                          'الإصلاح التلقائي:', _autoFix ? 'مفعل' : 'معطل'),
                      _buildPreviewRow('المشاكل المكتشفة:',
                          '${_detectedIssues.length} مشكلة'),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 24),

            // أزرار العمليات
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isProcessing ? null : _startMaintenance,
                    icon: _isProcessing
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.search),
                    label: Text(
                        _isProcessing ? 'جاري الفحص...' : 'بدء فحص الحجوزات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _detectedIssues.isEmpty ? null : _fixAllIssues,
                    icon: const Icon(Icons.build),
                    label: const Text('إصلاح جميع المشاكل'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Color _getSeverityColor(String severity) {
    switch (severity) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.yellow;
      default:
        return Colors.grey;
    }
  }

  IconData _getIssueTypeIcon(String type) {
    switch (type) {
      case 'expired_reservations':
        return Icons.schedule;
      case 'duplicate_reservations':
        return Icons.content_copy;
      case 'invalid_quantities':
        return Icons.warning;
      case 'orphaned_reservations':
        return Icons.help_outline;
      case 'cancelled_not_released':
        return Icons.cancel;
      case 'completed_not_released':
        return Icons.check_circle;
      default:
        return Icons.error;
    }
  }

  String _getReservationTypeName() {
    if (_selectedReservationType == null) return 'غير محدد';
    final type = _reservationTypes
        .firstWhere((t) => t['id'] == _selectedReservationType);
    return type['name']!;
  }

  String _getStatusName() {
    if (_selectedStatus == null) return 'غير محدد';
    final status =
        _reservationStatuses.firstWhere((s) => s['id'] == _selectedStatus);
    return status['name']!;
  }

  String _getMaintenanceTypeName() {
    if (_maintenanceType == null) return 'غير محدد';
    final type =
        _maintenanceTypes.firstWhere((t) => t['id'] == _maintenanceType);
    return type['name']!;
  }

  Future<void> _selectDate(bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : _endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  Future<void> _startMaintenance() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isProcessing = true;
      });

      // محاكاة عملية الفحص
      await Future.delayed(const Duration(seconds: 3));

      setState(() {
        _isProcessing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('تم اكتشاف ${_detectedIssues.length} مشكلة في الحجوزات'),
            backgroundColor:
                _detectedIssues.isEmpty ? Colors.green : Colors.orange,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  void _fixReservationIssue(int issueId) {
    setState(() {
      _detectedIssues.removeWhere((issue) => issue['id'] == issueId);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إصلاح مشكلة الحجز رقم $issueId'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _fixAllIssues() {
    final issueCount = _detectedIssues.length;
    setState(() {
      _detectedIssues.clear();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إصلاح جميع مشاكل الحجوزات ($issueCount مشكلة)'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _viewReservationDetails(String reservationNumber) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض تفاصيل الحجز $reservationNumber')),
    );
  }

  void _releaseReservation(String reservationNumber) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تحرير الحجز $reservationNumber')),
    );
  }

  void _showReservationsReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تقرير الحجوزات')),
    );
  }

  void _showMaintenanceHistory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض سجل صيانة الحجوزات')),
    );
  }
}
