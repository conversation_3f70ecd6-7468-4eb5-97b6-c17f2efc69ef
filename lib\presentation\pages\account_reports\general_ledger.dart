import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة دفتر الأستاذ العام
/// تعرض تفاصيل حركة جميع الحسابات
class GeneralLedgerPage extends StatefulWidget {
  const GeneralLedgerPage({super.key});

  @override
  State<GeneralLedgerPage> createState() => _GeneralLedgerPageState();
}

class _GeneralLedgerPageState extends State<GeneralLedgerPage> {
  String _searchQuery = '';
  String _selectedAccount = 'all';

  // بيانات تجريبية لدفتر الأستاذ
  final List<Map<String, dynamic>> _ledgerEntries = [
    {
      'date': '2024-01-15',
      'accountCode': '1101',
      'accountName': 'النقدية في الصندوق',
      'description': 'إيداع نقدي',
      'reference': 'JE-001',
      'debit': 5000.0,
      'credit': 0.0,
      'balance': 5000.0,
    },
    {
      'date': '2024-01-16',
      'accountCode': '1101',
      'accountName': 'النقدية في الصندوق',
      'description': 'سحب نقدي',
      'reference': 'JE-002',
      'debit': 0.0,
      'credit': 1000.0,
      'balance': 4000.0,
    },
    {
      'date': '2024-01-17',
      'accountCode': '1102',
      'accountName': 'البنك الأهلي',
      'description': 'إيداع بنكي',
      'reference': 'JE-003',
      'debit': 10000.0,
      'credit': 0.0,
      'balance': 10000.0,
    },
    {
      'date': '2024-01-18',
      'accountCode': '4101',
      'accountName': 'إيرادات المبيعات',
      'description': 'مبيعات نقدية',
      'reference': 'INV-001',
      'debit': 0.0,
      'credit': 15000.0,
      'balance': 15000.0,
    },
    {
      'date': '2024-01-19',
      'accountCode': '5101',
      'accountName': 'مصروفات الرواتب',
      'description': 'راتب شهر يناير',
      'reference': 'PAY-001',
      'debit': 8000.0,
      'credit': 0.0,
      'balance': 8000.0,
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.generalLedger),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                TextField(
                  decoration: const InputDecoration(
                    hintText: 'البحث في القيود...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedAccount,
                        decoration: const InputDecoration(
                          labelText: 'الحساب',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(
                              value: 'all', child: Text('جميع الحسابات')),
                          DropdownMenuItem(
                              value: '1101', child: Text('النقدية في الصندوق')),
                          DropdownMenuItem(
                              value: '1102', child: Text('البنك الأهلي')),
                          DropdownMenuItem(
                              value: '4101', child: Text('إيرادات المبيعات')),
                          DropdownMenuItem(
                              value: '5101', child: Text('مصروفات الرواتب')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedAccount = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                _buildStatCard('إجمالي القيود',
                    _ledgerEntries.length.toString(), Colors.blue),
                _buildStatCard(
                    'إجمالي المدين', '${_getTotalDebit()} ر.س', Colors.green),
                _buildStatCard(
                    'إجمالي الدائن', '${_getTotalCredit()} ر.س', Colors.red),
                _buildStatCard(
                    'الحسابات', _getUniqueAccounts().toString(), Colors.orange),
              ],
            ),
          ),

          // قائمة القيود
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _ledgerEntries.length,
              itemBuilder: (context, index) {
                final entry = _ledgerEntries[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor:
                          _getAccountTypeColor(entry['accountCode']),
                      child: Text(
                        entry['accountCode'].substring(0, 2),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    title: Text(
                      entry['accountName'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                            'التاريخ: ${entry['date']} | المرجع: ${entry['reference']}'),
                        Text('الوصف: ${entry['description']}'),
                        Row(
                          children: [
                            if (entry['debit'] > 0)
                              Text(
                                'مدين: ${entry['debit'].toStringAsFixed(2)} ر.س',
                                style: const TextStyle(
                                  color: Colors.green,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            if (entry['credit'] > 0)
                              Text(
                                'دائن: ${entry['credit'].toStringAsFixed(2)} ر.س',
                                style: const TextStyle(
                                  color: Colors.red,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                    trailing: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          'الرصيد',
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey[600],
                          ),
                        ),
                        Text(
                          '${entry['balance'].toStringAsFixed(2)} ر.س',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                    isThreeLine: true,
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshData,
        backgroundColor: Colors.orange,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث البيانات'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getAccountTypeColor(String accountCode) {
    String firstDigit = accountCode.substring(0, 1);
    switch (firstDigit) {
      case '1':
        return Colors.green; // أصول
      case '2':
        return Colors.red; // خصوم
      case '3':
        return Colors.blue; // حقوق ملكية
      case '4':
        return Colors.purple; // إيرادات
      case '5':
        return Colors.orange; // مصروفات
      default:
        return Colors.grey;
    }
  }

  double _getTotalDebit() {
    return _ledgerEntries.fold(0.0, (sum, entry) => sum + entry['debit']);
  }

  double _getTotalCredit() {
    return _ledgerEntries.fold(0.0, (sum, entry) => sum + entry['credit']);
  }

  int _getUniqueAccounts() {
    Set<String> uniqueAccounts =
        _ledgerEntries.map((entry) => entry['accountCode'] as String).toSet();
    return uniqueAccounts.length;
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة دفتر الأستاذ العام')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير دفتر الأستاذ العام')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث بيانات دفتر الأستاذ العام'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
