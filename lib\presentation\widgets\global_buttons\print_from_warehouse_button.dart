import 'package:flutter/material.dart';

class PrintFromWarehouseButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String? tooltip;
  final bool isLoading;
  final bool isDisabled;

  const PrintFromWarehouseButton({
    super.key,
    this.onPressed,
    this.tooltip = 'Print From Warehouse',
    this.isLoading = false,
    this.isDisabled = false,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip ?? 'Print From Warehouse',
      child: ElevatedButton.icon(
        onPressed: isDisabled || isLoading ? null : onPressed,
        icon: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              )
            : const Icon(Icons.warehouse),
        label: const Text('Print From Warehouse'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.brown,
          foregroundColor: Colors.white,
        ),
      ),
    );
  }
}
