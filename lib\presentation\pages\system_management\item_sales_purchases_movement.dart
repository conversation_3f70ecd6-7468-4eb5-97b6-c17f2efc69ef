import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة حركة مبيعات ومشتريات صنف
/// تعرض تفاصيل حركة المبيعات والمشتريات لصنف معين
class ItemSalesPurchasesMovementPage extends StatefulWidget {
  const ItemSalesPurchasesMovementPage({super.key});

  @override
  State<ItemSalesPurchasesMovementPage> createState() =>
      _ItemSalesPurchasesMovementPageState();
}

class _ItemSalesPurchasesMovementPageState
    extends State<ItemSalesPurchasesMovementPage> {
  final _searchController = TextEditingController();

  String? _selectedItem;
  String? _selectedPeriod;
  String? _selectedMovementType;
  String _searchQuery = '';

  final List<Map<String, dynamic>> _items = [
    {
      'id': 'item1',
      'code': 'A001',
      'name': 'لابتوب ديل XPS',
      'category': 'إلكترونيات',
      'currentStock': 25,
      'unitPrice': 2500.0,
    },
    {
      'id': 'item2',
      'code': 'B002',
      'name': 'طابعة HP LaserJet',
      'category': 'إلكترونيات',
      'currentStock': 12,
      'unitPrice': 450.0,
    },
    {
      'id': 'item3',
      'code': 'C003',
      'name': 'كرسي مكتب جلد',
      'category': 'أثاث',
      'currentStock': 8,
      'unitPrice': 350.0,
    },
  ];

  final List<Map<String, String>> _periods = [
    {'id': 'today', 'name': 'اليوم'},
    {'id': 'week', 'name': 'هذا الأسبوع'},
    {'id': 'month', 'name': 'هذا الشهر'},
    {'id': 'quarter', 'name': 'هذا الربع'},
    {'id': 'year', 'name': 'هذا العام'},
    {'id': 'custom', 'name': 'فترة مخصصة'},
  ];

  final List<Map<String, String>> _movementTypes = [
    {'id': 'all', 'name': 'جميع الحركات'},
    {'id': 'sales', 'name': 'المبيعات فقط'},
    {'id': 'purchases', 'name': 'المشتريات فقط'},
    {'id': 'returns', 'name': 'المرتجعات فقط'},
  ];

  final List<Map<String, dynamic>> _movements = [
    {
      'id': 'mov1',
      'date': '2024/01/25',
      'time': '10:30',
      'type': 'مبيعات',
      'documentNumber': 'INV-2024-001',
      'customer': 'شركة الأمل',
      'quantity': 2,
      'unitPrice': 2500.0,
      'totalAmount': 5000.0,
      'user': 'أحمد محمد',
      'notes': 'بيع عادي',
    },
    {
      'id': 'mov2',
      'date': '2024/01/24',
      'time': '14:15',
      'type': 'مشتريات',
      'documentNumber': 'PINV-2024-012',
      'customer': 'شركة التوريدات المتقدمة',
      'quantity': 10,
      'unitPrice': 2300.0,
      'totalAmount': 23000.0,
      'user': 'فاطمة علي',
      'notes': 'شراء للمخزون',
    },
    {
      'id': 'mov3',
      'date': '2024/01/23',
      'time': '09:45',
      'type': 'مرتجع مبيعات',
      'documentNumber': 'RET-2024-003',
      'customer': 'مؤسسة النور',
      'quantity': -1,
      'unitPrice': 2500.0,
      'totalAmount': -2500.0,
      'user': 'محمد سالم',
      'notes': 'عيب في المنتج',
    },
    {
      'id': 'mov4',
      'date': '2024/01/22',
      'time': '16:20',
      'type': 'مبيعات',
      'documentNumber': 'INV-2024-002',
      'customer': 'شركة الفجر',
      'quantity': 3,
      'unitPrice': 2500.0,
      'totalAmount': 7500.0,
      'user': 'نورا أحمد',
      'notes': 'بيع بخصم 5%',
    },
  ];

  List<Map<String, dynamic>> get _filteredItems {
    if (_searchQuery.isEmpty) return _items;
    return _items
        .where((item) =>
            item['name']
                .toString()
                .toLowerCase()
                .contains(_searchQuery.toLowerCase()) ||
            item['code']
                .toString()
                .toLowerCase()
                .contains(_searchQuery.toLowerCase()) ||
            item['category']
                .toString()
                .toLowerCase()
                .contains(_searchQuery.toLowerCase()))
        .toList();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.itemSalesPurchasesMovement),
        backgroundColor: Colors.cyan,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showAnalytics,
            tooltip: 'تحليلات الحركة',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportMovement,
            tooltip: 'تصدير الحركة',
          ),
        ],
      ),
      body: Column(
        children: [
          // بطاقة اختيار الصنف
          Card(
            margin: const EdgeInsets.all(16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'اختيار الصنف',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.cyan,
                    ),
                  ),
                  const SizedBox(height: 16),

                  TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      labelText: 'البحث عن صنف',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),

                  const SizedBox(height: 16),

                  // قائمة الأصناف
                  Container(
                    height: 150,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: ListView.builder(
                      itemCount: _filteredItems.length,
                      itemBuilder: (context, index) {
                        final item = _filteredItems[index];
                        final isSelected = _selectedItem == item['id'];

                        return ListTile(
                          selected: isSelected,
                          leading: CircleAvatar(
                            backgroundColor:
                                isSelected ? Colors.cyan : Colors.grey,
                            child: Text(
                              item['code'].substring(0, 1),
                              style: const TextStyle(color: Colors.white),
                            ),
                          ),
                          title: Text('${item['code']} - ${item['name']}'),
                          subtitle: Text(
                              '${item['category']} | المخزون: ${item['currentStock']} | السعر: ${item['unitPrice']} ر.س'),
                          onTap: () {
                            setState(() {
                              _selectedItem = item['id'];
                            });
                          },
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),

          // بطاقة الفلاتر
          Card(
            margin: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  // فلتر الفترة
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _selectedPeriod,
                      decoration: const InputDecoration(
                        labelText: 'الفترة الزمنية',
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                      ),
                      items: _periods.map<DropdownMenuItem<String>>((period) {
                        return DropdownMenuItem<String>(
                          value: period['id'],
                          child: Text(
                            period['name']!,
                            overflow: TextOverflow.ellipsis,
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedPeriod = value;
                        });
                      },
                    ),
                  ),

                  const SizedBox(width: 16),

                  // فلتر نوع الحركة
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _selectedMovementType,
                      decoration: const InputDecoration(
                        labelText: 'نوع الحركة',
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                      ),
                      items:
                          _movementTypes.map<DropdownMenuItem<String>>((type) {
                        return DropdownMenuItem<String>(
                          value: type['id'],
                          child: Text(
                            type['name']!,
                            overflow: TextOverflow.ellipsis,
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedMovementType = value;
                        });
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // ملخص الحركة
          if (_selectedItem != null)
            Card(
              margin: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    const Text(
                      'ملخص الحركة',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.cyan,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildSummaryCard('إجمالي المبيعات', '12', '30,000 ر.س',
                            Colors.green),
                        _buildSummaryCard('إجمالي المشتريات', '10',
                            '23,000 ر.س', Colors.blue),
                        _buildSummaryCard(
                            'صافي الحركة', '2', '7,000 ر.س', Colors.orange),
                      ],
                    ),
                  ],
                ),
              ),
            ),

          const SizedBox(height: 16),

          // قائمة الحركات
          Expanded(
            child: Card(
              margin: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(16.0),
                    decoration: BoxDecoration(
                      color: Colors.cyan.shade50,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(8),
                        topRight: Radius.circular(8),
                      ),
                    ),
                    child: const Row(
                      children: [
                        Icon(Icons.list, color: Colors.cyan),
                        SizedBox(width: 8),
                        Text(
                          'تفاصيل الحركة',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.cyan,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: ListView.builder(
                      padding: const EdgeInsets.all(8.0),
                      itemCount: _movements.length,
                      itemBuilder: (context, index) {
                        final movement = _movements[index];
                        return Card(
                          margin: const EdgeInsets.symmetric(vertical: 4),
                          child: ListTile(
                            leading: CircleAvatar(
                              backgroundColor:
                                  _getMovementTypeColor(movement['type']),
                              child: Icon(
                                _getMovementTypeIcon(movement['type']),
                                color: Colors.white,
                                size: 20,
                              ),
                            ),
                            title: Text(
                              '${movement['documentNumber']} - ${movement['type']}',
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                    '${movement['customer']} | ${movement['date']} ${movement['time']}'),
                                Text(
                                  'الكمية: ${movement['quantity']} | المبلغ: ${movement['totalAmount']} ر.س',
                                  style: TextStyle(
                                    color: movement['totalAmount'] >= 0
                                        ? Colors.green
                                        : Colors.red,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                if (movement['notes'].isNotEmpty)
                                  Text(
                                    'ملاحظات: ${movement['notes']}',
                                    style: const TextStyle(
                                        fontSize: 12,
                                        fontStyle: FontStyle.italic),
                                  ),
                              ],
                            ),
                            trailing: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  '${movement['unitPrice']} ر.س',
                                  style: const TextStyle(
                                      fontWeight: FontWeight.bold),
                                ),
                                Text(
                                  movement['user'],
                                  style: const TextStyle(
                                      fontSize: 10, color: Colors.grey),
                                ),
                              ],
                            ),
                            isThreeLine: true,
                            onTap: () => _viewMovementDetails(movement),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: _selectedItem != null
          ? FloatingActionButton.extended(
              onPressed: _generateDetailedReport,
              backgroundColor: Colors.cyan,
              icon: const Icon(Icons.assessment),
              label: const Text('تقرير مفصل'),
            )
          : null,
    );
  }

  Widget _buildSummaryCard(
      String title, String quantity, String amount, Color color) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.trending_up,
            color: color,
            size: 24,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
          textAlign: TextAlign.center,
        ),
        Text(
          '$quantity قطعة',
          style: TextStyle(fontSize: 10, color: color),
        ),
        Text(
          amount,
          style: TextStyle(
              fontSize: 10, color: color, fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  Color _getMovementTypeColor(String type) {
    switch (type) {
      case 'مبيعات':
        return Colors.green;
      case 'مشتريات':
        return Colors.blue;
      case 'مرتجع مبيعات':
        return Colors.red;
      case 'مرتجع مشتريات':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getMovementTypeIcon(String type) {
    switch (type) {
      case 'مبيعات':
        return Icons.trending_up;
      case 'مشتريات':
        return Icons.trending_down;
      case 'مرتجع مبيعات':
        return Icons.keyboard_return;
      case 'مرتجع مشتريات':
        return Icons.undo;
      default:
        return Icons.help;
    }
  }

  void _viewMovementDetails(Map<String, dynamic> movement) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
          content: Text('عرض تفاصيل الحركة ${movement['documentNumber']}')),
    );
  }

  void _generateDetailedReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء تقرير مفصل لحركة الصنف')),
    );
  }

  void _showAnalytics() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تحليلات حركة المبيعات والمشتريات')),
    );
  }

  void _exportMovement() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير بيانات حركة الصنف')),
    );
  }
}
