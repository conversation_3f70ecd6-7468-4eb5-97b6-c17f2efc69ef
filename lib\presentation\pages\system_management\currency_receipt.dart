import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة إستلام العملات
/// تتيح إدارة وتسجيل استلام العملات المختلفة
class CurrencyReceiptPage extends StatefulWidget {
  const CurrencyReceiptPage({super.key});

  @override
  State<CurrencyReceiptPage> createState() => _CurrencyReceiptPageState();
}

class _CurrencyReceiptPageState extends State<CurrencyReceiptPage> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();

  String? _selectedCurrency;
  String? _selectedSource;
  String? _selectedAccount;
  DateTime _selectedDate = DateTime.now();
  bool _isProcessing = false;

  final List<Map<String, dynamic>> _currencies = [
    {
      'code': 'SAR',
      'name': 'ريال سعودي',
      'symbol': 'ر.س',
      'rate': 1.0,
      'isBase': true,
    },
    {
      'code': 'USD',
      'name': 'دولار أمريكي',
      'symbol': '\$',
      'rate': 3.75,
      'isBase': false,
    },
    {
      'code': 'EUR',
      'name': 'يورو',
      'symbol': '€',
      'rate': 4.10,
      'isBase': false,
    },
    {
      'code': 'GBP',
      'name': 'جنيه إسترليني',
      'symbol': '£',
      'rate': 4.65,
      'isBase': false,
    },
    {
      'code': 'AED',
      'name': 'درهم إماراتي',
      'symbol': 'د.إ',
      'rate': 1.02,
      'isBase': false,
    },
  ];

  final List<Map<String, String>> _sources = [
    {'id': 'bank_transfer', 'name': 'تحويل بنكي'},
    {'id': 'cash_deposit', 'name': 'إيداع نقدي'},
    {'id': 'customer_payment', 'name': 'دفعة عميل'},
    {'id': 'currency_exchange', 'name': 'صرافة'},
    {'id': 'investment_return', 'name': 'عائد استثمار'},
    {'id': 'other', 'name': 'أخرى'},
  ];

  final List<Map<String, String>> _accounts = [
    {'id': 'main_cash', 'name': 'الخزينة الرئيسية'},
    {'id': 'bank_account_1', 'name': 'حساب البنك الأهلي'},
    {'id': 'bank_account_2', 'name': 'حساب بنك الراجحي'},
    {'id': 'foreign_currency', 'name': 'حساب العملات الأجنبية'},
    {'id': 'petty_cash', 'name': 'النثرية'},
  ];

  final List<Map<String, dynamic>> _recentReceipts = [
    {
      'id': 'rec1',
      'date': '2024/01/25',
      'currency': 'USD',
      'amount': 1000.0,
      'sarAmount': 3750.0,
      'source': 'تحويل بنكي',
      'account': 'حساب البنك الأهلي',
      'notes': 'دفعة من عميل أمريكي',
    },
    {
      'id': 'rec2',
      'date': '2024/01/24',
      'currency': 'EUR',
      'amount': 500.0,
      'sarAmount': 2050.0,
      'source': 'صرافة',
      'account': 'حساب العملات الأجنبية',
      'notes': 'تحويل من اليورو للريال',
    },
    {
      'id': 'rec3',
      'date': '2024/01/23',
      'currency': 'SAR',
      'amount': 5000.0,
      'sarAmount': 5000.0,
      'source': 'إيداع نقدي',
      'account': 'الخزينة الرئيسية',
      'notes': 'إيداع نقدي من المبيعات',
    },
  ];

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.currencyReceipt),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: _showReceiptHistory,
            tooltip: 'سجل الاستلام',
          ),
          IconButton(
            icon: const Icon(Icons.currency_exchange),
            onPressed: _showExchangeRates,
            tooltip: 'أسعار الصرف',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // بطاقة إستلام العملة
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'تسجيل استلام عملة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(height: 16),

                    Row(
                      children: [
                        // العملة
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedCurrency,
                            decoration: const InputDecoration(
                              labelText: 'العملة',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 8),
                            ),
                            items: _currencies
                                .map<DropdownMenuItem<String>>((currency) {
                              return DropdownMenuItem<String>(
                                value: currency['code'],
                                child: Row(
                                  children: [
                                    Text(currency['symbol']),
                                    const SizedBox(width: 8),
                                    Text(
                                        '${currency['name']} (${currency['code']})'),
                                  ],
                                ),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedCurrency = value;
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'يرجى اختيار العملة';
                              }
                              return null;
                            },
                          ),
                        ),

                        const SizedBox(width: 16),

                        // المبلغ
                        Expanded(
                          child: TextFormField(
                            controller: _amountController,
                            decoration: InputDecoration(
                              labelText: 'المبلغ',
                              suffixText: _getSelectedCurrencySymbol(),
                              border: const OutlineInputBorder(),
                              contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 8),
                            ),
                            keyboardType: TextInputType.number,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال المبلغ';
                              }
                              if (double.tryParse(value) == null ||
                                  double.parse(value) <= 0) {
                                return 'يرجى إدخال مبلغ صحيح';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    Row(
                      children: [
                        // مصدر الاستلام
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedSource,
                            decoration: const InputDecoration(
                              labelText: 'مصدر الاستلام',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 8),
                            ),
                            items: _sources
                                .map<DropdownMenuItem<String>>((source) {
                              return DropdownMenuItem<String>(
                                value: source['id'],
                                child: Text(source['name']!),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedSource = value;
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'يرجى اختيار المصدر';
                              }
                              return null;
                            },
                          ),
                        ),

                        const SizedBox(width: 16),

                        // الحساب
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedAccount,
                            decoration: const InputDecoration(
                              labelText: 'الحساب المستلم',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 8),
                            ),
                            items: _accounts
                                .map<DropdownMenuItem<String>>((account) {
                              return DropdownMenuItem<String>(
                                value: account['id'],
                                child: Text(account['name']!),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedAccount = value;
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'يرجى اختيار الحساب';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // التاريخ والملاحظات
                    Row(
                      children: [
                        Expanded(
                          child: InkWell(
                            onTap: _selectDate,
                            child: InputDecorator(
                              decoration: const InputDecoration(
                                labelText: 'تاريخ الاستلام',
                                border: OutlineInputBorder(),
                                contentPadding: EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 8),
                              ),
                              child: Text(
                                '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _notesController,
                            decoration: const InputDecoration(
                              labelText: 'ملاحظات',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 8),
                            ),
                            maxLines: 1,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // معاينة التحويل
            if (_selectedCurrency != null && _amountController.text.isNotEmpty)
              Card(
                color: Colors.green.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'معاينة التحويل',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildPreviewRow('العملة المستلمة:',
                          '${_amountController.text} ${_getSelectedCurrencySymbol()}'),
                      _buildPreviewRow(
                          'سعر الصرف:', '${_getSelectedCurrencyRate()} ر.س'),
                      _buildPreviewRow(
                          'المبلغ بالريال:', '${_calculateSarAmount()} ر.س'),
                      _buildPreviewRow('المصدر:', _getSelectedSourceName()),
                      _buildPreviewRow('الحساب:', _getSelectedAccountName()),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 16),

            // أزرار العمليات
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isProcessing ? null : _saveReceipt,
                    icon: _isProcessing
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.save),
                    label:
                        Text(_isProcessing ? 'جاري الحفظ...' : 'حفظ الاستلام'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _resetForm,
                    icon: const Icon(Icons.refresh),
                    label: const Text('إعادة تعيين'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // آخر عمليات الاستلام
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'آخر عمليات الاستلام',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _recentReceipts.length,
                      itemBuilder: (context, index) {
                        final receipt = _recentReceipts[index];
                        return ListTile(
                          leading: CircleAvatar(
                            backgroundColor: Colors.green,
                            child: Text(
                              receipt['currency'],
                              style: const TextStyle(
                                  color: Colors.white, fontSize: 10),
                            ),
                          ),
                          title: Text(
                              '${receipt['amount']} ${receipt['currency']} = ${receipt['sarAmount']} ر.س'),
                          subtitle:
                              Text('${receipt['source']} | ${receipt['date']}'),
                          trailing: IconButton(
                            icon: const Icon(Icons.info_outline),
                            onPressed: () => _showReceiptDetails(receipt),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  String _getSelectedCurrencySymbol() {
    if (_selectedCurrency == null) return '';
    final currency =
        _currencies.firstWhere((c) => c['code'] == _selectedCurrency);
    return currency['symbol'];
  }

  double _getSelectedCurrencyRate() {
    if (_selectedCurrency == null) return 1.0;
    final currency =
        _currencies.firstWhere((c) => c['code'] == _selectedCurrency);
    return currency['rate'];
  }

  String _calculateSarAmount() {
    if (_selectedCurrency == null || _amountController.text.isEmpty) {
      return '0.00';
    }
    final amount = double.tryParse(_amountController.text) ?? 0.0;
    final rate = _getSelectedCurrencyRate();
    return (amount * rate).toStringAsFixed(2);
  }

  String _getSelectedSourceName() {
    if (_selectedSource == null) return 'غير محدد';
    final source = _sources.firstWhere((s) => s['id'] == _selectedSource);
    return source['name']!;
  }

  String _getSelectedAccountName() {
    if (_selectedAccount == null) return 'غير محدد';
    final account = _accounts.firstWhere((a) => a['id'] == _selectedAccount);
    return account['name']!;
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _saveReceipt() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isProcessing = true;
      });

      // محاكاة عملية الحفظ
      await Future.delayed(const Duration(seconds: 2));

      setState(() {
        _isProcessing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ عملية استلام العملة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        _resetForm();
      }
    }
  }

  void _resetForm() {
    setState(() {
      _selectedCurrency = null;
      _selectedSource = null;
      _selectedAccount = null;
      _selectedDate = DateTime.now();
      _amountController.clear();
      _notesController.clear();
    });
  }

  void _showReceiptHistory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض سجل استلام العملات')),
    );
  }

  void _showExchangeRates() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض أسعار صرف العملات')),
    );
  }

  void _showReceiptDetails(Map<String, dynamic> receipt) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تفاصيل الاستلام: ${receipt['id']}')),
    );
  }
}
