import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة جرد مستودعات فرع معين
/// تعرض تفاصيل جرد مستودعات فرع محدد مع مقارنة الأرصدة
class BranchInventoryCountReportPage extends StatefulWidget {
  const BranchInventoryCountReportPage({super.key});

  @override
  State<BranchInventoryCountReportPage> createState() => _BranchInventoryCountReportPageState();
}

class _BranchInventoryCountReportPageState extends State<BranchInventoryCountReportPage> {
  DateTime? _countDate;
  String? _selectedBranch;
  String? _selectedWarehouse;
  String? _countMethod = 'complete';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.branchInventoryCount),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.compare_arrows),
            onPressed: _compareBranches,
            tooltip: 'مقارنة الفروع',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.indigo[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'الفرع',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedBranch,
                        items: const [
                          DropdownMenuItem(value: 'branch1', child: Text('الفرع الأول - الرياض')),
                          DropdownMenuItem(value: 'branch2', child: Text('الفرع الثاني - جدة')),
                          DropdownMenuItem(value: 'branch3', child: Text('الفرع الثالث - الدمام')),
                          DropdownMenuItem(value: 'branch4', child: Text('الفرع الرابع - المدينة')),
                        ],
                        onChanged: (value) => setState(() => _selectedBranch = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'المستودع',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedWarehouse,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع المستودعات')),
                          DropdownMenuItem(value: 'main', child: Text('المستودع الرئيسي')),
                          DropdownMenuItem(value: 'secondary', child: Text('المستودع الثانوي')),
                          DropdownMenuItem(value: 'damaged', child: Text('مستودع التالف')),
                        ],
                        onChanged: (value) => setState(() => _selectedWarehouse = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'تاريخ الجرد',
                          border: OutlineInputBorder(),
                          suffixIcon: Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context),
                        controller: TextEditingController(
                          text: _countDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'طريقة الجرد',
                          border: OutlineInputBorder(),
                        ),
                        value: _countMethod,
                        items: const [
                          DropdownMenuItem(value: 'complete', child: Text('جرد شامل')),
                          DropdownMenuItem(value: 'selective', child: Text('جرد انتقائي')),
                          DropdownMenuItem(value: 'cycle', child: Text('جرد دوري')),
                          DropdownMenuItem(value: 'spot', child: Text('جرد عشوائي')),
                        ],
                        onChanged: (value) => setState(() => _countMethod = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.search),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.indigo,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات الفرع
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.business, color: Colors.indigo, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'معلومات الفرع',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildInfoCard('اسم الفرع', 'الفرع الأول - الرياض', Icons.location_city),
                              _buildInfoCard('المدير', 'خالد أحمد', Icons.person),
                              _buildInfoCard('عدد المستودعات', '3', Icons.warehouse),
                              _buildInfoCard('آخر جرد', '2024-01-10', Icons.history),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // ملخص الجرد
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'ملخص نتائج الجرد',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSummaryCard('إجمالي الأصناف', '245', Colors.blue, Icons.inventory),
                              _buildSummaryCard('أصناف مطابقة', '220', Colors.green, Icons.check_circle),
                              _buildSummaryCard('فروقات موجبة', '15', Colors.orange, Icons.trending_up),
                              _buildSummaryCard('فروقات سالبة', '10', Colors.red, Icons.trending_down),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // تفاصيل الجرد حسب المستودع
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل الجرد حسب المستودع',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('المستودع')),
                                DataColumn(label: Text('عدد الأصناف')),
                                DataColumn(label: Text('القيمة النظرية')),
                                DataColumn(label: Text('القيمة الفعلية')),
                                DataColumn(label: Text('الفرق')),
                                DataColumn(label: Text('نسبة الدقة')),
                                DataColumn(label: Text('الحالة')),
                              ],
                              rows: _buildWarehouseRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // الفروقات الرئيسية
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'الفروقات الرئيسية',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('كود الصنف')),
                                DataColumn(label: Text('اسم الصنف')),
                                DataColumn(label: Text('المستودع')),
                                DataColumn(label: Text('الكمية النظرية')),
                                DataColumn(label: Text('الكمية الفعلية')),
                                DataColumn(label: Text('الفرق')),
                                DataColumn(label: Text('قيمة الفرق')),
                                DataColumn(label: Text('نوع الفرق')),
                              ],
                              rows: _buildVarianceRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // مقارنة مع الفروع الأخرى
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'مقارنة مع الفروع الأخرى',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildComparisonCard('الفرع الأول', '95.2%', Colors.green, Icons.star),
                              _buildComparisonCard('الفرع الثاني', '92.8%', Colors.blue, Icons.trending_up),
                              _buildComparisonCard('الفرع الثالث', '89.5%', Colors.orange, Icons.trending_flat),
                              _buildComparisonCard('المتوسط العام', '92.5%', Colors.purple, Icons.analytics),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _approveCount,
                                  icon: const Icon(Icons.check),
                                  label: const Text('اعتماد الجرد'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _adjustInventory,
                                  icon: const Icon(Icons.tune),
                                  label: const Text('تسوية المخزون'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _sendToManager,
                                  icon: const Icon(Icons.send),
                                  label: const Text('إرسال للمدير'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _scheduleRecount,
                                  icon: const Icon(Icons.refresh),
                                  label: const Text('إعادة جرد'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<DataRow> _buildWarehouseRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('المستودع الرئيسي')),
        const DataCell(Text('180')),
        const DataCell(Text('450,000 ر.س')),
        const DataCell(Text('448,500 ر.س')),
        const DataCell(Text('-1,500 ر.س')),
        const DataCell(Text('99.7%')),
        DataCell(_buildStatusBadge('ممتاز', Colors.green)),
      ]),
      DataRow(cells: [
        const DataCell(Text('المستودع الثانوي')),
        const DataCell(Text('50')),
        const DataCell(Text('125,000 ر.س')),
        const DataCell(Text('127,200 ر.س')),
        const DataCell(Text('+2,200 ر.س')),
        const DataCell(Text('101.8%')),
        DataCell(_buildStatusBadge('جيد', Colors.blue)),
      ]),
      DataRow(cells: [
        const DataCell(Text('مستودع التالف')),
        const DataCell(Text('15')),
        const DataCell(Text('8,500 ر.س')),
        const DataCell(Text('8,200 ر.س')),
        const DataCell(Text('-300 ر.س')),
        const DataCell(Text('96.5%')),
        DataCell(_buildStatusBadge('مقبول', Colors.orange)),
      ]),
    ];
  }

  List<DataRow> _buildVarianceRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('ITEM-001')),
        const DataCell(Text('لابتوب ديل')),
        const DataCell(Text('المستودع الرئيسي')),
        const DataCell(Text('25')),
        const DataCell(Text('23')),
        const DataCell(Text('-2')),
        const DataCell(Text('-5,000 ر.س')),
        DataCell(_buildVarianceType('نقص', Colors.red)),
      ]),
      DataRow(cells: [
        const DataCell(Text('ITEM-002')),
        const DataCell(Text('طابعة HP')),
        const DataCell(Text('المستودع الثانوي')),
        const DataCell(Text('15')),
        const DataCell(Text('17')),
        const DataCell(Text('+2')),
        const DataCell(Text('+1,200 ر.س')),
        DataCell(_buildVarianceType('زيادة', Colors.green)),
      ]),
    ];
  }

  Widget _buildInfoCard(String title, String value, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            children: [
              Icon(icon, color: Colors.indigo, size: 24),
              const SizedBox(height: 8),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildComparisonCard(String branch, String accuracy, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 28),
              const SizedBox(height: 8),
              Text(
                accuracy,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                branch,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(status, style: const TextStyle(color: Colors.white, fontSize: 12)),
    );
  }

  Widget _buildVarianceType(String type, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(type, style: const TextStyle(color: Colors.white, fontSize: 12)),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        _countDate = picked;
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير جرد الفرع بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة تقرير جرد الفرع...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير تقرير جرد الفرع...')),
    );
  }

  void _compareBranches() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض مقارنة تفصيلية بين الفروع')),
    );
  }

  void _approveCount() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم اعتماد نتائج الجرد')),
    );
  }

  void _adjustInventory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء قيود تسوية المخزون')),
    );
  }

  void _sendToManager() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إرسال التقرير لمدير الفرع')),
    );
  }

  void _scheduleRecount() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم جدولة إعادة جرد للأصناف المختلفة')),
    );
  }
}
