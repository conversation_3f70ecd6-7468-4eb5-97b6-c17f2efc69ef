import 'package:flutter/material.dart';

/// صفحة حركة مندوبين فرع معين
/// تعرض حركة المندوبين في فرع محدد
class BranchRepresentativesMovementPage extends StatefulWidget {
  const BranchRepresentativesMovementPage({super.key});

  @override
  State<BranchRepresentativesMovementPage> createState() => _BranchRepresentativesMovementPageState();
}

class _BranchRepresentativesMovementPageState extends State<BranchRepresentativesMovementPage> {
  String _selectedBranch = 'riyadh';
  String _selectedPeriod = 'current_month';

  // بيانات تجريبية للفروع
  final Map<String, String> _branches = {
    'riyadh': 'فرع الرياض',
    'jeddah': 'فرع جدة',
    'dammam': 'فرع الدمام',
    'mecca': 'فرع مكة',
  };

  // بيانات تجريبية لحركة المندوبين
  final Map<String, List<Map<String, dynamic>>> _representativesData = {
    'riyadh': [
      {
        'id': 'REP001',
        'name': 'أحمد محمد',
        'totalSales': 150000.0,
        'totalCommission': 7500.0,
        'ordersCount': 45,
        'customersCount': 25,
        'performance': 92.5,
      },
      {
        'id': 'REP002',
        'name': 'محمد علي',
        'totalSales': 120000.0,
        'totalCommission': 6000.0,
        'ordersCount': 38,
        'customersCount': 20,
        'performance': 88.0,
      },
      {
        'id': 'REP003',
        'name': 'علي أحمد',
        'totalSales': 95000.0,
        'totalCommission': 4750.0,
        'ordersCount': 32,
        'customersCount': 18,
        'performance': 85.5,
      },
    ],
    'jeddah': [
      {
        'id': 'REP004',
        'name': 'سعد عبدالله',
        'totalSales': 180000.0,
        'totalCommission': 9000.0,
        'ordersCount': 52,
        'customersCount': 30,
        'performance': 95.0,
      },
      {
        'id': 'REP005',
        'name': 'عبدالله سعد',
        'totalSales': 140000.0,
        'totalCommission': 7000.0,
        'ordersCount': 42,
        'customersCount': 24,
        'performance': 90.0,
      },
    ],
  };

  @override
  Widget build(BuildContext context) {
    final currentRepresentatives = _representativesData[_selectedBranch] ?? [];
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('حركة مندوبين فرع معين'),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedBranch,
                        decoration: const InputDecoration(
                          labelText: 'الفرع',
                          border: OutlineInputBorder(),
                        ),
                        items: _branches.entries.map((entry) {
                          return DropdownMenuItem(
                            value: entry.key,
                            child: Text(entry.value),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedBranch = value!;
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedPeriod,
                        decoration: const InputDecoration(
                          labelText: 'الفترة',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'current_month', child: Text('الشهر الحالي')),
                          DropdownMenuItem(value: 'current_quarter', child: Text('الربع الحالي')),
                          DropdownMenuItem(value: 'current_year', child: Text('السنة الحالية')),
                          DropdownMenuItem(value: 'custom', child: Text('فترة مخصصة')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedPeriod = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // معلومات الفرع
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              color: Colors.indigo[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      _branches[_selectedBranch] ?? '',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Column(
                          children: [
                            const Text('عدد المندوبين'),
                            Text(
                              currentRepresentatives.length.toString(),
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('إجمالي المبيعات'),
                            Text(
                              '${_getTotalSales(currentRepresentatives)} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('إجمالي العمولات'),
                            Text(
                              '${_getTotalCommissions(currentRepresentatives)} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.orange,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                _buildStatCard('إجمالي المبيعات', '${_getTotalSales(currentRepresentatives)} ر.س', Colors.green),
                _buildStatCard('إجمالي العمولات', '${_getTotalCommissions(currentRepresentatives)} ر.س', Colors.orange),
                _buildStatCard('متوسط الأداء', '${_getAveragePerformance(currentRepresentatives)}%', Colors.blue),
                _buildStatCard('إجمالي الطلبات', _getTotalOrders(currentRepresentatives).toString(), Colors.purple),
              ],
            ),
          ),

          // قائمة المندوبين
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: currentRepresentatives.length,
              itemBuilder: (context, index) {
                final rep = currentRepresentatives[index];
                
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: _getPerformanceColor(rep['performance']),
                      child: Text(
                        rep['name'].split(' ')[0].substring(0, 1),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    title: Text(
                      '${rep['id']} - ${rep['name']}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('المبيعات: ${rep['totalSales'].toStringAsFixed(2)} ر.س'),
                        Text('الأداء: ${rep['performance']}%'),
                        const SizedBox(height: 4),
                        LinearProgressIndicator(
                          value: rep['performance'] / 100,
                          backgroundColor: Colors.grey[300],
                          valueColor: AlwaysStoppedAnimation<Color>(
                            _getPerformanceColor(rep['performance']),
                          ),
                        ),
                      ],
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('إجمالي المبيعات', '${rep['totalSales'].toStringAsFixed(2)} ر.س', Icons.attach_money),
                                ),
                                Expanded(
                                  child: _buildDetailCard('العمولة', '${rep['totalCommission'].toStringAsFixed(2)} ر.س', Icons.percent),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('عدد الطلبات', rep['ordersCount'].toString(), Icons.shopping_cart),
                                ),
                                Expanded(
                                  child: _buildDetailCard('عدد العملاء', rep['customersCount'].toString(), Icons.people),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('متوسط الطلب', '${(rep['totalSales'] / rep['ordersCount']).toStringAsFixed(2)} ر.س', Icons.calculate),
                                ),
                                Expanded(
                                  child: _buildDetailCard('نسبة العمولة', '${((rep['totalCommission'] / rep['totalSales']) * 100).toStringAsFixed(1)}%', Icons.trending_up),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshData,
        backgroundColor: Colors.indigo,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث البيانات'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailCard(String title, String value, IconData icon) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Icon(icon, size: 20, color: Colors.grey[600]),
            const SizedBox(height: 4),
            Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
            Text(
              title,
              style: const TextStyle(fontSize: 10),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Color _getPerformanceColor(double performance) {
    if (performance >= 90) return Colors.green;
    if (performance >= 80) return Colors.orange;
    return Colors.red;
  }

  double _getTotalSales(List<Map<String, dynamic>> representatives) {
    return representatives.fold(0.0, (sum, rep) => sum + rep['totalSales']);
  }

  double _getTotalCommissions(List<Map<String, dynamic>> representatives) {
    return representatives.fold(0.0, (sum, rep) => sum + rep['totalCommission']);
  }

  String _getAveragePerformance(List<Map<String, dynamic>> representatives) {
    if (representatives.isEmpty) return '0.0';
    double total = representatives.fold(0.0, (sum, rep) => sum + rep['performance']);
    return (total / representatives.length).toStringAsFixed(1);
  }

  int _getTotalOrders(List<Map<String, dynamic>> representatives) {
    return representatives.fold(0, (sum, rep) => sum + rep['ordersCount'] as int);
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة تقرير حركة مندوبين الفرع')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير تقرير حركة مندوبين الفرع')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث بيانات حركة مندوبين الفرع'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
