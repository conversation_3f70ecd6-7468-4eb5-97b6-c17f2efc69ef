import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير الأصناف ناقصة الكميات
/// تعرض الأصناف التي وصلت إلى الحد الأدنى أو أقل
class LowStockItemsReportPage extends StatefulWidget {
  const LowStockItemsReportPage({super.key});

  @override
  State<LowStockItemsReportPage> createState() => _LowStockItemsReportPageState();
}

class _LowStockItemsReportPageState extends State<LowStockItemsReportPage> {
  String? _selectedCategory;
  String? _selectedWarehouse;
  String? _alertLevel = 'all';
  double _customThreshold = 10.0;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.lowStockItems),
        backgroundColor: Colors.amber,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: _setupAlerts,
            tooltip: 'إعداد التنبيهات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.amber[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.category,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedCategory,
                        items: [
                          DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                          DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                          DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                          DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                        ],
                        onChanged: (value) => setState(() => _selectedCategory = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'مستوى التنبيه',
                          border: OutlineInputBorder(),
                        ),
                        value: _alertLevel,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع المستويات')),
                          DropdownMenuItem(value: 'critical', child: Text('حرج (أقل من 5)')),
                          DropdownMenuItem(value: 'low', child: Text('منخفض (5-10)')),
                          DropdownMenuItem(value: 'warning', child: Text('تحذير (10-20)')),
                          DropdownMenuItem(value: 'custom', child: Text('مخصص')),
                        ],
                        onChanged: (value) => setState(() => _alertLevel = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.warehouse,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedWarehouse,
                        items: [
                          DropdownMenuItem(value: 'all', child: Text(localizations.allWarehouses)),
                          DropdownMenuItem(value: 'main', child: Text(localizations.mainWarehouse)),
                          DropdownMenuItem(value: 'branch1', child: Text('${localizations.branchWarehouse} الأول')),
                        ],
                        onChanged: (value) => setState(() => _selectedWarehouse = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    if (_alertLevel == 'custom')
                      Expanded(
                        child: TextFormField(
                          decoration: const InputDecoration(
                            labelText: 'الحد المخصص',
                            border: OutlineInputBorder(),
                            suffixText: 'قطعة',
                          ),
                          keyboardType: TextInputType.number,
                          onChanged: (value) => setState(() => _customThreshold = double.tryParse(value) ?? 10.0),
                        ),
                      )
                    else
                      const Expanded(child: SizedBox()),
                  ],
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.search),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.amber,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // تحذير المخزون المنخفض
                  Card(
                    color: Colors.amber[50],
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        children: [
                          const Icon(Icons.warning_amber, color: Colors.amber, size: 32),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'تحذير: مخزون منخفض',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.amber,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                const Text(
                                  'يوجد أصناف وصلت للحد الأدنى وتحتاج إلى إعادة تموين.',
                                  style: TextStyle(color: Colors.amber),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إحصائيات المخزون المنخفض
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إحصائيات المخزون المنخفض',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildStatCard('حرج', '12', Colors.red, Icons.error),
                              _buildStatCard('منخفض', '25', Colors.orange, Icons.warning),
                              _buildStatCard('تحذير', '18', Colors.amber, Icons.info),
                              _buildStatCard('إجمالي', '55', Colors.blue, Icons.inventory),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول الأصناف ناقصة الكميات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل الأصناف ناقصة الكميات',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: [
                                DataColumn(label: Text(localizations.itemCode)),
                                DataColumn(label: Text(localizations.itemName)),
                                DataColumn(label: Text('الكمية الحالية')),
                                DataColumn(label: Text('الحد الأدنى')),
                                DataColumn(label: Text('النقص')),
                                DataColumn(label: Text('الكمية المقترحة')),
                                DataColumn(label: Text('آخر طلب')),
                                DataColumn(label: Text('مستوى التنبيه')),
                                DataColumn(label: Text('الإجراء')),
                              ],
                              rows: _buildLowStockRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // تحليل الاتجاهات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تحليل اتجاهات الاستهلاك',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('الصنف')),
                                DataColumn(label: Text('الاستهلاك الأسبوعي')),
                                DataColumn(label: Text('الاتجاه')),
                                DataColumn(label: Text('التوقع القادم')),
                                DataColumn(label: Text('التوصية')),
                              ],
                              rows: _buildTrendAnalysisRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _createPurchaseOrder,
                                  icon: const Icon(Icons.shopping_cart),
                                  label: const Text('إنشاء أمر شراء'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _transferFromBranch,
                                  icon: const Icon(Icons.swap_horiz),
                                  label: const Text('نقل من فرع'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _setupAlerts,
                                  icon: const Icon(Icons.notifications),
                                  label: const Text('إعداد تنبيهات'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _updateMinimumLevels,
                                  icon: const Icon(Icons.tune),
                                  label: const Text('تحديث الحدود'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<DataRow> _buildLowStockRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('001')),
        const DataCell(Text('ماوس لاسلكي')),
        const DataCell(Text('3')),
        const DataCell(Text('10')),
        const DataCell(Text('7')),
        const DataCell(Text('25')),
        const DataCell(Text('2024-01-05')),
        DataCell(_buildAlertBadge('حرج', Colors.red)),
        DataCell(IconButton(
          icon: const Icon(Icons.add_shopping_cart, color: Colors.green),
          onPressed: () => _addToOrder('001'),
        )),
      ]),
      DataRow(cells: [
        const DataCell(Text('002')),
        const DataCell(Text('كيبورد ميكانيكي')),
        const DataCell(Text('8')),
        const DataCell(Text('15')),
        const DataCell(Text('7')),
        const DataCell(Text('30')),
        const DataCell(Text('2024-01-08')),
        DataCell(_buildAlertBadge('منخفض', Colors.orange)),
        DataCell(IconButton(
          icon: const Icon(Icons.add_shopping_cart, color: Colors.green),
          onPressed: () => _addToOrder('002'),
        )),
      ]),
      DataRow(cells: [
        const DataCell(Text('003')),
        const DataCell(Text('سماعات بلوتوث')),
        const DataCell(Text('12')),
        const DataCell(Text('20')),
        const DataCell(Text('8')),
        const DataCell(Text('40')),
        const DataCell(Text('2024-01-10')),
        DataCell(_buildAlertBadge('تحذير', Colors.amber)),
        DataCell(IconButton(
          icon: const Icon(Icons.add_shopping_cart, color: Colors.green),
          onPressed: () => _addToOrder('003'),
        )),
      ]),
    ];
  }

  List<DataRow> _buildTrendAnalysisRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('ماوس لاسلكي')),
        const DataCell(Text('5 قطع')),
        DataCell(Row(
          children: [
            const Icon(Icons.trending_up, color: Colors.red, size: 16),
            const Text(' متزايد'),
          ],
        )),
        const DataCell(Text('نفاد خلال أسبوع')),
        DataCell(_buildRecommendationBadge('طلب فوري', Colors.red)),
      ]),
      DataRow(cells: [
        const DataCell(Text('كيبورد ميكانيكي')),
        const DataCell(Text('3 قطع')),
        DataCell(Row(
          children: [
            const Icon(Icons.trending_flat, color: Colors.blue, size: 16),
            const Text(' مستقر'),
          ],
        )),
        const DataCell(Text('نفاد خلال 3 أسابيع')),
        DataCell(_buildRecommendationBadge('طلب عادي', Colors.orange)),
      ]),
      DataRow(cells: [
        const DataCell(Text('سماعات بلوتوث')),
        const DataCell(Text('2 قطع')),
        DataCell(Row(
          children: [
            const Icon(Icons.trending_down, color: Colors.green, size: 16),
            const Text(' متناقص'),
          ],
        )),
        const DataCell(Text('نفاد خلال 6 أسابيع')),
        DataCell(_buildRecommendationBadge('مراقبة', Colors.green)),
      ]),
    ];
  }

  Widget _buildStatCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAlertBadge(String level, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(level, style: const TextStyle(color: Colors.white, fontSize: 12)),
    );
  }

  Widget _buildRecommendationBadge(String recommendation, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(recommendation, style: const TextStyle(color: Colors.white, fontSize: 12)),
    );
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء التقرير بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _createPurchaseOrder() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء أمر الشراء بنجاح')),
    );
  }

  void _transferFromBranch() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم طلب النقل من الفرع')),
    );
  }

  void _setupAlerts() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إعداد التنبيهات')),
    );
  }

  void _updateMinimumLevels() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تحديث الحدود الدنيا')),
    );
  }

  void _addToOrder(String itemCode) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم إضافة الصنف $itemCode لأمر الشراء')),
    );
  }
}
