import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة إعدادات خدمات الدفع (تابي وتمارا)
/// تتيح تكوين إعدادات خدمات الدفع الإلكتروني
class PaymentServicesSettingsPage extends StatefulWidget {
  const PaymentServicesSettingsPage({super.key});

  @override
  State<PaymentServicesSettingsPage> createState() =>
      _PaymentServicesSettingsPageState();
}

class _PaymentServicesSettingsPageState
    extends State<PaymentServicesSettingsPage> {
  // إعدادات تابي
  bool _tabbyEnabled = false;
  final _tabbyApiKeyController = TextEditingController();
  final _tabbyMerchantIdController = TextEditingController();

  // إعدادات تمارا
  bool _tamaraEnabled = false;
  final _tamaraApiKeyController = TextEditingController();
  final _tamaraMerchantIdController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.paymentServicesSettings),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveSettings,
            tooltip: 'حفظ الإعدادات',
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          // بطاقة حالة الخدمات
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'حالة خدمات الدفع',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.purple,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: _buildServiceStatus(
                          'تابي',
                          _tabbyEnabled,
                          Colors.blue,
                          Icons.credit_card,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildServiceStatus(
                          'تمارا',
                          _tamaraEnabled,
                          Colors.green,
                          Icons.payment,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // إعدادات تابي
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.credit_card, color: Colors.blue),
                      const SizedBox(width: 8),
                      const Text(
                        'إعدادات تابي (Tabby)',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  SwitchListTile(
                    title: const Text('تفعيل خدمة تابي'),
                    subtitle: const Text('السماح بالدفع عبر تابي'),
                    value: _tabbyEnabled,
                    onChanged: (value) {
                      setState(() {
                        _tabbyEnabled = value;
                      });
                    },
                  ),
                  if (_tabbyEnabled) ...[
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _tabbyApiKeyController,
                      decoration: const InputDecoration(
                        labelText: 'مفتاح API',
                        hintText: 'أدخل مفتاح API الخاص بتابي',
                        prefixIcon: Icon(Icons.key),
                        border: OutlineInputBorder(),
                      ),
                      obscureText: true,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _tabbyMerchantIdController,
                      decoration: const InputDecoration(
                        labelText: 'معرف التاجر',
                        hintText: 'أدخل معرف التاجر',
                        prefixIcon: Icon(Icons.store),
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // إعدادات تمارا
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.payment, color: Colors.green),
                      const SizedBox(width: 8),
                      const Text(
                        'إعدادات تمارا (Tamara)',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  SwitchListTile(
                    title: const Text('تفعيل خدمة تمارا'),
                    subtitle: const Text('السماح بالدفع عبر تمارا'),
                    value: _tamaraEnabled,
                    onChanged: (value) {
                      setState(() {
                        _tamaraEnabled = value;
                      });
                    },
                  ),
                  if (_tamaraEnabled) ...[
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _tamaraApiKeyController,
                      decoration: const InputDecoration(
                        labelText: 'مفتاح API',
                        hintText: 'أدخل مفتاح API الخاص بتمارا',
                        prefixIcon: Icon(Icons.key),
                        border: OutlineInputBorder(),
                      ),
                      obscureText: true,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _tamaraMerchantIdController,
                      decoration: const InputDecoration(
                        labelText: 'معرف التاجر',
                        hintText: 'أدخل معرف التاجر',
                        prefixIcon: Icon(Icons.store),
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // إعدادات عامة
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'الإعدادات العامة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ListTile(
                    leading: const Icon(Icons.wifi_tethering),
                    title: const Text('اختبار الاتصال'),
                    subtitle: const Text('اختبار الاتصال مع خدمات الدفع'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: _testConnection,
                  ),
                  ListTile(
                    leading: const Icon(Icons.history),
                    title: const Text('سجل المعاملات'),
                    subtitle: const Text('عرض سجل معاملات الدفع'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: _showTransactionHistory,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // أزرار الحفظ والإلغاء
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: _saveSettings,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text('حفظ الإعدادات'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: OutlinedButton(
                  onPressed: _resetSettings,
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text('إعادة تعيين'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildServiceStatus(
      String name, bool enabled, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            name,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: enabled ? Colors.green : Colors.red,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              enabled ? 'مفعل' : 'معطل',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _saveSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم حفظ إعدادات خدمات الدفع بنجاح'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _resetSettings() {
    setState(() {
      _tabbyEnabled = false;
      _tamaraEnabled = false;
      _tabbyApiKeyController.clear();
      _tabbyMerchantIdController.clear();
      _tamaraApiKeyController.clear();
      _tamaraMerchantIdController.clear();
    });
  }

  void _testConnection() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('اختبار الاتصال مع خدمات الدفع')),
    );
  }

  void _showTransactionHistory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض سجل معاملات الدفع')),
    );
  }

  @override
  void dispose() {
    _tabbyApiKeyController.dispose();
    _tabbyMerchantIdController.dispose();
    _tamaraApiKeyController.dispose();
    _tamaraMerchantIdController.dispose();
    super.dispose();
  }
}
