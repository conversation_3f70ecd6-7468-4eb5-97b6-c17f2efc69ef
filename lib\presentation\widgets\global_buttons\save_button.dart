import 'package:flutter/material.dart';

class SaveButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String? tooltip;
  final bool isLoading;
  final bool isDisabled;

  const SaveButton({
    super.key,
    this.onPressed,
    this.tooltip = 'حفظ',
    this.isLoading = false,
    this.isDisabled = false,
  });

  @override
  Widget build(BuildContext context) {
    // تحديد حجم الشاشة للتجاوب
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;
    final isMediumScreen = screenWidth < 900;

    return Tooltip(
      message: tooltip ?? 'حفظ',
      child: isSmallScreen
          ? _buildIconOnlyButton()
          : _buildFullButton(isMediumScreen),
    );
  }

  /// زر بأيقونة فقط للشاشات الصغيرة
  Widget _buildIconOnlyButton() {
    return ElevatedButton(
      onPressed: isDisabled || isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.all(12),
        minimumSize: const Size(48, 48),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        elevation: 2,
      ),
      child: isLoading
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: Colors.white,
              ),
            )
          : const Icon(Icons.save, size: 20, color: Colors.white),
    );
  }

  /// زر كامل مع نص وأيقونة للشاشات الكبيرة والمتوسطة
  Widget _buildFullButton(bool isMediumScreen) {
    return ElevatedButton.icon(
      onPressed: isDisabled || isLoading ? null : onPressed,
      icon: isLoading
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: Colors.white,
              ),
            )
          : const Icon(Icons.save, size: 20, color: Colors.white),
      label: Text(
        'حفظ',
        style: TextStyle(
          fontSize: isMediumScreen ? 14 : 16,
          fontWeight: FontWeight.w600,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(
          horizontal: isMediumScreen ? 12 : 16,
          vertical: isMediumScreen ? 8 : 12,
        ),
        minimumSize: Size(isMediumScreen ? 80 : 100, 40),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        elevation: 2,
      ),
    );
  }
}
