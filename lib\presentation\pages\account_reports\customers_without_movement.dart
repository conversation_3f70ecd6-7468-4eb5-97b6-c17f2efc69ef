import 'package:flutter/material.dart';

/// صفحة عملاء بدون حركة
/// تعرض العملاء الذين لم يقوموا بأي معاملات خلال فترة محددة
class CustomersWithoutMovementPage extends StatefulWidget {
  const CustomersWithoutMovementPage({super.key});

  @override
  State<CustomersWithoutMovementPage> createState() => _CustomersWithoutMovementPageState();
}

class _CustomersWithoutMovementPageState extends State<CustomersWithoutMovementPage> {
  String _selectedPeriod = 'last_3_months';
  String _selectedRegion = 'all';

  // بيانات تجريبية للعملاء بدون حركة
  final List<Map<String, dynamic>> _inactiveCustomers = [
    {
      'customerId': 'CUST001',
      'customerName': 'شركة الأمل للتجارة',
      'contactPerson': 'أحمد محمد',
      'phone': '0501234567',
      'email': '<EMAIL>',
      'region': 'الرياض',
      'lastTransactionDate': '2023-08-15',
      'lastTransactionAmount': 15000.0,
      'totalPurchases': 125000.0,
      'daysSinceLastTransaction': 158,
      'customerSince': '2020-01-15',
      'status': 'نشط',
      'creditLimit': 50000.0,
      'currentBalance': 8500.0,
      'category': 'عميل ذهبي',
    },
    {
      'customerId': 'CUST002',
      'customerName': 'مؤسسة النور التجارية',
      'contactPerson': 'فاطمة أحمد',
      'phone': '0509876543',
      'email': '<EMAIL>',
      'region': 'جدة',
      'lastTransactionDate': '2023-07-20',
      'lastTransactionAmount': 8500.0,
      'totalPurchases': 85000.0,
      'daysSinceLastTransaction': 184,
      'customerSince': '2019-05-10',
      'status': 'نشط',
      'creditLimit': 30000.0,
      'currentBalance': 12000.0,
      'category': 'عميل فضي',
    },
    {
      'customerId': 'CUST003',
      'customerName': 'شركة الفجر للمقاولات',
      'contactPerson': 'محمد علي',
      'phone': '0551122334',
      'email': '<EMAIL>',
      'region': 'الدمام',
      'lastTransactionDate': '2023-06-05',
      'lastTransactionAmount': 25000.0,
      'totalPurchases': 200000.0,
      'daysSinceLastTransaction': 229,
      'customerSince': '2018-03-20',
      'status': 'معلق',
      'creditLimit': 75000.0,
      'currentBalance': 5000.0,
      'category': 'عميل ذهبي',
    },
    {
      'customerId': 'CUST004',
      'customerName': 'مكتب الإبداع للاستشارات',
      'contactPerson': 'سارة سعد',
      'phone': '0567788990',
      'email': '<EMAIL>',
      'region': 'الرياض',
      'lastTransactionDate': '2023-04-10',
      'lastTransactionAmount': 3500.0,
      'totalPurchases': 45000.0,
      'daysSinceLastTransaction': 285,
      'customerSince': '2021-08-01',
      'status': 'نشط',
      'creditLimit': 20000.0,
      'currentBalance': 2500.0,
      'category': 'عميل برونزي',
    },
    {
      'customerId': 'CUST005',
      'customerName': 'شركة التقنية المتطورة',
      'contactPerson': 'خالد عبدالله',
      'phone': '0544556677',
      'email': '<EMAIL>',
      'region': 'مكة',
      'lastTransactionDate': '2023-05-15',
      'lastTransactionAmount': 18000.0,
      'totalPurchases': 95000.0,
      'daysSinceLastTransaction': 250,
      'customerSince': '2020-11-05',
      'status': 'نشط',
      'creditLimit': 40000.0,
      'currentBalance': 15000.0,
      'category': 'عميل فضي',
    },
  ];

  @override
  Widget build(BuildContext context) {
    final filteredCustomers = _getFilteredCustomers();
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('عملاء بدون حركة'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedPeriod,
                        decoration: const InputDecoration(
                          labelText: 'فترة عدم النشاط',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'last_month', child: Text('آخر شهر')),
                          DropdownMenuItem(value: 'last_3_months', child: Text('آخر 3 أشهر')),
                          DropdownMenuItem(value: 'last_6_months', child: Text('آخر 6 أشهر')),
                          DropdownMenuItem(value: 'last_year', child: Text('آخر سنة')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedPeriod = value!;
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedRegion,
                        decoration: const InputDecoration(
                          labelText: 'المنطقة',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع المناطق')),
                          DropdownMenuItem(value: 'الرياض', child: Text('الرياض')),
                          DropdownMenuItem(value: 'جدة', child: Text('جدة')),
                          DropdownMenuItem(value: 'الدمام', child: Text('الدمام')),
                          DropdownMenuItem(value: 'مكة', child: Text('مكة')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedRegion = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // ملخص العملاء غير النشطين
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              color: Colors.deepPurple[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    const Text(
                      'ملخص العملاء بدون حركة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Column(
                          children: [
                            const Text('عدد العملاء'),
                            Text(
                              filteredCustomers.length.toString(),
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.red,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('إجمالي الأرصدة'),
                            Text(
                              '${_getTotalBalance(filteredCustomers)} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('متوسط عدم النشاط'),
                            Text(
                              '${_getAverageInactiveDays(filteredCustomers)} يوم',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.orange,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                _buildStatCard('العملاء الذهبيين', _getGoldCustomersCount(filteredCustomers).toString(), Colors.amber),
                _buildStatCard('العملاء الفضيين', _getSilverCustomersCount(filteredCustomers).toString(), Colors.grey),
                _buildStatCard('أطول فترة', '${_getLongestInactivePeriod(filteredCustomers)} يوم', Colors.red),
                _buildStatCard('إجمالي المشتريات', '${_getTotalPurchases(filteredCustomers)} ر.س', Colors.green),
              ],
            ),
          ),

          // قائمة العملاء
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: filteredCustomers.length,
              itemBuilder: (context, index) {
                final customer = filteredCustomers[index];
                
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: _getCategoryColor(customer['category']),
                      child: Text(
                        customer['customerName'].substring(0, 2),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    title: Text(
                      '${customer['customerId']} - ${customer['customerName']}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('جهة الاتصال: ${customer['contactPerson']}'),
                        Text('آخر معاملة: ${customer['lastTransactionDate']}'),
                        Text('عدم النشاط: ${customer['daysSinceLastTransaction']} يوم'),
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: _getCategoryColor(customer['category']),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            customer['category'],
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('الهاتف', customer['phone'], Icons.phone),
                                ),
                                Expanded(
                                  child: _buildDetailCard('البريد الإلكتروني', customer['email'], Icons.email),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('المنطقة', customer['region'], Icons.location_on),
                                ),
                                Expanded(
                                  child: _buildDetailCard('عميل منذ', customer['customerSince'], Icons.calendar_today),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('إجمالي المشتريات', '${customer['totalPurchases'].toStringAsFixed(2)} ر.س', Icons.shopping_cart),
                                ),
                                Expanded(
                                  child: _buildDetailCard('آخر معاملة', '${customer['lastTransactionAmount'].toStringAsFixed(2)} ر.س', Icons.receipt),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('الحد الائتماني', '${customer['creditLimit'].toStringAsFixed(2)} ر.س', Icons.credit_card),
                                ),
                                Expanded(
                                  child: _buildDetailCard('الرصيد الحالي', '${customer['currentBalance'].toStringAsFixed(2)} ر.س', Icons.account_balance),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Row(
                              children: [
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () => _contactCustomer(customer),
                                    icon: const Icon(Icons.phone),
                                    label: const Text('اتصال'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.green,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () => _sendEmail(customer),
                                    icon: const Icon(Icons.email),
                                    label: const Text('إرسال بريد'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.blue,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () => _viewCustomerHistory(customer),
                                    icon: const Icon(Icons.history),
                                    label: const Text('التاريخ'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.orange,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshData,
        backgroundColor: Colors.deepPurple,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث البيانات'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailCard(String title, String value, IconData icon) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Icon(icon, size: 20, color: Colors.grey[600]),
            const SizedBox(height: 4),
            Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
            Text(
              title,
              style: const TextStyle(fontSize: 10),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'عميل ذهبي':
        return Colors.amber;
      case 'عميل فضي':
        return Colors.grey;
      case 'عميل برونزي':
        return Colors.brown;
      default:
        return Colors.blue;
    }
  }

  List<Map<String, dynamic>> _getFilteredCustomers() {
    int periodDays = _getPeriodDays();
    
    return _inactiveCustomers.where((customer) {
      bool periodMatch = customer['daysSinceLastTransaction'] >= periodDays;
      bool regionMatch = _selectedRegion == 'all' || customer['region'] == _selectedRegion;
      return periodMatch && regionMatch;
    }).toList();
  }

  int _getPeriodDays() {
    switch (_selectedPeriod) {
      case 'last_month':
        return 30;
      case 'last_3_months':
        return 90;
      case 'last_6_months':
        return 180;
      case 'last_year':
        return 365;
      default:
        return 90;
    }
  }

  double _getTotalBalance(List<Map<String, dynamic>> customers) {
    return customers.fold(0.0, (sum, customer) => sum + customer['currentBalance']);
  }

  int _getAverageInactiveDays(List<Map<String, dynamic>> customers) {
    if (customers.isEmpty) return 0;
    int total = customers.fold(0, (sum, customer) => sum + customer['daysSinceLastTransaction'] as int);
    return (total / customers.length).round();
  }

  int _getGoldCustomersCount(List<Map<String, dynamic>> customers) {
    return customers.where((customer) => customer['category'] == 'عميل ذهبي').length;
  }

  int _getSilverCustomersCount(List<Map<String, dynamic>> customers) {
    return customers.where((customer) => customer['category'] == 'عميل فضي').length;
  }

  int _getLongestInactivePeriod(List<Map<String, dynamic>> customers) {
    if (customers.isEmpty) return 0;
    return customers.map((customer) => customer['daysSinceLastTransaction'] as int).reduce((a, b) => a > b ? a : b);
  }

  double _getTotalPurchases(List<Map<String, dynamic>> customers) {
    return customers.fold(0.0, (sum, customer) => sum + customer['totalPurchases']);
  }

  void _contactCustomer(Map<String, dynamic> customer) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('الاتصال بـ ${customer['customerName']}')),
    );
  }

  void _sendEmail(Map<String, dynamic> customer) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('إرسال بريد إلى ${customer['customerName']}')),
    );
  }

  void _viewCustomerHistory(Map<String, dynamic> customer) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض تاريخ ${customer['customerName']}')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة تقرير العملاء بدون حركة')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير تقرير العملاء بدون حركة')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث بيانات العملاء بدون حركة'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
