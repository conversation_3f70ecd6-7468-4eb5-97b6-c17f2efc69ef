import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير مبيعات سند حجز
/// يعرض المبيعات المرتبطة بسندات الحجز
class ReservationVoucherSalesReportPage extends StatefulWidget {
  const ReservationVoucherSalesReportPage({super.key});

  @override
  State<ReservationVoucherSalesReportPage> createState() =>
      _ReservationVoucherSalesReportPageState();
}

class _ReservationVoucherSalesReportPageState
    extends State<ReservationVoucherSalesReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedCategory;
  String? _voucherStatus = 'all';
  String? _selectedEmployee;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('مبيعات سند حجز'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.receipt),
            onPressed: _showVoucherDetails,
            tooltip: 'تفاصيل السندات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          _buildFilterSection(localizations),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildVoucherStatusSection(),
                  const SizedBox(height: 16),
                  _buildSalesTableSection(),
                  const SizedBox(height: 16),
                  _buildEmployeePerformanceSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.deepPurple[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.fromDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, true),
                  controller: TextEditingController(
                    text: _startDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.toDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, false),
                  controller: TextEditingController(
                    text: _endDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: localizations.category,
                    border: const OutlineInputBorder(),
                  ),
                  value: _selectedCategory,
                  items: [
                    DropdownMenuItem(
                        value: 'all', child: Text(localizations.allCategories)),
                    DropdownMenuItem(
                        value: 'electronics',
                        child: Text(localizations.electronics)),
                    DropdownMenuItem(
                        value: 'clothing', child: Text(localizations.clothing)),
                    DropdownMenuItem(
                        value: 'food', child: Text(localizations.food)),
                  ],
                  onChanged: (value) =>
                      setState(() => _selectedCategory = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'حالة السند',
                    border: OutlineInputBorder(),
                  ),
                  value: _voucherStatus,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الحالات')),
                    DropdownMenuItem(value: 'active', child: Text('نشط')),
                    DropdownMenuItem(value: 'completed', child: Text('مكتمل')),
                    DropdownMenuItem(value: 'cancelled', child: Text('ملغي')),
                    DropdownMenuItem(value: 'partial', child: Text('جزئي')),
                  ],
                  onChanged: (value) => setState(() => _voucherStatus = value),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'الموظف',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedEmployee,
                  items: const [
                    DropdownMenuItem(
                        value: 'all', child: Text('جميع الموظفين')),
                    DropdownMenuItem(value: 'emp1', child: Text('أحمد محمد')),
                    DropdownMenuItem(value: 'emp2', child: Text('فاطمة علي')),
                    DropdownMenuItem(value: 'emp3', child: Text('محمد سالم')),
                  ],
                  onChanged: (value) =>
                      setState(() => _selectedEmployee = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.analytics),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.deepPurple,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.receipt_long,
                    color: Colors.deepPurple, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص مبيعات سندات الحجز',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard(
                    'إجمالي السندات', '185', Colors.deepPurple, Icons.receipt),
                _buildSummaryCard('قيمة المبيعات', '750,000 ر.س', Colors.green,
                    Icons.monetization_on),
                _buildSummaryCard(
                    'سندات مكتملة', '125', Colors.blue, Icons.check_circle),
                _buildSummaryCard(
                    'سندات نشطة', '60', Colors.orange, Icons.pending),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVoucherStatusSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.pie_chart, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'توزيع حالات السندات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildStatusCard('مكتملة', '125 سند', Colors.green),
                _buildStatusCard('نشطة', '60 سند', Colors.blue),
                _buildStatusCard('جزئية', '35 سند', Colors.orange),
                _buildStatusCard('ملغية', '15 سند', Colors.red),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalesTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل مبيعات سندات الحجز',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('رقم السند')),
                  DataColumn(label: Text('اسم الصنف')),
                  DataColumn(label: Text('العميل')),
                  DataColumn(label: Text('الكمية المحجوزة')),
                  DataColumn(label: Text('الكمية المباعة')),
                  DataColumn(label: Text('قيمة المبيعات')),
                  DataColumn(label: Text('تاريخ السند')),
                  DataColumn(label: Text('تاريخ البيع')),
                  DataColumn(label: Text('الموظف')),
                  DataColumn(label: Text('الحالة')),
                ],
                rows: _buildSalesRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmployeePerformanceSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.person, color: Colors.teal, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'أداء الموظفين في سندات الحجز',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildEmployeePerformanceList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _createNewVoucher,
                    icon: const Icon(Icons.add),
                    label: const Text('إنشاء سند جديد'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _processVouchers,
                    icon: const Icon(Icons.settings),
                    label: const Text('معالجة السندات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _cancelExpiredVouchers,
                    icon: const Icon(Icons.cancel),
                    label: const Text('إلغاء المنتهية'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _generateAnalytics,
                    icon: const Icon(Icons.analytics),
                    label: const Text('تحليل مفصل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildEmployeePerformanceList() {
    final employees = [
      {
        'name': 'أحمد محمد',
        'vouchers': '45 سند',
        'sales': '285,000 ر.س',
        'completion': '92%'
      },
      {
        'name': 'فاطمة علي',
        'vouchers': '38 سند',
        'sales': '225,000 ر.س',
        'completion': '88%'
      },
      {
        'name': 'محمد سالم',
        'vouchers': '32 سند',
        'sales': '185,000 ر.س',
        'completion': '85%'
      },
      {
        'name': 'سارة أحمد',
        'vouchers': '28 سند',
        'sales': '155,000 ر.س',
        'completion': '82%'
      },
    ];

    return employees
        .map((emp) => Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(
                    color: _getCompletionColor(emp['completion']!)
                        .withValues(alpha: 0.3)),
                borderRadius: BorderRadius.circular(8),
                color: _getCompletionColor(emp['completion']!)
                    .withValues(alpha: 0.05),
              ),
              child: Row(
                children: [
                  const Icon(Icons.person, color: Colors.teal, size: 24),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          emp['name']!,
                          style: const TextStyle(
                              fontSize: 14, fontWeight: FontWeight.bold),
                        ),
                        Text(
                          '${emp['vouchers']} • المبيعات: ${emp['sales']}',
                          style:
                              TextStyle(fontSize: 12, color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getCompletionColor(emp['completion']!)
                          .withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      emp['completion']!,
                      style: TextStyle(
                        color: _getCompletionColor(emp['completion']!),
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ))
        .toList();
  }

  List<DataRow> _buildSalesRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('RV001')),
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('أحمد محمد')),
        const DataCell(Text('5')),
        const DataCell(Text('5')),
        const DataCell(Text('22,500 ر.س')),
        const DataCell(Text('2024-01-10')),
        const DataCell(Text('2024-01-15')),
        const DataCell(Text('فاطمة علي')),
        DataCell(_buildStatusBadge('مكتمل', Colors.green)),
      ]),
      DataRow(cells: [
        const DataCell(Text('RV002')),
        const DataCell(Text('هاتف آيفون 15')),
        const DataCell(Text('محمد سالم')),
        const DataCell(Text('3')),
        const DataCell(Text('2')),
        const DataCell(Text('7,600 ر.س')),
        const DataCell(Text('2024-01-12')),
        const DataCell(Text('2024-01-18')),
        const DataCell(Text('أحمد محمد')),
        DataCell(_buildStatusBadge('جزئي', Colors.orange)),
      ]),
    ];
  }

  Widget _buildSummaryCard(
      String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                    fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title,
                  style: const TextStyle(fontSize: 12),
                  textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusCard(String status, String count, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(
                    fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(status,
                  style: const TextStyle(fontSize: 12),
                  textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(status,
          style: TextStyle(
              color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Color _getCompletionColor(String completion) {
    final percentage = int.tryParse(completion.replaceAll('%', '')) ?? 0;
    if (percentage >= 90) return Colors.green;
    if (percentage >= 80) return Colors.blue;
    if (percentage >= 70) return Colors.orange;
    return Colors.red;
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير مبيعات سندات الحجز بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showVoucherDetails() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تفاصيل السندات')),
    );
  }

  void _createNewVoucher() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء سند حجز جديد')),
    );
  }

  void _processVouchers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('معالجة السندات المعلقة')),
    );
  }

  void _cancelExpiredVouchers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إلغاء السندات المنتهية الصلاحية')),
    );
  }

  void _generateAnalytics() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء تحليل مفصل للسندات')),
    );
  }
}
