import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

class VatSettings extends StatelessWidget {
  const VatSettings({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.vatSettings),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Text(
          localizations.vatSettings,
          style: const TextStyle(fontSize: 24),
        ),
      ),
    );
  }
}
