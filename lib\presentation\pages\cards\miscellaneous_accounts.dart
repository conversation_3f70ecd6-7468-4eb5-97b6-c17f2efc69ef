import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة الحسابات المتنوعة
/// تتيح إدارة الحسابات المختلفة في النظام
class MiscellaneousAccountsPage extends StatefulWidget {
  const MiscellaneousAccountsPage({super.key});

  @override
  State<MiscellaneousAccountsPage> createState() => _MiscellaneousAccountsPageState();
}

class _MiscellaneousAccountsPageState extends State<MiscellaneousAccountsPage> {
  String _searchQuery = '';
  String _selectedType = 'all';

  // بيانات تجريبية للحسابات المتنوعة
  final List<Map<String, dynamic>> _accounts = [
    {
      'id': 'ACC001',
      'name': 'حساب المصروفات العامة',
      'code': '5001',
      'type': 'مصروفات',
      'balance': -15000.0,
      'isActive': true,
      'description': 'حساب للمصروفات العامة للشركة',
      'parentAccount': 'المصروفات',
    },
    {
      'id': 'ACC002',
      'name': 'حساب الإيرادات الأخرى',
      'code': '4001',
      'type': 'إيرادات',
      'balance': 25000.0,
      'isActive': true,
      'description': 'حساب للإيرادات غير التشغيلية',
      'parentAccount': 'الإيرادات',
    },
    {
      'id': 'ACC003',
      'name': 'حساب الأصول الثابتة',
      'code': '1500',
      'type': 'أصول',
      'balance': 150000.0,
      'isActive': true,
      'description': 'حساب الأصول الثابتة والمعدات',
      'parentAccount': 'الأصول',
    },
    {
      'id': 'ACC004',
      'name': 'حساب الخصومات المتداولة',
      'code': '2100',
      'type': 'خصوم',
      'balance': -45000.0,
      'isActive': false,
      'description': 'حساب الخصومات قصيرة المدى',
      'parentAccount': 'الخصوم',
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.miscellaneousAccounts),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addAccount,
            tooltip: 'إضافة حساب جديد',
          ),
          IconButton(
            icon: const Icon(Icons.import_export),
            onPressed: _importExportAccounts,
            tooltip: 'استيراد/تصدير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                TextField(
                  decoration: const InputDecoration(
                    hintText: 'البحث في الحسابات...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedType,
                        decoration: const InputDecoration(
                          labelText: 'نوع الحساب',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الأنواع')),
                          DropdownMenuItem(value: 'أصول', child: Text('الأصول')),
                          DropdownMenuItem(value: 'خصوم', child: Text('الخصوم')),
                          DropdownMenuItem(value: 'إيرادات', child: Text('الإيرادات')),
                          DropdownMenuItem(value: 'مصروفات', child: Text('المصروفات')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedType = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                _buildStatCard('المجموع', _accounts.length.toString(), Colors.blue),
                _buildStatCard('النشطة', _accounts.where((a) => a['isActive']).length.toString(), Colors.green),
                _buildStatCard('غير النشطة', _accounts.where((a) => !a['isActive']).length.toString(), Colors.orange),
                _buildStatCard('الرصيد الإجمالي', _getTotalBalance(), Colors.purple),
              ],
            ),
          ),

          // قائمة الحسابات
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _accounts.length,
              itemBuilder: (context, index) {
                final account = _accounts[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: _getTypeColor(account['type']),
                      child: Icon(
                        _getTypeIcon(account['type']),
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    title: Text(
                      account['name'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('الكود: ${account['code']} | النوع: ${account['type']}'),
                        Text(
                          'الرصيد: ${account['balance']} ر.س',
                          style: TextStyle(
                            color: account['balance'] >= 0 ? Colors.green : Colors.red,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          account['isActive'] ? Icons.check_circle : Icons.cancel,
                          color: account['isActive'] ? Colors.green : Colors.red,
                        ),
                        PopupMenuButton<String>(
                          onSelected: (value) => _handleAction(value, account),
                          itemBuilder: (context) => [
                            const PopupMenuItem(
                              value: 'edit',
                              child: ListTile(
                                leading: Icon(Icons.edit),
                                title: Text('تعديل'),
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'toggle',
                              child: ListTile(
                                leading: Icon(Icons.toggle_on),
                                title: Text('تغيير الحالة'),
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'statement',
                              child: ListTile(
                                leading: Icon(Icons.account_balance),
                                title: Text('كشف الحساب'),
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'delete',
                              child: ListTile(
                                leading: Icon(Icons.delete, color: Colors.red),
                                title: Text('حذف', style: TextStyle(color: Colors.red)),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('الوصف: ${account['description']}'),
                            const SizedBox(height: 8),
                            Text('الحساب الأب: ${account['parentAccount']}'),
                            const SizedBox(height: 8),
                            Text('الحالة: ${account['isActive'] ? 'نشط' : 'غير نشط'}'),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addAccount,
        backgroundColor: Colors.indigo,
        icon: const Icon(Icons.add),
        label: const Text('إضافة حساب'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getTypeColor(String type) {
    switch (type) {
      case 'أصول':
        return Colors.green;
      case 'خصوم':
        return Colors.red;
      case 'إيرادات':
        return Colors.blue;
      case 'مصروفات':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getTypeIcon(String type) {
    switch (type) {
      case 'أصول':
        return Icons.trending_up;
      case 'خصوم':
        return Icons.trending_down;
      case 'إيرادات':
        return Icons.attach_money;
      case 'مصروفات':
        return Icons.money_off;
      default:
        return Icons.account_balance;
    }
  }

  String _getTotalBalance() {
    double total = _accounts.fold(0.0, (sum, account) => sum + account['balance']);
    return total.toStringAsFixed(2);
  }

  void _addAccount() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إضافة حساب جديد')),
    );
  }

  void _importExportAccounts() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('استيراد/تصدير الحسابات')),
    );
  }

  void _handleAction(String action, Map<String, dynamic> account) {
    switch (action) {
      case 'edit':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تعديل الحساب ${account['name']}')),
        );
        break;
      case 'toggle':
        setState(() {
          account['isActive'] = !account['isActive'];
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم ${account['isActive'] ? 'تفعيل' : 'إلغاء تفعيل'} الحساب'),
          ),
        );
        break;
      case 'statement':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('كشف حساب ${account['name']}')),
        );
        break;
      case 'delete':
        _showDeleteConfirmation(account);
        break;
    }
  }

  void _showDeleteConfirmation(Map<String, dynamic> account) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الحساب ${account['name']}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _accounts.removeWhere((a) => a['id'] == account['id']);
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف الحساب ${account['name']}'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
