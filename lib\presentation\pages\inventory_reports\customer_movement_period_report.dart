import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير حركة العملاء خلال فترة
/// يعرض تفاصيل حركة العملاء والمعاملات معهم
class CustomerMovementPeriodReportPage extends StatefulWidget {
  const CustomerMovementPeriodReportPage({super.key});

  @override
  State<CustomerMovementPeriodReportPage> createState() => _CustomerMovementPeriodReportPageState();
}

class _CustomerMovementPeriodReportPageState extends State<CustomerMovementPeriodReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedCustomer;
  String? _movementType = 'all';
  final String _customerType = 'all';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('حركة العملاء خلال فترة'),
        backgroundColor: Colors.cyan,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.people),
            onPressed: _showCustomerAnalysis,
            tooltip: 'تحليل العملاء',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilterSection(localizations),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildMovementTypesSection(),
                  const SizedBox(height: 16),
                  _buildCustomerMovementTableSection(),
                  const SizedBox(height: 16),
                  _buildTopCustomersSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.cyan[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.fromDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, true),
                  controller: TextEditingController(
                    text: _startDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.toDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, false),
                  controller: TextEditingController(
                    text: _endDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'العميل',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedCustomer,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع العملاء')),
                    DropdownMenuItem(value: 'customer1', child: Text('أحمد محمد')),
                    DropdownMenuItem(value: 'customer2', child: Text('فاطمة علي')),
                    DropdownMenuItem(value: 'customer3', child: Text('محمد سالم')),
                    DropdownMenuItem(value: 'customer4', child: Text('نورا أحمد')),
                  ],
                  onChanged: (value) => setState(() => _selectedCustomer = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'نوع الحركة',
                    border: OutlineInputBorder(),
                  ),
                  value: _movementType,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الحركات')),
                    DropdownMenuItem(value: 'sales', child: Text('مبيعات')),
                    DropdownMenuItem(value: 'payments', child: Text('مدفوعات')),
                    DropdownMenuItem(value: 'returns', child: Text('مرتجعات')),
                    DropdownMenuItem(value: 'credit', child: Text('ائتمان')),
                  ],
                  onChanged: (value) => setState(() => _movementType = value),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.people, color: Colors.cyan, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص حركة العملاء',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي العملاء', '285', Colors.cyan, Icons.people),
                _buildSummaryCard('إجمالي المعاملات', '1,485', Colors.blue, Icons.receipt),
                _buildSummaryCard('إجمالي المبيعات', '2,850,000 ر.س', Colors.green, Icons.monetization_on),
                _buildSummaryCard('متوسط المعاملة', '1,920 ر.س', Colors.orange, Icons.calculate),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMovementTypesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.pie_chart, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'توزيع أنواع الحركات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildMovementCard('مبيعات', '985 حركة', Colors.green),
                _buildMovementCard('مدفوعات', '325 حركة', Colors.blue),
                _buildMovementCard('مرتجعات', '125 حركة', Colors.orange),
                _buildMovementCard('ائتمان', '50 حركة', Colors.purple),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerMovementTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل حركة العملاء',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('التاريخ')),
                  DataColumn(label: Text('اسم العميل')),
                  DataColumn(label: Text('نوع الحركة')),
                  DataColumn(label: Text('رقم المستند')),
                  DataColumn(label: Text('المبلغ')),
                  DataColumn(label: Text('الرصيد')),
                  DataColumn(label: Text('طريقة الدفع')),
                  DataColumn(label: Text('الحالة')),
                  DataColumn(label: Text('ملاحظات')),
                ],
                rows: _buildMovementRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopCustomersSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.star, color: Colors.amber, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'أهم العملاء',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildTopCustomersList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _sendStatements,
                    icon: const Icon(Icons.email),
                    label: const Text('إرسال كشوف حساب'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _followUpPayments,
                    icon: const Icon(Icons.payment),
                    label: const Text('متابعة المدفوعات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _analyzeCustomerBehavior,
                    icon: const Icon(Icons.psychology),
                    label: const Text('تحليل سلوك العملاء'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _createLoyaltyProgram,
                    icon: const Icon(Icons.card_giftcard),
                    label: const Text('برنامج ولاء'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildTopCustomersList() {
    final topCustomers = [
      {'name': 'أحمد محمد', 'transactions': '125 معاملة', 'amount': '485,000 ر.س', 'balance': '25,000 ر.س'},
      {'name': 'فاطمة علي', 'transactions': '95 معاملة', 'amount': '385,000 ر.س', 'balance': '15,000 ر.س'},
      {'name': 'محمد سالم', 'transactions': '85 معاملة', 'amount': '325,000 ر.س', 'balance': '8,000 ر.س'},
      {'name': 'نورا أحمد', 'transactions': '75 معاملة', 'amount': '285,000 ر.س', 'balance': '12,000 ر.س'},
    ];

    return topCustomers.map((customer) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.amber.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.amber.withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.star, color: Colors.amber, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  customer['name']!,
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  '${customer['transactions']} • مبلغ: ${customer['amount']} • رصيد: ${customer['balance']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildMovementRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('2024-01-15')),
        const DataCell(Text('أحمد محمد')),
        DataCell(_buildMovementTypeBadge('مبيعات', Colors.green)),
        const DataCell(Text('INV-001')),
        const DataCell(Text('25,000 ر.س')),
        const DataCell(Text('125,000 ر.س')),
        const DataCell(Text('نقداً')),
        DataCell(_buildStatusBadge('مكتمل', Colors.green)),
        const DataCell(Text('فاتورة عادية')),
      ]),
      DataRow(cells: [
        const DataCell(Text('2024-01-18')),
        const DataCell(Text('فاطمة علي')),
        DataCell(_buildMovementTypeBadge('مدفوعات', Colors.blue)),
        const DataCell(Text('PAY-002')),
        const DataCell(Text('15,000 ر.س')),
        const DataCell(Text('85,000 ر.س')),
        const DataCell(Text('تحويل بنكي')),
        DataCell(_buildStatusBadge('مكتمل', Colors.green)),
        const DataCell(Text('دفعة جزئية')),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMovementCard(String type, String count, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(type, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMovementTypeBadge(String type, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(type, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(status, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showCustomerAnalysis() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تحليل العملاء المتقدم')),
    );
  }

  void _sendStatements() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إرسال كشوف حساب للعملاء')),
    );
  }

  void _followUpPayments() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('متابعة المدفوعات المستحقة')),
    );
  }

  void _analyzeCustomerBehavior() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحليل سلوك العملاء الشرائي')),
    );
  }

  void _createLoyaltyProgram() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء برنامج ولاء للعملاء')),
    );
  }
}
