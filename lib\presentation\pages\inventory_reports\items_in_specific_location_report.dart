import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير بأصناف في موقع معين
/// تعرض الأصناف الموجودة في موقع أو مستودع معين
class ItemsInSpecificLocationReportPage extends StatefulWidget {
  const ItemsInSpecificLocationReportPage({super.key});

  @override
  State<ItemsInSpecificLocationReportPage> createState() =>
      _ItemsInSpecificLocationReportPageState();
}

class _ItemsInSpecificLocationReportPageState
    extends State<ItemsInSpecificLocationReportPage> {
  String? _selectedWarehouse;
  String? _selectedLocation;
  String? _selectedCategory;
  bool _showZeroQuantity = false;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.itemsInSpecificLocation),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.warehouse,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedWarehouse,
                        items: const [
                          DropdownMenuItem(
                              value: 'main', child: Text('المستودع الرئيسي')),
                          DropdownMenuItem(
                              value: 'branch1',
                              child: Text('مستودع الفرع الأول')),
                          DropdownMenuItem(
                              value: 'branch2',
                              child: Text('مستودع الفرع الثاني')),
                          DropdownMenuItem(
                              value: 'warehouse3',
                              child: Text('مستودع الجملة')),
                        ],
                        onChanged: (value) =>
                            setState(() => _selectedWarehouse = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.location,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedLocation,
                        items: const [
                          DropdownMenuItem(value: 'A1', child: Text('الرف A1')),
                          DropdownMenuItem(value: 'A2', child: Text('الرف A2')),
                          DropdownMenuItem(value: 'B1', child: Text('الرف B1')),
                          DropdownMenuItem(value: 'B2', child: Text('الرف B2')),
                          DropdownMenuItem(value: 'C1', child: Text('الرف C1')),
                        ],
                        onChanged: (value) =>
                            setState(() => _selectedLocation = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.category,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedCategory,
                        items: const [
                          DropdownMenuItem(
                              value: 'all', child: Text('جميع التصنيفات')),
                          DropdownMenuItem(
                              value: 'electronics', child: Text('إلكترونيات')),
                          DropdownMenuItem(
                              value: 'clothing', child: Text('ملابس')),
                          DropdownMenuItem(
                              value: 'food', child: Text('مواد غذائية')),
                        ],
                        onChanged: (value) =>
                            setState(() => _selectedCategory = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: CheckboxListTile(
                        title: const Text('إظهار الكميات الصفرية'),
                        value: _showZeroQuantity,
                        onChanged: (value) =>
                            setState(() => _showZeroQuantity = value!),
                        controlAffinity: ListTileControlAffinity.leading,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.search),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.teal,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات الموقع
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'معلومات الموقع',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                        'المستودع: ${_selectedWarehouse ?? 'غير محدد'}'),
                                    Text(
                                        'الموقع: ${_selectedLocation ?? 'غير محدد'}'),
                                  ],
                                ),
                              ),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                        'التصنيف: ${_selectedCategory ?? 'جميع التصنيفات'}'),
                                    Text(
                                        'تاريخ التقرير: ${DateTime.now().toString().split(' ')[0]}'),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إحصائيات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إحصائيات الموقع',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildStatCard(
                                  'إجمالي الأصناف', '45', Colors.blue),
                              _buildStatCard(
                                  'الأصناف المتوفرة', '38', Colors.green),
                              _buildStatCard(
                                  'الأصناف المنتهية', '7', Colors.red),
                              _buildStatCard('إجمالي القيمة', '125,750 ر.س',
                                  Colors.purple),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول الأصناف
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'الأصناف في الموقع',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: [
                                DataColumn(label: Text(localizations.itemCode)),
                                DataColumn(label: Text(localizations.itemName)),
                                DataColumn(label: Text(localizations.category)),
                                DataColumn(label: Text(localizations.unit)),
                                DataColumn(
                                    label:
                                        Text(localizations.availableQuantity)),
                                DataColumn(
                                    label: Text(localizations.minimumLimit)),
                                DataColumn(
                                    label: Text(localizations.costPrice)),
                                DataColumn(
                                    label: Text(localizations.sellingPrice)),
                                DataColumn(label: Text(localizations.value)),
                                DataColumn(label: Text(localizations.status)),
                              ],
                              rows: _buildItemRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<DataRow> _buildItemRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('001')),
        const DataCell(Text('جهاز كمبيوتر محمول')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('قطعة')),
        const DataCell(Text('12')),
        const DataCell(Text('5')),
        const DataCell(Text('2,500.00')),
        const DataCell(Text('3,200.00')),
        const DataCell(Text('38,400.00')),
        DataCell(Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.green,
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Text('متوفر',
              style: TextStyle(color: Colors.white, fontSize: 12)),
        )),
      ]),
      DataRow(cells: [
        const DataCell(Text('002')),
        const DataCell(Text('طابعة ليزر')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('قطعة')),
        const DataCell(Text('8')),
        const DataCell(Text('3')),
        const DataCell(Text('800.00')),
        const DataCell(Text('1,200.00')),
        const DataCell(Text('9,600.00')),
        DataCell(Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.green,
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Text('متوفر',
              style: TextStyle(color: Colors.white, fontSize: 12)),
        )),
      ]),
      DataRow(cells: [
        const DataCell(Text('003')),
        const DataCell(Text('ماوس لاسلكي')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('قطعة')),
        const DataCell(Text('2')),
        const DataCell(Text('10')),
        const DataCell(Text('45.00')),
        const DataCell(Text('75.00')),
        const DataCell(Text('150.00')),
        DataCell(Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.orange,
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Text('منخفض',
              style: TextStyle(color: Colors.white, fontSize: 12)),
        )),
      ]),
    ];
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء التقرير بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }
}
