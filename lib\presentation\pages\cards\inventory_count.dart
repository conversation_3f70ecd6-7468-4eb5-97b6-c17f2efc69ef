import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة جرد المخزون
/// تتيح إدارة عمليات جرد المخزون والتحكم فيها
class InventoryCountPage extends StatefulWidget {
  const InventoryCountPage({super.key});

  @override
  State<InventoryCountPage> createState() => _InventoryCountPageState();
}

class _InventoryCountPageState extends State<InventoryCountPage> {
  String _searchQuery = '';
  String _selectedStatus = 'all';

  // بيانات تجريبية لعمليات الجرد
  final List<Map<String, dynamic>> _inventoryCounts = [
    {
      'id': 'INV001',
      'name': 'جرد المستودع الرئيسي - يناير 2024',
      'warehouse': 'المستودع الرئيسي',
      'startDate': '2024-01-15',
      'endDate': '2024-01-20',
      'status': 'مكتمل',
      'itemsCount': 1250,
      'countedItems': 1250,
      'discrepancies': 15,
      'supervisor': 'أحمد محمد',
      'team': ['فاطمة أحمد', 'محمد علي', 'سارة خالد'],
      'notes': 'جرد دوري شهري للمستودع الرئيسي',
    },
    {
      'id': 'INV002',
      'name': 'جرد مستودع التبريد - فبراير 2024',
      'warehouse': 'مستودع التبريد',
      'startDate': '2024-02-01',
      'endDate': '2024-02-05',
      'status': 'جاري',
      'itemsCount': 850,
      'countedItems': 620,
      'discrepancies': 8,
      'supervisor': 'فاطمة أحمد',
      'team': ['محمد علي', 'نورا سعد'],
      'notes': 'جرد المواد المبردة والمجمدة',
    },
    {
      'id': 'INV003',
      'name': 'جرد الفرع الشرقي - مارس 2024',
      'warehouse': 'مستودع الفرع الشرقي',
      'startDate': '2024-03-10',
      'endDate': null,
      'status': 'مجدول',
      'itemsCount': 650,
      'countedItems': 0,
      'discrepancies': 0,
      'supervisor': 'محمد عبدالله',
      'team': ['علي أحمد', 'هند محمد'],
      'notes': 'جرد ربع سنوي للفرع الشرقي',
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.inventoryCount),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _createNewCount,
            tooltip: 'إنشاء جرد جديد',
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showReports,
            tooltip: 'التقارير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                TextField(
                  decoration: const InputDecoration(
                    hintText: 'البحث في عمليات الجرد...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedStatus,
                        decoration: const InputDecoration(
                          labelText: 'حالة الجرد',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الحالات')),
                          DropdownMenuItem(value: 'مجدول', child: Text('مجدول')),
                          DropdownMenuItem(value: 'جاري', child: Text('جاري')),
                          DropdownMenuItem(value: 'مكتمل', child: Text('مكتمل')),
                          DropdownMenuItem(value: 'ملغي', child: Text('ملغي')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedStatus = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                _buildStatCard('المجموع', _inventoryCounts.length.toString(), Colors.blue),
                _buildStatCard('مكتملة', _inventoryCounts.where((c) => c['status'] == 'مكتمل').length.toString(), Colors.green),
                _buildStatCard('جارية', _inventoryCounts.where((c) => c['status'] == 'جاري').length.toString(), Colors.orange),
                _buildStatCard('مجدولة', _inventoryCounts.where((c) => c['status'] == 'مجدول').length.toString(), Colors.purple),
              ],
            ),
          ),

          // قائمة عمليات الجرد
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _inventoryCounts.length,
              itemBuilder: (context, index) {
                final count = _inventoryCounts[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: _getStatusColor(count['status']),
                      child: Icon(
                        _getStatusIcon(count['status']),
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    title: Text(
                      count['name'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('المستودع: ${count['warehouse']}'),
                        Text('المشرف: ${count['supervisor']}'),
                        Text('التقدم: ${count['countedItems']}/${count['itemsCount']} صنف'),
                        if (count['discrepancies'] > 0)
                          Text(
                            'التباينات: ${count['discrepancies']}',
                            style: const TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
                          ),
                      ],
                    ),
                    trailing: PopupMenuButton<String>(
                      onSelected: (value) => _handleAction(value, count),
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'view',
                          child: ListTile(
                            leading: Icon(Icons.visibility),
                            title: Text('عرض التفاصيل'),
                          ),
                        ),
                        if (count['status'] == 'مجدول')
                          const PopupMenuItem(
                            value: 'start',
                            child: ListTile(
                              leading: Icon(Icons.play_arrow, color: Colors.green),
                              title: Text('بدء الجرد'),
                            ),
                          ),
                        if (count['status'] == 'جاري')
                          const PopupMenuItem(
                            value: 'continue',
                            child: ListTile(
                              leading: Icon(Icons.edit),
                              title: Text('متابعة الجرد'),
                            ),
                          ),
                        if (count['status'] == 'مكتمل')
                          const PopupMenuItem(
                            value: 'report',
                            child: ListTile(
                              leading: Icon(Icons.assessment),
                              title: Text('تقرير الجرد'),
                            ),
                          ),
                        const PopupMenuItem(
                          value: 'edit',
                          child: ListTile(
                            leading: Icon(Icons.edit),
                            title: Text('تعديل'),
                          ),
                        ),
                        if (count['status'] != 'مكتمل')
                          const PopupMenuItem(
                            value: 'cancel',
                            child: ListTile(
                              leading: Icon(Icons.cancel, color: Colors.red),
                              title: Text('إلغاء', style: TextStyle(color: Colors.red)),
                            ),
                          ),
                      ],
                    ),
                    isThreeLine: true,
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _createNewCount,
        backgroundColor: Colors.green,
        icon: const Icon(Icons.add),
        label: const Text('جرد جديد'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'مكتمل':
        return Colors.green;
      case 'جاري':
        return Colors.orange;
      case 'مجدول':
        return Colors.blue;
      case 'ملغي':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'مكتمل':
        return Icons.check_circle;
      case 'جاري':
        return Icons.hourglass_empty;
      case 'مجدول':
        return Icons.schedule;
      case 'ملغي':
        return Icons.cancel;
      default:
        return Icons.inventory;
    }
  }

  void _createNewCount() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء عملية جرد جديدة')),
    );
  }

  void _showReports() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تقارير الجرد')),
    );
  }

  void _handleAction(String action, Map<String, dynamic> count) {
    switch (action) {
      case 'view':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('عرض تفاصيل ${count['name']}')),
        );
        break;
      case 'start':
        setState(() {
          count['status'] = 'جاري';
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم بدء جرد ${count['name']}'),
            backgroundColor: Colors.green,
          ),
        );
        break;
      case 'continue':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('متابعة جرد ${count['name']}')),
        );
        break;
      case 'report':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تقرير جرد ${count['name']}')),
        );
        break;
      case 'edit':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تعديل ${count['name']}')),
        );
        break;
      case 'cancel':
        _showCancelConfirmation(count);
        break;
    }
  }

  void _showCancelConfirmation(Map<String, dynamic> count) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الإلغاء'),
        content: Text('هل أنت متأكد من إلغاء عملية الجرد ${count['name']}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                count['status'] = 'ملغي';
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم إلغاء عملية الجرد ${count['name']}'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('إلغاء الجرد', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
