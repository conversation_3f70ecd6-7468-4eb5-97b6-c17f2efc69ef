import 'package:flutter/material.dart';

class PrintPreviewButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String? tooltip;
  final bool isLoading;
  final bool isDisabled;

  const PrintPreviewButton({
    super.key,
    this.onPressed,
    this.tooltip = 'Print Preview',
    this.isLoading = false,
    this.isDisabled = false,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip ?? 'Print Preview',
      child: ElevatedButton.icon(
        onPressed: isDisabled || isLoading ? null : onPressed,
        icon: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              )
            : const Icon(Icons.preview),
        label: const Text('Print Preview'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blueGrey.shade700,
          foregroundColor: Colors.white,
        ),
      ),
    );
  }
}
