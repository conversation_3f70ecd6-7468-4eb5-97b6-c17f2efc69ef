import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير الأصناف التي ليس لها صور
/// يعرض جميع الأصناف التي لا تحتوي على صور مرفقة
class ItemsWithoutImagesReportPage extends StatefulWidget {
  const ItemsWithoutImagesReportPage({super.key});

  @override
  State<ItemsWithoutImagesReportPage> createState() => _ItemsWithoutImagesReportPageState();
}

class _ItemsWithoutImagesReportPageState extends State<ItemsWithoutImagesReportPage> {
  String? _selectedCategory;
  String? _priority = 'all';
  String? _sortBy = 'item_name';
  String? _addedDate = 'all';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الأصناف التي ليس لها صور'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.warning),
            onPressed: _showMissingImagesAlert,
            tooltip: 'تنبيه الصور المفقودة',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          _buildFilterSection(localizations),
          
          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildPrioritySection(),
                  const SizedBox(height: 16),
                  _buildItemsTableSection(),
                  const SizedBox(height: 16),
                  _buildImpactAnalysisSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.red[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: localizations.category,
                    border: const OutlineInputBorder(),
                  ),
                  value: _selectedCategory,
                  items: [
                    DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                    DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                    DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                    DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                  ],
                  onChanged: (value) => setState(() => _selectedCategory = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'الأولوية',
                    border: OutlineInputBorder(),
                  ),
                  value: _priority,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الأولويات')),
                    DropdownMenuItem(value: 'urgent', child: Text('عاجل')),
                    DropdownMenuItem(value: 'high', child: Text('عالي')),
                    DropdownMenuItem(value: 'medium', child: Text('متوسط')),
                    DropdownMenuItem(value: 'low', child: Text('منخفض')),
                  ],
                  onChanged: (value) => setState(() => _priority = value),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'تاريخ الإضافة',
                    border: OutlineInputBorder(),
                  ),
                  value: _addedDate,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع التواريخ')),
                    DropdownMenuItem(value: 'recent', child: Text('آخر شهر')),
                    DropdownMenuItem(value: 'old', child: Text('أكثر من 3 أشهر')),
                    DropdownMenuItem(value: 'very_old', child: Text('أكثر من سنة')),
                  ],
                  onChanged: (value) => setState(() => _addedDate = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'ترتيب حسب',
                    border: OutlineInputBorder(),
                  ),
                  value: _sortBy,
                  items: const [
                    DropdownMenuItem(value: 'item_name', child: Text('اسم الصنف')),
                    DropdownMenuItem(value: 'priority', child: Text('الأولوية')),
                    DropdownMenuItem(value: 'sales_volume', child: Text('حجم المبيعات')),
                    DropdownMenuItem(value: 'added_date', child: Text('تاريخ الإضافة')),
                  ],
                  onChanged: (value) => setState(() => _sortBy = value),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.image_not_supported, color: Colors.red, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص الأصناف بدون صور',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي الأصناف', '285', Colors.red, Icons.inventory),
                _buildSummaryCard('نسبة الأصناف بدون صور', '23%', Colors.orange, Icons.percent),
                _buildSummaryCard('أصناف عالية الأولوية', '85', Colors.purple, Icons.priority_high),
                _buildSummaryCard('تأثير على المبيعات', '15%', Colors.blue, Icons.trending_down),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrioritySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.priority_high, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'توزيع الأولويات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildPriorityCard('عاجل', '45 صنف', Colors.red),
                _buildPriorityCard('عالي', '85 صنف', Colors.orange),
                _buildPriorityCard('متوسط', '105 صنف', Colors.blue),
                _buildPriorityCard('منخفض', '50 صنف', Colors.green),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الأصناف التي تحتاج إلى صور',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('اسم الصنف')),
                  DataColumn(label: Text('الفئة')),
                  DataColumn(label: Text('تاريخ الإضافة')),
                  DataColumn(label: Text('حجم المبيعات')),
                  DataColumn(label: Text('السعر')),
                  DataColumn(label: Text('الأولوية')),
                  DataColumn(label: Text('التأثير على المبيعات')),
                  DataColumn(label: Text('الحالة')),
                  DataColumn(label: Text('إجراءات')),
                ],
                rows: _buildItemsRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImpactAnalysisSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.analytics, color: Colors.teal, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'تحليل التأثير على المبيعات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildImpactAnalysisList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _bulkImageUpload,
                    icon: const Icon(Icons.cloud_upload),
                    label: const Text('رفع صور جماعي'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _assignPhotographer,
                    icon: const Icon(Icons.camera_alt),
                    label: const Text('تكليف مصور'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _createPhotoSchedule,
                    icon: const Icon(Icons.schedule),
                    label: const Text('جدولة التصوير'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _setReminders,
                    icon: const Icon(Icons.notifications),
                    label: const Text('تعيين تذكيرات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildImpactAnalysisList() {
    final impactData = [
      {'category': 'إلكترونيات', 'items': '85 صنف', 'impact': 'انخفاض 25% في المبيعات', 'priority': 'عاجل'},
      {'category': 'ملابس', 'items': '125 صنف', 'impact': 'انخفاض 35% في المبيعات', 'priority': 'عاجل'},
      {'category': 'أغذية', 'items': '45 صنف', 'impact': 'انخفاض 10% في المبيعات', 'priority': 'متوسط'},
      {'category': 'أدوات منزلية', 'items': '30 صنف', 'impact': 'انخفاض 15% في المبيعات', 'priority': 'عالي'},
    ];

    return impactData.map((data) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: _getPriorityColor(data['priority']!).withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: _getPriorityColor(data['priority']!).withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.analytics, color: Colors.teal, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  data['category']!,
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  '${data['items']} • ${data['impact']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getPriorityColor(data['priority']!).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              data['priority']!,
              style: TextStyle(
                color: _getPriorityColor(data['priority']!),
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildItemsRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('2024-01-10')),
        const DataCell(Text('عالي')),
        const DataCell(Text('4,500 ر.س')),
        DataCell(_buildPriorityBadge('عاجل', Colors.red)),
        const DataCell(Text('انخفاض 30%')),
        DataCell(_buildStatusBadge('مطلوب', Colors.orange)),
        DataCell(_buildActionButton()),
      ]),
      DataRow(cells: [
        const DataCell(Text('هاتف آيفون 15')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('2024-01-12')),
        const DataCell(Text('عالي')),
        const DataCell(Text('3,800 ر.س')),
        DataCell(_buildPriorityBadge('عاجل', Colors.red)),
        const DataCell(Text('انخفاض 25%')),
        DataCell(_buildStatusBadge('مطلوب', Colors.orange)),
        DataCell(_buildActionButton()),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPriorityCard(String priority, String count, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(priority, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPriorityBadge(String priority, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(priority, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(status, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Widget _buildActionButton() {
    return ElevatedButton(
      onPressed: () => _uploadImage(),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        minimumSize: const Size(80, 32),
      ),
      child: const Text('رفع صورة', style: TextStyle(fontSize: 10)),
    );
  }

  Color _getPriorityColor(String priority) {
    switch (priority) {
      case 'عاجل':
        return Colors.red;
      case 'عالي':
        return Colors.orange;
      case 'متوسط':
        return Colors.blue;
      case 'منخفض':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showMissingImagesAlert() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تنبيه الصور المفقودة')),
    );
  }

  void _bulkImageUpload() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('رفع صور جماعي للأصناف')),
    );
  }

  void _assignPhotographer() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تكليف مصور للأصناف')),
    );
  }

  void _createPhotoSchedule() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء جدولة التصوير')),
    );
  }

  void _setReminders() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تعيين تذكيرات للصور المفقودة')),
    );
  }

  void _uploadImage() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('رفع صورة للصنف')),
    );
  }
}
