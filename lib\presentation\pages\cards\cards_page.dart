import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';
import 'financial_guide.dart';
import 'cost_center.dart';
import 'customers.dart';
import 'suppliers.dart';
import 'sales_representatives.dart';
import 'miscellaneous_accounts.dart';
import 'branches.dart';
import 'warehouses.dart';
import 'automatic_pricing.dart';
import 'alternatives.dart';
import 'inventory_count.dart';
import 'inventory_assignment.dart';
import 'units/units_page.dart';
import 'categories/categories_page.dart';
import 'currencies.dart';
import 'warehouse_linking.dart';
import 'facilities.dart';
import 'expense_types.dart';
import 'expense_data.dart';
import 'items/items_page.dart';
import 'stores.dart';

/// صفحة الكروت الرئيسية
/// تعرض ملخص الكروت والعمليات السريعة
class CardsPage extends StatelessWidget {
  const CardsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.cards),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقات الإحصائيات السريعة
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    title: localizations.totalCustomers,
                    value: '245',
                    icon: Icons.people,
                    color: Colors.blue,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    title: localizations.totalSuppliers,
                    value: '89',
                    icon: Icons.local_shipping,
                    color: Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    title: localizations.totalItems,
                    value: '1,234',
                    icon: Icons.inventory,
                    color: Colors.purple,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    title: localizations.totalWarehouses,
                    value: '12',
                    icon: Icons.store,
                    color: Colors.teal,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // قائمة الكروت
            Text(
              localizations.cardsManagement,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            Expanded(
              child: ListView(
                children: [
                  // الصفحات حسب الترتيب المطلوب مع أرقام تسلسلية
                  _buildNumberedCardItem(
                    number: 1,
                    title: localizations.financialGuide,
                    icon: Icons.menu_book,
                    color: Colors.indigo,
                    isImplemented: true,
                    onTap: () => _navigateToCard(context, 'financial_guide'),
                    localizations: localizations,
                  ),
                  _buildNumberedCardItem(
                    number: 2,
                    title: localizations.costCenter,
                    icon: Icons.account_balance,
                    color: Colors.brown,
                    isImplemented: true,
                    onTap: () => _navigateToCard(context, 'cost_center'),
                    localizations: localizations,
                  ),
                  _buildNumberedCardItem(
                    number: 3,
                    title: localizations.customers,
                    icon: Icons.people,
                    color: Colors.blue,
                    isImplemented: true,
                    onTap: () => _navigateToCard(context, 'customers'),
                    localizations: localizations,
                  ),
                  _buildNumberedCardItem(
                    number: 4,
                    title: localizations.suppliers,
                    icon: Icons.local_shipping,
                    color: Colors.orange,
                    isImplemented: true,
                    onTap: () => _navigateToCard(context, 'suppliers'),
                    localizations: localizations,
                  ),
                  _buildNumberedCardItem(
                    number: 5,
                    title: localizations.salesRepresentatives,
                    icon: Icons.person_pin,
                    color: Colors.teal,
                    isImplemented: true,
                    onTap: () =>
                        _navigateToCard(context, 'sales_representatives'),
                    localizations: localizations,
                  ),
                  _buildNumberedCardItem(
                    number: 6,
                    title: localizations.miscellaneousAccounts,
                    icon: Icons.account_tree,
                    color: Colors.indigo,
                    isImplemented: true,
                    onTap: () =>
                        _navigateToCard(context, 'miscellaneous_accounts'),
                    localizations: localizations,
                  ),
                  _buildNumberedCardItem(
                    number: 7,
                    title: localizations.branches,
                    icon: Icons.store,
                    color: Colors.deepOrange,
                    isImplemented: true,
                    onTap: () => _navigateToCard(context, 'branches'),
                    localizations: localizations,
                  ),
                  _buildNumberedCardItem(
                    number: 8,
                    title: localizations.warehouses,
                    icon: Icons.warehouse,
                    color: Colors.brown,
                    isImplemented: true,
                    onTap: () => _navigateToCard(context, 'warehouses'),
                    localizations: localizations,
                  ),
                  _buildNumberedCardItem(
                    number: 9,
                    title: localizations.items,
                    icon: Icons.inventory_2,
                    color: Colors.purple,
                    isImplemented: true,
                    onTap: () => _navigateToCard(context, 'items'),
                    localizations: localizations,
                  ),
                  _buildNumberedCardItem(
                    number: 10,
                    title: localizations.automaticPricing,
                    icon: Icons.auto_awesome,
                    color: Colors.deepPurple,
                    isImplemented: true,
                    onTap: () => _navigateToCard(context, 'automatic_pricing'),
                    localizations: localizations,
                  ),
                  _buildNumberedCardItem(
                    number: 11,
                    title: localizations.alternatives,
                    icon: Icons.swap_horiz,
                    color: Colors.cyan,
                    isImplemented: true,
                    onTap: () => _navigateToCard(context, 'alternatives'),
                    localizations: localizations,
                  ),
                  _buildNumberedCardItem(
                    number: 12,
                    title: localizations.inventoryCount,
                    icon: Icons.inventory,
                    color: Colors.green,
                    isImplemented: true,
                    onTap: () => _navigateToCard(context, 'inventory_count'),
                    localizations: localizations,
                  ),
                  _buildNumberedCardItem(
                    number: 13,
                    title: localizations.inventoryAssignment,
                    icon: Icons.assignment,
                    color: Colors.deepPurple,
                    isImplemented: true,
                    onTap: () =>
                        _navigateToCard(context, 'inventory_assignment'),
                    localizations: localizations,
                  ),
                  _buildNumberedCardItem(
                    number: 14,
                    title: localizations.units,
                    icon: Icons.straighten,
                    color: Colors.indigo,
                    isImplemented: true,
                    onTap: () => _navigateToCard(context, 'units'),
                    localizations: localizations,
                  ),
                  _buildNumberedCardItem(
                    number: 15,
                    title: localizations.categories,
                    icon: Icons.category,
                    color: Colors.pink,
                    isImplemented: true,
                    onTap: () => _navigateToCard(context, 'categories'),
                    localizations: localizations,
                  ),
                  _buildNumberedCardItem(
                    number: 16,
                    title: localizations.currencies,
                    icon: Icons.currency_exchange,
                    color: Colors.amber,
                    isImplemented: true,
                    onTap: () => _navigateToCard(context, 'currencies'),
                    localizations: localizations,
                  ),
                  _buildNumberedCardItem(
                    number: 17,
                    title: localizations.warehouseLinking,
                    icon: Icons.link,
                    color: Colors.lightBlue,
                    isImplemented: true,
                    onTap: () => _navigateToCard(context, 'warehouse_linking'),
                    localizations: localizations,
                  ),
                  _buildNumberedCardItem(
                    number: 18,
                    title: localizations.facilities,
                    icon: Icons.business,
                    color: Colors.lightGreen,
                    isImplemented: true,
                    onTap: () => _navigateToCard(context, 'facilities'),
                    localizations: localizations,
                  ),
                  _buildNumberedCardItem(
                    number: 19,
                    title: localizations.expenseTypes,
                    icon: Icons.category_outlined,
                    color: Colors.grey,
                    isImplemented: true,
                    onTap: () => _navigateToCard(context, 'expense_types'),
                    localizations: localizations,
                  ),
                  _buildNumberedCardItem(
                    number: 20,
                    title: localizations.expenseData,
                    icon: Icons.receipt_long,
                    color: Colors.blueGrey,
                    isImplemented: true,
                    onTap: () => _navigateToCard(context, 'expense_data'),
                    localizations: localizations,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const Spacer(),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة كرت مرقمة
  Widget _buildNumberedCardItem({
    required int number,
    required String title,
    required IconData icon,
    required Color color,
    required bool isImplemented,
    required VoidCallback onTap,
    required AppLocalizations localizations,
  }) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              // رقم تسلسلي
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: color, width: 2),
                ),
                child: Center(
                  child: Text(
                    number.toString(),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),

              // أيقونة
              Icon(
                icon,
                size: 32,
                color: color,
              ),
              const SizedBox(width: 16),

              // العنوان
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),

              // مؤشر الحالة
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: isImplemented ? Colors.green : Colors.grey,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  isImplemented
                      ? localizations.completed
                      : localizations.comingSoon,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

              const SizedBox(width: 8),

              // سهم
              Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey[400],
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// التنقل إلى كرت معين
  void _navigateToCard(BuildContext context, String card) {
    Widget? targetPage;

    switch (card) {
      case 'financial_guide':
        targetPage = const FinancialGuide();
        break;
      case 'cost_center':
        targetPage = const CostCenter();
        break;
      case 'customers':
        targetPage = const Customers();
        break;
      case 'suppliers':
        targetPage = const Suppliers();
        break;
      case 'sales_representatives':
        targetPage = const SalesRepresentativesPage();
        break;
      case 'miscellaneous_accounts':
        targetPage = const MiscellaneousAccountsPage();
        break;
      case 'branches':
        targetPage = const BranchesPage();
        break;
      case 'warehouses':
        targetPage = const WarehousesPage();
        break;
      case 'automatic_pricing':
        targetPage = const AutomaticPricingPage();
        break;
      case 'alternatives':
        targetPage = const AlternativesPage();
        break;
      case 'inventory_count':
        targetPage = const InventoryCountPage();
        break;
      case 'inventory_assignment':
        targetPage = const InventoryAssignmentPage();
        break;
      case 'units':
        targetPage = const UnitsPage();
        break;
      case 'items':
        targetPage = const ItemsPage();
        break;
      case 'stores':
        targetPage = const Stores();
        break;
      case 'categories':
        targetPage = const CategoriesPage();
        break;
      case 'currencies':
        targetPage = const CurrenciesPage();
        break;
      case 'warehouse_linking':
        targetPage = const WarehouseLinkingPage();
        break;
      case 'facilities':
        targetPage = const FacilitiesPage();
        break;
      case 'expense_types':
        targetPage = const ExpenseTypesPage();
        break;
      case 'expense_data':
        targetPage = const ExpenseDataPage();
        break;
      default:
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('الانتقال إلى $card')),
        );
        return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => targetPage!,
      ),
    );
  }
}
