import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';
import 'change_language.dart';
import 'fiscal_year/fiscal_year_page.dart';

/// صفحة الإعدادات الرئيسية
/// تعرض جميع إعدادات التطبيق والنظام
class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.settings),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان الصفحة
            Text(
              localizations.settings,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),

            Expanded(
              child: <PERSON>View(
                children: [
                  // قسم الإعدادات العامة
                  _buildSectionHeader('الإعدادات العامة'),
                  _buildSettingsTile(
                    title: localizations.changeLanguage,
                    subtitle: 'تغيير لغة التطبيق',
                    icon: Icons.language,
                    onTap: () => _navigateToSetting(context, 'change_language'),
                  ),
                  _buildSettingsTile(
                    title: localizations.changeTheme,
                    subtitle: 'تغيير مظهر التطبيق',
                    icon: Icons.color_lens,
                    onTap: () => _navigateToSetting(context, 'change_theme'),
                  ),
                  _buildSettingsTile(
                    title: localizations.printerSettings,
                    subtitle: 'إعدادات الطابعة',
                    icon: Icons.print,
                    onTap: () =>
                        _navigateToSetting(context, 'printer_settings'),
                  ),

                  const SizedBox(height: 20),

                  // قسم إعدادات الشركة
                  _buildSectionHeader('إعدادات الشركة'),
                  _buildSettingsTile(
                    title: localizations.companyData,
                    subtitle: 'بيانات الشركة الأساسية',
                    icon: Icons.business,
                    onTap: () => _navigateToSetting(context, 'company_data'),
                  ),
                  _buildSettingsTile(
                    title: localizations.createEditFiscalYear,
                    subtitle: 'إدارة السنوات المالية',
                    icon: Icons.calendar_today,
                    onTap: () => _navigateToSetting(context, 'fiscal_year'),
                  ),

                  const SizedBox(height: 20),

                  // قسم النسخ الاحتياطي
                  _buildSectionHeader('النسخ الاحتياطي'),
                  _buildSettingsTile(
                    title: localizations.backupRestore,
                    subtitle: 'نسخ احتياطي واستعادة البيانات',
                    icon: Icons.backup,
                    onTap: () => _navigateToSetting(context, 'backup_restore'),
                  ),

                  const SizedBox(height: 20),

                  // قسم إدارة النظام
                  _buildSectionHeader('إدارة النظام'),
                  _buildSettingsTile(
                    title: 'إدارة المستخدمين',
                    subtitle: 'إضافة وتعديل المستخدمين',
                    icon: Icons.people,
                    onTap: () =>
                        _navigateToSetting(context, 'users_management'),
                  ),
                  _buildSettingsTile(
                    title: 'الأدوار والصلاحيات',
                    subtitle: 'إدارة صلاحيات المستخدمين',
                    icon: Icons.security,
                    onTap: () =>
                        _navigateToSetting(context, 'user_permissions'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنوان قسم
  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.blue,
        ),
      ),
    );
  }

  /// بناء عنصر إعدادات
  Widget _buildSettingsTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      child: ListTile(
        leading: Icon(
          icon,
          color: Colors.blue,
          size: 28,
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: const TextStyle(
            color: Colors.grey,
            fontSize: 12,
          ),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: Colors.grey,
        ),
        onTap: onTap,
      ),
    );
  }

  /// التنقل إلى إعداد معين
  void _navigateToSetting(BuildContext context, String setting) {
    if (setting == 'change_language') {
      // التنقل إلى صفحة تغيير اللغة
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const ChangeLanguage(),
        ),
      );
    } else if (setting == 'fiscal_year') {
      // التنقل إلى صفحة السنة المالية
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const FiscalYearPage(),
        ),
      );
    } else {
      // لباقي الإعدادات، إظهار رسالة
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('الانتقال إلى إعدادات $setting')),
      );
    }
  }
}
