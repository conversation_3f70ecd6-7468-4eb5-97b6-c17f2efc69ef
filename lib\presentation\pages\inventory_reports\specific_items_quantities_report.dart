import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير الكميات لأصناف معينة
/// يعرض كميات أصناف محددة في المخازن المختلفة
class SpecificItemsQuantitiesReportPage extends StatefulWidget {
  const SpecificItemsQuantitiesReportPage({super.key});

  @override
  State<SpecificItemsQuantitiesReportPage> createState() => _SpecificItemsQuantitiesReportPageState();
}

class _SpecificItemsQuantitiesReportPageState extends State<SpecificItemsQuantitiesReportPage> {
  final List<String> _selectedItems = [];
  String? _selectedWarehouse;
  String? _quantityFilter = 'all';
  String? _sortBy = 'item_name';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الكميات لأصناف معينة'),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshQuantities,
            tooltip: 'تحديث الكميات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          _buildFilterSection(localizations),
          
          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildSelectedItemsSection(),
                  const SizedBox(height: 16),
                  _buildQuantitiesTableSection(),
                  const SizedBox(height: 16),
                  _buildWarehouseDistributionSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.indigo[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _selectItems,
                  icon: const Icon(Icons.add),
                  label: Text('اختيار الأصناف (${_selectedItems.length})'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'المستودع',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedWarehouse,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع المستودعات')),
                    DropdownMenuItem(value: 'main', child: Text('المستودع الرئيسي')),
                    DropdownMenuItem(value: 'branch1', child: Text('مستودع الفرع الأول')),
                    DropdownMenuItem(value: 'branch2', child: Text('مستودع الفرع الثاني')),
                    DropdownMenuItem(value: 'branch3', child: Text('مستودع الفرع الثالث')),
                  ],
                  onChanged: (value) => setState(() => _selectedWarehouse = value),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'فلتر الكمية',
                    border: OutlineInputBorder(),
                  ),
                  value: _quantityFilter,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الكميات')),
                    DropdownMenuItem(value: 'in_stock', child: Text('متوفر في المخزن')),
                    DropdownMenuItem(value: 'out_of_stock', child: Text('غير متوفر')),
                    DropdownMenuItem(value: 'low_stock', child: Text('كمية منخفضة')),
                    DropdownMenuItem(value: 'high_stock', child: Text('كمية عالية')),
                  ],
                  onChanged: (value) => setState(() => _quantityFilter = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'ترتيب حسب',
                    border: OutlineInputBorder(),
                  ),
                  value: _sortBy,
                  items: const [
                    DropdownMenuItem(value: 'item_name', child: Text('اسم الصنف')),
                    DropdownMenuItem(value: 'quantity', child: Text('الكمية')),
                    DropdownMenuItem(value: 'value', child: Text('القيمة')),
                    DropdownMenuItem(value: 'warehouse', child: Text('المستودع')),
                  ],
                  onChanged: (value) => setState(() => _sortBy = value),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.inventory, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص كميات الأصناف المحددة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('الأصناف المحددة', '${_selectedItems.length}', Colors.indigo, Icons.checklist),
                _buildSummaryCard('إجمالي الكمية', '2,485', Colors.blue, Icons.inventory_2),
                _buildSummaryCard('إجمالي القيمة', '485,000 ر.س', Colors.green, Icons.monetization_on),
                _buildSummaryCard('متوسط الكمية', '124', Colors.orange, Icons.calculate),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectedItemsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.list, color: Colors.blue, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'الأصناف المحددة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_selectedItems.isEmpty)
              Container(
                padding: const EdgeInsets.all(32),
                child: const Center(
                  child: Column(
                    children: [
                      Icon(Icons.inbox, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text(
                        'لم يتم اختيار أي أصناف بعد',
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'اضغط على "اختيار الأصناف" لإضافة أصناف',
                        style: TextStyle(fontSize: 14, color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              )
            else
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _selectedItems.map((item) => Chip(
                  label: Text(item),
                  deleteIcon: const Icon(Icons.close, size: 18),
                  onDeleted: () => _removeItem(item),
                  backgroundColor: Colors.indigo.withValues(alpha: 0.1),
                )).toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuantitiesTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل كميات الأصناف',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (_selectedItems.isEmpty)
              Container(
                padding: const EdgeInsets.all(32),
                child: const Center(
                  child: Text(
                    'اختر أصناف لعرض تفاصيل الكميات',
                    style: TextStyle(fontSize: 16, color: Colors.grey),
                  ),
                ),
              )
            else
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: DataTable(
                  columns: const [
                    DataColumn(label: Text('اسم الصنف')),
                    DataColumn(label: Text('المستودع الرئيسي')),
                    DataColumn(label: Text('الفرع الأول')),
                    DataColumn(label: Text('الفرع الثاني')),
                    DataColumn(label: Text('الفرع الثالث')),
                    DataColumn(label: Text('إجمالي الكمية')),
                    DataColumn(label: Text('الوحدة')),
                    DataColumn(label: Text('القيمة الإجمالية')),
                    DataColumn(label: Text('الحالة')),
                  ],
                  rows: _buildQuantitiesRows(),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildWarehouseDistributionSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.warehouse, color: Colors.teal, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'توزيع الكميات على المستودعات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildWarehouseCard('المستودع الرئيسي', '1,285', Colors.blue),
                _buildWarehouseCard('الفرع الأول', '485', Colors.green),
                _buildWarehouseCard('الفرع الثاني', '385', Colors.orange),
                _buildWarehouseCard('الفرع الثالث', '330', Colors.purple),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _transferBetweenWarehouses,
                    icon: const Icon(Icons.swap_horiz),
                    label: const Text('نقل بين المستودعات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _adjustQuantities,
                    icon: const Icon(Icons.edit),
                    label: const Text('تعديل الكميات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _createPurchaseOrder,
                    icon: const Icon(Icons.shopping_cart),
                    label: const Text('إنشاء طلب شراء'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _setStockAlerts,
                    icon: const Icon(Icons.notifications),
                    label: const Text('تعيين تنبيهات المخزون'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<DataRow> _buildQuantitiesRows() {
    // عرض بيانات وهمية للأصناف المحددة
    final sampleData = [
      {
        'name': 'لابتوب ديل XPS 13',
        'main': '25',
        'branch1': '15',
        'branch2': '10',
        'branch3': '8',
        'total': '58',
        'unit': 'قطعة',
        'value': '261,000 ر.س',
        'status': 'متوفر'
      },
      {
        'name': 'هاتف آيفون 15',
        'main': '30',
        'branch1': '20',
        'branch2': '15',
        'branch3': '12',
        'total': '77',
        'unit': 'قطعة',
        'value': '292,600 ر.س',
        'status': 'متوفر'
      },
    ];

    return sampleData.map((item) => DataRow(cells: [
      DataCell(Text(item['name']!)),
      DataCell(Text(item['main']!)),
      DataCell(Text(item['branch1']!)),
      DataCell(Text(item['branch2']!)),
      DataCell(Text(item['branch3']!)),
      DataCell(Text(item['total']!)),
      DataCell(Text(item['unit']!)),
      DataCell(Text(item['value']!)),
      DataCell(_buildStatusBadge(item['status']!, Colors.green)),
    ])).toList();
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWarehouseCard(String warehouse, String quantity, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                quantity,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(warehouse, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(status, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  void _selectItems() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار الأصناف'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: ListView(
            children: [
              'لابتوب ديل XPS 13',
              'هاتف آيفون 15',
              'طابعة HP LaserJet',
              'شاشة سامسونج 27 بوصة',
              'ماوس لوجيتك',
              'كيبورد ميكانيكي',
              'سماعات بلوتوث',
              'كاميرا كانون',
            ].map((item) => CheckboxListTile(
              title: Text(item),
              value: _selectedItems.contains(item),
              onChanged: (bool? value) {
                setState(() {
                  if (value == true) {
                    _selectedItems.add(item);
                  } else {
                    _selectedItems.remove(item);
                  }
                });
                Navigator.of(context).pop();
                _selectItems(); // إعادة فتح الحوار
              },
            )).toList(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _removeItem(String item) {
    setState(() {
      _selectedItems.remove(item);
    });
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _refreshQuantities() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تحديث الكميات')),
    );
  }

  void _transferBetweenWarehouses() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('نقل الأصناف بين المستودعات')),
    );
  }

  void _adjustQuantities() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تعديل كميات الأصناف')),
    );
  }

  void _createPurchaseOrder() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء طلب شراء للأصناف المحددة')),
    );
  }

  void _setStockAlerts() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تعيين تنبيهات المخزون للأصناف المحددة')),
    );
  }
}
