import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير تقييم انحراف التكلفة وسعر البيع
/// يعرض تحليل الانحرافات بين التكلفة الفعلية وسعر البيع
class CostSellingPriceDeviationReportPage extends StatefulWidget {
  const CostSellingPriceDeviationReportPage({super.key});

  @override
  State<CostSellingPriceDeviationReportPage> createState() => _CostSellingPriceDeviationReportPageState();
}

class _CostSellingPriceDeviationReportPageState extends State<CostSellingPriceDeviationReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedCategory;
  String? _deviationType = 'all';
  String? _sortBy = 'deviation_percentage';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('تقييم انحراف التكلفة وسعر البيع'),
        backgroundColor: Colors.deepOrange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showAdvancedAnalytics,
            tooltip: 'تحليلات متقدمة',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          _buildFilterSection(localizations),
          
          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildDeviationTypesSection(),
                  const SizedBox(height: 16),
                  _buildDeviationTableSection(),
                  const SizedBox(height: 16),
                  _buildCriticalDeviationsSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.deepOrange[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.fromDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, true),
                  controller: TextEditingController(
                    text: _startDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.toDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, false),
                  controller: TextEditingController(
                    text: _endDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: localizations.category,
                    border: const OutlineInputBorder(),
                  ),
                  value: _selectedCategory,
                  items: [
                    DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                    DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                    DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                    DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                  ],
                  onChanged: (value) => setState(() => _selectedCategory = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'نوع الانحراف',
                    border: OutlineInputBorder(),
                  ),
                  value: _deviationType,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الانحرافات')),
                    DropdownMenuItem(value: 'positive', child: Text('انحراف إيجابي')),
                    DropdownMenuItem(value: 'negative', child: Text('انحراف سلبي')),
                    DropdownMenuItem(value: 'critical', child: Text('انحراف حرج')),
                    DropdownMenuItem(value: 'normal', child: Text('انحراف طبيعي')),
                  ],
                  onChanged: (value) => setState(() => _deviationType = value),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'ترتيب حسب',
                    border: OutlineInputBorder(),
                  ),
                  value: _sortBy,
                  items: const [
                    DropdownMenuItem(value: 'deviation_percentage', child: Text('نسبة الانحراف')),
                    DropdownMenuItem(value: 'item_name', child: Text('اسم الصنف')),
                    DropdownMenuItem(value: 'cost_difference', child: Text('فرق التكلفة')),
                    DropdownMenuItem(value: 'impact_level', child: Text('مستوى التأثير')),
                  ],
                  onChanged: (value) => setState(() => _sortBy = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.analytics),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.deepOrange,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.trending_up, color: Colors.deepOrange, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص انحرافات التكلفة وسعر البيع',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي الأصناف', '485', Colors.deepOrange, Icons.inventory),
                _buildSummaryCard('متوسط الانحراف', '15.2%', Colors.blue, Icons.percent),
                _buildSummaryCard('انحرافات حرجة', '45', Colors.red, Icons.warning),
                _buildSummaryCard('تأثير مالي', '125,000 ر.س', Colors.green, Icons.monetization_on),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeviationTypesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.pie_chart, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'توزيع أنواع الانحرافات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildDeviationCard('انحراف إيجابي', '285 صنف', Colors.green),
                _buildDeviationCard('انحراف سلبي', '155 صنف', Colors.red),
                _buildDeviationCard('انحراف حرج', '45 صنف', Colors.purple),
                _buildDeviationCard('ضمن المعدل', '125 صنف', Colors.blue),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeviationTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل انحرافات التكلفة وسعر البيع',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('اسم الصنف')),
                  DataColumn(label: Text('التكلفة المعيارية')),
                  DataColumn(label: Text('التكلفة الفعلية')),
                  DataColumn(label: Text('سعر البيع')),
                  DataColumn(label: Text('انحراف التكلفة')),
                  DataColumn(label: Text('نسبة الانحراف')),
                  DataColumn(label: Text('هامش الربح')),
                  DataColumn(label: Text('التأثير المالي')),
                  DataColumn(label: Text('التصنيف')),
                ],
                rows: _buildDeviationRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCriticalDeviationsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.priority_high, color: Colors.red, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'الانحرافات الحرجة (أكثر من 25%)',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildCriticalDeviationsList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _adjustPrices,
                    icon: const Icon(Icons.price_change),
                    label: const Text('تعديل الأسعار'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _reviewCosts,
                    icon: const Icon(Icons.calculate),
                    label: const Text('مراجعة التكاليف'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _setDeviationAlerts,
                    icon: const Icon(Icons.notifications),
                    label: const Text('تعيين تنبيهات الانحراف'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _generateActionPlan,
                    icon: const Icon(Icons.assignment),
                    label: const Text('خطة عمل تصحيحية'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildCriticalDeviationsList() {
    final criticalItems = [
      {'name': 'لابتوب ديل XPS 13', 'deviation': '+35%', 'impact': '15,000 ر.س', 'reason': 'ارتفاع أسعار المكونات'},
      {'name': 'هاتف آيفون 15', 'deviation': '-28%', 'impact': '12,000 ر.س', 'reason': 'تخفيضات المورد'},
      {'name': 'طابعة HP LaserJet', 'deviation': '+42%', 'impact': '8,500 ر.س', 'reason': 'تكاليف شحن إضافية'},
      {'name': 'شاشة سامسونج 27"', 'deviation': '-31%', 'impact': '6,200 ر.س', 'reason': 'عرض خاص من المورد'},
    ];

    return criticalItems.map((item) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.red.withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.priority_high, color: Colors.red, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item['name']!,
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  'انحراف: ${item['deviation']} • تأثير: ${item['impact']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
                Text(
                  'السبب: ${item['reason']}',
                  style: TextStyle(fontSize: 11, color: Colors.grey[500]),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Text(
              'حرج',
              style: TextStyle(
                color: Colors.red,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildDeviationRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('3,200 ر.س')),
        const DataCell(Text('4,320 ر.س')),
        const DataCell(Text('4,500 ر.س')),
        const DataCell(Text('+1,120 ر.س')),
        DataCell(_buildDeviationBadge('+35%', Colors.red)),
        const DataCell(Text('4.2%')),
        const DataCell(Text('15,000 ر.س')),
        DataCell(_buildCriticalityBadge('حرج', Colors.red)),
      ]),
      DataRow(cells: [
        const DataCell(Text('هاتف آيفون 15')),
        const DataCell(Text('2,800 ر.س')),
        const DataCell(Text('2,016 ر.س')),
        const DataCell(Text('3,800 ر.س')),
        const DataCell(Text('-784 ر.س')),
        DataCell(_buildDeviationBadge('-28%', Colors.green)),
        const DataCell(Text('88.9%')),
        const DataCell(Text('12,000 ر.س')),
        DataCell(_buildCriticalityBadge('حرج', Colors.red)),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDeviationCard(String type, String count, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(type, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDeviationBadge(String deviation, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(deviation, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Widget _buildCriticalityBadge(String level, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(level, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير انحراف التكلفة وسعر البيع بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showAdvancedAnalytics() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض التحليلات المتقدمة للانحرافات')),
    );
  }

  void _adjustPrices() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تعديل أسعار البيع بناءً على الانحرافات')),
    );
  }

  void _reviewCosts() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مراجعة وتحديث التكاليف المعيارية')),
    );
  }

  void _setDeviationAlerts() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تعيين تنبيهات للانحرافات الحرجة')),
    );
  }

  void _generateActionPlan() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء خطة عمل تصحيحية للانحرافات')),
    );
  }
}
