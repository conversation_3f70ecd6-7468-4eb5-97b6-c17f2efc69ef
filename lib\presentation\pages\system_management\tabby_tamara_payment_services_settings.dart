import 'package:flutter/material.dart';

/// صفحة إعدادات ربط خدمات الدفع بواسطة تابي و تمارا
/// تتيح تكوين إعدادات خدمات الدفع الإلكتروني المتقدمة
class TabbyTamaraPaymentServicesSettingsPage extends StatefulWidget {
  const TabbyTamaraPaymentServicesSettingsPage({super.key});

  @override
  State<TabbyTamaraPaymentServicesSettingsPage> createState() => _TabbyTamaraPaymentServicesSettingsPageState();
}

class _TabbyTamaraPaymentServicesSettingsPageState extends State<TabbyTamaraPaymentServicesSettingsPage> {
  // إعدادات تابي
  bool _tabbyEnabled = false;
  final _tabbyApiKeyController = TextEditingController();
  final _tabbyMerchantIdController = TextEditingController();
  final _tabbySecretKeyController = TextEditingController();
  String _tabbyEnvironment = 'sandbox';
  
  // إعدادات تمارا
  bool _tamaraEnabled = false;
  final _tamaraApiKeyController = TextEditingController();
  final _tamaraMerchantIdController = TextEditingController();
  final _tamaraSecretKeyController = TextEditingController();
  String _tamaraEnvironment = 'sandbox';
  
  // إعدادات عامة
  double _minimumAmount = 100.0;
  double _maximumAmount = 10000.0;
  bool _enableInstallments = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات ربط خدمات الدفع بواسطة تابي و تمارا'),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _savePaymentSettings,
            tooltip: 'حفظ الإعدادات',
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          // بطاقة حالة الخدمات
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'حالة خدمات الدفع الإلكتروني',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.purple,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: _buildServiceStatusCard(
                          'تابي (Tabby)',
                          _tabbyEnabled,
                          Colors.blue,
                          Icons.credit_card,
                          'دفع بالتقسيط',
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildServiceStatusCard(
                          'تمارا (Tamara)',
                          _tamaraEnabled,
                          Colors.green,
                          Icons.payment,
                          'اشتري الآن وادفع لاحقاً',
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // إعدادات تابي
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.credit_card, color: Colors.blue),
                      const SizedBox(width: 8),
                      const Text(
                        'إعدادات تابي (Tabby) - الدفع بالتقسيط',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  
                  SwitchListTile(
                    title: const Text('تفعيل خدمة تابي'),
                    subtitle: const Text('السماح بالدفع بالتقسيط عبر تابي'),
                    value: _tabbyEnabled,
                    onChanged: (value) {
                      setState(() {
                        _tabbyEnabled = value;
                      });
                    },
                  ),
                  
                  if (_tabbyEnabled) ...[
                    const SizedBox(height: 16),
                    
                    // بيئة العمل
                    DropdownButtonFormField<String>(
                      value: _tabbyEnvironment,
                      decoration: const InputDecoration(
                        labelText: 'بيئة العمل',
                        prefixIcon: Icon(Icons.cloud),
                        border: OutlineInputBorder(),
                      ),
                      items: const [
                        DropdownMenuItem(value: 'sandbox', child: Text('بيئة التجريب (Sandbox)')),
                        DropdownMenuItem(value: 'production', child: Text('بيئة الإنتاج (Production)')),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _tabbyEnvironment = value!;
                        });
                      },
                    ),
                    
                    const SizedBox(height: 16),
                    
                    TextFormField(
                      controller: _tabbyApiKeyController,
                      decoration: const InputDecoration(
                        labelText: 'مفتاح API العام (Public Key)',
                        hintText: 'pk_test_...',
                        prefixIcon: Icon(Icons.key),
                        border: OutlineInputBorder(),
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    TextFormField(
                      controller: _tabbySecretKeyController,
                      decoration: const InputDecoration(
                        labelText: 'مفتاح API السري (Secret Key)',
                        hintText: 'sk_test_...',
                        prefixIcon: Icon(Icons.lock),
                        border: OutlineInputBorder(),
                      ),
                      obscureText: true,
                    ),
                    
                    const SizedBox(height: 16),
                    
                    TextFormField(
                      controller: _tabbyMerchantIdController,
                      decoration: const InputDecoration(
                        labelText: 'معرف التاجر (Merchant ID)',
                        hintText: 'أدخل معرف التاجر الخاص بتابي',
                        prefixIcon: Icon(Icons.store),
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // إعدادات تمارا
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.payment, color: Colors.green),
                      const SizedBox(width: 8),
                      const Text(
                        'إعدادات تمارا (Tamara) - اشتري الآن وادفع لاحقاً',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  
                  SwitchListTile(
                    title: const Text('تفعيل خدمة تمارا'),
                    subtitle: const Text('السماح بالدفع المؤجل عبر تمارا'),
                    value: _tamaraEnabled,
                    onChanged: (value) {
                      setState(() {
                        _tamaraEnabled = value;
                      });
                    },
                  ),
                  
                  if (_tamaraEnabled) ...[
                    const SizedBox(height: 16),
                    
                    // بيئة العمل
                    DropdownButtonFormField<String>(
                      value: _tamaraEnvironment,
                      decoration: const InputDecoration(
                        labelText: 'بيئة العمل',
                        prefixIcon: Icon(Icons.cloud),
                        border: OutlineInputBorder(),
                      ),
                      items: const [
                        DropdownMenuItem(value: 'sandbox', child: Text('بيئة التجريب (Sandbox)')),
                        DropdownMenuItem(value: 'production', child: Text('بيئة الإنتاج (Production)')),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _tamaraEnvironment = value!;
                        });
                      },
                    ),
                    
                    const SizedBox(height: 16),
                    
                    TextFormField(
                      controller: _tamaraApiKeyController,
                      decoration: const InputDecoration(
                        labelText: 'مفتاح API العام (Public Key)',
                        hintText: 'pk_test_...',
                        prefixIcon: Icon(Icons.key),
                        border: OutlineInputBorder(),
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    TextFormField(
                      controller: _tamaraSecretKeyController,
                      decoration: const InputDecoration(
                        labelText: 'مفتاح API السري (Secret Key)',
                        hintText: 'sk_test_...',
                        prefixIcon: Icon(Icons.lock),
                        border: OutlineInputBorder(),
                      ),
                      obscureText: true,
                    ),
                    
                    const SizedBox(height: 16),
                    
                    TextFormField(
                      controller: _tamaraMerchantIdController,
                      decoration: const InputDecoration(
                        labelText: 'معرف التاجر (Merchant ID)',
                        hintText: 'أدخل معرف التاجر الخاص بتمارا',
                        prefixIcon: Icon(Icons.store),
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // الإعدادات العامة
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'الإعدادات العامة للدفع',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.purple,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // الحد الأدنى للمبلغ
                  TextFormField(
                    initialValue: _minimumAmount.toString(),
                    decoration: const InputDecoration(
                      labelText: 'الحد الأدنى للمبلغ (ر.س)',
                      hintText: '100',
                      prefixIcon: Icon(Icons.money),
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      _minimumAmount = double.tryParse(value) ?? 100.0;
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // الحد الأقصى للمبلغ
                  TextFormField(
                    initialValue: _maximumAmount.toString(),
                    decoration: const InputDecoration(
                      labelText: 'الحد الأقصى للمبلغ (ر.س)',
                      hintText: '10000',
                      prefixIcon: Icon(Icons.money_off),
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      _maximumAmount = double.tryParse(value) ?? 10000.0;
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // تفعيل التقسيط
                  SwitchListTile(
                    title: const Text('تفعيل خيارات التقسيط'),
                    subtitle: const Text('السماح بالدفع على أقساط'),
                    value: _enableInstallments,
                    onChanged: (value) {
                      setState(() {
                        _enableInstallments = value;
                      });
                    },
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // أدوات الاختبار
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'أدوات الاختبار والمراقبة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.purple,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: (_tabbyEnabled || _tamaraEnabled) ? _testConnection : null,
                          icon: const Icon(Icons.wifi_tethering),
                          label: const Text('اختبار الاتصال'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: (_tabbyEnabled || _tamaraEnabled) ? _viewTransactionHistory : null,
                          icon: const Icon(Icons.history),
                          label: const Text('سجل المعاملات'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 16),
                  
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: (_tabbyEnabled || _tamaraEnabled) ? _testPayment : null,
                          icon: const Icon(Icons.payment),
                          label: const Text('اختبار دفعة'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: (_tabbyEnabled || _tamaraEnabled) ? _viewWebhookLogs : null,
                          icon: const Icon(Icons.webhook),
                          label: const Text('سجل Webhooks'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.indigo,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // أزرار الحفظ والإعادة
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _savePaymentSettings,
                  icon: const Icon(Icons.save),
                  label: const Text('حفظ إعدادات الدفع'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _resetPaymentSettings,
                  icon: const Icon(Icons.refresh),
                  label: const Text('إعادة تعيين'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildServiceStatusCard(String name, bool enabled, Color color, IconData icon, String description) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            name,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: const TextStyle(fontSize: 10, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: enabled ? Colors.green : Colors.red,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              enabled ? 'مفعل' : 'معطل',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _testConnection() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('اختبار الاتصال مع خدمات الدفع...'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _viewTransactionHistory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض سجل معاملات الدفع')),
    );
  }

  void _testPayment() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('إجراء اختبار دفعة تجريبية...'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _viewWebhookLogs() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض سجل Webhook')),
    );
  }

  void _savePaymentSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم حفظ إعدادات خدمات الدفع بنجاح'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _resetPaymentSettings() {
    setState(() {
      _tabbyEnabled = false;
      _tamaraEnabled = false;
      _tabbyApiKeyController.clear();
      _tabbyMerchantIdController.clear();
      _tabbySecretKeyController.clear();
      _tamaraApiKeyController.clear();
      _tamaraMerchantIdController.clear();
      _tamaraSecretKeyController.clear();
      _tabbyEnvironment = 'sandbox';
      _tamaraEnvironment = 'sandbox';
      _minimumAmount = 100.0;
      _maximumAmount = 10000.0;
      _enableInstallments = true;
    });
  }

  @override
  void dispose() {
    _tabbyApiKeyController.dispose();
    _tabbyMerchantIdController.dispose();
    _tabbySecretKeyController.dispose();
    _tamaraApiKeyController.dispose();
    _tamaraMerchantIdController.dispose();
    _tamaraSecretKeyController.dispose();
    super.dispose();
  }
}
