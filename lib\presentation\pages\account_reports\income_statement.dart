import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة قائمة الدخل
/// تعرض الإيرادات والمصروفات وصافي الربح
class IncomeStatementPage extends StatefulWidget {
  const IncomeStatementPage({super.key});

  @override
  State<IncomeStatementPage> createState() => _IncomeStatementPageState();
}

class _IncomeStatementPageState extends State<IncomeStatementPage> {
  String _selectedPeriod = 'current_month';

  // بيانات تجريبية لقائمة الدخل
  final Map<String, dynamic> _incomeStatementData = {
    'revenues': [
      {'name': 'إيرادات المبيعات', 'amount': 150000.0},
      {'name': 'إيرادات أخرى', 'amount': 5000.0},
    ],
    'expenses': [
      {'name': 'تكلفة البضاعة المباعة', 'amount': 80000.0},
      {'name': 'مصروفات الرواتب', 'amount': 35000.0},
      {'name': 'مصروفات الإيجار', 'amount': 20000.0},
      {'name': 'مصروفات التسويق', 'amount': 8000.0},
      {'name': 'مصروفات أخرى', 'amount': 3000.0},
    ],
  };

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.incomeStatement),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedPeriod,
                    decoration: const InputDecoration(
                      labelText: 'الفترة المالية',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'current_month', child: Text('الشهر الحالي')),
                      DropdownMenuItem(value: 'current_quarter', child: Text('الربع الحالي')),
                      DropdownMenuItem(value: 'current_year', child: Text('السنة الحالية')),
                      DropdownMenuItem(value: 'custom', child: Text('فترة مخصصة')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedPeriod = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                _buildStatCard('إجمالي الإيرادات', '${_getTotalRevenues()} ر.س', Colors.green),
                _buildStatCard('إجمالي المصروفات', '${_getTotalExpenses()} ر.س', Colors.red),
                _buildStatCard('صافي الربح', '${_getNetIncome()} ر.س', _getNetIncome() >= 0 ? Colors.blue : Colors.red),
                _buildStatCard('هامش الربح', '${_getProfitMargin()}%', Colors.purple),
              ],
            ),
          ),

          // قائمة الدخل
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              children: [
                // الإيرادات
                Card(
                  child: ExpansionTile(
                    leading: const CircleAvatar(
                      backgroundColor: Colors.green,
                      child: Icon(Icons.trending_up, color: Colors.white),
                    ),
                    title: const Text(
                      'الإيرادات',
                      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
                    ),
                    subtitle: Text('إجمالي: ${_getTotalRevenues()} ر.س'),
                    children: _incomeStatementData['revenues'].map<Widget>((revenue) {
                      return ListTile(
                        title: Text(revenue['name']),
                        trailing: Text(
                          '${revenue['amount'].toStringAsFixed(2)} ر.س',
                          style: const TextStyle(
                            color: Colors.green,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),

                const SizedBox(height: 8),

                // المصروفات
                Card(
                  child: ExpansionTile(
                    leading: const CircleAvatar(
                      backgroundColor: Colors.red,
                      child: Icon(Icons.trending_down, color: Colors.white),
                    ),
                    title: const Text(
                      'المصروفات',
                      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
                    ),
                    subtitle: Text('إجمالي: ${_getTotalExpenses()} ر.س'),
                    children: _incomeStatementData['expenses'].map<Widget>((expense) {
                      return ListTile(
                        title: Text(expense['name']),
                        trailing: Text(
                          '${expense['amount'].toStringAsFixed(2)} ر.س',
                          style: const TextStyle(
                            color: Colors.red,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),

                const SizedBox(height: 16),

                // صافي الربح/الخسارة
                Card(
                  color: _getNetIncome() >= 0 ? Colors.blue[50] : Colors.red[50],
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          _getNetIncome() >= 0 ? 'صافي الربح' : 'صافي الخسارة',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: _getNetIncome() >= 0 ? Colors.blue : Colors.red,
                          ),
                        ),
                        Text(
                          '${_getNetIncome().abs().toStringAsFixed(2)} ر.س',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: _getNetIncome() >= 0 ? Colors.blue : Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshData,
        backgroundColor: Colors.green,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث البيانات'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  double _getTotalRevenues() {
    return _incomeStatementData['revenues'].fold(0.0, (sum, revenue) => sum + revenue['amount']);
  }

  double _getTotalExpenses() {
    return _incomeStatementData['expenses'].fold(0.0, (sum, expense) => sum + expense['amount']);
  }

  double _getNetIncome() {
    return _getTotalRevenues() - _getTotalExpenses();
  }

  String _getProfitMargin() {
    double totalRevenues = _getTotalRevenues();
    if (totalRevenues == 0) return '0.0';
    return ((_getNetIncome() / totalRevenues) * 100).toStringAsFixed(1);
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة قائمة الدخل')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير قائمة الدخل')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث بيانات قائمة الدخل'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
