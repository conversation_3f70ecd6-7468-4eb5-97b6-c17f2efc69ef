import 'package:flutter/material.dart';

/// صفحة تقرير نسب المندوبين بأصناف معينة
/// تعرض نسب مبيعات المندوبين حسب فئات المنتجات
class RepresentativesRatiosByCategoriesPage extends StatefulWidget {
  const RepresentativesRatiosByCategoriesPage({super.key});

  @override
  State<RepresentativesRatiosByCategoriesPage> createState() => _RepresentativesRatiosByCategoriesPageState();
}

class _RepresentativesRatiosByCategoriesPageState extends State<RepresentativesRatiosByCategoriesPage> {
  String _selectedCategory = 'electronics';
  String _selectedPeriod = 'current_month';

  // بيانات تجريبية للفئات
  final Map<String, String> _categories = {
    'electronics': 'الإلكترونيات',
    'clothing': 'الملابس',
    'food': 'المواد الغذائية',
    'furniture': 'الأثاث',
    'books': 'الكتب',
  };

  // بيانات تجريبية لنسب المندوبين
  final Map<String, List<Map<String, dynamic>>> _representativesData = {
    'electronics': [
      {
        'id': 'REP001',
        'name': 'أحمد محمد',
        'totalSales': 85000.0,
        'categoryRatio': 35.2,
        'itemsSold': 125,
        'avgOrderValue': 680.0,
        'commission': 4250.0,
      },
      {
        'id': 'REP002',
        'name': 'محمد علي',
        'totalSales': 72000.0,
        'categoryRatio': 29.8,
        'itemsSold': 98,
        'avgOrderValue': 735.0,
        'commission': 3600.0,
      },
      {
        'id': 'REP003',
        'name': 'علي أحمد',
        'totalSales': 58000.0,
        'categoryRatio': 24.0,
        'itemsSold': 82,
        'avgOrderValue': 707.0,
        'commission': 2900.0,
      },
      {
        'id': 'REP004',
        'name': 'سعد عبدالله',
        'totalSales': 27000.0,
        'categoryRatio': 11.0,
        'itemsSold': 35,
        'avgOrderValue': 771.0,
        'commission': 1350.0,
      },
    ],
    'clothing': [
      {
        'id': 'REP001',
        'name': 'أحمد محمد',
        'totalSales': 45000.0,
        'categoryRatio': 28.5,
        'itemsSold': 180,
        'avgOrderValue': 250.0,
        'commission': 2250.0,
      },
      {
        'id': 'REP005',
        'name': 'فاطمة أحمد',
        'totalSales': 62000.0,
        'categoryRatio': 39.2,
        'itemsSold': 220,
        'avgOrderValue': 282.0,
        'commission': 3100.0,
      },
      {
        'id': 'REP006',
        'name': 'نورا سعد',
        'totalSales': 51000.0,
        'categoryRatio': 32.3,
        'itemsSold': 195,
        'avgOrderValue': 262.0,
        'commission': 2550.0,
      },
    ],
  };

  @override
  Widget build(BuildContext context) {
    final currentData = _representativesData[_selectedCategory] ?? [];
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير نسب المندوبين بأصناف معينة'),
        backgroundColor: Colors.deepOrange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedCategory,
                        decoration: const InputDecoration(
                          labelText: 'فئة المنتجات',
                          border: OutlineInputBorder(),
                        ),
                        items: _categories.entries.map((entry) {
                          return DropdownMenuItem(
                            value: entry.key,
                            child: Text(entry.value),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedCategory = value!;
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedPeriod,
                        decoration: const InputDecoration(
                          labelText: 'الفترة',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'current_month', child: Text('الشهر الحالي')),
                          DropdownMenuItem(value: 'current_quarter', child: Text('الربع الحالي')),
                          DropdownMenuItem(value: 'current_year', child: Text('السنة الحالية')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedPeriod = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // معلومات الفئة
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              color: Colors.deepOrange[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      'تقرير فئة: ${_categories[_selectedCategory]}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Column(
                          children: [
                            const Text('عدد المندوبين'),
                            Text(
                              currentData.length.toString(),
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('إجمالي المبيعات'),
                            Text(
                              '${_getTotalSales(currentData)} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('إجمالي القطع'),
                            Text(
                              _getTotalItems(currentData).toString(),
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.orange,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                _buildStatCard('إجمالي المبيعات', '${_getTotalSales(currentData)} ر.س', Colors.green),
                _buildStatCard('متوسط النسبة', '${_getAverageRatio(currentData)}%', Colors.blue),
                _buildStatCard('أعلى نسبة', '${_getHighestRatio(currentData)}%', Colors.orange),
                _buildStatCard('إجمالي العمولات', '${_getTotalCommissions(currentData)} ر.س', Colors.purple),
              ],
            ),
          ),

          // قائمة المندوبين
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: currentData.length,
              itemBuilder: (context, index) {
                final rep = currentData[index];
                
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: _getRatioColor(rep['categoryRatio']),
                      child: Text(
                        '${rep['categoryRatio'].toStringAsFixed(0)}%',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        ),
                      ),
                    ),
                    title: Text(
                      '${rep['id']} - ${rep['name']}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('المبيعات: ${rep['totalSales'].toStringAsFixed(2)} ر.س'),
                        Text('النسبة: ${rep['categoryRatio']}%'),
                        const SizedBox(height: 4),
                        LinearProgressIndicator(
                          value: rep['categoryRatio'] / 100,
                          backgroundColor: Colors.grey[300],
                          valueColor: AlwaysStoppedAnimation<Color>(
                            _getRatioColor(rep['categoryRatio']),
                          ),
                        ),
                      ],
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('إجمالي المبيعات', '${rep['totalSales'].toStringAsFixed(2)} ر.س', Icons.attach_money),
                                ),
                                Expanded(
                                  child: _buildDetailCard('نسبة الفئة', '${rep['categoryRatio']}%', Icons.pie_chart),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('القطع المباعة', rep['itemsSold'].toString(), Icons.inventory),
                                ),
                                Expanded(
                                  child: _buildDetailCard('متوسط الطلب', '${rep['avgOrderValue'].toStringAsFixed(2)} ر.س', Icons.calculate),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard('العمولة', '${rep['commission'].toStringAsFixed(2)} ر.س', Icons.percent),
                                ),
                                Expanded(
                                  child: _buildDetailCard('نسبة العمولة', '${((rep['commission'] / rep['totalSales']) * 100).toStringAsFixed(1)}%', Icons.trending_up),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshData,
        backgroundColor: Colors.deepOrange,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث البيانات'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailCard(String title, String value, IconData icon) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Icon(icon, size: 20, color: Colors.grey[600]),
            const SizedBox(height: 4),
            Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
            Text(
              title,
              style: const TextStyle(fontSize: 10),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Color _getRatioColor(double ratio) {
    if (ratio >= 30) return Colors.green;
    if (ratio >= 20) return Colors.orange;
    return Colors.red;
  }

  double _getTotalSales(List<Map<String, dynamic>> data) {
    return data.fold(0.0, (sum, rep) => sum + rep['totalSales']);
  }

  int _getTotalItems(List<Map<String, dynamic>> data) {
    return data.fold(0, (sum, rep) => sum + rep['itemsSold'] as int);
  }

  double _getTotalCommissions(List<Map<String, dynamic>> data) {
    return data.fold(0.0, (sum, rep) => sum + rep['commission']);
  }

  String _getAverageRatio(List<Map<String, dynamic>> data) {
    if (data.isEmpty) return '0.0';
    double total = data.fold(0.0, (sum, rep) => sum + rep['categoryRatio']);
    return (total / data.length).toStringAsFixed(1);
  }

  String _getHighestRatio(List<Map<String, dynamic>> data) {
    if (data.isEmpty) return '0.0';
    double max = data.map((rep) => rep['categoryRatio'] as double).reduce((a, b) => a > b ? a : b);
    return max.toStringAsFixed(1);
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة تقرير نسب المندوبين بالأصناف')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير تقرير نسب المندوبين بالأصناف')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث بيانات نسب المندوبين بالأصناف'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
