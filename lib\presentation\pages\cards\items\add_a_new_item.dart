import 'package:flutter/material.dart';

/// نموذج بيانات المستودع
class WarehouseData {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController location1Controller = TextEditingController();
  final TextEditingController location2Controller = TextEditingController();
  final TextEditingController location3Controller = TextEditingController();
  final TextEditingController currentQuantityController =
      TextEditingController();
  final TextEditingController initialBalanceController =
      TextEditingController();
  final TextEditingController incomingController = TextEditingController();
  final TextEditingController outgoingController = TextEditingController();
  final TextEditingController purchasesController = TextEditingController();
  final TextEditingController salesController = TextEditingController();
  final TextEditingController minLimitController = TextEditingController();
  final TextEditingController maxLimitController = TextEditingController();

  void dispose() {
    nameController.dispose();
    location1Controller.dispose();
    location2Controller.dispose();
    location3Controller.dispose();
    currentQuantityController.dispose();
    initialBalanceController.dispose();
    incomingController.dispose();
    outgoingController.dispose();
    purchasesController.dispose();
    salesController.dispose();
    minLimitController.dispose();
    maxLimitController.dispose();
  }
}

/// نموذج بيانات المورد
class SupplierData {
  final TextEditingController accountNumberController = TextEditingController();
  final TextEditingController arabicNameController = TextEditingController();
  final TextEditingController englishNameController = TextEditingController();

  void dispose() {
    accountNumberController.dispose();
    arabicNameController.dispose();
    englishNameController.dispose();
  }
}

/// نموذج بيانات الصنف التابع
class RelatedItemData {
  final TextEditingController itemCodeController = TextEditingController();
  final TextEditingController arabicNameController = TextEditingController();
  final TextEditingController quantityController = TextEditingController();

  void dispose() {
    itemCodeController.dispose();
    arabicNameController.dispose();
    quantityController.dispose();
  }
}

/// صفحة إضافة صنف جديد
class AddNewItemPage extends StatefulWidget {
  const AddNewItemPage({super.key});

  @override
  State<AddNewItemPage> createState() => _AddNewItemPageState();
}

class _AddNewItemPageState extends State<AddNewItemPage>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // متحكمات النصوص - البيانات الأساسية
  final TextEditingController _itemCodeController = TextEditingController();
  final TextEditingController _arabicNameController = TextEditingController();
  final TextEditingController _englishNameController = TextEditingController();
  final TextEditingController _localBarcodeController = TextEditingController();
  final TextEditingController _externalBarcodeController =
      TextEditingController();
  final TextEditingController _internalBarcodeController =
      TextEditingController();
  final TextEditingController _imagePathController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  // متحكمات النصوص - الوحدات
  final TextEditingController _unit1EquivalentController =
      TextEditingController();
  final TextEditingController _unit2EquivalentController =
      TextEditingController();
  final TextEditingController _itemDateController = TextEditingController();

  // متغيرات التصنيفات
  String _selectedClassification1 = '';
  String _selectedClassification2 = '';
  String _selectedClassification3 = '';
  String _selectedClassification4 = '';
  String _selectedClassification5 = '';
  String _selectedClassification6 = '';
  String _selectedClassification7 = '';
  String _selectedClassification8 = '';

  // متغيرات الوحدات
  String _selectedBasicSalesUnit = '';
  String _selectedUnit1 = '';
  String _selectedUnit2 = '';
  String _selectedUnit3 = '';
  String _selectedSalesPreference = '';
  int _selectedUnitRadio = 1;

  // متغيرات التحكم
  bool _stopItem = false;
  bool _preventPriceEdit = false;
  bool _stopItemInPurchases = false;
  bool _stopItemInSales = false;
  bool _stopItemInTransfers = false;
  bool _compositeItem = false;

  // قائمة المستودعات
  final List<WarehouseData> _warehouses = [
    WarehouseData(), // مستودع افتراضي واحد
  ];

  // قائمة الموردين
  final List<SupplierData> _suppliers = [
    SupplierData(), // مورد افتراضي واحد
  ];

  // قائمة الأصناف التابعة
  final List<RelatedItemData> _relatedItems = [
    RelatedItemData(), // صنف تابع افتراضي واحد
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 8, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    // تنظيف متحكمات النصوص
    _itemCodeController.dispose();
    _arabicNameController.dispose();
    _englishNameController.dispose();
    _localBarcodeController.dispose();
    _externalBarcodeController.dispose();
    _internalBarcodeController.dispose();
    _imagePathController.dispose();
    _notesController.dispose();
    _unit1EquivalentController.dispose();
    _unit2EquivalentController.dispose();
    _itemDateController.dispose();
    // تنظيف متحكمات المستودعات
    for (var warehouse in _warehouses) {
      warehouse.dispose();
    }
    // تنظيف متحكمات الموردين
    for (var supplier in _suppliers) {
      supplier.dispose();
    }
    // تنظيف متحكمات الأصناف التابعة
    for (var relatedItem in _relatedItems) {
      relatedItem.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إضافة صنف جديد'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          // أزرار الأدوات في AppBar
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveItem,
            tooltip: 'حفظ',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _newItem,
            tooltip: 'جديد',
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: _deleteItem,
            tooltip: 'حذف',
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _searchItem,
            tooltip: 'بحث',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط التنقل المحسن
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              border: Border(
                bottom: BorderSide(color: Colors.grey[300]!),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Row(
              children: [
                // مؤشر التبويب الحالي
                Expanded(
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.green[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.green[200]!),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _getTabIcon(_tabController.index),
                          color: Colors.green[700],
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _getTabName(_tabController.index),
                            style: TextStyle(
                              color: Colors.green[700],
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Text(
                          '${_tabController.index + 1}/8',
                          style: TextStyle(
                            color: Colors.green[600],
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 16),

                // أزرار التنقل
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildNavigationButton(
                      icon: Icons.first_page,
                      tooltip: 'التبويب الأول',
                      onPressed: _tabController.index > 0 ? _goToFirst : null,
                      isEnabled: _tabController.index > 0,
                    ),
                    const SizedBox(width: 4),
                    _buildNavigationButton(
                      icon: Icons.chevron_left,
                      tooltip: 'التبويب السابق',
                      onPressed:
                          _tabController.index > 0 ? _goToPrevious : null,
                      isEnabled: _tabController.index > 0,
                    ),
                    const SizedBox(width: 4),
                    _buildNavigationButton(
                      icon: Icons.chevron_right,
                      tooltip: 'التبويب التالي',
                      onPressed: _tabController.index < 7 ? _goToNext : null,
                      isEnabled: _tabController.index < 7,
                    ),
                    const SizedBox(width: 4),
                    _buildNavigationButton(
                      icon: Icons.last_page,
                      tooltip: 'التبويب الأخير',
                      onPressed: _tabController.index < 7 ? _goToLast : null,
                      isEnabled: _tabController.index < 7,
                    ),
                  ],
                ),
              ],
            ),
          ),
          // التبويبات
          TabBar(
            controller: _tabController,
            labelColor: Colors.green,
            unselectedLabelColor: Colors.grey,
            indicatorColor: Colors.green,
            isScrollable: true,
            tabs: const [
              Tab(text: 'البيانات الأساسية'),
              Tab(text: 'التصنيفات'),
              Tab(text: 'الوحدات'),
              Tab(text: 'المستودعات'),
              Tab(text: 'الفروع'),
              Tab(text: 'الموردين'),
              Tab(text: 'أصناف تابعة'),
              Tab(text: 'صلاحيات الصنف'),
            ],
          ),
          // محتوى التبويبات
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildBasicDataTab(),
                _buildClassificationsTab(),
                _buildUnitsTab(),
                _buildWarehousesTab(),
                _buildBranchesTab(),
                _buildSuppliersTab(),
                _buildRelatedItemsTab(),
                _buildItemPermissionsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // دوال الأدوات
  void _saveItem() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم حفظ الصنف بنجاح')),
    );
  }

  void _newItem() {
    setState(() {
      // مسح جميع الحقول
      _itemCodeController.clear();
      _arabicNameController.clear();
      _englishNameController.clear();
      _localBarcodeController.clear();
      _externalBarcodeController.clear();
      _internalBarcodeController.clear();
      _imagePathController.clear();
      _notesController.clear();
      _unit1EquivalentController.clear();
      _unit2EquivalentController.clear();
      _itemDateController.clear();
    });
  }

  void _deleteItem() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل تريد حذف هذا الصنف؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم حذف الصنف')),
              );
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _searchItem() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('وظيفة البحث')),
    );
  }

  // دوال التنقل المحسنة
  void _goToFirst() {
    setState(() {
      _tabController.animateTo(0);
    });
  }

  void _goToPrevious() {
    if (_tabController.index > 0) {
      setState(() {
        _tabController.animateTo(_tabController.index - 1);
      });
    }
  }

  void _goToNext() {
    if (_tabController.index < 7) {
      setState(() {
        _tabController.animateTo(_tabController.index + 1);
      });
    }
  }

  void _goToLast() {
    setState(() {
      _tabController.animateTo(7);
    });
  }

  // دوال مساعدة للتنقل
  IconData _getTabIcon(int index) {
    switch (index) {
      case 0:
        return Icons.info_outline;
      case 1:
        return Icons.category_outlined;
      case 2:
        return Icons.straighten_outlined;
      case 3:
        return Icons.warehouse_outlined;
      case 4:
        return Icons.store_outlined;
      case 5:
        return Icons.local_shipping_outlined;
      case 6:
        return Icons.inventory_2_outlined;
      case 7:
        return Icons.security_outlined;
      default:
        return Icons.tab;
    }
  }

  String _getTabName(int index) {
    switch (index) {
      case 0:
        return 'البيانات الأساسية';
      case 1:
        return 'التصنيفات';
      case 2:
        return 'الوحدات';
      case 3:
        return 'المستودعات';
      case 4:
        return 'الفروع';
      case 5:
        return 'الموردين';
      case 6:
        return 'أصناف تابعة';
      case 7:
        return 'صلاحيات الصنف';
      default:
        return 'تبويب';
    }
  }

  Widget _buildNavigationButton({
    required IconData icon,
    required String tooltip,
    required VoidCallback? onPressed,
    required bool isEnabled,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6),
        color: isEnabled ? Colors.green[50] : Colors.grey[100],
        border: Border.all(
          color: isEnabled ? Colors.green[200]! : Colors.grey[300]!,
        ),
      ),
      child: IconButton(
        icon: Icon(
          icon,
          size: 20,
          color: isEnabled ? Colors.green[700] : Colors.grey[400],
        ),
        onPressed: onPressed,
        tooltip: tooltip,
        padding: const EdgeInsets.all(8),
        constraints: const BoxConstraints(
          minWidth: 40,
          minHeight: 40,
        ),
      ),
    );
  }

  // بناء التبويبات
  Widget _buildBasicDataTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // منطقة الصورة
          Center(
            child: Container(
              width: 150,
              height: 150,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.image_not_supported, size: 50, color: Colors.grey),
                  Text('NO IMAGE FOUND', style: TextStyle(color: Colors.grey)),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          // رقم الصنف
          _buildDropdownField('رقم الصنف', '', [
            'AUTO001',
            'AUTO002',
            'AUTO003',
            'AUTO004',
            'AUTO005',
          ], (value) {
            setState(() {
              _itemCodeController.text = value ?? '';
            });
          }),
          const SizedBox(height: 16),

          // الأسماء
          Row(
            children: [
              Expanded(
                child: _buildTextField('الاسم العربي', _arabicNameController),
              ),
              const SizedBox(width: 16),
              Expanded(
                child:
                    _buildTextField('الاسم الإنجليزي', _englishNameController),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // الباركودات
          _buildTextField('باركود محلي', _localBarcodeController),
          const SizedBox(height: 16),
          _buildTextField('باركود الخارجي للكرتون', _externalBarcodeController),
          const SizedBox(height: 16),
          _buildTextField('باركود الداخلي لصنف', _internalBarcodeController),
          const SizedBox(height: 16),

          // ملف الصورة
          _buildTextField('ملف الصورة', _imagePathController),
          const SizedBox(height: 16),

          // ملاحظات
          _buildTextField('ملاحظات', _notesController, maxLines: 3),
        ],
      ),
    );
  }

  Widget _buildClassificationsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          const Text(
            'التصنيفات',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),

          // التصنيفات الثمانية
          for (int i = 1; i <= 8; i++) ...[
            _buildDropdownField(
              _getClassificationLabel(i),
              _getClassificationValue(i),
              _getClassificationOptions(i),
              (value) => _setClassificationValue(i, value ?? ''),
            ),
            const SizedBox(height: 16),
          ],
        ],
      ),
    );
  }

  Widget _buildUnitsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'الوحدات',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),

          // وحدة أساسية البيع
          _buildDropdownField('وحدة أساسية البيع', _selectedBasicSalesUnit, [
            'قطعة',
            'كيلو',
            'متر',
            'لتر',
            'علبة',
          ], (value) {
            setState(() {
              _selectedBasicSalesUnit = value ?? '';
            });
          }),
          const SizedBox(height: 20),

          // الوحدة 1
          Row(
            children: [
              Expanded(
                flex: 2,
                child: _buildDropdownField('الوحدة 1', _selectedUnit1, [
                  'قطعة',
                  'كيلو',
                  'متر',
                  'لتر',
                ], (value) {
                  setState(() {
                    _selectedUnit1 = value ?? '';
                  });
                }),
              ),
              const SizedBox(width: 8),
              const Text('تعادل'),
              const SizedBox(width: 8),
              Expanded(
                child: _buildTextField('', _unit1EquivalentController),
              ),
              const SizedBox(width: 8),
              Radio<int>(
                value: 1,
                groupValue: _selectedUnitRadio,
                onChanged: (value) {
                  setState(() {
                    _selectedUnitRadio = value!;
                  });
                },
              ),
            ],
          ),
          const SizedBox(height: 16),

          // الوحدة 2
          Row(
            children: [
              Expanded(
                flex: 2,
                child: _buildDropdownField('الوحدة 2', _selectedUnit2, [
                  'قطعة',
                  'كيلو',
                  'متر',
                  'لتر',
                ], (value) {
                  setState(() {
                    _selectedUnit2 = value ?? '';
                  });
                }),
              ),
              const SizedBox(width: 8),
              const Text('تعادل'),
              const SizedBox(width: 8),
              Expanded(
                child: _buildTextField('', _unit2EquivalentController),
              ),
              const SizedBox(width: 8),
              Radio<int>(
                value: 2,
                groupValue: _selectedUnitRadio,
                onChanged: (value) {
                  setState(() {
                    _selectedUnitRadio = value!;
                  });
                },
              ),
            ],
          ),
          const SizedBox(height: 16),

          // الوحدة 3
          Row(
            children: [
              Expanded(
                flex: 2,
                child: _buildDropdownField('الوحدة 3', _selectedUnit3, [
                  'قطعة',
                  'كيلو',
                  'متر',
                  'لتر',
                ], (value) {
                  setState(() {
                    _selectedUnit3 = value ?? '';
                  });
                }),
              ),
              const Spacer(),
              Radio<int>(
                value: 3,
                groupValue: _selectedUnitRadio,
                onChanged: (value) {
                  setState(() {
                    _selectedUnitRadio = value!;
                  });
                },
              ),
            ],
          ),
          const SizedBox(height: 20),

          // تاريخ الصنف
          _buildTextField('تاريخ الصنف', _itemDateController),
          const SizedBox(height: 16),

          // صنف مجمع
          CheckboxListTile(
            title: const Text('صنف مجمع'),
            value: _compositeItem,
            onChanged: (value) {
              setState(() {
                _compositeItem = value ?? false;
              });
            },
          ),
          const SizedBox(height: 16),

          // أفضلية البيع
          _buildDropdownField('أفضلية البيع', _selectedSalesPreference, [
            'أفضلية عالية',
            'أفضلية متوسطة',
            'أفضلية منخفضة',
          ], (value) {
            setState(() {
              _selectedSalesPreference = value ?? '';
            });
          }),
        ],
      ),
    );
  }

  Widget _buildWarehousesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان مع زر الإضافة
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'المستودعات',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              ElevatedButton.icon(
                onPressed: _addNewWarehouse,
                icon: const Icon(Icons.add),
                label: const Text('إضافة مستودع'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // قائمة المستودعات
          ..._buildWarehouseCards(),
        ],
      ),
    );
  }

  List<Widget> _buildWarehouseCards() {
    return _warehouses.asMap().entries.map((entry) {
      int index = entry.key;
      WarehouseData warehouse = entry.value;

      return Card(
        margin: const EdgeInsets.only(bottom: 16),
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس الكرت مع زر الحذف
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'مستودع ${index + 1}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                  if (_warehouses.length > 1)
                    IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      onPressed: () => _removeWarehouse(index),
                      tooltip: 'حذف المستودع',
                    ),
                ],
              ),
              const SizedBox(height: 16),

              // اسم المستودع
              _buildTextField('اسم المستودع', warehouse.nameController),
              const SizedBox(height: 16),

              // المواقع الثلاثة
              const Text(
                'المواقع',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: _buildTextField(
                        'موقع 1', warehouse.location1Controller),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildTextField(
                        'موقع 2', warehouse.location2Controller),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildTextField(
                        'موقع 3', warehouse.location3Controller),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // الكميات والأرصدة
              const Text(
                'الكميات والأرصدة',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: _buildTextField(
                        'الكمية الحالية', warehouse.currentQuantityController),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildTextField(
                        'رصيد البداية', warehouse.initialBalanceController),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // الحركات
              const Text(
                'الحركات',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child:
                        _buildTextField('وارد', warehouse.incomingController),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child:
                        _buildTextField('منصرف', warehouse.outgoingController),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildTextField(
                        'مشتريات', warehouse.purchasesController),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildTextField('مبيعات', warehouse.salesController),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // الحدود
              const Text(
                'الحدود',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: _buildTextField(
                        'حد أدنى', warehouse.minLimitController),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildTextField(
                        'حد أعلى', warehouse.maxLimitController),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    }).toList();
  }

  /// إضافة مستودع جديد
  void _addNewWarehouse() {
    setState(() {
      _warehouses.add(WarehouseData());
    });
  }

  /// حذف مستودع
  void _removeWarehouse(int index) {
    if (_warehouses.length > 1) {
      setState(() {
        _warehouses[index].dispose();
        _warehouses.removeAt(index);
      });
    }
  }

  Widget _buildBranchesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'الفروع',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),

          // معلومات الفرع الأساسية
          Row(
            children: [
              Expanded(
                child: _buildTextField('رقم الفرع', TextEditingController()),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTextField('اسم الفرع', TextEditingController()),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // التكاليف والأرصدة
          Row(
            children: [
              Expanded(
                child:
                    _buildTextField('تكلفة أول المدة', TextEditingController()),
              ),
              const SizedBox(width: 16),
              Expanded(
                child:
                    _buildTextField('متوسط التكلفة', TextEditingController()),
              ),
            ],
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildTextField('أعلى رصيد', TextEditingController()),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTextField('أدنى رصيد', TextEditingController()),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // الأسعار الأربعة
          const Text(
            'الأسعار',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildTextField('سعر أ', TextEditingController()),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTextField('سعر ب', TextEditingController()),
              ),
            ],
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildTextField('سعر ج', TextEditingController()),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTextField('سعر د', TextEditingController()),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // الخصومات
          Row(
            children: [
              Expanded(
                child:
                    _buildTextField('الخصم الافتراضي', TextEditingController()),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTextField(
                    'الحد الأعلى للخصم', TextEditingController()),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // التكاليف الأخيرة
          Row(
            children: [
              Expanded(
                child: _buildTextField('آخر تكلفة', TextEditingController()),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTextField(
                    'آخر تكلفة (عملة)', TextEditingController()),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // الطلبات
          _buildTextField('طلبات مثبتة', TextEditingController()),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildTextField(
                    'نوتة الطلبات خارجي', TextEditingController()),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTextField(
                    'نوتة الطلبات داخلي', TextEditingController()),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // كمية سعر الجملة
          _buildTextField('كمية سعر الجملة', TextEditingController()),
          const SizedBox(height: 16),

          // الباركود مع checkbox
          Row(
            children: [
              Expanded(
                child: _buildTextField('باركود', TextEditingController()),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: CheckboxListTile(
                  title: const Text('تفعيل الباركود'),
                  value: false,
                  onChanged: (value) {},
                  dense: true,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // الكميات والأسعار الأخيرة
          Row(
            children: [
              Expanded(
                child: _buildTextField('كمية محجوزة', TextEditingController()),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTextField('سعر المنافس', TextEditingController()),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // حد المباع
          _buildTextField('حد المباع', TextEditingController()),
        ],
      ),
    );
  }

  Widget _buildSuppliersTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان مع زر الإضافة
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'الموردين',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              ElevatedButton.icon(
                onPressed: _addNewSupplier,
                icon: const Icon(Icons.add),
                label: const Text('إضافة مورد'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // قائمة الموردين
          ..._buildSupplierCards(),
        ],
      ),
    );
  }

  List<Widget> _buildSupplierCards() {
    return _suppliers.asMap().entries.map((entry) {
      int index = entry.key;
      SupplierData supplier = entry.value;

      return Card(
        margin: const EdgeInsets.only(bottom: 16),
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس الكرت مع زر الحذف
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'مورد ${index + 1}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                  if (_suppliers.length > 1)
                    IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      onPressed: () => _removeSupplier(index),
                      tooltip: 'حذف المورد',
                    ),
                ],
              ),
              const SizedBox(height: 16),

              // رقم الحساب
              _buildTextField('رقم الحساب', supplier.accountNumberController),
              const SizedBox(height: 16),

              // الأسماء
              Row(
                children: [
                  Expanded(
                    child: _buildTextField(
                        'اسم الحساب عربي', supplier.arabicNameController),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildTextField(
                        'اسم الحساب En', supplier.englishNameController),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    }).toList();
  }

  /// إضافة مورد جديد
  void _addNewSupplier() {
    setState(() {
      _suppliers.add(SupplierData());
    });
  }

  /// حذف مورد
  void _removeSupplier(int index) {
    if (_suppliers.length > 1) {
      setState(() {
        _suppliers[index].dispose();
        _suppliers.removeAt(index);
      });
    }
  }

  Widget _buildRelatedItemsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان مع زر الإضافة
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'الأصناف التابعة',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              ElevatedButton.icon(
                onPressed: _addNewRelatedItem,
                icon: const Icon(Icons.add),
                label: const Text('إضافة صنف تابع'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // قائمة الأصناف التابعة
          ..._buildRelatedItemCards(),
        ],
      ),
    );
  }

  List<Widget> _buildRelatedItemCards() {
    return _relatedItems.asMap().entries.map((entry) {
      int index = entry.key;
      RelatedItemData relatedItem = entry.value;

      return Card(
        margin: const EdgeInsets.only(bottom: 16),
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس الكرت مع زر الحذف
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'صنف تابع ${index + 1}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                  if (_relatedItems.length > 1)
                    IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      onPressed: () => _removeRelatedItem(index),
                      tooltip: 'حذف الصنف التابع',
                    ),
                ],
              ),
              const SizedBox(height: 16),

              // كود الصنف التابع
              _buildTextField('الصنف التابع', relatedItem.itemCodeController),
              const SizedBox(height: 16),

              // الاسم والكمية
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: _buildTextField(
                        'الاسم العربي', relatedItem.arabicNameController),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildTextField(
                        'الكمية', relatedItem.quantityController),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    }).toList();
  }

  /// إضافة صنف تابع جديد
  void _addNewRelatedItem() {
    setState(() {
      _relatedItems.add(RelatedItemData());
    });
  }

  /// حذف صنف تابع
  void _removeRelatedItem(int index) {
    if (_relatedItems.length > 1) {
      setState(() {
        _relatedItems[index].dispose();
        _relatedItems.removeAt(index);
      });
    }
  }

  Widget _buildItemPermissionsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'صلاحيات الصنف',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),

          // كرت صلاحيات التحكم
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'صلاحيات التحكم في الصنف',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // إيقاف الصنف
                  CheckboxListTile(
                    title: const Text('إيقاف الصنف'),
                    subtitle:
                        const Text('منع استخدام هذا الصنف في جميع العمليات'),
                    value: _stopItem,
                    onChanged: (value) {
                      setState(() {
                        _stopItem = value ?? false;
                      });
                    },
                    activeColor: Colors.green,
                  ),
                  const Divider(),

                  // منع تعديل الأسعار
                  CheckboxListTile(
                    title: const Text('منع تعديل الأسعار'),
                    subtitle: const Text(
                        'منع تعديل أسعار هذا الصنف من قبل المستخدمين'),
                    value: _preventPriceEdit,
                    onChanged: (value) {
                      setState(() {
                        _preventPriceEdit = value ?? false;
                      });
                    },
                    activeColor: Colors.green,
                  ),
                  const Divider(),

                  // إيقاف الصنف في المشتريات
                  CheckboxListTile(
                    title: const Text('إيقاف الصنف في المشتريات'),
                    subtitle: const Text(
                        'منع استخدام هذا الصنف في عمليات الشراء فقط'),
                    value: _stopItemInPurchases,
                    onChanged: (value) {
                      setState(() {
                        _stopItemInPurchases = value ?? false;
                      });
                    },
                    activeColor: Colors.green,
                  ),
                  const Divider(),

                  // إيقاف الصنف في المبيعات
                  CheckboxListTile(
                    title: const Text('إيقاف الصنف في المبيعات'),
                    subtitle:
                        const Text('منع استخدام هذا الصنف في عمليات البيع فقط'),
                    value: _stopItemInSales,
                    onChanged: (value) {
                      setState(() {
                        _stopItemInSales = value ?? false;
                      });
                    },
                    activeColor: Colors.green,
                  ),
                  const Divider(),

                  // إيقاف الصنف من تحويل بين الفروع
                  CheckboxListTile(
                    title: const Text('إيقاف الصنف من تحويل بين الفروع'),
                    subtitle: const Text(
                        'منع تحويل هذا الصنف بين الفروع والمستودعات'),
                    value: _stopItemInTransfers,
                    onChanged: (value) {
                      setState(() {
                        _stopItemInTransfers = value ?? false;
                      });
                    },
                    activeColor: Colors.green,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          // كرت معلومات إضافية
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'معلومات الصلاحيات',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.blue, size: 20),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'إيقاف الصنف: يمنع استخدام الصنف في جميع العمليات (مبيعات، مشتريات، تحويلات)',
                          style: TextStyle(fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const Row(
                    children: [
                      Icon(Icons.lock_outline, color: Colors.orange, size: 20),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'منع تعديل الأسعار: يحمي أسعار الصنف من التعديل غير المصرح به',
                          style: TextStyle(fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const Row(
                    children: [
                      Icon(Icons.shopping_cart_outlined,
                          color: Colors.red, size: 20),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'إيقاف في المشتريات: يمنع شراء الصنف مع السماح ببيعه',
                          style: TextStyle(fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const Row(
                    children: [
                      Icon(Icons.point_of_sale_outlined,
                          color: Colors.purple, size: 20),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'إيقاف في المبيعات: يمنع بيع الصنف مع السماح بشرائه',
                          style: TextStyle(fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const Row(
                    children: [
                      Icon(Icons.transfer_within_a_station_outlined,
                          color: Colors.teal, size: 20),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'إيقاف التحويل: يمنع تحويل الصنف بين الفروع والمستودعات',
                          style: TextStyle(fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // دوال مساعدة للتصنيفات
  String _getClassificationLabel(int index) {
    const labels = ['1-أ', '2-ب', '3-ج', '4-د', '5-ه', '6-و', '7-ز', '8-ح'];
    return labels[index - 1];
  }

  String _getClassificationValue(int index) {
    switch (index) {
      case 1:
        return _selectedClassification1;
      case 2:
        return _selectedClassification2;
      case 3:
        return _selectedClassification3;
      case 4:
        return _selectedClassification4;
      case 5:
        return _selectedClassification5;
      case 6:
        return _selectedClassification6;
      case 7:
        return _selectedClassification7;
      case 8:
        return _selectedClassification8;
      default:
        return '';
    }
  }

  List<String> _getClassificationOptions(int index) {
    return [
      'تصنيف ${_getClassificationLabel(index)} - 1',
      'تصنيف ${_getClassificationLabel(index)} - 2',
      'تصنيف ${_getClassificationLabel(index)} - 3',
      'تصنيف ${_getClassificationLabel(index)} - 4',
    ];
  }

  void _setClassificationValue(int index, String value) {
    setState(() {
      switch (index) {
        case 1:
          _selectedClassification1 = value;
          break;
        case 2:
          _selectedClassification2 = value;
          break;
        case 3:
          _selectedClassification3 = value;
          break;
        case 4:
          _selectedClassification4 = value;
          break;
        case 5:
          _selectedClassification5 = value;
          break;
        case 6:
          _selectedClassification6 = value;
          break;
        case 7:
          _selectedClassification7 = value;
          break;
        case 8:
          _selectedClassification8 = value;
          break;
      }
    });
  }

  // دوال بناء العناصر
  Widget _buildTextField(String label, TextEditingController controller,
      {int maxLines = 1}) {
    return TextField(
      controller: controller,
      maxLines: maxLines,
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
    );
  }

  Widget _buildDropdownField(String label, String value, List<String> items,
      ValueChanged<String?> onChanged) {
    return DropdownButtonFormField<String>(
      value: value.isEmpty ? null : value,
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      items: items.map((String item) {
        return DropdownMenuItem<String>(
          value: item,
          child: Text(item),
        );
      }).toList(),
      onChanged: onChanged,
    );
  }
}
