import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة طباعة الفواتير من المستودع
/// يعرض ويطبع فواتير المستودع
class PrintWarehouseInvoicesReportPage extends StatefulWidget {
  const PrintWarehouseInvoicesReportPage({super.key});

  @override
  State<PrintWarehouseInvoicesReportPage> createState() =>
      _PrintWarehouseInvoicesReportPageState();
}

class _PrintWarehouseInvoicesReportPageState
    extends State<PrintWarehouseInvoicesReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedWarehouse;
  String? _invoiceType = 'all';
  String? _invoiceStatus = 'all';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('طباعة الفواتير من المستودع'),
        backgroundColor: Colors.deepOrange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printAllInvoices,
            tooltip: 'طباعة جميع الفواتير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportInvoices,
            tooltip: 'تصدير الفواتير',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _printSettings,
            tooltip: 'إعدادات الطباعة',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.deepOrange[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.fromDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, true),
                        controller: TextEditingController(
                          text: _startDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.toDate,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context, false),
                        controller: TextEditingController(
                          text: _endDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.warehouse,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedWarehouse,
                        items: [
                          DropdownMenuItem(
                              value: 'all',
                              child: Text(localizations.allWarehouses)),
                          DropdownMenuItem(
                              value: 'main',
                              child: Text(localizations.mainWarehouse)),
                          DropdownMenuItem(
                              value: 'branch1',
                              child: Text(
                                  '${localizations.branchWarehouse} الأول')),
                          DropdownMenuItem(
                              value: 'branch2',
                              child: Text(
                                  '${localizations.branchWarehouse} الثاني')),
                        ],
                        onChanged: (value) =>
                            setState(() => _selectedWarehouse = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'نوع الفاتورة',
                          border: OutlineInputBorder(),
                        ),
                        value: _invoiceType,
                        items: const [
                          DropdownMenuItem(
                              value: 'all', child: Text('جميع الأنواع')),
                          DropdownMenuItem(
                              value: 'sales', child: Text('فواتير مبيعات')),
                          DropdownMenuItem(
                              value: 'purchase', child: Text('فواتير مشتريات')),
                          DropdownMenuItem(
                              value: 'return', child: Text('فواتير مرتجعات')),
                          DropdownMenuItem(
                              value: 'transfer', child: Text('فواتير نقل')),
                        ],
                        onChanged: (value) =>
                            setState(() => _invoiceType = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'حالة الفاتورة',
                          border: OutlineInputBorder(),
                        ),
                        value: _invoiceStatus,
                        items: const [
                          DropdownMenuItem(
                              value: 'all', child: Text('جميع الحالات')),
                          DropdownMenuItem(
                              value: 'draft', child: Text('مسودة')),
                          DropdownMenuItem(
                              value: 'confirmed', child: Text('مؤكدة')),
                          DropdownMenuItem(
                              value: 'printed', child: Text('مطبوعة')),
                          DropdownMenuItem(
                              value: 'cancelled', child: Text('ملغية')),
                        ],
                        onChanged: (value) =>
                            setState(() => _invoiceStatus = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _loadInvoices,
                        icon: const Icon(Icons.search),
                        label: const Text('تحميل الفواتير'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.deepOrange,
                          foregroundColor: Colors.white,
                          minimumSize: const Size(double.infinity, 48),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // محتوى الفواتير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص الفواتير
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.receipt_long,
                                  color: Colors.deepOrange, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'ملخص الفواتير',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSummaryCard('إجمالي الفواتير', '485',
                                  Colors.blue, Icons.receipt),
                              _buildSummaryCard(
                                  'مطبوعة', '320', Colors.green, Icons.print),
                              _buildSummaryCard('غير مطبوعة', '165',
                                  Colors.orange, Icons.print_disabled),
                              _buildSummaryCard(
                                  'إجمالي القيمة',
                                  '2,850,000 ر.س',
                                  Colors.purple,
                                  Icons.monetization_on),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // قائمة الفواتير للطباعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Text(
                                'قائمة الفواتير للطباعة',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const Spacer(),
                              Checkbox(
                                value: false,
                                onChanged: (value) => _selectAllInvoices(),
                              ),
                              const Text('تحديد الكل'),
                            ],
                          ),
                          const SizedBox(height: 16),
                          ..._buildInvoicesList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول تفاصيل الفواتير
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل الفواتير',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('رقم الفاتورة')),
                                DataColumn(label: Text('التاريخ')),
                                DataColumn(label: Text('النوع')),
                                DataColumn(label: Text('العميل/المورد')),
                                DataColumn(label: Text('المستودع')),
                                DataColumn(label: Text('القيمة')),
                                DataColumn(label: Text('الحالة')),
                                DataColumn(label: Text('مرات الطباعة')),
                                DataColumn(label: Text('إجراءات')),
                              ],
                              rows: _buildInvoiceRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إعدادات الطباعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.print_outlined,
                                  color: Colors.indigo, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'إعدادات الطباعة',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildPrintSettingCard(
                                  'طباعة أصلية',
                                  'نسخة أصلية للعميل',
                                  Icons.description,
                                  Colors.blue),
                              _buildPrintSettingCard(
                                  'طباعة صورة',
                                  'نسخة صورة للأرشيف',
                                  Icons.copy,
                                  Colors.green),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              _buildPrintSettingCard(
                                  'مع الشعار',
                                  'إضافة شعار الشركة',
                                  Icons.image,
                                  Colors.orange),
                              _buildPrintSettingCard(
                                  'مع التوقيع',
                                  'إضافة مساحة للتوقيع',
                                  Icons.edit,
                                  Colors.purple),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إحصائيات الطباعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.bar_chart,
                                  color: Colors.green, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'إحصائيات الطباعة',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildStatCard('اليوم', '45 فاتورة', Colors.blue),
                              _buildStatCard(
                                  'هذا الأسبوع', '285 فاتورة', Colors.green),
                              _buildStatCard(
                                  'هذا الشهر', '1,245 فاتورة', Colors.orange),
                              _buildStatCard('إجمالي السنة', '15,680 فاتورة',
                                  Colors.purple),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _printSelected,
                                  icon: const Icon(Icons.print),
                                  label: const Text('طباعة المحدد'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _batchPrint,
                                  icon: const Icon(Icons.print_outlined),
                                  label: const Text('طباعة مجمعة'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _emailInvoices,
                                  icon: const Icon(Icons.email),
                                  label: const Text('إرسال بالإيميل'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _generatePDF,
                                  icon: const Icon(Icons.picture_as_pdf),
                                  label: const Text('تحويل لـ PDF'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildInvoicesList() {
    final invoices = [
      {
        'id': 'INV-2024-001',
        'type': 'مبيعات',
        'customer': 'شركة الأمل التجارية',
        'value': '125,000 ر.س',
        'status': 'مؤكدة'
      },
      {
        'id': 'INV-2024-002',
        'type': 'مشتريات',
        'customer': 'مؤسسة الجودة العالمية',
        'value': '85,000 ر.س',
        'status': 'مطبوعة'
      },
      {
        'id': 'INV-2024-003',
        'type': 'مبيعات',
        'customer': 'شركة الفجر الجديد',
        'value': '65,000 ر.س',
        'status': 'مسودة'
      },
      {
        'id': 'INV-2024-004',
        'type': 'نقل',
        'customer': 'نقل داخلي',
        'value': '45,000 ر.س',
        'status': 'مؤكدة'
      },
    ];

    return invoices
        .map((invoice) => Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.deepOrange.withOpacity(0.3)),
                borderRadius: BorderRadius.circular(8),
                color: Colors.deepOrange.withOpacity(0.05),
              ),
              child: Row(
                children: [
                  Checkbox(
                    value: false,
                    onChanged: (value) {},
                  ),
                  const SizedBox(width: 12),
                  const Icon(Icons.receipt_long,
                      color: Colors.deepOrange, size: 24),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          invoice['id']!,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '${invoice['type']} • ${invoice['customer']} • ${invoice['value']}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildStatusBadge(invoice['status']!),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.print, color: Colors.blue),
                    onPressed: () => _printSingleInvoice(invoice['id']!),
                    tooltip: 'طباعة',
                  ),
                ],
              ),
            ))
        .toList();
  }

  List<DataRow> _buildInvoiceRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('INV-2024-001')),
        const DataCell(Text('2024-02-15')),
        const DataCell(Text('مبيعات')),
        const DataCell(Text('شركة الأمل التجارية')),
        const DataCell(Text('المستودع الرئيسي')),
        const DataCell(Text('125,000 ر.س')),
        DataCell(_buildStatusBadge('مؤكدة')),
        const DataCell(Text('2')),
        DataCell(Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.print, color: Colors.blue, size: 16),
              onPressed: () => _printSingleInvoice('INV-2024-001'),
            ),
            IconButton(
              icon: const Icon(Icons.email, color: Colors.green, size: 16),
              onPressed: () => _emailSingleInvoice('INV-2024-001'),
            ),
          ],
        )),
      ]),
      DataRow(cells: [
        const DataCell(Text('INV-2024-002')),
        const DataCell(Text('2024-02-14')),
        const DataCell(Text('مشتريات')),
        const DataCell(Text('مؤسسة الجودة العالمية')),
        const DataCell(Text('الفرع الأول')),
        const DataCell(Text('85,000 ر.س')),
        DataCell(_buildStatusBadge('مطبوعة')),
        const DataCell(Text('1')),
        DataCell(Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.print, color: Colors.blue, size: 16),
              onPressed: () => _printSingleInvoice('INV-2024-002'),
            ),
            IconButton(
              icon: const Icon(Icons.email, color: Colors.green, size: 16),
              onPressed: () => _emailSingleInvoice('INV-2024-002'),
            ),
          ],
        )),
      ]),
    ];
  }

  Widget _buildSummaryCard(
      String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPrintSettingCard(
      String title, String description, IconData icon, Color color) {
    return Expanded(
      child: Card(
        child: InkWell(
          onTap: () => _selectPrintSetting(title),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Icon(icon, color: color, size: 32),
                const SizedBox(height: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(fontSize: 10),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatCard(String period, String count, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                period,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status) {
    Color color;
    switch (status) {
      case 'مؤكدة':
        color = Colors.green;
        break;
      case 'مطبوعة':
        color = Colors.blue;
        break;
      case 'مسودة':
        color = Colors.orange;
        break;
      case 'ملغية':
        color = Colors.red;
        break;
      default:
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        status,
        style:
            TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _loadInvoices() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تحميل الفواتير بنجاح')),
    );
  }

  void _printAllInvoices() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة جميع الفواتير...')),
    );
  }

  void _exportInvoices() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير الفواتير...')),
    );
  }

  void _printSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('فتح إعدادات الطباعة')),
    );
  }

  void _selectAllInvoices() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تحديد جميع الفواتير')),
    );
  }

  void _printSelected() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة الفواتير المحددة')),
    );
  }

  void _batchPrint() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة مجمعة للفواتير')),
    );
  }

  void _emailInvoices() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إرسال الفواتير بالإيميل')),
    );
  }

  void _generatePDF() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحويل الفواتير إلى PDF')),
    );
  }

  void _printSingleInvoice(String invoiceId) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('طباعة الفاتورة $invoiceId')),
    );
  }

  void _emailSingleInvoice(String invoiceId) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('إرسال الفاتورة $invoiceId بالإيميل')),
    );
  }

  void _selectPrintSetting(String setting) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم اختيار إعداد: $setting')),
    );
  }
}
