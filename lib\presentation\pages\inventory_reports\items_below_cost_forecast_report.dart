import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير الأصناف التي سوف تباع بأقل من التكلفة
/// يعرض الأصناف المتوقع بيعها بأقل من التكلفة
class ItemsBelowCostForecastReportPage extends StatefulWidget {
  const ItemsBelowCostForecastReportPage({super.key});

  @override
  State<ItemsBelowCostForecastReportPage> createState() => _ItemsBelowCostForecastReportPageState();
}

class _ItemsBelowCostForecastReportPageState extends State<ItemsBelowCostForecastReportPage> {
  String? _selectedCategory;
  String? _selectedSupplier;
  String? _riskLevel = 'all';
  String? _timeFrame = 'next_month';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الأصناف التي سوف تباع بأقل من التكلفة'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.warning),
            onPressed: _setAlerts,
            tooltip: 'تعيين تنبيهات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.red[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.category,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedCategory,
                        items: [
                          DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                          DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                          DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                          DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                        ],
                        onChanged: (value) => setState(() => _selectedCategory = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'المورد',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedSupplier,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الموردين')),
                          DropdownMenuItem(value: 'supplier1', child: Text('شركة التقنية المتقدمة')),
                          DropdownMenuItem(value: 'supplier2', child: Text('مؤسسة الجودة العالمية')),
                          DropdownMenuItem(value: 'supplier3', child: Text('شركة الإمداد الشامل')),
                        ],
                        onChanged: (value) => setState(() => _selectedSupplier = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'مستوى المخاطر',
                          border: OutlineInputBorder(),
                        ),
                        value: _riskLevel,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع المستويات')),
                          DropdownMenuItem(value: 'high', child: Text('مخاطر عالية')),
                          DropdownMenuItem(value: 'medium', child: Text('مخاطر متوسطة')),
                          DropdownMenuItem(value: 'low', child: Text('مخاطر منخفضة')),
                        ],
                        onChanged: (value) => setState(() => _riskLevel = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'الإطار الزمني',
                          border: OutlineInputBorder(),
                        ),
                        value: _timeFrame,
                        items: const [
                          DropdownMenuItem(value: 'next_week', child: Text('الأسبوع القادم')),
                          DropdownMenuItem(value: 'next_month', child: Text('الشهر القادم')),
                          DropdownMenuItem(value: 'next_quarter', child: Text('الربع القادم')),
                          DropdownMenuItem(value: 'next_year', child: Text('السنة القادمة')),
                        ],
                        onChanged: (value) => setState(() => _timeFrame = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _generateForecast,
                  icon: const Icon(Icons.analytics),
                  label: const Text('تحليل التوقعات'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص المخاطر
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.warning, color: Colors.red, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'ملخص مخاطر البيع بأقل من التكلفة',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildRiskCard('مخاطر عالية', '45 صنف', Colors.red, Icons.dangerous),
                              _buildRiskCard('مخاطر متوسطة', '85 صنف', Colors.orange, Icons.warning),
                              _buildRiskCard('مخاطر منخفضة', '125 صنف', Colors.yellow, Icons.info),
                              _buildRiskCard('آمنة', '990 صنف', Colors.green, Icons.check_circle),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // الأصناف عالية المخاطر
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.dangerous, color: Colors.red, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'أصناف عالية المخاطر',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          ..._buildHighRiskItemsList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول تفاصيل التوقعات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل توقعات الأصناف',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('اسم الصنف')),
                                DataColumn(label: Text('التكلفة الحالية')),
                                DataColumn(label: Text('السعر المتوقع')),
                                DataColumn(label: Text('الخسارة المتوقعة')),
                                DataColumn(label: Text('الكمية المتوقعة')),
                                DataColumn(label: Text('إجمالي الخسارة')),
                                DataColumn(label: Text('مستوى المخاطر')),
                                DataColumn(label: Text('التوقيت المتوقع')),
                                DataColumn(label: Text('إجراءات')),
                              ],
                              rows: _buildForecastRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // أسباب البيع بأقل من التكلفة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.psychology, color: Colors.indigo, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'أسباب البيع بأقل من التكلفة',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildReasonCard('انتهاء الصلاحية', '35%', Colors.red),
                              _buildReasonCard('تقادم المنتج', '28%', Colors.orange),
                              _buildReasonCard('منافسة السوق', '22%', Colors.blue),
                              _buildReasonCard('فائض المخزون', '15%', Colors.purple),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // استراتيجيات التخفيف
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.lightbulb, color: Colors.green, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'استراتيجيات التخفيف من المخاطر',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          ..._buildMitigationStrategiesList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _createPromotions,
                                  icon: const Icon(Icons.local_offer),
                                  label: const Text('إنشاء عروض'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _adjustPrices,
                                  icon: const Icon(Icons.price_change),
                                  label: const Text('تعديل الأسعار'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _transferStock,
                                  icon: const Icon(Icons.transfer_within_a_station),
                                  label: const Text('نقل المخزون'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _contactSuppliers,
                                  icon: const Icon(Icons.phone),
                                  label: const Text('اتصال بالموردين'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildHighRiskItemsList() {
    final items = [
      {'name': 'لابتوب قديم موديل 2020', 'cost': '2,500 ر.س', 'expected': '1,800 ر.س', 'loss': '700 ر.س', 'reason': 'تقادم المنتج'},
      {'name': 'هاتف سامسونج S20', 'cost': '1,800 ر.س', 'expected': '1,200 ر.س', 'loss': '600 ر.س', 'reason': 'منافسة السوق'},
      {'name': 'أغذية منتهية الصلاحية', 'cost': '150 ر.س', 'expected': '50 ر.س', 'loss': '100 ر.س', 'reason': 'انتهاء الصلاحية'},
      {'name': 'ملابس شتوية', 'cost': '200 ر.س', 'expected': '120 ر.س', 'loss': '80 ر.س', 'reason': 'تغير الموسم'},
    ];

    return items.map((item) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.red.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.red.withOpacity(0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.dangerous, color: Colors.red, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item['name']!,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'التكلفة: ${item['cost']} • المتوقع: ${item['expected']} • الخسارة: ${item['loss']}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  'السبب: ${item['reason']}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          ElevatedButton(
            onPressed: () => _takeAction(item['name']!),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              minimumSize: const Size(80, 32),
            ),
            child: const Text('إجراء'),
          ),
        ],
      ),
    )).toList();
  }

  List<Widget> _buildMitigationStrategiesList() {
    final strategies = [
      {'title': 'عروض ترويجية سريعة', 'description': 'إنشاء عروض خاصة للأصناف عالية المخاطر', 'impact': 'عالي'},
      {'title': 'نقل المخزون', 'description': 'نقل الأصناف إلى فروع أخرى ذات طلب أعلى', 'impact': 'متوسط'},
      {'title': 'تعديل الأسعار', 'description': 'تقليل الأسعار تدريجياً لتحفيز المبيعات', 'impact': 'متوسط'},
      {'title': 'إرجاع للموردين', 'description': 'التفاوض مع الموردين لإرجاع البضاعة', 'impact': 'عالي'},
    ];

    return strategies.map((strategy) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.green.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.green.withOpacity(0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.lightbulb, color: Colors.green, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  strategy['title']!,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  strategy['description']!,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: strategy['impact'] == 'عالي' ? Colors.green.withOpacity(0.2) : Colors.orange.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              'تأثير ${strategy['impact']!}',
              style: TextStyle(
                color: strategy['impact'] == 'عالي' ? Colors.green : Colors.orange,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildForecastRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('لابتوب قديم موديل 2020')),
        const DataCell(Text('2,500 ر.س')),
        const DataCell(Text('1,800 ر.س')),
        const DataCell(Text('700 ر.س')),
        const DataCell(Text('15')),
        const DataCell(Text('10,500 ر.س')),
        DataCell(_buildRiskBadge('عالي', Colors.red)),
        const DataCell(Text('خلال أسبوعين')),
        DataCell(IconButton(
          icon: const Icon(Icons.warning, color: Colors.red, size: 16),
          onPressed: () => _takeAction('لابتوب قديم موديل 2020'),
        )),
      ]),
      DataRow(cells: [
        const DataCell(Text('هاتف سامسونج S20')),
        const DataCell(Text('1,800 ر.س')),
        const DataCell(Text('1,200 ر.س')),
        const DataCell(Text('600 ر.س')),
        const DataCell(Text('25')),
        const DataCell(Text('15,000 ر.س')),
        DataCell(_buildRiskBadge('عالي', Colors.red)),
        const DataCell(Text('خلال شهر')),
        DataCell(IconButton(
          icon: const Icon(Icons.warning, color: Colors.red, size: 16),
          onPressed: () => _takeAction('هاتف سامسونج S20'),
        )),
      ]),
      DataRow(cells: [
        const DataCell(Text('ملابس شتوية')),
        const DataCell(Text('200 ر.س')),
        const DataCell(Text('120 ر.س')),
        const DataCell(Text('80 ر.س')),
        const DataCell(Text('50')),
        const DataCell(Text('4,000 ر.س')),
        DataCell(_buildRiskBadge('متوسط', Colors.orange)),
        const DataCell(Text('نهاية الموسم')),
        DataCell(IconButton(
          icon: const Icon(Icons.info, color: Colors.orange, size: 16),
          onPressed: () => _takeAction('ملابس شتوية'),
        )),
      ]),
    ];
  }

  Widget _buildRiskCard(String title, String count, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                count,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildReasonCard(String reason, String percentage, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                percentage,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                reason,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRiskBadge(String risk, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        risk,
        style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold),
      ),
    );
  }

  void _generateForecast() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تحليل توقعات الأصناف بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _setAlerts() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تعيين تنبيهات المخاطر')),
    );
  }

  void _createPromotions() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء عروض ترويجية للأصناف عالية المخاطر')),
    );
  }

  void _adjustPrices() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تعديل أسعار الأصناف المعرضة للخطر')),
    );
  }

  void _transferStock() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('نقل المخزون إلى فروع أخرى')),
    );
  }

  void _contactSuppliers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('الاتصال بالموردين لمناقشة الخيارات')),
    );
  }

  void _takeAction(String itemName) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('اتخاذ إجراء للصنف: $itemName')),
    );
  }
}
