class SalesLocalizations {
  static const Map<String, Map<String, String>> values = {
    'en': {
      // صفحات وحدة المبيعات
      'sales': 'Sales',
      'sales_invoice': 'Sales Invoice',
      'sales_return_invoice': 'Sales Return Invoice',
      'sales_order': 'Sales Order',
      'price_quote': 'Price Quote',
      'sales_offer_price': 'Sales Offer Price',
      'total_sales_today': 'Total Sales Today',
      'invoice_count': 'Invoice Count',
      'quick_operations': 'Quick Operations',
      'navigate_to': 'Navigate to',
      'settle_previous_sales_invoices': 'Settle Previous Sales Invoices',
      'sales_details': 'Sales Details',
      'monthly_branch_sales_movement': 'Monthly Branch Sales Movement',
      'item_sales_purchases_movement': 'Item Sales & Purchases Movement',
      'monthly_sales_average': 'Monthly Sales Average',
    },
    'ar': {
      // صفحات وحدة المبيعات
      'sales': 'المبيعات',
      'sales_invoice': 'فاتورة مبيعات',
      'sales_return_invoice': 'فاتورة مرتجع مبيعات',
      'sales_order': 'أمر بيع',
      'price_quote': 'عرض سعر',
      'sales_offer_price': 'عرض سعر مبيعات',
      'total_sales_today': 'إجمالي المبيعات اليوم',
      'invoice_count': 'عدد الفواتير',
      'quick_operations': 'العمليات السريعة',
      'navigate_to': 'الانتقال إلى',
      'settle_previous_sales_invoices': 'تسوية فواتير المبيعات السابقة',
      'sales_details': 'تفاصيل المبيعات',
      'monthly_branch_sales_movement': 'حركة مبيعات الفروع الشهرية',
      'item_sales_purchases_movement': 'حركة مبيعات ومشتريات الأصناف',
      'monthly_sales_average': 'تقرير المعدل الشهري لمبيعات الأصناف',
    },
  };
}
