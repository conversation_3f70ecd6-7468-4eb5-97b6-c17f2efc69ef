import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير حجوزات الأصناف
/// يعرض جميع الحجوزات المسجلة على الأصناف
class ItemReservationsReportPage extends StatefulWidget {
  const ItemReservationsReportPage({super.key});

  @override
  State<ItemReservationsReportPage> createState() => _ItemReservationsReportPageState();
}

class _ItemReservationsReportPageState extends State<ItemReservationsReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedCategory;
  String? _reservationStatus = 'all';
  String? _selectedCustomer;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('حجوزات الأصناف'),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _createNewReservation,
            tooltip: 'إنشاء حجز جديد',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          _buildFilterSection(localizations),
          
          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildReservationStatusSection(),
                  const SizedBox(height: 16),
                  _buildReservationsTableSection(),
                  const SizedBox(height: 16),
                  _buildCustomerAnalysisSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.purple[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.fromDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, true),
                  controller: TextEditingController(
                    text: _startDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.toDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, false),
                  controller: TextEditingController(
                    text: _endDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: localizations.category,
                    border: const OutlineInputBorder(),
                  ),
                  value: _selectedCategory,
                  items: [
                    DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                    DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                    DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                    DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                    const DropdownMenuItem(value: 'home', child: Text('أدوات منزلية')),
                  ],
                  onChanged: (value) => setState(() => _selectedCategory = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'حالة الحجز',
                    border: OutlineInputBorder(),
                  ),
                  value: _reservationStatus,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الحالات')),
                    DropdownMenuItem(value: 'active', child: Text('نشط')),
                    DropdownMenuItem(value: 'pending', child: Text('في الانتظار')),
                    DropdownMenuItem(value: 'confirmed', child: Text('مؤكد')),
                    DropdownMenuItem(value: 'cancelled', child: Text('ملغي')),
                    DropdownMenuItem(value: 'completed', child: Text('مكتمل')),
                  ],
                  onChanged: (value) => setState(() => _reservationStatus = value),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'العميل',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedCustomer,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع العملاء')),
                    DropdownMenuItem(value: 'cust1', child: Text('أحمد محمد')),
                    DropdownMenuItem(value: 'cust2', child: Text('فاطمة علي')),
                    DropdownMenuItem(value: 'cust3', child: Text('محمد سالم')),
                    DropdownMenuItem(value: 'cust4', child: Text('سارة أحمد')),
                  ],
                  onChanged: (value) => setState(() => _selectedCustomer = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.analytics),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.bookmark_border, color: Colors.purple, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص حجوزات الأصناف',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي الحجوزات', '285', Colors.purple, Icons.bookmark),
                _buildSummaryCard('قيمة الحجوزات', '850,000 ر.س', Colors.blue, Icons.monetization_on),
                _buildSummaryCard('حجوزات نشطة', '185', Colors.green, Icons.check_circle),
                _buildSummaryCard('في الانتظار', '45', Colors.orange, Icons.schedule),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReservationStatusSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.pie_chart, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'توزيع حالات الحجز',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildStatusCard('نشط', '185 حجز', Colors.green),
                _buildStatusCard('مؤكد', '65 حجز', Colors.blue),
                _buildStatusCard('في الانتظار', '45 حجز', Colors.orange),
                _buildStatusCard('ملغي', '25 حجز', Colors.red),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReservationsTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل حجوزات الأصناف',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('رقم الحجز')),
                  DataColumn(label: Text('اسم الصنف')),
                  DataColumn(label: Text('العميل')),
                  DataColumn(label: Text('الكمية المحجوزة')),
                  DataColumn(label: Text('القيمة')),
                  DataColumn(label: Text('تاريخ الحجز')),
                  DataColumn(label: Text('تاريخ الانتهاء')),
                  DataColumn(label: Text('الحالة')),
                  DataColumn(label: Text('ملاحظات')),
                  DataColumn(label: Text('إجراءات')),
                ],
                rows: _buildReservationsRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerAnalysisSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.people, color: Colors.teal, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'تحليل العملاء',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildCustomerAnalysisList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _confirmReservations,
                    icon: const Icon(Icons.check),
                    label: const Text('تأكيد الحجوزات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _cancelExpiredReservations,
                    icon: const Icon(Icons.cancel),
                    label: const Text('إلغاء المنتهية'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _sendNotifications,
                    icon: const Icon(Icons.notifications),
                    label: const Text('إرسال إشعارات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _generateFollowUpReport,
                    icon: const Icon(Icons.follow_the_signs),
                    label: const Text('تقرير متابعة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildCustomerAnalysisList() {
    final customers = [
      {'name': 'أحمد محمد', 'reservations': '25 حجز', 'value': '125,000 ر.س', 'status': 'عميل مميز'},
      {'name': 'فاطمة علي', 'reservations': '18 حجز', 'value': '95,000 ر.س', 'status': 'عميل جيد'},
      {'name': 'محمد سالم', 'reservations': '15 حجز', 'value': '75,000 ر.س', 'status': 'عميل عادي'},
      {'name': 'سارة أحمد', 'reservations': '12 حجز', 'value': '60,000 ر.س', 'status': 'عميل جديد'},
    ];

    return customers.map((customer) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: _getCustomerStatusColor(customer['status']!).withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: _getCustomerStatusColor(customer['status']!).withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.person, color: Colors.teal, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  customer['name']!,
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  '${customer['reservations']} • القيمة: ${customer['value']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getCustomerStatusColor(customer['status']!).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              customer['status']!,
              style: TextStyle(
                color: _getCustomerStatusColor(customer['status']!),
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildReservationsRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('RES001')),
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('أحمد محمد')),
        const DataCell(Text('5')),
        const DataCell(Text('22,500 ر.س')),
        const DataCell(Text('2024-01-15')),
        const DataCell(Text('2024-01-30')),
        DataCell(_buildStatusBadge('نشط', Colors.green)),
        const DataCell(Text('طلب خاص')),
        DataCell(Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit, color: Colors.blue, size: 16),
              onPressed: () => _editReservation('RES001'),
            ),
            IconButton(
              icon: const Icon(Icons.check, color: Colors.green, size: 16),
              onPressed: () => _confirmReservation('RES001'),
            ),
          ],
        )),
      ]),
      DataRow(cells: [
        const DataCell(Text('RES002')),
        const DataCell(Text('هاتف آيفون 15')),
        const DataCell(Text('فاطمة علي')),
        const DataCell(Text('3')),
        const DataCell(Text('11,400 ر.س')),
        const DataCell(Text('2024-01-18')),
        const DataCell(Text('2024-02-02')),
        DataCell(_buildStatusBadge('مؤكد', Colors.blue)),
        const DataCell(Text('عميل مميز')),
        DataCell(Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit, color: Colors.blue, size: 16),
              onPressed: () => _editReservation('RES002'),
            ),
            IconButton(
              icon: const Icon(Icons.shopping_cart, color: Colors.orange, size: 16),
              onPressed: () => _convertToSale('RES002'),
            ),
          ],
        )),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusCard(String status, String count, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(status, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(status, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Color _getCustomerStatusColor(String status) {
    switch (status) {
      case 'عميل مميز':
        return Colors.green;
      case 'عميل جيد':
        return Colors.blue;
      case 'عميل عادي':
        return Colors.orange;
      case 'عميل جديد':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير حجوزات الأصناف بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _createNewReservation() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء حجز جديد')),
    );
  }

  void _confirmReservations() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تأكيد الحجوزات المحددة')),
    );
  }

  void _cancelExpiredReservations() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إلغاء الحجوزات المنتهية الصلاحية')),
    );
  }

  void _sendNotifications() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إرسال إشعارات للعملاء')),
    );
  }

  void _generateFollowUpReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء تقرير متابعة الحجوزات')),
    );
  }

  void _editReservation(String reservationId) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تعديل الحجز $reservationId')),
    );
  }

  void _confirmReservation(String reservationId) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تأكيد الحجز $reservationId')),
    );
  }

  void _convertToSale(String reservationId) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تحويل الحجز $reservationId إلى مبيعة')),
    );
  }
}
