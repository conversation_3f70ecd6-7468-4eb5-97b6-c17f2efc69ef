import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';
import 'change_language.dart';
import 'dashboard_page.dart';
import 'settings_page.dart';
import '../sales/sales_page.dart';
import '../purchases/purchases_page.dart';
import '../inventory_reports/inventory_page.dart';
import '../account_reports/account_reports_page.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  int _selectedIndex = 0;

  final List<Widget> _pages = [
    const DashboardPage(),
    const SalesPage(),
    const PurchasesPage(),
    const InventoryPage(),
    const AccountReportsPage(),
    const SettingsPage(),
  ];

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.appTitle),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // TODO: Implement notifications
            },
            tooltip: localizations.notifications,
          ),
          IconButton(
            icon: const Icon(Icons.person),
            onPressed: () {
              // TODO: Implement profile
            },
            tooltip: localizations.profile,
          ),
        ],
      ),
      drawer: Drawer(
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            DrawerHeader(
              decoration: const BoxDecoration(
                color: Colors.blue,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const CircleAvatar(
                    radius: 30,
                    child: Icon(Icons.person, size: 30),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    localizations.userName,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                    ),
                  ),
                  Text(
                    localizations.userEmail,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            ListTile(
              leading: const Icon(Icons.dashboard),
              title: Text(localizations.dashboard),
              selected: _selectedIndex == 0,
              onTap: () {
                _onItemTapped(0);
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.shopping_cart),
              title: Text(localizations.sales),
              selected: _selectedIndex == 1,
              onTap: () {
                _onItemTapped(1);
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.store),
              title: Text(localizations.purchases),
              selected: _selectedIndex == 2,
              onTap: () {
                _onItemTapped(2);
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.inventory),
              title: Text(localizations.inventoryReports),
              selected: _selectedIndex == 3,
              onTap: () {
                _onItemTapped(3);
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.account_balance),
              title: Text(localizations.accountReports),
              selected: _selectedIndex == 4,
              onTap: () {
                _onItemTapped(4);
                Navigator.pop(context);
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.settings),
              title: Text(localizations.settings),
              selected: _selectedIndex == 5,
              onTap: () {
                _onItemTapped(5);
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.logout),
              title: Text(localizations.logout),
              onTap: () {
                // TODO: Implement logout
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
      body: _pages[_selectedIndex],
    );
  }
}

class ModulePagesScreen extends StatelessWidget {
  final String module;

  const ModulePagesScreen({super.key, required this.module});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    // Map of module names to their display names
    final Map<String, String> moduleDisplayNames = {
      'general': localizations.general,
      'cards': localizations.cards,
      'purchases': localizations.purchases,
      'sales': localizations.sales,
      'vouchers': localizations.vouchers,
      'inventory_reports': localizations.inventoryReports,
      'account_reports': localizations.accountReports,
      'statistical_reports': localizations.statisticalReports,
      'system_management': localizations.systemManagement,
      'tools': localizations.tools,
      'help': localizations.help,
    };

    // Map of module names to their pages
    final Map<String, List<Map<String, dynamic>>> modulePages = {
      'general': [
        {
          'name': localizations.createEditFiscalYear,
          'route': '/general/create_edit_fiscal_year',
          'icon': Icons.calendar_today
        },
        {
          'name': localizations.pageSettings,
          'route': '/general/page_settings',
          'icon': Icons.settings_applications
        },
        {
          'name': localizations.printerSettings,
          'route': '/general/printer_settings',
          'icon': Icons.print
        },
        {
          'name': localizations.changeLanguage,
          'route': '/general/change_language',
          'icon': Icons.language
        },
        {
          'name': localizations.changeTheme,
          'route': '/general/change_theme',
          'icon': Icons.color_lens
        },
        {
          'name': localizations.companyData,
          'route': '/general/company_data',
          'icon': Icons.business
        },
        {
          'name': localizations.backupRestore,
          'route': '/general/backup_restore',
          'icon': Icons.backup
        },
        {
          'name': localizations.homePage,
          'route': '/general/home_page',
          'icon': Icons.home
        },
        {
          'name': localizations.loginPage,
          'route': '/general/login_page',
          'icon': Icons.login
        },
      ],
      'cards': [
        {
          'name': localizations.financialGuide,
          'route': '/cards/financial_guide',
          'icon': Icons.menu_book
        },
        {
          'name': localizations.costCenter,
          'route': '/cards/cost_center',
          'icon': Icons.account_balance
        },
        {
          'name': localizations.customers,
          'route': '/cards/customers',
          'icon': Icons.people
        },
        {
          'name': localizations.suppliers,
          'route': '/cards/suppliers',
          'icon': Icons.local_shipping
        },
        {
          'name': localizations.items,
          'route': '/cards/items',
          'icon': Icons.inventory
        },
        {
          'name': localizations.stores,
          'route': '/cards/stores',
          'icon': Icons.store
        },
      ],
      'purchases': [
        {
          'name': localizations.purchaseInvoice,
          'route': '/purchases/purchase_invoice',
          'icon': Icons.receipt
        },
        {
          'name': localizations.purchaseReturnInvoice,
          'route': '/purchases/purchase_return_invoice',
          'icon': Icons.assignment_return
        },
        {
          'name': localizations.purchaseOrder,
          'route': '/purchases/purchase_order',
          'icon': Icons.shopping_cart
        },
        {
          'name': localizations.requestPriceQuotes,
          'route': '/purchases/request_price_quotes_from_supplier',
          'icon': Icons.request_quote
        },
      ],
      'sales': [
        {
          'name': localizations.salesInvoice,
          'route': '/sales/sales_invoice',
          'icon': Icons.receipt
        },
        {
          'name': localizations.salesReturnInvoice,
          'route': '/sales/sales_return_invoice',
          'icon': Icons.assignment_return
        },
        {
          'name': localizations.salesOrder,
          'route': '/sales/sales_order',
          'icon': Icons.shopping_cart
        },
        {
          'name': localizations.priceQuote,
          'route': '/sales/price_quote',
          'icon': Icons.request_quote
        },
        {
          'name': localizations.salesOfferPrice,
          'route': '/sales/sales_offer_price',
          'icon': Icons.local_offer
        },
      ],
      'vouchers': [
        {
          'name': localizations.journalEntry,
          'route': '/vouchers/journal_entry',
          'icon': Icons.book
        },
        {
          'name': localizations.receiptVoucher,
          'route': '/vouchers/receipt_voucher',
          'icon': Icons.receipt_long
        },
        {
          'name': localizations.paymentVoucher,
          'route': '/vouchers/payment_voucher',
          'icon': Icons.payments
        },
      ],
      'inventory_reports': [
        {
          'name': localizations.itemMovementReport,
          'route': '/inventory_reports/item_movement_report',
          'icon': Icons.trending_up
        },
        {
          'name': localizations.itemBalanceReport,
          'route': '/inventory_reports/item_balance_report',
          'icon': Icons.balance
        },
        {
          'name': localizations.itemCardReport,
          'route': '/inventory_reports/item_card_report',
          'icon': Icons.credit_card
        },
        {
          'name': localizations.itemMovementDuringPeriod,
          'route': '/inventory_reports/item_movement_during_period_report',
          'icon': Icons.date_range
        },
      ],
      'account_reports': [
        {
          'name': localizations.trialBalance,
          'route': '/account_reports/trial_balance',
          'icon': Icons.balance
        },
        {
          'name': localizations.incomeStatement,
          'route': '/account_reports/income_statement',
          'icon': Icons.attach_money
        },
        {
          'name': localizations.balanceSheet,
          'route': '/account_reports/balance_sheet',
          'icon': Icons.account_balance_wallet
        },
        {
          'name': localizations.generalLedger,
          'route': '/account_reports/general_ledger',
          'icon': Icons.book
        },
        {
          'name': localizations.dailyRestrictionReport,
          'route': '/account_reports/daily_restriction_report',
          'icon': Icons.today
        },
        {
          'name': localizations.continuousAccountStatement,
          'route': '/account_reports/continuous_account_statement',
          'icon': Icons.receipt_long
        },
        {
          'name': localizations.mainAccountStatement,
          'route': '/account_reports/main_account_statement',
          'icon': Icons.account_box
        },
      ],
      'statistical_reports': [
        {
          'name': localizations.salesDetails,
          'route': '/statistical_reports/statistical_reports_sales_details',
          'icon': Icons.bar_chart
        },
        {
          'name': localizations.purchasesDetails,
          'route': '/statistical_reports/statistical_reports_purchases_details',
          'icon': Icons.shopping_cart
        },
        {
          'name': localizations.monthlyBranchSalesMovement,
          'route':
              '/statistical_reports/statistical_reports_monthly_branch_sales_movement',
          'icon': Icons.trending_up
        },
        {
          'name': localizations.monthlyBranchPurchasesMovement,
          'route':
              '/statistical_reports/statistical_reports_monthly_branch_purchases_movement',
          'icon': Icons.trending_down
        },
      ],
      'system_management': [
        {
          'name': localizations.userPermissions,
          'route': '/system_management/user_permissions',
          'icon': Icons.security
        },
        {
          'name': localizations.usersManagement,
          'route': '/system_management/users_management',
          'icon': Icons.people
        },
        {
          'name': localizations.roles,
          'route': '/system_management/roles',
          'icon': Icons.admin_panel_settings
        },
        {
          'name': localizations.vatSettings,
          'route': '/system_management/vat_settings',
          'icon': Icons.receipt
        },
        {
          'name': localizations.whatsappServiceConnection,
          'route': '/system_management/whatsapp_service_connection_settings',
          'icon': Icons.chat
        },
      ],
      'tools': [
        {
          'name': localizations.calculator,
          'route': '/tools/calculator',
          'icon': Icons.calculate
        },
        {
          'name': localizations.notes,
          'route': '/tools/notes',
          'icon': Icons.note
        },
        {
          'name': localizations.phoneDirectory,
          'route': '/tools/phone_directory',
          'icon': Icons.phone
        },
      ],
      'help': [
        {
          'name': localizations.contactUs,
          'route': '/help/contact_us',
          'icon': Icons.contact_support
        },
        {
          'name': localizations.aboutUs,
          'route': '/help/about_us',
          'icon': Icons.info
        },
        {
          'name': localizations.programContent,
          'route': '/help/program_content',
          'icon': Icons.menu_book
        },
        {
          'name': localizations.quickTips,
          'route': '/help/quick_tips',
          'icon': Icons.lightbulb
        },
      ],
    };

    return Scaffold(
      appBar: AppBar(
        title: Text(moduleDisplayNames[module] ?? localizations.general),
      ),
      body: ListView.builder(
        itemCount: modulePages[module]?.length ?? 0,
        itemBuilder: (context, index) {
          final page = modulePages[module]?[index];
          if (page == null) return const SizedBox.shrink();

          return Card(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: ListTile(
              leading: Icon(page['icon'] as IconData? ?? Icons.article,
                  color: Theme.of(context).primaryColor),
              title: Text(page['name'] as String),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                // Navigate to the page based on the route
                final route = page['route'] as String;

                if (route == '/general/change_language') {
                  // Navigate to the change language page
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const ChangeLanguage(),
                    ),
                  );
                } else {
                  // For other pages, show a placeholder message
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Navigating to ${page['name']}')),
                  );
                }
              },
            ),
          );
        },
      ),
    );
  }
}
