import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تعديل المرتجعات المطبوعة
/// تتيح تعديل المرتجعات التي تم طباعتها مسبقاً
class EditPrintedReturnsPage extends StatefulWidget {
  const EditPrintedReturnsPage({super.key});

  @override
  State<EditPrintedReturnsPage> createState() => _EditPrintedReturnsPageState();
}

class _EditPrintedReturnsPageState extends State<EditPrintedReturnsPage> {
  final _searchController = TextEditingController();

  String _searchQuery = '';
  String? _selectedType;
  String? _selectedStatus;

  final List<Map<String, String>> _returnTypes = [
    {'id': 'sales_return', 'name': 'مرتجع مبيعات'},
    {'id': 'purchase_return', 'name': 'مرتجع مشتريات'},
  ];

  final List<Map<String, String>> _statuses = [
    {'id': 'printed', 'name': 'مطبوع'},
    {'id': 'edited', 'name': 'معدل'},
    {'id': 'cancelled', 'name': 'ملغي'},
  ];

  final List<Map<String, dynamic>> _printedReturns = [
    {
      'id': 'ret1',
      'number': 'RET-S-2024-001',
      'type': 'مرتجع مبيعات',
      'customer': 'شركة الأمل للتجارة',
      'originalInvoice': 'INV-2024-001',
      'date': '2024/01/25',
      'printDate': '2024/01/25 11:30',
      'amount': -2500.0,
      'status': 'مطبوع',
      'reason': 'عيب في المنتج',
      'canEdit': true,
      'printCount': 1,
    },
    {
      'id': 'ret2',
      'number': 'RET-P-2024-002',
      'type': 'مرتجع مشتريات',
      'customer': 'شركة التوريدات المتقدمة',
      'originalInvoice': 'PINV-2024-012',
      'date': '2024/01/24',
      'printDate': '2024/01/24 15:45',
      'amount': -5000.0,
      'status': 'معدل',
      'reason': 'كمية زائدة',
      'canEdit': false,
      'printCount': 2,
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.editPrintedReturns),
        backgroundColor: Colors.deepOrange,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // بطاقة البحث والفلترة
          Card(
            margin: const EdgeInsets.all(16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      labelText: 'البحث برقم المرتجع أو العميل',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedType,
                          decoration: const InputDecoration(
                            labelText: 'نوع المرتجع',
                            border: OutlineInputBorder(),
                          ),
                          items: [
                            const DropdownMenuItem(
                                value: null, child: Text('جميع الأنواع')),
                            ..._returnTypes.map((type) => DropdownMenuItem(
                                  value: type['id'],
                                  child: Text(type['name']!),
                                )),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedType = value;
                            });
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedStatus,
                          decoration: const InputDecoration(
                            labelText: 'الحالة',
                            border: OutlineInputBorder(),
                          ),
                          items: [
                            const DropdownMenuItem(
                                value: null, child: Text('جميع الحالات')),
                            ..._statuses.map((status) => DropdownMenuItem(
                                  value: status['id'],
                                  child: Text(status['name']!),
                                )),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedStatus = value;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // قائمة المرتجعات
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _printedReturns.length,
              itemBuilder: (context, index) {
                final returnDoc = _printedReturns[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: returnDoc['type'] == 'مرتجع مبيعات'
                          ? Colors.red
                          : Colors.blue,
                      child: const Icon(Icons.keyboard_return,
                          color: Colors.white),
                    ),
                    title: Text(returnDoc['number'],
                        style: const TextStyle(fontWeight: FontWeight.bold)),
                    subtitle: Text(
                        '${returnDoc['customer']} | ${returnDoc['amount']} ر.س'),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            _buildDetailRow('النوع:', returnDoc['type']),
                            _buildDetailRow('الفاتورة الأصلية:',
                                returnDoc['originalInvoice']),
                            _buildDetailRow('السبب:', returnDoc['reason']),
                            _buildDetailRow(
                                'تاريخ الطباعة:', returnDoc['printDate']),
                            const SizedBox(height: 16),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                ElevatedButton.icon(
                                  onPressed: () => _viewReturn(returnDoc),
                                  icon: const Icon(Icons.visibility, size: 16),
                                  label: const Text('عرض'),
                                  style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.blue,
                                      foregroundColor: Colors.white),
                                ),
                                if (returnDoc['canEdit'])
                                  ElevatedButton.icon(
                                    onPressed: () => _editReturn(returnDoc),
                                    icon: const Icon(Icons.edit, size: 16),
                                    label: const Text('تعديل'),
                                    style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.orange,
                                        foregroundColor: Colors.white),
                                  ),
                                ElevatedButton.icon(
                                  onPressed: () => _reprintReturn(returnDoc),
                                  icon: const Icon(Icons.print, size: 16),
                                  label: const Text('طباعة'),
                                  style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.green,
                                      foregroundColor: Colors.white),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
              width: 120,
              child: Text(label,
                  style: const TextStyle(
                      fontWeight: FontWeight.bold, fontSize: 12))),
          Expanded(child: Text(value, style: const TextStyle(fontSize: 12))),
        ],
      ),
    );
  }

  void _viewReturn(Map<String, dynamic> returnDoc) {
    ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('عرض المرتجع ${returnDoc['number']}')));
  }

  void _editReturn(Map<String, dynamic> returnDoc) {
    ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('تعديل المرتجع ${returnDoc['number']}')));
  }

  void _reprintReturn(Map<String, dynamic> returnDoc) {
    ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('إعادة طباعة المرتجع ${returnDoc['number']}')));
  }
}
