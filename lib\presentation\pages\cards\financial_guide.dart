import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';
import '../../widgets/account_data_form.dart';

class FinancialGuide extends StatefulWidget {
  const FinancialGuide({super.key});

  @override
  State<FinancialGuide> createState() => _FinancialGuideState();
}

class _FinancialGuideState extends State<FinancialGuide>
    with TickerProviderStateMixin {
  // خيارات الشجرة
  bool _includeSubAccounts = false;
  int _selectedTreeLevel = 1;
  bool _showAllLevels = false; // خيار عرض جميع المستويات

  // حالات الطي والفتح للعقد
  final Map<String, bool> _expandedNodes = {};

  // متحكم التمرير الأفقي
  final ScrollController _horizontalScrollController = ScrollController();

  // العقدة المحددة حالياً
  TreeNodeData? _selectedNode;

  // متحكم الحركة للتحديد
  late AnimationController _selectionAnimationController;
  late Animation<double> _selectionAnimation;

  // اتجاه النص (true للعربية، false للإنجليزية)
  bool _isRTL = true;

  /// قائمة الحسابات المحفوظة
  List<AccountData> _savedAccounts = [];

  /// تحديد ما إذا كان يمكن إضافة حساب جديد
  bool get _canAddAccount {
    if (_selectedNode == null) return false;
    // يمكن الإضافة فقط إذا كانت العقدة المحددة مجلد (hasChildren = true)
    return _selectedNode!.hasChildren;
  }

  /// تحديد ما إذا كان يمكن استعلام/تعديل حساب
  bool get _canQueryAccount {
    if (_selectedNode == null) return false;
    // يمكن الاستعلام فقط إذا كانت العقدة المحددة ملف (hasChildren = false)
    return !_selectedNode!.hasChildren;
  }

  @override
  void initState() {
    super.initState();

    // تهيئة متحكم الحركة للتحديد - حركة أبطأ
    _selectionAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    // تهيئة حركة التحديد
    _selectionAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _selectionAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  /// عرض نافذة إضافة حساب جديد
  void _showAddAccountDialog() {
    if (!_canAddAccount) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار مجلد من شجرة الحسابات أولاً'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          child: Container(
            width: MediaQuery.of(context).size.width * 0.8,
            height: MediaQuery.of(context).size.height * 0.8,
            padding: const EdgeInsets.all(20),
            child: AccountDataForm(
              parentAccountCode: _selectedNode!.code,
              onSave: (AccountData accountData) {
                _saveNewAccount(accountData);
                Navigator.of(context).pop();
              },
              onCancel: () {
                Navigator.of(context).pop();
              },
            ),
          ),
        );
      },
    );
  }

  /// حفظ حساب جديد
  void _saveNewAccount(AccountData accountData) {
    // إضافة الحساب إلى القائمة
    _savedAccounts.add(accountData);

    // عرض رسالة نجاح
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إضافة الحساب "${accountData.arabicName}" بنجاح'),
        backgroundColor: Colors.green,
      ),
    );

    // تحديث الواجهة
    setState(() {});

    // هنا يمكن إضافة منطق حفظ البيانات في قاعدة البيانات
    // _saveToDatabase(accountData);
  }

  /// عرض نافذة الاستعلام/التعديل
  void _showQueryDialog() {
    if (!_canQueryAccount) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار ملف من شجرة الحسابات أولاً'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // إنشاء بيانات الحساب من العقدة المحددة
    final accountData = _createAccountDataFromNode(_selectedNode!);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          child: Container(
            width: MediaQuery.of(context).size.width * 0.8,
            height: MediaQuery.of(context).size.height * 0.8,
            padding: const EdgeInsets.all(20),
            child: AccountDataForm(
              initialData: accountData,
              isEditMode: true,
              onSave: (AccountData updatedAccountData) {
                _updateExistingAccount(updatedAccountData);
                Navigator.of(context).pop();
              },
              onCancel: () {
                Navigator.of(context).pop();
              },
            ),
          ),
        );
      },
    );
  }

  /// إنشاء بيانات الحساب من العقدة المحددة
  AccountData _createAccountDataFromNode(TreeNodeData node) {
    return AccountData(
      branchNumber: '001', // افتراضي
      accountNumber: node.code,
      arabicName: node.name,
      englishName: _getEnglishName(node.name),
      accountType: 'ميزانية',
      freezeType: 'بدون',
      currency: 'ريال سعودي',
      securityLevel: 'عادي',
      subAccountType: '',
      balanceWarning: 'بدون',
      showNetMovement: false,
      isFolder: node.hasChildren,
    );
  }

  /// تحديث حساب موجود
  void _updateExistingAccount(AccountData accountData) {
    // البحث عن الحساب في القائمة وتحديثه
    final index = _savedAccounts.indexWhere(
      (account) => account.accountNumber == accountData.accountNumber,
    );

    if (index != -1) {
      _savedAccounts[index] = accountData;
    } else {
      // إذا لم يكن موجود، أضفه كحساب جديد
      _savedAccounts.add(accountData);
    }

    // عرض رسالة نجاح
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تحديث الحساب "${accountData.arabicName}" بنجاح'),
        backgroundColor: Colors.blue,
      ),
    );

    // تحديث الواجهة
    setState(() {});

    // هنا يمكن إضافة منطق حفظ البيانات في قاعدة البيانات
    // _updateInDatabase(accountData);
  }

  /// عرض تأكيد الحذف
  void _showDeleteConfirmation() {
    if (_selectedNode == null) return;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text('هل أنت متأكد من حذف الحساب "${_selectedNode!.name}"؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                setState(() {
                  _selectedNode = null;
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('تم حذف الحساب بنجاح')),
                );
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('حذف', style: TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    );
  }

  /// تراجع عن آخر عملية
  void _undoLastAction() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم التراجع عن آخر عملية')),
    );
  }

  /// حفظ التغييرات
  void _saveChanges() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم حفظ التغييرات بنجاح')),
    );
  }

  /// طباعة شجرة الحسابات
  void _printAccountTree() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('طباعة شجرة الحسابات'),
          content: const Text('سيتم طباعة شجرة الحسابات الحالية'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('تم إرسال الطباعة')),
                );
              },
              child: const Text('طباعة'),
            ),
          ],
        );
      },
    );
  }

  @override
  void dispose() {
    _horizontalScrollController.dispose();
    _selectionAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.financialGuide),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // الكرت الأول - عرض الشجرة (ملء كامل الصفحة)
            Expanded(
              child: _buildTreeViewCard(localizations),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء أزرار التحكم
  Widget _buildControlButtons() {
    return Container(
      width: double.infinity,
      height: 50,
      padding: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // زر إضافة
            _buildCompactButton(
              icon: Icons.add,
              color: _canAddAccount ? Colors.green : Colors.grey,
              tooltip: _canAddAccount
                  ? 'إضافة حساب جديد داخل "${_selectedNode?.name ?? ''}"'
                  : 'اختر مجلد من الشجرة أولاً',
              onPressed: _canAddAccount ? _showAddAccountDialog : null,
            ),
            const SizedBox(width: 6),

            // زر استعلام
            _buildCompactButton(
              icon: Icons.search,
              color: _canQueryAccount ? Colors.blue : Colors.grey,
              tooltip: _canQueryAccount
                  ? 'استعلام/تعديل الحساب "${_selectedNode?.name ?? ''}"'
                  : 'اختر ملف من الشجرة أولاً',
              onPressed: _canQueryAccount ? _showQueryDialog : null,
            ),
            const SizedBox(width: 6),

            // زر حذف
            _buildCompactButton(
              icon: Icons.delete,
              color: Colors.red,
              tooltip: 'حذف الحساب المحدد',
              onPressed: _selectedNode != null ? _showDeleteConfirmation : null,
            ),
            const SizedBox(width: 6),

            // زر تراجع
            _buildCompactButton(
              icon: Icons.undo,
              color: Colors.orange,
              tooltip: 'تراجع عن آخر عملية',
              onPressed: _undoLastAction,
            ),
            const SizedBox(width: 6),

            // زر حفظ
            _buildCompactButton(
              icon: Icons.save,
              color: Colors.purple,
              tooltip: 'حفظ التغييرات',
              onPressed: _saveChanges,
            ),
            const SizedBox(width: 6),

            // زر طباعة
            _buildCompactButton(
              icon: Icons.print,
              color: Colors.indigo,
              tooltip: 'طباعة شجرة الحسابات',
              onPressed: _printAccountTree,
            ),

            const SizedBox(width: 6),

            // زر إعادة الترقيم
            _buildCompactButton(
              icon: Icons.format_list_numbered,
              color: Colors.teal,
              tooltip: 'إعادة ترقيم الحسابات وفقاً للنظام السعودي المعتمد',
              onPressed: _renumberAllTreeNodes,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر مضغوط
  Widget _buildCompactButton({
    required IconData icon,
    required Color color,
    required String tooltip,
    required VoidCallback? onPressed,
  }) {
    return Tooltip(
      message: tooltip,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.all(8),
          minimumSize: const Size(36, 36),
          maximumSize: const Size(36, 36),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
          elevation: 2,
        ),
        child: Icon(icon, size: 18),
      ),
    );
  }

  /// بناء كرت عرض الشجرة
  Widget _buildTreeViewCard(AppLocalizations localizations) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // أزرار التحكم فوق العنوان
            _buildControlButtons(),
            const SizedBox(height: 12),

            // العنوان
            Text(
              'عرض الشجرة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue[700],
              ),
            ),
            const SizedBox(height: 16),

            // اختيار مستويات الشجرة
            const Text(
              'اختيار عرض مستويات الشجرة المراد عرضها:',
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),

            // أزرار مستويات الشجرة
            Wrap(
              spacing: 8,
              children: [
                // أزرار المستويات من 1 إلى 6
                ...List.generate(6, (index) {
                  final level = index + 1;
                  final isSelected =
                      !_showAllLevels && _selectedTreeLevel == level;
                  return AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    child: ChoiceChip(
                      label: Text('$level'),
                      selected: isSelected,
                      selectedColor: Colors.blue[100],
                      backgroundColor: Colors.grey[100],
                      elevation: isSelected ? 4 : 1,
                      shadowColor: Colors.blue.withValues(alpha: 0.3),
                      onSelected: (selected) {
                        if (selected) {
                          setState(() {
                            _selectedTreeLevel = level;
                            _showAllLevels = false;
                          });
                          // تحديث التمرير بعد تغيير المستوى
                          WidgetsBinding.instance.addPostFrameCallback((_) {
                            _updateScrollPosition();
                          });
                        }
                      },
                    ),
                  );
                }),
                // زر عرض جميع المستويات
                AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  child: ChoiceChip(
                    label: const Text('*'),
                    selected: _showAllLevels,
                    selectedColor: Colors.orange[100],
                    backgroundColor: Colors.grey[100],
                    elevation: _showAllLevels ? 4 : 1,
                    shadowColor: Colors.orange.withValues(alpha: 0.3),
                    onSelected: (selected) {
                      if (selected) {
                        setState(() {
                          _showAllLevels = true;
                        });
                        // تحديث التمرير بعد عرض جميع المستويات
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          _updateScrollPosition();
                        });
                      }
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // إدراج حسابات الأستاذة المساعدة
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              decoration: BoxDecoration(
                color:
                    _includeSubAccounts ? Colors.blue[25] : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
                border: _includeSubAccounts
                    ? Border.all(color: Colors.blue[200]!, width: 1)
                    : null,
              ),
              child: CheckboxListTile(
                title: const Text('إدراج حسابات الأستاذة المساعدة'),
                value: _includeSubAccounts,
                activeColor: Colors.blue[600],
                checkColor: Colors.white,
                onChanged: (value) {
                  setState(() {
                    _includeSubAccounts = value!;
                  });
                  // تحديث التمرير بعد تغيير إعداد الحسابات المساعدة
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    _updateScrollPosition();
                  });
                },
                controlAffinity: ListTileControlAffinity.leading,
              ),
            ),

            const SizedBox(height: 8),

            // تبديل اتجاه الشجرة
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              decoration: BoxDecoration(
                color: _isRTL ? Colors.green[25] : Colors.orange[25],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _isRTL ? Colors.green[200]! : Colors.orange[200]!,
                  width: 1,
                ),
              ),
              child: SwitchListTile(
                title: Text(_isRTL
                    ? 'اتجاه عربي (يمين-يسار)'
                    : 'English Direction (LTR)'),
                subtitle: Text(_isRTL ? 'Arabic Layout' : 'التخطيط الإنجليزي'),
                value: _isRTL,
                activeColor: _isRTL ? Colors.green[600] : Colors.orange[600],
                onChanged: (value) {
                  setState(() {
                    _isRTL = value;
                  });
                },
              ),
            ),

            const SizedBox(height: 16),

            // منطقة العرض
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey[50],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ClipRect(
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          return Row(
                            children: [
                              Expanded(
                                child: Text(
                                  _showAllLevels
                                      ? (_isRTL
                                          ? 'شجرة الحسابات - جميع المستويات'
                                          : 'Chart of Accounts - All Levels')
                                      : (_isRTL
                                          ? 'شجرة الحسابات - المستوى $_selectedTreeLevel'
                                          : 'Chart of Accounts - Level $_selectedTreeLevel'),
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              ),
                              if (constraints.maxWidth > 200) ...[
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 4, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: _isRTL
                                        ? Colors.green[50]
                                        : Colors.blue[50],
                                    border: Border.all(
                                      color: _isRTL
                                          ? Colors.green[200]!
                                          : Colors.blue[200]!,
                                    ),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Icon(
                                    _isRTL
                                        ? Icons.format_textdirection_r_to_l
                                        : Icons.format_textdirection_l_to_r,
                                    size: 12,
                                    color: _isRTL
                                        ? Colors.green[700]
                                        : Colors.blue[700],
                                  ),
                                ),
                              ],
                            ],
                          );
                        },
                      ),
                    ),
                    const SizedBox(height: 12),
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            final treeWidth =
                                _calculateTreeWidth(constraints.maxWidth);

                            return Stack(
                              children: [
                                Scrollbar(
                                  controller: _horizontalScrollController,
                                  child: SingleChildScrollView(
                                    controller: _horizontalScrollController,
                                    scrollDirection: Axis.horizontal,
                                    physics: const BouncingScrollPhysics(),
                                    padding: const EdgeInsets.all(8),
                                    child: SizedBox(
                                      width: treeWidth,
                                      child: _buildAccountTree(),
                                    ),
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// حساب عرض الشجرة المطلوب بناءً على المستويات المفتوحة (محسن للنصوص الطويلة)
  double _calculateTreeWidth(double availableWidth) {
    // حساب أقصى مستوى مفتوح
    int maxExpandedLevel = _getMaxExpandedLevel();

    // حساب أطول نص في الشجرة المرئية
    double maxTextWidth = _calculateMaxTextWidth();

    // العرض الأساسي للعقدة (أيقونة + رقم + مساحات ثابتة)
    double baseFixedWidth = 120; // العناصر الثابتة (أيقونة + رقم + مساحات)

    // عرض إضافي لكل مستوى (خطوط التفرع المحسنة + المسافة)
    double levelWidth = 25; // زيادة عرض المستوى للخطوط المحسنة

    // حساب العرض المطلوب
    double requiredWidth =
        baseFixedWidth + maxTextWidth + (maxExpandedLevel * levelWidth);

    // إضافة عرض إضافي للحسابات المساعدة إذا كانت مفعلة
    if (_includeSubAccounts) {
      requiredWidth += 100; // زيادة العرض الإضافي
    }

    // إضافة مساحة أمان إضافية
    requiredWidth += 50; // مساحة أمان

    // التأكد من أن العرض لا يقل عن العرض المتاح ولا يتجاوز حد معقول
    double maxWidth = availableWidth * 5; // زيادة الحد الأقصى للنصوص الطويلة
    return math.min(math.max(availableWidth, requiredWidth), maxWidth);
  }

  /// حساب أطول نص في الشجرة المرئية
  double _calculateMaxTextWidth() {
    double maxWidth = 200; // حد أدنى
    final treeData = _buildTreeData();

    void checkNodeWidth(TreeNodeData node) {
      // تقدير عرض النص (تقريبي)
      double textWidth = node.name.length * 8.0; // تقدير 8 بكسل لكل حرف
      maxWidth = math.max(maxWidth, textWidth);

      // فحص العقد الفرعية إذا كانت مفتوحة
      if (_expandedNodes[node.code] == true) {
        for (var child in node.children) {
          checkNodeWidth(child);
        }
      }
    }

    for (var node in treeData) {
      checkNodeWidth(node);
    }

    return math.min(maxWidth, 400); // حد أقصى 400 بكسل للنص
  }

  /// الحصول على أقصى مستوى مفتوح في الشجرة
  int _getMaxExpandedLevel() {
    int maxLevel = 0;

    void checkNode(TreeNodeData node) {
      // إضافة المستوى الحالي إذا كان مرئياً
      if (_showAllLevels || _selectedTreeLevel > node.level) {
        maxLevel = math.max(maxLevel, node.level);
      }

      // فحص العقد المفتوحة يدوياً
      if (_expandedNodes[node.code] == true) {
        maxLevel = math.max(maxLevel, node.level);
        for (var child in node.children) {
          checkNode(child);
        }
      }
    }

    final treeData = _buildTreeData();
    for (var node in treeData) {
      checkNode(node);
    }

    // إضافة مستوى إضافي للمستويات المرئية حسب الإعدادات
    if (_showAllLevels) {
      maxLevel = math.max(maxLevel, 7); // أقصى مستوى في البيانات
    } else {
      maxLevel = math.max(maxLevel, _selectedTreeLevel);
    }

    return maxLevel;
  }

  /// تحديث موضع التمرير عند فتح/إغلاق العقد
  void _updateScrollPosition() {
    if (_horizontalScrollController.hasClients) {
      // حساب أقصى مستوى مفتوح
      final maxLevel = _getMaxExpandedLevel();

      // التمرير تلقائياً بناءً على المستوى المفتوح
      final maxScrollExtent =
          _horizontalScrollController.position.maxScrollExtent;

      if (maxScrollExtent > 0) {
        double targetPosition;

        if (maxLevel <= 2) {
          // للمستويات الأولى، البقاء في البداية
          targetPosition = 0;
        } else if (maxLevel <= 4) {
          // للمستويات المتوسطة، التمرير قليلاً
          targetPosition = maxScrollExtent * 0.2;
        } else {
          // للمستويات العميقة، التمرير أكثر
          targetPosition = maxScrollExtent * 0.4;
        }

        _horizontalScrollController.animateTo(
          targetPosition,
          duration: const Duration(milliseconds: 400),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  /// تحديد عقدة (النقر المفرد - بدون عرض البيانات)
  void _selectNode(TreeNodeData node) {
    setState(() {
      _selectedNode = node;
    });

    // تشغيل حركة التحديد
    _selectionAnimationController.forward().then((_) {
      _selectionAnimationController.reverse();
    });

    // لا يتم عرض البيانات عند النقر المفرد
    // البيانات تُعرض فقط عند النقر المزدوج
  }

  /// النقر المزدوج على عقدة
  void _onNodeDoubleTap(TreeNodeData node) {
    // تحديد العقدة
    _selectNode(node);

    // إذا كانت العقدة لها أطفال، فتح/إغلاق
    if (node.hasChildren) {
      setState(() {
        _expandedNodes[node.code] = !(_expandedNodes[node.code] ?? false);
      });

      // تحديث التمرير
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _updateScrollPosition();
      });

      // عرض رسالة توضيحية للمجلدات
      // لا حاجة لعرض بيانات للمجلدات
    } else {
      // للملفات: يمكن إضافة منطق عرض البيانات هنا لاحقاً
    }
  }

  /// الحصول على الاسم الإنجليزي (مثال)
  String _getEnglishName(String arabicName) {
    final Map<String, String> translations = {
      'الأصول': 'Assets',
      'الأصول المتداولة': 'Current Assets',
      'النقدية وما في حكمها': 'Cash and Cash Equivalents',
      'الصندوق': 'Cash Box',
      'البنوك': 'Banks',
      'البنك الأهلي': 'National Bank',
      'بنك الرياض': 'Riyadh Bank',
      'حساب جاري': 'Current Account',
      'حساب جاري بالريال': 'SAR Current Account',
      'حساب جاري بالدولار': 'USD Current Account',
      'حساب جاري باليورو': 'EUR Current Account',
      'حساب توفير': 'Savings Account',
      'توفير قصير الأجل': 'Short Term Savings',
      'توفير طويل الأجل': 'Long Term Savings',
      'الخصوم': 'Liabilities',
      'حقوق الملكية': 'Equity',
    };

    return translations[arabicName] ?? arabicName;
  }

  /// إعادة ترقيم الشجرة بالكامل حسب النظام السعودي المعتمد
  void _renumberAllTreeNodes() {
    setState(() {
      // إعادة بناء الشجرة بالترقيم السعودي الجديد - سيتم استخدام _buildTreeData() المحدثة
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content:
            Text('تم إعادة ترقيم جميع الحسابات وفقاً للنظام السعودي المعتمد'),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// بناء شجرة الحسابات مع دعم محسن للاتجاهين
  Widget _buildAccountTree() {
    final treeData = _buildTreeData();
    return Directionality(
      textDirection: _isRTL ? TextDirection.rtl : TextDirection.ltr,
      child: SizedBox(
        width: double.infinity,
        child: SingleChildScrollView(
          scrollDirection: Axis.vertical,
          child: Column(
            crossAxisAlignment:
                _isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
            children: treeData
                .map((node) => _buildCollapsibleTreeNode(node))
                .toList(),
          ),
        ),
      ),
    );
  }

  /// بناء بيانات الشجرة وفقاً للنظام السعودي المعتمد
  List<TreeNodeData> _buildTreeData() {
    List<TreeNodeData> nodes = [];

    // المستوى الأول - الأصول (1)
    final assetsNode = TreeNodeData(
      code: '**********',
      name: 'الأصول',
      level: 0,
      hasChildren: true,
      children: [],
    );

    if (_showAllLevels || _selectedTreeLevel >= 2) {
      // الأصول المتداولة (11)
      final currentAssetsNode = TreeNodeData(
        code: '1100000000',
        name: 'الأصول المتداولة',
        level: 1,
        hasChildren: true,
        children: [],
      );

      if (_showAllLevels || _selectedTreeLevel >= 3) {
        // النقدية وما في حكمها (111)
        final cashNode = TreeNodeData(
          code: '1110000000',
          name: 'النقدية وما في حكمها',
          level: 2,
          hasChildren: true,
          children: [],
        );

        if (_showAllLevels || _selectedTreeLevel >= 4) {
          // الصندوق (1111)
          cashNode.children.add(TreeNodeData(
            code: '**********',
            name: 'الصندوق',
            level: 3,
            hasChildren: false,
            children: [],
          ));

          // البنوك (1112)
          final banksNode = TreeNodeData(
            code: '**********',
            name: 'البنوك',
            level: 3,
            hasChildren: true,
            children: [],
          );

          if (_showAllLevels || _selectedTreeLevel >= 5) {
            // البنك الأهلي (11121)
            banksNode.children.add(TreeNodeData(
              code: '**********',
              name: 'البنك الأهلي',
              level: 4,
              hasChildren: false,
              children: [],
            ));

            // بنك الرياض (11122)
            banksNode.children.add(TreeNodeData(
              code: '**********',
              name: 'بنك الرياض',
              level: 4,
              hasChildren: false,
              children: [],
            ));

            if (_showAllLevels || _selectedTreeLevel >= 6) {
              // حساب جاري (111211)
              final currentAccountNode = TreeNodeData(
                code: '**********',
                name: 'حساب جاري',
                level: 5,
                hasChildren: true,
                children: [],
              );

              if (_showAllLevels) {
                // تفاصيل الحساب الجاري
                final riyalAccountNode = TreeNodeData(
                  code: '**********',
                  name: 'حساب جاري بالريال',
                  level: 6,
                  hasChildren: true,
                  children: [
                    TreeNodeData(
                        code: '**********',
                        name: 'حساب جاري ريال - فرع الرياض',
                        level: 7,
                        hasChildren: false,
                        children: []),
                    TreeNodeData(
                        code: '**********',
                        name: 'حساب جاري ريال - فرع جدة',
                        level: 7,
                        hasChildren: false,
                        children: []),
                    TreeNodeData(
                        code: '**********',
                        name: 'حساب جاري ريال - فرع الدمام',
                        level: 7,
                        hasChildren: false,
                        children: []),
                  ],
                );

                final dollarAccountNode = TreeNodeData(
                  code: '**********',
                  name: 'حساب جاري بالدولار',
                  level: 6,
                  hasChildren: true,
                  children: [
                    TreeNodeData(
                        code: '**********',
                        name: 'حساب جاري دولار - فرع الرياض',
                        level: 7,
                        hasChildren: false,
                        children: []),
                    TreeNodeData(
                        code: '**********',
                        name: 'حساب جاري دولار - فرع جدة',
                        level: 7,
                        hasChildren: false,
                        children: []),
                  ],
                );

                currentAccountNode.children
                    .addAll([riyalAccountNode, dollarAccountNode]);
                currentAccountNode.children.add(TreeNodeData(
                    code: '**********',
                    name: 'حساب جاري باليورو',
                    level: 6,
                    hasChildren: false,
                    children: []));
              }

              banksNode.children.add(currentAccountNode);

              // حساب توفير (111212)
              final savingsAccountNode = TreeNodeData(
                code: '**********',
                name: 'حساب توفير',
                level: 5,
                hasChildren: true,
                children: [],
              );

              if (_showAllLevels) {
                final shortTermSavingsNode = TreeNodeData(
                  code: '**********',
                  name: 'توفير قصير الأجل',
                  level: 6,
                  hasChildren: true,
                  children: [
                    TreeNodeData(
                        code: '**********',
                        name: 'توفير 3 أشهر',
                        level: 7,
                        hasChildren: false,
                        children: []),
                    TreeNodeData(
                        code: '**********',
                        name: 'توفير 6 أشهر',
                        level: 7,
                        hasChildren: false,
                        children: []),
                  ],
                );

                final longTermSavingsNode = TreeNodeData(
                  code: '1112122000',
                  name: 'توفير طويل الأجل',
                  level: 6,
                  hasChildren: true,
                  children: [
                    TreeNodeData(
                        code: '1112122100',
                        name: 'توفير سنة',
                        level: 7,
                        hasChildren: false,
                        children: []),
                    TreeNodeData(
                        code: '1112122200',
                        name: 'توفير سنتين',
                        level: 7,
                        hasChildren: false,
                        children: []),
                    TreeNodeData(
                        code: '**********',
                        name: 'توفير 5 سنوات',
                        level: 7,
                        hasChildren: false,
                        children: []),
                  ],
                );

                savingsAccountNode.children
                    .addAll([shortTermSavingsNode, longTermSavingsNode]);
              }

              banksNode.children.add(savingsAccountNode);
            }
          }

          cashNode.children.add(banksNode);
        }

        currentAssetsNode.children.add(cashNode);

        // الاستثمارات قصيرة الأجل (112)
        currentAssetsNode.children.add(TreeNodeData(
          code: '**********',
          name: 'الاستثمارات قصيرة الأجل',
          level: 2,
          hasChildren: true,
          children: [],
        ));

        // العملاء (113)
        currentAssetsNode.children.add(TreeNodeData(
          code: '**********',
          name: 'العملاء',
          level: 2,
          hasChildren: true,
          children: [],
        ));

        // المخزون (114)
        currentAssetsNode.children.add(TreeNodeData(
          code: '**********',
          name: 'المخزون',
          level: 2,
          hasChildren: true,
          children: [],
        ));

        // المصروفات المدفوعة مقدماً (115)
        currentAssetsNode.children.add(TreeNodeData(
          code: '1150000000',
          name: 'المصروفات المدفوعة مقدماً',
          level: 2,
          hasChildren: true,
          children: [],
        ));
      }

      // الأصول غير المتداولة (12)
      final nonCurrentAssetsNode = TreeNodeData(
        code: '1200000000',
        name: 'الأصول غير المتداولة',
        level: 1,
        hasChildren: true,
        children: [],
      );

      if (_showAllLevels || _selectedTreeLevel >= 3) {
        // الاستثمارات طويلة الأجل (121)
        nonCurrentAssetsNode.children.add(TreeNodeData(
          code: '1210000000',
          name: 'الاستثمارات طويلة الأجل',
          level: 2,
          hasChildren: true,
          children: [],
        ));

        // الممتلكات والمعدات (122)
        nonCurrentAssetsNode.children.add(TreeNodeData(
          code: '1220000000',
          name: 'الممتلكات والمعدات',
          level: 2,
          hasChildren: true,
          children: [],
        ));

        // الأصول غير الملموسة (123)
        nonCurrentAssetsNode.children.add(TreeNodeData(
          code: '1230000000',
          name: 'الأصول غير الملموسة',
          level: 2,
          hasChildren: true,
          children: [],
        ));
      }

      assetsNode.children.add(currentAssetsNode);
      assetsNode.children.add(nonCurrentAssetsNode);
    }

    nodes.add(assetsNode);

    // الخصوم (2) - وفقاً للنظام السعودي
    final liabilitiesNode = TreeNodeData(
        code: '2000000000',
        name: 'الخصوم',
        level: 0,
        hasChildren: true,
        children: []);

    if (_showAllLevels || _selectedTreeLevel >= 2) {
      // الخصوم المتداولة (21)
      final currentLiabilitiesNode = TreeNodeData(
        code: '2100000000',
        name: 'الخصوم المتداولة',
        level: 1,
        hasChildren: true,
        children: [],
      );

      if (_showAllLevels || _selectedTreeLevel >= 3) {
        // الموردون (211)
        currentLiabilitiesNode.children.add(TreeNodeData(
          code: '2110000000',
          name: 'الموردون',
          level: 2,
          hasChildren: true,
          children: [],
        ));

        // القروض قصيرة الأجل (212)
        currentLiabilitiesNode.children.add(TreeNodeData(
          code: '2120000000',
          name: 'القروض قصيرة الأجل',
          level: 2,
          hasChildren: true,
          children: [],
        ));

        // المصروفات المستحقة (213)
        currentLiabilitiesNode.children.add(TreeNodeData(
          code: '2130000000',
          name: 'المصروفات المستحقة',
          level: 2,
          hasChildren: true,
          children: [],
        ));
      }

      // الخصوم غير المتداولة (22)
      final nonCurrentLiabilitiesNode = TreeNodeData(
        code: '2200000000',
        name: 'الخصوم غير المتداولة',
        level: 1,
        hasChildren: true,
        children: [],
      );

      if (_showAllLevels || _selectedTreeLevel >= 3) {
        // القروض طويلة الأجل (221)
        nonCurrentLiabilitiesNode.children.add(TreeNodeData(
          code: '2210000000',
          name: 'القروض طويلة الأجل',
          level: 2,
          hasChildren: true,
          children: [],
        ));

        // المخصصات (222)
        nonCurrentLiabilitiesNode.children.add(TreeNodeData(
          code: '2220000000',
          name: 'المخصصات',
          level: 2,
          hasChildren: true,
          children: [],
        ));
      }

      liabilitiesNode.children.add(currentLiabilitiesNode);
      liabilitiesNode.children.add(nonCurrentLiabilitiesNode);
    }

    // حقوق الملكية (3) - وفقاً للنظام السعودي
    final equityNode = TreeNodeData(
        code: '3000000000',
        name: 'حقوق الملكية',
        level: 0,
        hasChildren: true,
        children: []);

    if (_showAllLevels || _selectedTreeLevel >= 2) {
      // رأس المال (31)
      equityNode.children.add(TreeNodeData(
        code: '3100000000',
        name: 'رأس المال',
        level: 1,
        hasChildren: true,
        children: [],
      ));

      // الاحتياطيات (32)
      equityNode.children.add(TreeNodeData(
        code: '3200000000',
        name: 'الاحتياطيات',
        level: 1,
        hasChildren: true,
        children: [],
      ));

      // الأرباح المحتجزة (33)
      equityNode.children.add(TreeNodeData(
        code: '3300000000',
        name: 'الأرباح المحتجزة',
        level: 1,
        hasChildren: true,
        children: [],
      ));
    }

    // الإيرادات (4) - وفقاً للنظام السعودي
    final revenueNode = TreeNodeData(
        code: '4000000000',
        name: 'الإيرادات',
        level: 0,
        hasChildren: true,
        children: []);

    if (_showAllLevels || _selectedTreeLevel >= 2) {
      // إيرادات التشغيل (41)
      revenueNode.children.add(TreeNodeData(
        code: '4100000000',
        name: 'إيرادات التشغيل',
        level: 1,
        hasChildren: true,
        children: [],
      ));

      // الإيرادات الأخرى (42)
      revenueNode.children.add(TreeNodeData(
        code: '4200000000',
        name: 'الإيرادات الأخرى',
        level: 1,
        hasChildren: true,
        children: [],
      ));
    }

    // المصروفات (5) - وفقاً للنظام السعودي
    final expensesNode = TreeNodeData(
        code: '5000000000',
        name: 'المصروفات',
        level: 0,
        hasChildren: true,
        children: []);

    if (_showAllLevels || _selectedTreeLevel >= 2) {
      // تكلفة البضاعة المباعة (51)
      expensesNode.children.add(TreeNodeData(
        code: '5100000000',
        name: 'تكلفة البضاعة المباعة',
        level: 1,
        hasChildren: true,
        children: [],
      ));

      // مصروفات البيع والتوزيع (52)
      expensesNode.children.add(TreeNodeData(
        code: '5200000000',
        name: 'مصروفات البيع والتوزيع',
        level: 1,
        hasChildren: true,
        children: [],
      ));

      // المصروفات الإدارية (53)
      expensesNode.children.add(TreeNodeData(
        code: '5300000000',
        name: 'المصروفات الإدارية',
        level: 1,
        hasChildren: true,
        children: [],
      ));

      // مصروفات التمويل (54)
      expensesNode.children.add(TreeNodeData(
        code: '5400000000',
        name: 'مصروفات التمويل',
        level: 1,
        hasChildren: true,
        children: [],
      ));

      // مصروفات أخرى (55)
      expensesNode.children.add(TreeNodeData(
        code: '5500000000',
        name: 'مصروفات أخرى',
        level: 1,
        hasChildren: true,
        children: [],
      ));
    }

    nodes.add(liabilitiesNode);
    nodes.add(equityNode);
    nodes.add(revenueNode);
    nodes.add(expensesNode);

    return nodes;
  }

  /// بناء عقدة قابلة للطي
  Widget _buildCollapsibleTreeNode(TreeNodeData node) {
    final isExpanded = _expandedNodes[node.code] ?? false;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildTreeNodeRow(node, isExpanded),
        if (isExpanded && node.children.isNotEmpty)
          ...node.children.map((child) => Padding(
                padding: const EdgeInsets.only(
                    right: 20), // تغيير من left إلى right للعربية
                child: _buildCollapsibleTreeNode(child),
              )),
      ],
    );
  }

  /// بناء صف العقدة مع خطوط التفرع (بدون تأثيرات التمرير)
  Widget _buildTreeNodeRow(TreeNodeData node, bool isExpanded) {
    final isSelected = _selectedNode?.code == node.code;

    return AnimatedBuilder(
      animation: _selectionAnimation,
      builder: (context, child) {
        return GestureDetector(
          onTap: () => _selectNode(node),
          onDoubleTap: () => _onNodeDoubleTap(node),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 500),
            margin: const EdgeInsets.symmetric(vertical: 1),
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
            decoration: BoxDecoration(
              color: isSelected ? Colors.blue[50] : Colors.transparent,
              borderRadius: BorderRadius.circular(6),
              border: isSelected
                  ? Border.all(
                      color: Colors.blue[400]!,
                      width: 2,
                    )
                  : null,
            ),
            child: IntrinsicHeight(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // خطوط التفرع
                  _buildTreeLines(node),

                  // أيقونة الطي/الفتح
                  if (node.hasChildren)
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _expandedNodes[node.code] = !isExpanded;
                        });
                        // تحديث التمرير بعد تغيير حالة العقدة
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          _updateScrollPosition();
                        });
                      },
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 500),
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          color: isExpanded ? Colors.blue[50] : Colors.white,
                          borderRadius: BorderRadius.circular(3),
                        ),
                        child: Icon(
                          isExpanded ? Icons.remove : Icons.add,
                          size: 12,
                          color: isExpanded ? Colors.blue[700] : Colors.black,
                        ),
                      ),
                    )
                  else
                    const SizedBox(width: 20),

                  const SizedBox(width: 8),

                  // أيقونة المجلد/الملف
                  Icon(
                    node.hasChildren
                        ? (isExpanded ? Icons.folder_open : Icons.folder)
                        : Icons.description,
                    size: 16,
                    color: isSelected
                        ? Colors.blue[700]!
                        : (node.hasChildren
                            ? Colors.blue[600]!
                            : Colors.grey[600]!),
                  ),

                  const SizedBox(width: 8),

                  // رقم الحساب
                  Text(
                    node.code,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                      fontFamily: 'monospace',
                      color: isSelected ? Colors.blue[700] : Colors.black87,
                    ),
                  ),

                  const SizedBox(width: 8),

                  // اسم الحساب مع تحسين للنصوص الطويلة ودعم الاتجاهين
                  Expanded(
                    child: SizedBox(
                      height: 20, // تحديد ارتفاع ثابت
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        physics: const BouncingScrollPhysics(),
                        child: Container(
                          alignment: _isRTL
                              ? Alignment.centerRight
                              : Alignment.centerLeft,
                          constraints: BoxConstraints(
                            minWidth: node.name.length > 25 ? 300 : 150,
                          ),
                          child: Tooltip(
                            message: node.name,
                            child: Text(
                              node.name,
                              style: TextStyle(
                                fontSize: 12,
                                color: isSelected
                                    ? Colors.blue[800]!
                                    : (node.hasChildren
                                        ? Colors.black87
                                        : Colors.black54),
                                fontWeight: node.hasChildren
                                    ? FontWeight.w500
                                    : FontWeight.normal,
                              ),
                              maxLines: 1,
                              softWrap: false, // منع التفاف النص
                              textDirection: _isRTL
                                  ? TextDirection.rtl
                                  : TextDirection.ltr,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),

                  // مساحة صغيرة في النهاية
                  const SizedBox(width: 8),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء خطوط التفرع المحسنة بمواضع مطلقة
  Widget _buildTreeLines(TreeNodeData node) {
    // حساب العرض بناءً على الموضع المطلق للمستوى
    double width = _getAbsoluteLevelPositionForWidget(node.level) +
        50; // إضافة مساحة إضافية

    return SizedBox(
      width: width,
      height: 35, // ارتفاع أكبر للمسافات الجديدة
      child: CustomPaint(
        painter: TreeLinePainter(node.level, _isRTL),
      ),
    );
  }

  /// حساب الموضع المطلق للويدجت (نفس منطق الرسم)
  double _getAbsoluteLevelPositionForWidget(int level) {
    switch (level) {
      case 1:
        return 45.0;
      case 2:
        return 90.0;
      case 3:
        return 135.0;
      case 4:
        return 180.0;
      case 5:
        return 225.0;
      case 6:
        return 270.0;
      case 7:
        return 315.0;
      default:
        return 45.0 * level;
    }
  }
}

/// نموذج بيانات عقدة الشجرة
class TreeNodeData {
  final String code;
  final String name;
  final int level;
  final bool hasChildren;
  final List<TreeNodeData> children;

  TreeNodeData({
    required this.code,
    required this.name,
    required this.level,
    required this.hasChildren,
    required this.children,
  });
}

/// رسام خطوط التفرع المحسن (يدعم الاتجاهين العربي والإنجليزي)
class TreeLinePainter extends CustomPainter {
  final int level;
  final bool isRTL; // true للعربية، false للإنجليزية

  TreeLinePainter(this.level, this.isRTL);

  @override
  void paint(Canvas canvas, Size size) {
    if (level == 0) return; // لا نرسم خطوط للمستوى الأول

    // ألوان وسماكات متدرجة حسب المستوى
    final paint = Paint()
      ..color = _getLineColor(level)
      ..strokeWidth = _getLineWidth(level)
      ..style = PaintingStyle.stroke;

    final dotPaint = Paint()
      ..color = _getDotColor(level)
      ..style = PaintingStyle.fill;

    final connectorPaint = Paint()
      ..color = _getConnectorColor(level)
      ..strokeWidth = _getConnectorWidth(level)
      ..style = PaintingStyle.stroke;

    if (isRTL) {
      _paintRTLLines(canvas, size, paint, dotPaint, connectorPaint);
    } else {
      _paintLTRLines(canvas, size, paint, dotPaint, connectorPaint);
    }
  }

  /// تحديد لون الخط المميز حسب المستوى
  Color _getLineColor(int level) {
    switch (level) {
      case 1:
        return const Color(0xFF1976D2); // أزرق قوي
      case 2:
        return const Color(0xFF388E3C); // أخضر قوي
      case 3:
        return const Color(0xFFFF6F00); // برتقالي قوي
      case 4:
        return const Color(0xFF7B1FA2); // بنفسجي قوي
      case 5:
        return const Color(0xFFD32F2F); // أحمر قوي
      case 6:
        return const Color(0xFF00796B); // تركوازي قوي
      case 7:
        return const Color(0xFF5D4037); // بني قوي
      default:
        return const Color(0xFF303F9F); // نيلي قوي للمستويات الأعمق
    }
  }

  /// تحديد سماكة الخط المحسنة حسب المستوى
  double _getLineWidth(int level) {
    switch (level) {
      case 1:
        return 3.0; // سماكة قوية للمستوى الأول
      case 2:
        return 2.8; // سماكة قوية للمستوى الثاني
      case 3:
        return 2.5; // سماكة متوسطة قوية
      case 4:
        return 2.3; // سماكة متوسطة
      case 5:
        return 2.1; // سماكة أقل قليلاً
      case 6:
        return 1.9; // سماكة للمستوى السادس
      default:
        return 1.7; // سماكة للمستويات الأعمق
    }
  }

  /// تحديد لون النقطة حسب المستوى
  Color _getDotColor(int level) {
    return _getLineColor(level).withValues(alpha: 0.8);
  }

  /// تحديد لون خط الربط حسب المستوى
  Color _getConnectorColor(int level) {
    return _getLineColor(level).withValues(alpha: 0.6);
  }

  /// تحديد سماكة خط الربط حسب المستوى
  double _getConnectorWidth(int level) {
    if (level <= 2) return 1.5;
    if (level <= 4) return 1.3;
    if (level <= 6) return 1.1;
    return 1.0; // للمستويات الأعمق
  }

  /// تحديد طول الخط الأفقي المحسن
  double _getLineLength(int level) {
    // طول ثابت ومناسب للمسافات الجديدة
    return 30.0; // طول أكبر وثابت لجميع المستويات
  }

  /// حساب الموضع المطلق الثابت لكل مستوى (لضمان الاستقامة)
  double _getAbsoluteLevelPosition(int level) {
    // مواضع مطلقة ثابتة لكل مستوى
    switch (level) {
      case 1:
        return 45.0; // الموضع الثابت للمستوى الأول
      case 2:
        return 90.0; // الموضع الثابت للمستوى الثاني
      case 3:
        return 135.0; // الموضع الثابت للمستوى الثالث
      case 4:
        return 180.0; // الموضع الثابت للمستوى الرابع
      case 5:
        return 225.0; // الموضع الثابت للمستوى الخامس
      case 6:
        return 270.0; // الموضع الثابت للمستوى السادس
      case 7:
        return 315.0; // الموضع الثابت للمستوى السابع
      default:
        return 45.0 * level; // حساب تلقائي للمستويات الأعمق
    }
  }

  /// حساب الموضع المطلق للاتجاه العربي (RTL) من اليمين
  double _getAbsoluteLevelPositionRTL(int level, double containerWidth) {
    // العرض الثابت المرجعي للحاوية
    const double referenceWidth = 500.0;
    return referenceWidth - _getAbsoluteLevelPosition(level);
  }

  /// رسم خطوط الاتجاه العربي (RTL) مع تحسينات للمستويات العميقة
  void _paintRTLLines(Canvas canvas, Size size, Paint paint, Paint dotPaint,
      Paint connectorPaint) {
    // طول الخط الأفقي
    double lineLength = _getLineLength(level);

    // رسم الخطوط العمودية بمواضع مطلقة ثابتة (RTL)
    for (int i = 1; i < level; i++) {
      // حساب الموضع المطلق الثابت من اليمين (RTL)
      final x = _getAbsoluteLevelPositionRTL(i, size.width);

      // استخدام ألوان مختلفة للمستويات المختلفة
      final levelPaint = Paint()
        ..color = _getLineColor(i)
        ..strokeWidth = _getLineWidth(i)
        ..style = PaintingStyle.stroke;

      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        levelPaint,
      );

      // إضافة نقاط ربط محسنة للمستويات العميقة (3+)
      if (i >= 3) {
        canvas.drawCircle(
          Offset(x, size.height * 0.3),
          2.0, // نقاط أكبر
          Paint()
            ..color = _getLineColor(i).withValues(alpha: 0.8)
            ..style = PaintingStyle.fill,
        );
        canvas.drawCircle(
          Offset(x, size.height * 0.7),
          2.0, // نقاط أكبر
          Paint()
            ..color = _getLineColor(i).withValues(alpha: 0.8)
            ..style = PaintingStyle.fill,
        );
      }
    }

    // رسم الخط الأفقي للمستوى الحالي بمواضع مطلقة (RTL)
    final currentX = _getAbsoluteLevelPositionRTL(level, size.width);
    final parentX = _getAbsoluteLevelPositionRTL(level - 1, size.width);
    final centerY = size.height / 2;

    // خط أفقي من العقدة الرئيسية إلى العقدة الحالية (RTL)
    canvas.drawLine(
      Offset(parentX, centerY),
      Offset(currentX + lineLength, centerY),
      paint,
    );

    // خط عمودي للربط مع تحسين للمستويات العميقة
    if (level >= 4) {
      // خط ربط أكثر وضوحاً للمستويات العميقة
      canvas.drawLine(
        Offset(parentX, centerY - 2),
        Offset(parentX, size.height),
        connectorPaint,
      );
      canvas.drawLine(
        Offset(parentX, centerY + 2),
        Offset(parentX, size.height),
        connectorPaint,
      );
    } else {
      canvas.drawLine(
        Offset(parentX, centerY),
        Offset(parentX, size.height),
        paint,
      );
    }

    // نقطة نهاية محسنة مع حجم أكبر (RTL)
    double dotSize = 3.5; // حجم أكبر وثابت
    canvas.drawCircle(
      Offset(currentX + lineLength, centerY),
      dotSize,
      dotPaint,
    );

    // إضافة حلقة خارجية ملونة لجميع المستويات
    canvas.drawCircle(
      Offset(currentX + lineLength, centerY),
      dotSize + 2.0,
      Paint()
        ..color = _getLineColor(level).withValues(alpha: 0.4)
        ..strokeWidth = 1.5
        ..style = PaintingStyle.stroke,
    );

    // حلقة إضافية للمستويات العميقة (4+)
    if (level >= 4) {
      canvas.drawCircle(
        Offset(currentX + lineLength, centerY),
        dotSize + 4.0,
        Paint()
          ..color = _getLineColor(level).withValues(alpha: 0.2)
          ..strokeWidth = 1.0
          ..style = PaintingStyle.stroke,
      );
    }
  }

  /// رسم خطوط الاتجاه الإنجليزي (LTR) مع تحسينات للمستويات العميقة
  void _paintLTRLines(Canvas canvas, Size size, Paint paint, Paint dotPaint,
      Paint connectorPaint) {
    // طول الخط الأفقي
    double lineLength = _getLineLength(level);

    // رسم الخطوط العمودية بمواضع مطلقة ثابتة
    for (int i = 1; i < level; i++) {
      // حساب الموضع المطلق الثابت لكل مستوى
      final x = _getAbsoluteLevelPosition(i);

      // استخدام ألوان مختلفة للمستويات المختلفة
      final levelPaint = Paint()
        ..color = _getLineColor(i)
        ..strokeWidth = _getLineWidth(i)
        ..style = PaintingStyle.stroke;

      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        levelPaint,
      );

      // إضافة نقاط ربط محسنة للمستويات العميقة (3+)
      if (i >= 3) {
        canvas.drawCircle(
          Offset(x, size.height * 0.3),
          2.0, // نقاط أكبر
          Paint()
            ..color = _getLineColor(i).withValues(alpha: 0.8)
            ..style = PaintingStyle.fill,
        );
        canvas.drawCircle(
          Offset(x, size.height * 0.7),
          2.0, // نقاط أكبر
          Paint()
            ..color = _getLineColor(i).withValues(alpha: 0.8)
            ..style = PaintingStyle.fill,
        );
      }
    }

    // رسم الخط الأفقي للمستوى الحالي بمواضع مطلقة
    final currentX = _getAbsoluteLevelPosition(level);
    final parentX = _getAbsoluteLevelPosition(level - 1);
    final centerY = size.height / 2;

    // خط أفقي من العقدة الرئيسية إلى العقدة الحالية
    canvas.drawLine(
      Offset(parentX, centerY),
      Offset(currentX - lineLength, centerY),
      paint,
    );

    // خط عمودي للربط مع تحسين للمستويات العميقة
    if (level >= 4) {
      // خط ربط أكثر وضوحاً للمستويات العميقة
      canvas.drawLine(
        Offset(parentX, centerY - 2),
        Offset(parentX, size.height),
        connectorPaint,
      );
      canvas.drawLine(
        Offset(parentX, centerY + 2),
        Offset(parentX, size.height),
        connectorPaint,
      );
    } else {
      canvas.drawLine(
        Offset(parentX, centerY),
        Offset(parentX, size.height),
        paint,
      );
    }

    // نقطة نهاية محسنة مع حجم أكبر
    double dotSize = 3.5; // حجم أكبر وثابت
    canvas.drawCircle(
      Offset(currentX - lineLength, centerY),
      dotSize,
      dotPaint,
    );

    // إضافة حلقة خارجية ملونة لجميع المستويات
    canvas.drawCircle(
      Offset(currentX - lineLength, centerY),
      dotSize + 2.0,
      Paint()
        ..color = _getLineColor(level).withValues(alpha: 0.4)
        ..strokeWidth = 1.5
        ..style = PaintingStyle.stroke,
    );

    // حلقة إضافية للمستويات العميقة (4+)
    if (level >= 4) {
      canvas.drawCircle(
        Offset(currentX - lineLength, centerY),
        dotSize + 4.0,
        Paint()
          ..color = _getLineColor(level).withValues(alpha: 0.2)
          ..strokeWidth = 1.0
          ..style = PaintingStyle.stroke,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
