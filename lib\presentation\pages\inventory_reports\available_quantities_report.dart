import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير الكميات المتاحة
/// يعرض الكميات المتاحة للبيع من جميع الأصناف
class AvailableQuantitiesReportPage extends StatefulWidget {
  const AvailableQuantitiesReportPage({super.key});

  @override
  State<AvailableQuantitiesReportPage> createState() => _AvailableQuantitiesReportPageState();
}

class _AvailableQuantitiesReportPageState extends State<AvailableQuantitiesReportPage> {
  String? _selectedCategory;
  String? _selectedWarehouse;
  String? _stockLevel = 'all';
  String? _sortBy = 'item_name';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الكميات المتاحة'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
            tooltip: 'تحديث البيانات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          _buildFilterSection(localizations),
          
          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildStockLevelsSection(),
                  const SizedBox(height: 16),
                  _buildAvailableQuantitiesTableSection(),
                  const SizedBox(height: 16),
                  _buildWarehouseDistributionSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.blue[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: localizations.category,
                    border: const OutlineInputBorder(),
                  ),
                  value: _selectedCategory,
                  items: [
                    DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                    DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                    DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                    DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                    const DropdownMenuItem(value: 'home', child: Text('أدوات منزلية')),
                  ],
                  onChanged: (value) => setState(() => _selectedCategory = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'المستودع',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedWarehouse,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع المستودعات')),
                    DropdownMenuItem(value: 'main', child: Text('المستودع الرئيسي')),
                    DropdownMenuItem(value: 'branch1', child: Text('مستودع الفرع الأول')),
                    DropdownMenuItem(value: 'branch2', child: Text('مستودع الفرع الثاني')),
                    DropdownMenuItem(value: 'branch3', child: Text('مستودع الفرع الثالث')),
                  ],
                  onChanged: (value) => setState(() => _selectedWarehouse = value),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'مستوى المخزون',
                    border: OutlineInputBorder(),
                  ),
                  value: _stockLevel,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع المستويات')),
                    DropdownMenuItem(value: 'high', child: Text('مخزون عالي')),
                    DropdownMenuItem(value: 'normal', child: Text('مخزون عادي')),
                    DropdownMenuItem(value: 'low', child: Text('مخزون منخفض')),
                    DropdownMenuItem(value: 'out_of_stock', child: Text('نفد المخزون')),
                  ],
                  onChanged: (value) => setState(() => _stockLevel = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'ترتيب حسب',
                    border: OutlineInputBorder(),
                  ),
                  value: _sortBy,
                  items: const [
                    DropdownMenuItem(value: 'item_name', child: Text('اسم الصنف')),
                    DropdownMenuItem(value: 'quantity', child: Text('الكمية')),
                    DropdownMenuItem(value: 'category', child: Text('الفئة')),
                    DropdownMenuItem(value: 'warehouse', child: Text('المستودع')),
                  ],
                  onChanged: (value) => setState(() => _sortBy = value),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.inventory, color: Colors.blue, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص الكميات المتاحة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي الأصناف', '1,245', Colors.blue, Icons.inventory_2),
                _buildSummaryCard('إجمالي الكميات', '15,850', Colors.green, Icons.numbers),
                _buildSummaryCard('قيمة المخزون', '2,450,000 ر.س', Colors.orange, Icons.monetization_on),
                _buildSummaryCard('أصناف نفدت', '25', Colors.red, Icons.warning),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStockLevelsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.bar_chart, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'مستويات المخزون',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildStockLevelCard('مخزون عالي', '485 صنف', Colors.green),
                _buildStockLevelCard('مخزون عادي', '625 صنف', Colors.blue),
                _buildStockLevelCard('مخزون منخفض', '110 صنف', Colors.orange),
                _buildStockLevelCard('نفد المخزون', '25 صنف', Colors.red),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAvailableQuantitiesTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل الكميات المتاحة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('اسم الصنف')),
                  DataColumn(label: Text('الفئة')),
                  DataColumn(label: Text('المستودع')),
                  DataColumn(label: Text('الكمية المتاحة')),
                  DataColumn(label: Text('الكمية المحجوزة')),
                  DataColumn(label: Text('الحد الأدنى')),
                  DataColumn(label: Text('الحد الأقصى')),
                  DataColumn(label: Text('مستوى المخزون')),
                  DataColumn(label: Text('آخر تحديث')),
                ],
                rows: _buildQuantitiesRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWarehouseDistributionSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.warehouse, color: Colors.purple, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'توزيع المخزون حسب المستودعات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildWarehouseDistributionList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _createPurchaseOrder,
                    icon: const Icon(Icons.shopping_cart),
                    label: const Text('إنشاء طلب شراء'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _transferStock,
                    icon: const Icon(Icons.transfer_within_a_station),
                    label: const Text('نقل مخزون'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _adjustInventory,
                    icon: const Icon(Icons.tune),
                    label: const Text('تعديل المخزون'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _setAlerts,
                    icon: const Icon(Icons.notifications),
                    label: const Text('تعيين تنبيهات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildWarehouseDistributionList() {
    final warehouses = [
      {'name': 'المستودع الرئيسي', 'items': '485 صنف', 'quantity': '8,250', 'value': '1,250,000 ر.س', 'utilization': '85%'},
      {'name': 'مستودع الفرع الأول', 'items': '325 صنف', 'quantity': '4,150', 'value': '650,000 ر.س', 'utilization': '72%'},
      {'name': 'مستودع الفرع الثاني', 'items': '285 صنف', 'quantity': '2,850', 'value': '425,000 ر.س', 'utilization': '68%'},
      {'name': 'مستودع الفرع الثالث', 'items': '150 صنف', 'quantity': '600', 'value': '125,000 ر.س', 'utilization': '45%'},
    ];

    return warehouses.map((warehouse) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: _getUtilizationColor(warehouse['utilization']!).withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: _getUtilizationColor(warehouse['utilization']!).withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.warehouse, color: Colors.purple, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  warehouse['name']!,
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  '${warehouse['items']} • الكمية: ${warehouse['quantity']} • القيمة: ${warehouse['value']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getUtilizationColor(warehouse['utilization']!).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              warehouse['utilization']!,
              style: TextStyle(
                color: _getUtilizationColor(warehouse['utilization']!),
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildQuantitiesRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('المستودع الرئيسي')),
        const DataCell(Text('125')),
        const DataCell(Text('15')),
        const DataCell(Text('20')),
        const DataCell(Text('200')),
        DataCell(_buildStockLevelBadge('عادي', Colors.blue)),
        const DataCell(Text('2024-01-20')),
      ]),
      DataRow(cells: [
        const DataCell(Text('هاتف آيفون 15')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('مستودع الفرع الأول')),
        const DataCell(Text('85')),
        const DataCell(Text('10')),
        const DataCell(Text('15')),
        const DataCell(Text('150')),
        DataCell(_buildStockLevelBadge('عالي', Colors.green)),
        const DataCell(Text('2024-01-20')),
      ]),
      DataRow(cells: [
        const DataCell(Text('طابعة HP LaserJet')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('المستودع الرئيسي')),
        const DataCell(Text('8')),
        const DataCell(Text('2')),
        const DataCell(Text('10')),
        const DataCell(Text('50')),
        DataCell(_buildStockLevelBadge('منخفض', Colors.orange)),
        const DataCell(Text('2024-01-19')),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStockLevelCard(String level, String count, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(level, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStockLevelBadge(String level, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(level, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Color _getUtilizationColor(String utilization) {
    final percentage = int.tryParse(utilization.replaceAll('%', '')) ?? 0;
    if (percentage >= 80) return Colors.green;
    if (percentage >= 60) return Colors.blue;
    if (percentage >= 40) return Colors.orange;
    return Colors.red;
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تحديث بيانات المخزون')),
    );
  }

  void _createPurchaseOrder() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء طلب شراء للأصناف المنخفضة')),
    );
  }

  void _transferStock() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('نقل المخزون بين المستودعات')),
    );
  }

  void _adjustInventory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تعديل كميات المخزون')),
    );
  }

  void _setAlerts() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تعيين تنبيهات المخزون')),
    );
  }
}
