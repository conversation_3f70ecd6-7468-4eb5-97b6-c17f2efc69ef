import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير السدادات الغير مرتبطة بفاتورة خلال فترة
/// يعرض المدفوعات التي لم يتم ربطها بفواتير محددة
class UnlinkedPaymentsPeriodReportPage extends StatefulWidget {
  const UnlinkedPaymentsPeriodReportPage({super.key});

  @override
  State<UnlinkedPaymentsPeriodReportPage> createState() => _UnlinkedPaymentsPeriodReportPageState();
}

class _UnlinkedPaymentsPeriodReportPageState extends State<UnlinkedPaymentsPeriodReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedCustomer;
  String? _paymentMethod = 'all';
  final String _sortBy = 'payment_date';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('السدادات الغير مرتبطة بفاتورة'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.link),
            onPressed: _linkPayments,
            tooltip: 'ربط المدفوعات',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilterSection(localizations),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildPaymentMethodsSection(),
                  const SizedBox(height: 16),
                  _buildUnlinkedPaymentsTableSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.red[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.fromDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, true),
                  controller: TextEditingController(
                    text: _startDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.toDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, false),
                  controller: TextEditingController(
                    text: _endDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'العميل',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedCustomer,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع العملاء')),
                    DropdownMenuItem(value: 'customer1', child: Text('أحمد محمد')),
                    DropdownMenuItem(value: 'customer2', child: Text('فاطمة علي')),
                    DropdownMenuItem(value: 'customer3', child: Text('محمد سالم')),
                  ],
                  onChanged: (value) => setState(() => _selectedCustomer = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'طريقة الدفع',
                    border: OutlineInputBorder(),
                  ),
                  value: _paymentMethod,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الطرق')),
                    DropdownMenuItem(value: 'cash', child: Text('نقداً')),
                    DropdownMenuItem(value: 'bank_transfer', child: Text('تحويل بنكي')),
                    DropdownMenuItem(value: 'check', child: Text('شيك')),
                    DropdownMenuItem(value: 'credit_card', child: Text('بطاقة ائتمان')),
                  ],
                  onChanged: (value) => setState(() => _paymentMethod = value),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.warning, color: Colors.red, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص السدادات الغير مرتبطة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي السدادات', '125', Colors.red, Icons.payment),
                _buildSummaryCard('إجمالي المبلغ', '485,000 ر.س', Colors.orange, Icons.monetization_on),
                _buildSummaryCard('متوسط السداد', '3,880 ر.س', Colors.blue, Icons.calculate),
                _buildSummaryCard('أقدم سداد', '45 يوم', Colors.purple, Icons.schedule),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethodsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.pie_chart, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'توزيع طرق الدفع',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildMethodCard('نقداً', '45 سداد', Colors.green),
                _buildMethodCard('تحويل بنكي', '35 سداد', Colors.blue),
                _buildMethodCard('شيك', '25 سداد', Colors.orange),
                _buildMethodCard('بطاقة ائتمان', '20 سداد', Colors.purple),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUnlinkedPaymentsTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل السدادات الغير مرتبطة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('رقم السداد')),
                  DataColumn(label: Text('التاريخ')),
                  DataColumn(label: Text('اسم العميل')),
                  DataColumn(label: Text('المبلغ')),
                  DataColumn(label: Text('طريقة الدفع')),
                  DataColumn(label: Text('رقم المرجع')),
                  DataColumn(label: Text('عدد الأيام')),
                  DataColumn(label: Text('الحالة')),
                  DataColumn(label: Text('إجراءات')),
                ],
                rows: _buildPaymentsRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _autoLinkPayments,
                    icon: const Icon(Icons.auto_fix_high),
                    label: const Text('ربط تلقائي'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _manualLinkPayments,
                    icon: const Icon(Icons.link),
                    label: const Text('ربط يدوي'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _refundPayments,
                    icon: const Icon(Icons.undo),
                    label: const Text('استرداد المدفوعات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _sendNotifications,
                    icon: const Icon(Icons.notifications),
                    label: const Text('إرسال تنبيهات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<DataRow> _buildPaymentsRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('PAY-001')),
        const DataCell(Text('2024-01-15')),
        const DataCell(Text('أحمد محمد')),
        const DataCell(Text('15,000 ر.س')),
        DataCell(_buildMethodBadge('نقداً', Colors.green)),
        const DataCell(Text('REF-12345')),
        const DataCell(Text('15 يوم')),
        DataCell(_buildStatusBadge('غير مرتبط', Colors.red)),
        DataCell(_buildActionButton()),
      ]),
      DataRow(cells: [
        const DataCell(Text('PAY-002')),
        const DataCell(Text('2024-01-18')),
        const DataCell(Text('فاطمة علي')),
        const DataCell(Text('25,000 ر.س')),
        DataCell(_buildMethodBadge('تحويل بنكي', Colors.blue)),
        const DataCell(Text('TRF-67890')),
        const DataCell(Text('12 يوم')),
        DataCell(_buildStatusBadge('غير مرتبط', Colors.red)),
        DataCell(_buildActionButton()),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMethodCard(String method, String count, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(method, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMethodBadge(String method, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(method, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(status, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Widget _buildActionButton() {
    return ElevatedButton(
      onPressed: () => _linkPayment(),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        minimumSize: const Size(80, 32),
      ),
      child: const Text('ربط', style: TextStyle(fontSize: 10)),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _linkPayments() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ربط المدفوعات بالفواتير')),
    );
  }

  void _autoLinkPayments() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ربط تلقائي للمدفوعات')),
    );
  }

  void _manualLinkPayments() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ربط يدوي للمدفوعات')),
    );
  }

  void _refundPayments() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('استرداد المدفوعات')),
    );
  }

  void _sendNotifications() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إرسال تنبيهات للعملاء')),
    );
  }

  void _linkPayment() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ربط المدفوعة بفاتورة')),
    );
  }
}
