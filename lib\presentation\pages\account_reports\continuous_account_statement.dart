import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة كشف الحساب المستمر
/// تعرض كشف حساب مفصل ومستمر
class ContinuousAccountStatementPage extends StatefulWidget {
  const ContinuousAccountStatementPage({super.key});

  @override
  State<ContinuousAccountStatementPage> createState() => _ContinuousAccountStatementPageState();
}

class _ContinuousAccountStatementPageState extends State<ContinuousAccountStatementPage> {
  String _selectedAccount = '1101';

  // بيانات تجريبية لكشف الحساب
  final Map<String, List<Map<String, dynamic>>> _accountStatements = {
    '1101': [
      {
        'date': '2024-01-01',
        'description': 'رصيد أول المدة',
        'reference': 'OB-001',
        'debit': 10000.0,
        'credit': 0.0,
        'balance': 10000.0,
      },
      {
        'date': '2024-01-15',
        'description': 'إيداع نقدي',
        'reference': 'JE-001',
        'debit': 5000.0,
        'credit': 0.0,
        'balance': 15000.0,
      },
      {
        'date': '2024-01-16',
        'description': 'سحب نقدي',
        'reference': 'JE-002',
        'debit': 0.0,
        'credit': 1000.0,
        'balance': 14000.0,
      },
      {
        'date': '2024-01-20',
        'description': 'مبيعات نقدية',
        'reference': 'INV-001',
        'debit': 8000.0,
        'credit': 0.0,
        'balance': 22000.0,
      },
    ],
    '1102': [
      {
        'date': '2024-01-01',
        'description': 'رصيد أول المدة',
        'reference': 'OB-002',
        'debit': 50000.0,
        'credit': 0.0,
        'balance': 50000.0,
      },
      {
        'date': '2024-01-17',
        'description': 'إيداع بنكي',
        'reference': 'JE-003',
        'debit': 10000.0,
        'credit': 0.0,
        'balance': 60000.0,
      },
      {
        'date': '2024-01-19',
        'description': 'دفع راتب',
        'reference': 'PAY-001',
        'debit': 0.0,
        'credit': 8000.0,
        'balance': 52000.0,
      },
    ],
  };

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final currentStatements = _accountStatements[_selectedAccount] ?? [];
    
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.continuousAccountStatement),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedAccount,
                    decoration: const InputDecoration(
                      labelText: 'الحساب',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: '1101', child: Text('1101 - النقدية في الصندوق')),
                      DropdownMenuItem(value: '1102', child: Text('1102 - البنك الأهلي')),
                      DropdownMenuItem(value: '1201', child: Text('1201 - العملاء')),
                      DropdownMenuItem(value: '2101', child: Text('2101 - الموردين')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedAccount = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
          ),

          // معلومات الحساب
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              color: Colors.red[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      _getAccountName(_selectedAccount),
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Column(
                          children: [
                            const Text('رصيد أول المدة'),
                            Text(
                              '${_getOpeningBalance(currentStatements)} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('الرصيد الحالي'),
                            Text(
                              '${_getCurrentBalance(currentStatements)} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('عدد الحركات'),
                            Text(
                              currentStatements.length.toString(),
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.orange,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

          // كشف الحساب
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: currentStatements.length,
              itemBuilder: (context, index) {
                final statement = currentStatements[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: statement['debit'] > 0 ? Colors.green : Colors.red,
                      child: Icon(
                        statement['debit'] > 0 ? Icons.add : Icons.remove,
                        color: Colors.white,
                      ),
                    ),
                    title: Text(
                      statement['description'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('التاريخ: ${statement['date']}'),
                        Text('المرجع: ${statement['reference']}'),
                        Row(
                          children: [
                            if (statement['debit'] > 0)
                              Text(
                                'مدين: ${statement['debit'].toStringAsFixed(2)} ر.س',
                                style: const TextStyle(
                                  color: Colors.green,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            if (statement['credit'] > 0)
                              Text(
                                'دائن: ${statement['credit'].toStringAsFixed(2)} ر.س',
                                style: const TextStyle(
                                  color: Colors.red,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                    trailing: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        const Text(
                          'الرصيد',
                          style: TextStyle(fontSize: 10),
                        ),
                        Text(
                          '${statement['balance'].toStringAsFixed(2)} ر.س',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                    isThreeLine: true,
                  ),
                );
              },
            ),
          ),

          // ملخص الحساب
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.red[50],
              border: Border(top: BorderSide(color: Colors.red[200]!)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Text(
                  'إجمالي المدين: ${_getTotalDebit(currentStatements)} ر.س',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
                Text(
                  'إجمالي الدائن: ${_getTotalCredit(currentStatements)} ر.س',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshData,
        backgroundColor: Colors.red,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث البيانات'),
      ),
    );
  }

  String _getAccountName(String accountCode) {
    switch (accountCode) {
      case '1101':
        return '1101 - النقدية في الصندوق';
      case '1102':
        return '1102 - البنك الأهلي';
      case '1201':
        return '1201 - العملاء';
      case '2101':
        return '2101 - الموردين';
      default:
        return 'حساب غير معروف';
    }
  }

  double _getOpeningBalance(List<Map<String, dynamic>> statements) {
    if (statements.isEmpty) return 0.0;
    return statements.first['balance'] - statements.first['debit'] + statements.first['credit'];
  }

  double _getCurrentBalance(List<Map<String, dynamic>> statements) {
    if (statements.isEmpty) return 0.0;
    return statements.last['balance'];
  }

  double _getTotalDebit(List<Map<String, dynamic>> statements) {
    return statements.fold(0.0, (sum, statement) => sum + statement['debit']);
  }

  double _getTotalCredit(List<Map<String, dynamic>> statements) {
    return statements.fold(0.0, (sum, statement) => sum + statement['credit']);
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة كشف الحساب المستمر')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير كشف الحساب المستمر')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث بيانات كشف الحساب'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
