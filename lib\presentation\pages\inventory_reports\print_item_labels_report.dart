import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير طباعة ملصقات الأصناف
/// يتيح طباعة ملصقات للأصناف مع الباركود والمعلومات الأساسية
class PrintItemLabelsReportPage extends StatefulWidget {
  const PrintItemLabelsReportPage({super.key});

  @override
  State<PrintItemLabelsReportPage> createState() => _PrintItemLabelsReportPageState();
}

class _PrintItemLabelsReportPageState extends State<PrintItemLabelsReportPage> {
  String? _selectedCategory;
  String? _selectedWarehouse;
  String? _labelSize = 'medium';
  String? _labelType = 'barcode_price';
  bool _includeBarcode = true;
  bool _includePrice = true;
  bool _includeDescription = true;
  int _copiesPerItem = 1;
  List<String> _selectedItems = [];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('طباعة ملصقات الأصناف'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printLabels,
            tooltip: 'طباعة الملصقات',
          ),
          IconButton(
            icon: const Icon(Icons.preview),
            onPressed: _previewLabels,
            tooltip: 'معاينة الملصقات',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _labelSettings,
            tooltip: 'إعدادات الملصقات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة والإعدادات
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.teal[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.category,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedCategory,
                        items: [
                          DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                          DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                          DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                          DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                        ],
                        onChanged: (value) => setState(() => _selectedCategory = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.warehouse,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedWarehouse,
                        items: [
                          DropdownMenuItem(value: 'all', child: Text(localizations.allWarehouses)),
                          DropdownMenuItem(value: 'main', child: Text(localizations.mainWarehouse)),
                          DropdownMenuItem(value: 'branch1', child: Text('${localizations.branchWarehouse} الأول')),
                          DropdownMenuItem(value: 'branch2', child: Text('${localizations.branchWarehouse} الثاني')),
                        ],
                        onChanged: (value) => setState(() => _selectedWarehouse = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'حجم الملصق',
                          border: OutlineInputBorder(),
                        ),
                        value: _labelSize,
                        items: const [
                          DropdownMenuItem(value: 'small', child: Text('صغير (2x1 سم)')),
                          DropdownMenuItem(value: 'medium', child: Text('متوسط (4x2 سم)')),
                          DropdownMenuItem(value: 'large', child: Text('كبير (6x3 سم)')),
                          DropdownMenuItem(value: 'custom', child: Text('مخصص')),
                        ],
                        onChanged: (value) => setState(() => _labelSize = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'نوع الملصق',
                          border: OutlineInputBorder(),
                        ),
                        value: _labelType,
                        items: const [
                          DropdownMenuItem(value: 'barcode_only', child: Text('باركود فقط')),
                          DropdownMenuItem(value: 'barcode_price', child: Text('باركود + سعر')),
                          DropdownMenuItem(value: 'full_info', child: Text('معلومات كاملة')),
                          DropdownMenuItem(value: 'qr_code', child: Text('رمز QR')),
                        ],
                        onChanged: (value) => setState(() => _labelType = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'عدد النسخ لكل صنف',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        initialValue: _copiesPerItem.toString(),
                        onChanged: (value) => _copiesPerItem = int.tryParse(value) ?? 1,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _loadItems,
                        icon: const Icon(Icons.refresh),
                        label: const Text('تحميل الأصناف'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.teal,
                          foregroundColor: Colors.white,
                          minimumSize: const Size(double.infinity, 48),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // خيارات الملصق
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'خيارات الملصق',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: CheckboxListTile(
                        title: const Text('تضمين الباركود'),
                        value: _includeBarcode,
                        onChanged: (value) => setState(() => _includeBarcode = value ?? true),
                        dense: true,
                      ),
                    ),
                    Expanded(
                      child: CheckboxListTile(
                        title: const Text('تضمين السعر'),
                        value: _includePrice,
                        onChanged: (value) => setState(() => _includePrice = value ?? true),
                        dense: true,
                      ),
                    ),
                    Expanded(
                      child: CheckboxListTile(
                        title: const Text('تضمين الوصف'),
                        value: _includeDescription,
                        onChanged: (value) => setState(() => _includeDescription = value ?? true),
                        dense: true,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // قائمة الأصناف
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // إحصائيات الطباعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.label, color: Colors.teal, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'إحصائيات الطباعة',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildStatCard('الأصناف المحددة', '${_selectedItems.length}', Colors.blue, Icons.check_box),
                              _buildStatCard('إجمالي الملصقات', '${_selectedItems.length * _copiesPerItem}', Colors.green, Icons.label_outline),
                              _buildStatCard('الصفحات المتوقعة', '${((_selectedItems.length * _copiesPerItem) / 24).ceil()}', Colors.orange, Icons.description),
                              _buildStatCard('التكلفة المتوقعة', '${(_selectedItems.length * _copiesPerItem * 0.05).toStringAsFixed(2)} ر.س', Colors.purple, Icons.monetization_on),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // قائمة الأصناف للطباعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'الأصناف المتاحة للطباعة',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Row(
                                children: [
                                  TextButton(
                                    onPressed: _selectAllItems,
                                    child: const Text('تحديد الكل'),
                                  ),
                                  TextButton(
                                    onPressed: _clearSelection,
                                    child: const Text('إلغاء التحديد'),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('تحديد')),
                                DataColumn(label: Text('كود الصنف')),
                                DataColumn(label: Text('اسم الصنف')),
                                DataColumn(label: Text('التصنيف')),
                                DataColumn(label: Text('السعر')),
                                DataColumn(label: Text('الكمية المتاحة')),
                                DataColumn(label: Text('المستودع')),
                                DataColumn(label: Text('معاينة الملصق')),
                              ],
                              rows: _buildItemRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // معاينة الملصق
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'معاينة تصميم الملصق',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Center(
                            child: Container(
                              width: 200,
                              height: 120,
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: BorderRadius.circular(8),
                                color: Colors.white,
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  if (_includeDescription)
                                    const Text(
                                      'لابتوب ديل XPS 13',
                                      style: TextStyle(fontSize: 10, fontWeight: FontWeight.bold),
                                      textAlign: TextAlign.center,
                                    ),
                                  if (_includeBarcode)
                                    Container(
                                      width: 120,
                                      height: 30,
                                      margin: const EdgeInsets.symmetric(vertical: 4),
                                      decoration: BoxDecoration(
                                        border: Border.all(color: Colors.black),
                                      ),
                                      child: const Center(
                                        child: Text(
                                          '||||| |||| |||||',
                                          style: TextStyle(fontSize: 8),
                                        ),
                                      ),
                                    ),
                                  if (_includePrice)
                                    const Text(
                                      '2,500 ر.س',
                                      style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
                                    ),
                                  const Text(
                                    'ITEM-001',
                                    style: TextStyle(fontSize: 8),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات الطباعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات الطباعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _previewLabels,
                                  icon: const Icon(Icons.preview),
                                  label: const Text('معاينة الملصقات'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _printLabels,
                                  icon: const Icon(Icons.print),
                                  label: const Text('طباعة الملصقات'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _saveTemplate,
                                  icon: const Icon(Icons.save),
                                  label: const Text('حفظ القالب'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _exportToPDF,
                                  icon: const Icon(Icons.picture_as_pdf),
                                  label: const Text('تصدير PDF'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<DataRow> _buildItemRows() {
    final items = [
      {'code': 'ITEM-001', 'name': 'لابتوب ديل XPS 13', 'category': 'إلكترونيات', 'price': '2,500', 'quantity': '85', 'warehouse': 'الرئيسي'},
      {'code': 'ITEM-002', 'name': 'طابعة HP LaserJet', 'category': 'إلكترونيات', 'price': '800', 'quantity': '45', 'warehouse': 'الرئيسي'},
      {'code': 'ITEM-003', 'name': 'ماوس لوجيتك', 'category': 'إلكترونيات', 'price': '50', 'quantity': '120', 'warehouse': 'الفرع الأول'},
    ];

    return items.map((item) => DataRow(cells: [
      DataCell(
        Checkbox(
          value: _selectedItems.contains(item['code']),
          onChanged: (value) {
            setState(() {
              if (value == true) {
                _selectedItems.add(item['code']!);
              } else {
                _selectedItems.remove(item['code']);
              }
            });
          },
        ),
      ),
      DataCell(Text(item['code']!)),
      DataCell(Text(item['name']!)),
      DataCell(Text(item['category']!)),
      DataCell(Text('${item['price']} ر.س')),
      DataCell(Text(item['quantity']!)),
      DataCell(Text(item['warehouse']!)),
      DataCell(
        IconButton(
          icon: const Icon(Icons.visibility, color: Colors.blue),
          onPressed: () => _previewSingleLabel(item['code']!),
          tooltip: 'معاينة الملصق',
        ),
      ),
    ])).toList();
  }

  Widget _buildStatCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _loadItems() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تحميل الأصناف بنجاح')),
    );
  }

  void _selectAllItems() {
    setState(() {
      _selectedItems = ['ITEM-001', 'ITEM-002', 'ITEM-003'];
    });
  }

  void _clearSelection() {
    setState(() {
      _selectedItems.clear();
    });
  }

  void _previewLabels() {
    if (_selectedItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى تحديد أصناف للمعاينة')),
      );
      return;
    }
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('معاينة ${_selectedItems.length * _copiesPerItem} ملصق')),
    );
  }

  void _printLabels() {
    if (_selectedItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى تحديد أصناف للطباعة')),
      );
      return;
    }
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('جاري طباعة ${_selectedItems.length * _copiesPerItem} ملصق...')),
    );
  }

  void _labelSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('فتح إعدادات الملصقات')),
    );
  }

  void _previewSingleLabel(String itemCode) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('معاينة ملصق الصنف $itemCode')),
    );
  }

  void _saveTemplate() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم حفظ قالب الملصق')),
    );
  }

  void _exportToPDF() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تصدير الملصقات إلى PDF')),
    );
  }
}
