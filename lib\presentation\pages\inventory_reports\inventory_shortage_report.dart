import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة كشف نواقص بضاعة
/// يعرض تقرير بالأصناف التي تعاني من نقص في الكميات
class InventoryShortageReportPage extends StatefulWidget {
  const InventoryShortageReportPage({super.key});

  @override
  State<InventoryShortageReportPage> createState() => _InventoryShortageReportPageState();
}

class _InventoryShortageReportPageState extends State<InventoryShortageReportPage> {
  DateTime? _reportDate;
  String? _selectedWarehouse;
  String? _selectedCategory;
  String? _shortageLevel = 'all';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('كشف نواقص بضاعة'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.notification_important),
            onPressed: _sendAlerts,
            tooltip: 'إرسال تنبيهات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.red[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'تاريخ التقرير',
                          border: OutlineInputBorder(),
                          suffixIcon: Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context),
                        controller: TextEditingController(
                          text: _reportDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.warehouse,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedWarehouse,
                        items: [
                          DropdownMenuItem(value: 'all', child: Text(localizations.allWarehouses)),
                          DropdownMenuItem(value: 'main', child: Text(localizations.mainWarehouse)),
                          DropdownMenuItem(value: 'branch1', child: Text('${localizations.branchWarehouse} الأول')),
                          DropdownMenuItem(value: 'branch2', child: Text('${localizations.branchWarehouse} الثاني')),
                        ],
                        onChanged: (value) => setState(() => _selectedWarehouse = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.category,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedCategory,
                        items: [
                          DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                          DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                          DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                          DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                        ],
                        onChanged: (value) => setState(() => _selectedCategory = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'مستوى النقص',
                          border: OutlineInputBorder(),
                        ),
                        value: _shortageLevel,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع المستويات')),
                          DropdownMenuItem(value: 'critical', child: Text('نقص حرج')),
                          DropdownMenuItem(value: 'moderate', child: Text('نقص متوسط')),
                          DropdownMenuItem(value: 'low', child: Text('نقص منخفض')),
                        ],
                        onChanged: (value) => setState(() => _shortageLevel = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.search),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص النواقص
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.warning, color: Colors.red, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'ملخص نواقص البضاعة',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSummaryCard('إجمالي النواقص', '89', Colors.red, Icons.warning),
                              _buildSummaryCard('نقص حرج', '23', Colors.red[800]!, Icons.error),
                              _buildSummaryCard('نقص متوسط', '34', Colors.orange, Icons.warning_amber),
                              _buildSummaryCard('نقص منخفض', '32', Colors.yellow[700]!, Icons.info),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // توزيع النواقص حسب المستوى
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'توزيع النواقص حسب المستوى',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Container(
                            height: 200,
                            width: double.infinity,
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey[300]!),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  flex: 26,
                                  child: Container(
                                    margin: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.red.withOpacity(0.8),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: const Center(
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Text('نقص حرج', style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
                                          Text('23 صنف', style: TextStyle(color: Colors.white, fontSize: 12)),
                                          Text('26%', style: TextStyle(color: Colors.white, fontSize: 12)),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                Expanded(
                                  flex: 38,
                                  child: Container(
                                    margin: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.orange.withOpacity(0.8),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: const Center(
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Text('نقص متوسط', style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
                                          Text('34 صنف', style: TextStyle(color: Colors.white, fontSize: 12)),
                                          Text('38%', style: TextStyle(color: Colors.white, fontSize: 12)),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                Expanded(
                                  flex: 36,
                                  child: Container(
                                    margin: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.yellow[700]!.withOpacity(0.8),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: const Center(
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Text('نقص منخفض', style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
                                          Text('32 صنف', style: TextStyle(color: Colors.white, fontSize: 12)),
                                          Text('36%', style: TextStyle(color: Colors.white, fontSize: 12)),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول تفاصيل النواقص
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل نواقص البضاعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('كود الصنف')),
                                DataColumn(label: Text('اسم الصنف')),
                                DataColumn(label: Text('الكمية الحالية')),
                                DataColumn(label: Text('الحد الأدنى')),
                                DataColumn(label: Text('النقص')),
                                DataColumn(label: Text('مستوى النقص')),
                                DataColumn(label: Text('المستودع')),
                                DataColumn(label: Text('آخر حركة')),
                                DataColumn(label: Text('الإجراء المطلوب')),
                              ],
                              rows: _buildShortageRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // النواقص الحرجة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.error, color: Colors.red, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'النواقص الحرجة (تحتاج إجراء فوري)',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          ..._buildCriticalShortagesList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _createPurchaseOrder,
                                  icon: const Icon(Icons.shopping_cart),
                                  label: const Text('أمر شراء'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _transferFromOtherWarehouse,
                                  icon: const Icon(Icons.transfer_within_a_station),
                                  label: const Text('نقل من مستودع'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _adjustMinimumLevels,
                                  icon: const Icon(Icons.tune),
                                  label: const Text('تعديل الحدود'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _scheduleReorder,
                                  icon: const Icon(Icons.schedule),
                                  label: const Text('جدولة إعادة طلب'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<DataRow> _buildShortageRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('ITEM-001')),
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('2')),
        const DataCell(Text('10')),
        const DataCell(Text('8')),
        DataCell(_buildShortageLevelBadge('حرج', Colors.red)),
        const DataCell(Text('المستودع الرئيسي')),
        const DataCell(Text('2024-02-10')),
        DataCell(_buildActionBadge('شراء فوري', Colors.red)),
      ]),
      DataRow(cells: [
        const DataCell(Text('ITEM-002')),
        const DataCell(Text('قميص قطني')),
        const DataCell(Text('15')),
        const DataCell(Text('25')),
        const DataCell(Text('10')),
        DataCell(_buildShortageLevelBadge('متوسط', Colors.orange)),
        const DataCell(Text('الفرع الأول')),
        const DataCell(Text('2024-02-12')),
        DataCell(_buildActionBadge('نقل من فرع', Colors.orange)),
      ]),
      DataRow(cells: [
        const DataCell(Text('ITEM-003')),
        const DataCell(Text('عصير برتقال')),
        const DataCell(Text('45')),
        const DataCell(Text('50')),
        const DataCell(Text('5')),
        DataCell(_buildShortageLevelBadge('منخفض', Colors.yellow[700]!)),
        const DataCell(Text('الفرع الثاني')),
        const DataCell(Text('2024-02-14')),
        DataCell(_buildActionBadge('مراقبة', Colors.yellow[700]!)),
      ]),
    ];
  }

  List<Widget> _buildCriticalShortagesList() {
    final criticalItems = [
      {'name': 'لابتوب ديل XPS 13', 'current': '2', 'minimum': '10', 'shortage': '8', 'impact': 'عالي'},
      {'name': 'هاتف آيفون 15', 'current': '0', 'minimum': '5', 'shortage': '5', 'impact': 'عالي'},
      {'name': 'طابعة HP LaserJet', 'current': '1', 'minimum': '8', 'shortage': '7', 'impact': 'متوسط'},
      {'name': 'ساعة ذكية', 'current': '3', 'minimum': '12', 'shortage': '9', 'impact': 'متوسط'},
    ];

    return criticalItems.map((item) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.red.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.red.withOpacity(0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.error, color: Colors.red, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item['name']!,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'الحالي: ${item['current']} | المطلوب: ${item['minimum']} | النقص: ${item['shortage']}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: item['impact'] == 'عالي' ? Colors.red : Colors.orange,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              'تأثير ${item['impact']!}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildShortageLevelBadge(String level, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        level,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildActionBadge(String action, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        action,
        style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        _reportDate = picked;
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء كشف نواقص البضاعة بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _sendAlerts() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إرسال تنبيهات النواقص')),
    );
  }

  void _createPurchaseOrder() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء أمر شراء للأصناف الناقصة')),
    );
  }

  void _transferFromOtherWarehouse() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('نقل من مستودع آخر')),
    );
  }

  void _adjustMinimumLevels() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تعديل الحدود الدنيا للأصناف')),
    );
  }

  void _scheduleReorder() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جدولة إعادة طلب تلقائي')),
    );
  }
}
