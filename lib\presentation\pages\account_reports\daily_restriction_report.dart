import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير القيود اليومية
/// تعرض القيود المحاسبية اليومية
class DailyRestrictionReportPage extends StatefulWidget {
  const DailyRestrictionReportPage({super.key});

  @override
  State<DailyRestrictionReportPage> createState() => _DailyRestrictionReportPageState();
}

class _DailyRestrictionReportPageState extends State<DailyRestrictionReportPage> {
  String _selectedDate = 'today';

  // بيانات تجريبية للقيود اليومية
  final List<Map<String, dynamic>> _dailyEntries = [
    {
      'entryNumber': 'JE-001',
      'date': '2024-01-15',
      'description': 'إيداع نقدي من العميل أحمد محمد',
      'totalAmount': 5000.0,
      'details': [
        {'account': '1101 - النقدية في الصندوق', 'debit': 5000.0, 'credit': 0.0},
        {'account': '1201 - العملاء', 'debit': 0.0, 'credit': 5000.0},
      ],
    },
    {
      'entryNumber': 'JE-002',
      'date': '2024-01-15',
      'description': 'مبيعات نقدية',
      'totalAmount': 15000.0,
      'details': [
        {'account': '1101 - النقدية في الصندوق', 'debit': 15000.0, 'credit': 0.0},
        {'account': '4101 - إيرادات المبيعات', 'debit': 0.0, 'credit': 15000.0},
      ],
    },
    {
      'entryNumber': 'JE-003',
      'date': '2024-01-15',
      'description': 'دفع راتب الموظف',
      'totalAmount': 8000.0,
      'details': [
        {'account': '5101 - مصروفات الرواتب', 'debit': 8000.0, 'credit': 0.0},
        {'account': '1102 - البنك الأهلي', 'debit': 0.0, 'credit': 8000.0},
      ],
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.dailyRestrictionReport),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedDate,
                    decoration: const InputDecoration(
                      labelText: 'التاريخ',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'today', child: Text('اليوم')),
                      DropdownMenuItem(value: 'yesterday', child: Text('أمس')),
                      DropdownMenuItem(value: 'this_week', child: Text('هذا الأسبوع')),
                      DropdownMenuItem(value: 'custom', child: Text('تاريخ مخصص')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedDate = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                _buildStatCard('عدد القيود', _dailyEntries.length.toString(), Colors.blue),
                _buildStatCard('إجمالي المبالغ', '${_getTotalAmount()} ر.س', Colors.green),
                _buildStatCard('متوسط القيد', '${_getAverageAmount()} ر.س', Colors.orange),
                _buildStatCard('أكبر قيد', '${_getMaxAmount()} ر.س', Colors.purple),
              ],
            ),
          ),

          // قائمة القيود اليومية
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _dailyEntries.length,
              itemBuilder: (context, index) {
                final entry = _dailyEntries[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: Colors.teal,
                      child: Text(
                        (index + 1).toString(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    title: Text(
                      'قيد رقم ${entry['entryNumber']}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('التاريخ: ${entry['date']}'),
                        Text('الوصف: ${entry['description']}'),
                        Text(
                          'المبلغ: ${entry['totalAmount'].toStringAsFixed(2)} ر.س',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                        ),
                      ],
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'تفاصيل القيد:',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(height: 8),
                            ...entry['details'].map<Widget>((detail) {
                              return Padding(
                                padding: const EdgeInsets.symmetric(vertical: 4.0),
                                child: Row(
                                  children: [
                                    Expanded(
                                      flex: 3,
                                      child: Text(detail['account']),
                                    ),
                                    Expanded(
                                      child: Text(
                                        detail['debit'] > 0 
                                            ? '${detail['debit'].toStringAsFixed(2)} ر.س'
                                            : '-',
                                        style: const TextStyle(
                                          color: Colors.green,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    Expanded(
                                      child: Text(
                                        detail['credit'] > 0 
                                            ? '${detail['credit'].toStringAsFixed(2)} ر.س'
                                            : '-',
                                        style: const TextStyle(
                                          color: Colors.red,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }).toList(),
                            const Divider(),
                            Row(
                              children: [
                                const Expanded(
                                  flex: 3,
                                  child: Text(
                                    'الإجمالي:',
                                    style: TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                ),
                                Expanded(
                                  child: Text(
                                    '${_getEntryTotalDebit(entry)} ر.س',
                                    style: const TextStyle(
                                      color: Colors.green,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                                Expanded(
                                  child: Text(
                                    '${_getEntryTotalCredit(entry)} ر.س',
                                    style: const TextStyle(
                                      color: Colors.red,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshData,
        backgroundColor: Colors.teal,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث البيانات'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  double _getTotalAmount() {
    return _dailyEntries.fold(0.0, (sum, entry) => sum + entry['totalAmount']);
  }

  String _getAverageAmount() {
    if (_dailyEntries.isEmpty) return '0.00';
    return (_getTotalAmount() / _dailyEntries.length).toStringAsFixed(2);
  }

  String _getMaxAmount() {
    if (_dailyEntries.isEmpty) return '0.00';
    double max = _dailyEntries.map((entry) => entry['totalAmount'] as double).reduce((a, b) => a > b ? a : b);
    return max.toStringAsFixed(2);
  }

  double _getEntryTotalDebit(Map<String, dynamic> entry) {
    return entry['details'].fold(0.0, (sum, detail) => sum + detail['debit']);
  }

  double _getEntryTotalCredit(Map<String, dynamic> entry) {
    return entry['details'].fold(0.0, (sum, detail) => sum + detail['credit']);
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة تقرير القيود اليومية')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير تقرير القيود اليومية')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث بيانات القيود اليومية'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
