import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير تغير تصنيفات الأصناف
/// يعرض التغييرات في تصنيفات الأصناف عبر الوقت
class ItemClassificationChangesReportPage extends StatefulWidget {
  const ItemClassificationChangesReportPage({super.key});

  @override
  State<ItemClassificationChangesReportPage> createState() => _ItemClassificationChangesReportPageState();
}

class _ItemClassificationChangesReportPageState extends State<ItemClassificationChangesReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedCategory;
  String? _changeType = 'all';
  String? _selectedEmployee;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('تغير تصنيفات الأصناف'),
        backgroundColor: Colors.brown,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: _showHistory,
            tooltip: 'عرض التاريخ',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          _buildFilterSection(localizations),
          
          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildChangeTypesSection(),
                  const SizedBox(height: 16),
                  _buildChangesTableSection(),
                  const SizedBox(height: 16),
                  _buildImpactAnalysisSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.brown[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.fromDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, true),
                  controller: TextEditingController(
                    text: _startDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.toDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, false),
                  controller: TextEditingController(
                    text: _endDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: localizations.category,
                    border: const OutlineInputBorder(),
                  ),
                  value: _selectedCategory,
                  items: [
                    DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                    DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                    DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                    DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                  ],
                  onChanged: (value) => setState(() => _selectedCategory = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'نوع التغيير',
                    border: OutlineInputBorder(),
                  ),
                  value: _changeType,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع التغييرات')),
                    DropdownMenuItem(value: 'category_change', child: Text('تغيير الفئة')),
                    DropdownMenuItem(value: 'subcategory_change', child: Text('تغيير الفئة الفرعية')),
                    DropdownMenuItem(value: 'brand_change', child: Text('تغيير العلامة التجارية')),
                    DropdownMenuItem(value: 'unit_change', child: Text('تغيير الوحدة')),
                  ],
                  onChanged: (value) => setState(() => _changeType = value),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'الموظف',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedEmployee,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الموظفين')),
                    DropdownMenuItem(value: 'emp1', child: Text('أحمد محمد')),
                    DropdownMenuItem(value: 'emp2', child: Text('فاطمة علي')),
                    DropdownMenuItem(value: 'emp3', child: Text('محمد سالم')),
                  ],
                  onChanged: (value) => setState(() => _selectedEmployee = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.analytics),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.brown,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.change_circle, color: Colors.brown, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص تغييرات التصنيفات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي التغييرات', '185', Colors.brown, Icons.swap_horiz),
                _buildSummaryCard('أصناف متأثرة', '125', Colors.blue, Icons.inventory),
                _buildSummaryCard('تغييرات الفئة', '85', Colors.green, Icons.category),
                _buildSummaryCard('تغييرات العلامة', '45', Colors.orange, Icons.branding_watermark),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChangeTypesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.pie_chart, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'توزيع أنواع التغييرات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildChangeTypeCard('تغيير الفئة', '85 تغيير', Colors.blue),
                _buildChangeTypeCard('تغيير الفئة الفرعية', '55 تغيير', Colors.green),
                _buildChangeTypeCard('تغيير العلامة', '45 تغيير', Colors.orange),
                _buildChangeTypeCard('تغيير الوحدة', '25 تغيير', Colors.purple),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChangesTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل تغييرات التصنيفات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('اسم الصنف')),
                  DataColumn(label: Text('نوع التغيير')),
                  DataColumn(label: Text('القيمة السابقة')),
                  DataColumn(label: Text('القيمة الجديدة')),
                  DataColumn(label: Text('تاريخ التغيير')),
                  DataColumn(label: Text('الموظف')),
                  DataColumn(label: Text('السبب')),
                  DataColumn(label: Text('التأثير')),
                ],
                rows: _buildChangesRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImpactAnalysisSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.assessment, color: Colors.teal, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'تحليل التأثير',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildImpactAnalysisList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _bulkClassificationUpdate,
                    icon: const Icon(Icons.update),
                    label: const Text('تحديث جماعي'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _validateClassifications,
                    icon: const Icon(Icons.check),
                    label: const Text('التحقق من التصنيفات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _revertChanges,
                    icon: const Icon(Icons.undo),
                    label: const Text('التراجع عن التغييرات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _auditTrail,
                    icon: const Icon(Icons.history),
                    label: const Text('مسار التدقيق'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildImpactAnalysisList() {
    final impacts = [
      {'change': 'تغيير فئة الإلكترونيات', 'items': '25 صنف', 'impact': 'تحسن في التقارير', 'level': 'إيجابي'},
      {'change': 'تغيير وحدة القياس', 'items': '15 صنف', 'impact': 'تحديث أسعار مطلوب', 'level': 'متوسط'},
      {'change': 'تغيير العلامة التجارية', 'items': '35 صنف', 'impact': 'إعادة تصنيف المخزون', 'level': 'عالي'},
      {'change': 'تغيير الفئة الفرعية', 'items': '45 صنف', 'impact': 'تحديث نظام الحجوزات', 'level': 'متوسط'},
    ];

    return impacts.map((impact) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: _getImpactLevelColor(impact['level']!).withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: _getImpactLevelColor(impact['level']!).withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.assessment, color: Colors.teal, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  impact['change']!,
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  '${impact['items']} • ${impact['impact']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getImpactLevelColor(impact['level']!).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              impact['level']!,
              style: TextStyle(
                color: _getImpactLevelColor(impact['level']!),
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildChangesRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('لابتوب ديل XPS 13')),
        DataCell(_buildChangeTypeBadge('تغيير الفئة', Colors.blue)),
        const DataCell(Text('أجهزة كمبيوتر')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('2024-01-15')),
        const DataCell(Text('أحمد محمد')),
        const DataCell(Text('إعادة تصنيف')),
        DataCell(_buildImpactBadge('متوسط', Colors.orange)),
      ]),
      DataRow(cells: [
        const DataCell(Text('هاتف آيفون 15')),
        DataCell(_buildChangeTypeBadge('تغيير العلامة', Colors.green)),
        const DataCell(Text('Apple Inc')),
        const DataCell(Text('Apple')),
        const DataCell(Text('2024-01-18')),
        const DataCell(Text('فاطمة علي')),
        const DataCell(Text('توحيد العلامات')),
        DataCell(_buildImpactBadge('منخفض', Colors.green)),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChangeTypeCard(String type, String count, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                count,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(type, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChangeTypeBadge(String type, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(type, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Widget _buildImpactBadge(String impact, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(impact, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Color _getImpactLevelColor(String level) {
    switch (level) {
      case 'إيجابي':
        return Colors.green;
      case 'عالي':
        return Colors.red;
      case 'متوسط':
        return Colors.orange;
      case 'منخفض':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير تغييرات التصنيفات بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showHistory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تاريخ التغييرات')),
    );
  }

  void _bulkClassificationUpdate() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحديث جماعي للتصنيفات')),
    );
  }

  void _validateClassifications() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('التحقق من صحة التصنيفات')),
    );
  }

  void _revertChanges() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('التراجع عن التغييرات المحددة')),
    );
  }

  void _auditTrail() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض مسار التدقيق للتغييرات')),
    );
  }
}
