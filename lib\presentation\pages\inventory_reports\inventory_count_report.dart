import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة قوائم الجرد
/// تعرض قوائم الجرد الدورية والاستثنائية للمخزون
class InventoryCountReportPage extends StatefulWidget {
  const InventoryCountReportPage({super.key});

  @override
  State<InventoryCountReportPage> createState() => _InventoryCountReportPageState();
}

class _InventoryCountReportPageState extends State<InventoryCountReportPage> {
  DateTime? _countDate;
  String? _selectedWarehouse;
  String? _countType = 'all';
  String? _countStatus = 'all';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.inventoryCountReport),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.add_task),
            onPressed: _createNewCount,
            tooltip: 'إنشاء جرد جديد',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.teal[50],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'تاريخ الجرد',
                          border: OutlineInputBorder(),
                          suffixIcon: Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context),
                        controller: TextEditingController(
                          text: _countDate?.toString().split(' ')[0] ?? '',
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: localizations.warehouse,
                          border: const OutlineInputBorder(),
                        ),
                        value: _selectedWarehouse,
                        items: [
                          DropdownMenuItem(value: 'all', child: Text(localizations.allWarehouses)),
                          DropdownMenuItem(value: 'main', child: Text(localizations.mainWarehouse)),
                          DropdownMenuItem(value: 'branch1', child: Text('${localizations.branchWarehouse} الأول')),
                          DropdownMenuItem(value: 'branch2', child: Text('${localizations.branchWarehouse} الثاني')),
                        ],
                        onChanged: (value) => setState(() => _selectedWarehouse = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'نوع الجرد',
                          border: OutlineInputBorder(),
                        ),
                        value: _countType,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الأنواع')),
                          DropdownMenuItem(value: 'periodic', child: Text('جرد دوري')),
                          DropdownMenuItem(value: 'exceptional', child: Text('جرد استثنائي')),
                          DropdownMenuItem(value: 'partial', child: Text('جرد جزئي')),
                          DropdownMenuItem(value: 'complete', child: Text('جرد شامل')),
                        ],
                        onChanged: (value) => setState(() => _countType = value),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'حالة الجرد',
                          border: OutlineInputBorder(),
                        ),
                        value: _countStatus,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الحالات')),
                          DropdownMenuItem(value: 'pending', child: Text('قيد التنفيذ')),
                          DropdownMenuItem(value: 'completed', child: Text('مكتمل')),
                          DropdownMenuItem(value: 'approved', child: Text('معتمد')),
                          DropdownMenuItem(value: 'cancelled', child: Text('ملغي')),
                        ],
                        onChanged: (value) => setState(() => _countStatus = value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.search),
                  label: Text(localizations.generateReport),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.teal,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص الجرد
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.inventory, color: Colors.teal, size: 24),
                              const SizedBox(width: 8),
                              const Text(
                                'ملخص عمليات الجرد',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildSummaryCard('إجمالي العمليات', '28', Colors.blue, Icons.list_alt),
                              _buildSummaryCard('قيد التنفيذ', '5', Colors.orange, Icons.pending),
                              _buildSummaryCard('مكتملة', '18', Colors.green, Icons.check_circle),
                              _buildSummaryCard('معتمدة', '15', Colors.purple, Icons.verified),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // جدول قوائم الجرد
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل قوائم الجرد',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('رقم الجرد')),
                                DataColumn(label: Text('تاريخ البدء')),
                                DataColumn(label: Text('تاريخ الانتهاء')),
                                DataColumn(label: Text('المستودع')),
                                DataColumn(label: Text('نوع الجرد')),
                                DataColumn(label: Text('عدد الأصناف')),
                                DataColumn(label: Text('المسؤول')),
                                DataColumn(label: Text('الحالة')),
                                DataColumn(label: Text('الإجراءات')),
                              ],
                              rows: _buildCountRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // تحليل الفروقات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تحليل الفروقات',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              _buildVarianceCard('فروقات موجبة', '12', '+25,000 ر.س', Colors.green, Icons.trending_up),
                              _buildVarianceCard('فروقات سالبة', '8', '-18,500 ر.س', Colors.red, Icons.trending_down),
                              _buildVarianceCard('بدون فروقات', '145', '0 ر.س', Colors.blue, Icons.check),
                              _buildVarianceCard('صافي الفروقات', '20', '+6,500 ر.س', Colors.purple, Icons.balance),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // تفاصيل الفروقات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تفاصيل الفروقات الرئيسية',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('كود الصنف')),
                                DataColumn(label: Text('اسم الصنف')),
                                DataColumn(label: Text('الكمية النظرية')),
                                DataColumn(label: Text('الكمية الفعلية')),
                                DataColumn(label: Text('الفرق')),
                                DataColumn(label: Text('قيمة الفرق')),
                                DataColumn(label: Text('السبب المحتمل')),
                              ],
                              rows: _buildVarianceRows(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // إجراءات سريعة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجراءات سريعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _createNewCount,
                                  icon: const Icon(Icons.add),
                                  label: const Text('جرد جديد'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _approveVariances,
                                  icon: const Icon(Icons.check),
                                  label: const Text('اعتماد الفروقات'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _generateAdjustments,
                                  icon: const Icon(Icons.tune),
                                  label: const Text('إنشاء قيود التسوية'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _scheduleNextCount,
                                  icon: const Icon(Icons.schedule),
                                  label: const Text('جدولة الجرد القادم'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.purple,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<DataRow> _buildCountRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('INV-2024-001')),
        const DataCell(Text('2024-01-15')),
        const DataCell(Text('2024-01-17')),
        const DataCell(Text('المستودع الرئيسي')),
        const DataCell(Text('جرد دوري')),
        const DataCell(Text('150')),
        const DataCell(Text('أحمد محمد')),
        DataCell(_buildStatusBadge('معتمد', Colors.green)),
        DataCell(Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.visibility, color: Colors.blue),
              onPressed: () => _viewCount('INV-2024-001'),
            ),
            IconButton(
              icon: const Icon(Icons.edit, color: Colors.orange),
              onPressed: () => _editCount('INV-2024-001'),
            ),
          ],
        )),
      ]),
      DataRow(cells: [
        const DataCell(Text('INV-2024-002')),
        const DataCell(Text('2024-01-20')),
        const DataCell(Text('2024-01-22')),
        const DataCell(Text('مستودع الفرع الأول')),
        const DataCell(Text('جرد استثنائي')),
        const DataCell(Text('85')),
        const DataCell(Text('فاطمة علي')),
        DataCell(_buildStatusBadge('قيد التنفيذ', Colors.orange)),
        DataCell(Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.visibility, color: Colors.blue),
              onPressed: () => _viewCount('INV-2024-002'),
            ),
            IconButton(
              icon: const Icon(Icons.edit, color: Colors.orange),
              onPressed: () => _editCount('INV-2024-002'),
            ),
          ],
        )),
      ]),
    ];
  }

  List<DataRow> _buildVarianceRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('ITEM-001')),
        const DataCell(Text('لابتوب ديل')),
        const DataCell(Text('50')),
        const DataCell(Text('48')),
        const DataCell(Text('-2')),
        const DataCell(Text('-5,000 ر.س')),
        const DataCell(Text('تلف أو سرقة')),
      ]),
      DataRow(cells: [
        const DataCell(Text('ITEM-002')),
        const DataCell(Text('طابعة HP')),
        const DataCell(Text('25')),
        const DataCell(Text('27')),
        const DataCell(Text('+2')),
        const DataCell(Text('+1,200 ر.س')),
        const DataCell(Text('خطأ في التسجيل')),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVarianceCard(String title, String count, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                count,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(status, style: const TextStyle(color: Colors.white, fontSize: 12)),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        _countDate = picked;
      });
    }
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء تقرير قوائم الجرد بنجاح')),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة تقرير الجرد...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير تقرير الجرد...')),
    );
  }

  void _createNewCount() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('فتح صفحة إنشاء جرد جديد')),
    );
  }

  void _approveVariances() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم اعتماد الفروقات المحددة')),
    );
  }

  void _generateAdjustments() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء قيود التسوية المحاسبية')),
    );
  }

  void _scheduleNextCount() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم جدولة الجرد القادم')),
    );
  }

  void _viewCount(String countId) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض تفاصيل الجرد $countId')),
    );
  }

  void _editCount(String countId) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تعديل الجرد $countId')),
    );
  }
}
