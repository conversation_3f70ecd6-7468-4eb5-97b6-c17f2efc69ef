import 'package:flutter/material.dart';

/// صفحة تقرير إيرادات مركز التكلفة
/// تعرض إيرادات مراكز التكلفة المختلفة
class CostCenterRevenuePage extends StatefulWidget {
  const CostCenterRevenuePage({super.key});

  @override
  State<CostCenterRevenuePage> createState() => _CostCenterRevenuePageState();
}

class _CostCenterRevenuePageState extends State<CostCenterRevenuePage> {
  String _selectedCostCenter = 'all';
  String _selectedPeriod = 'current_month';

  // بيانات تجريبية لإيرادات مراكز التكلفة
  final List<Map<String, dynamic>> _costCenterRevenues = [
    {
      'costCenterCode': 'CC001',
      'costCenterName': 'قسم المبيعات',
      'manager': 'أحمد محمد',
      'totalRevenue': 250000.0,
      'targetRevenue': 230000.0,
      'variance': 20000.0,
      'achievement': 108.7,
      'lastMonthRevenue': 220000.0,
      'growth': 13.6,
      'revenueStreams': [
        {'stream': 'مبيعات مباشرة', 'amount': 150000.0, 'percentage': 60.0},
        {
          'stream': 'مبيعات عبر الإنترنت',
          'amount': 70000.0,
          'percentage': 28.0
        },
        {'stream': 'مبيعات الشركاء', 'amount': 30000.0, 'percentage': 12.0},
      ],
    },
    {
      'costCenterCode': 'CC002',
      'costCenterName': 'قسم الخدمات',
      'manager': 'فاطمة أحمد',
      'totalRevenue': 180000.0,
      'targetRevenue': 200000.0,
      'variance': -20000.0,
      'achievement': 90.0,
      'lastMonthRevenue': 175000.0,
      'growth': 2.9,
      'revenueStreams': [
        {'stream': 'خدمات استشارية', 'amount': 100000.0, 'percentage': 55.6},
        {'stream': 'خدمات صيانة', 'amount': 50000.0, 'percentage': 27.8},
        {'stream': 'خدمات تدريب', 'amount': 30000.0, 'percentage': 16.7},
      ],
    },
    {
      'costCenterCode': 'CC003',
      'costCenterName': 'قسم التطوير',
      'manager': 'محمد علي',
      'totalRevenue': 120000.0,
      'targetRevenue': 110000.0,
      'variance': 10000.0,
      'achievement': 109.1,
      'lastMonthRevenue': 105000.0,
      'growth': 14.3,
      'revenueStreams': [
        {'stream': 'تطوير تطبيقات', 'amount': 70000.0, 'percentage': 58.3},
        {'stream': 'تطوير مواقع', 'amount': 35000.0, 'percentage': 29.2},
        {'stream': 'خدمات تقنية', 'amount': 15000.0, 'percentage': 12.5},
      ],
    },
    {
      'costCenterCode': 'CC004',
      'costCenterName': 'قسم التسويق',
      'manager': 'سارة سعد',
      'totalRevenue': 95000.0,
      'targetRevenue': 100000.0,
      'variance': -5000.0,
      'achievement': 95.0,
      'lastMonthRevenue': 90000.0,
      'growth': 5.6,
      'revenueStreams': [
        {'stream': 'حملات إعلانية', 'amount': 50000.0, 'percentage': 52.6},
        {
          'stream': 'إدارة وسائل التواصل',
          'amount': 30000.0,
          'percentage': 31.6
        },
        {'stream': 'تصميم وإبداع', 'amount': 15000.0, 'percentage': 15.8},
      ],
    },
  ];

  @override
  Widget build(BuildContext context) {
    final filteredData = _getFilteredData();

    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير إيرادات مركز التكلفة'),
        backgroundColor: Colors.cyan,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedCostCenter,
                        decoration: const InputDecoration(
                          labelText: 'مركز التكلفة',
                          border: OutlineInputBorder(),
                        ),
                        items: [
                          const DropdownMenuItem(
                              value: 'all', child: Text('جميع المراكز')),
                          ..._costCenterRevenues.map((center) {
                            return DropdownMenuItem<String>(
                              value: center['costCenterCode'],
                              child: Text(center['costCenterName']),
                            );
                          }),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedCostCenter = value!;
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedPeriod,
                        decoration: const InputDecoration(
                          labelText: 'الفترة',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(
                              value: 'current_month',
                              child: Text('الشهر الحالي')),
                          DropdownMenuItem(
                              value: 'current_quarter',
                              child: Text('الربع الحالي')),
                          DropdownMenuItem(
                              value: 'current_year',
                              child: Text('السنة الحالية')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedPeriod = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // ملخص إيرادات مراكز التكلفة
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              color: Colors.cyan[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    const Text(
                      'ملخص إيرادات مراكز التكلفة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Column(
                          children: [
                            const Text('إجمالي الإيرادات'),
                            Text(
                              '${_getTotalRevenue(filteredData)} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('إجمالي الأهداف'),
                            Text(
                              '${_getTotalTarget(filteredData)} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Text('نسبة التحقيق'),
                            Text(
                              '${_getOverallAchievement(filteredData)}%',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color:
                                    _getOverallAchievement(filteredData) >= 100
                                        ? Colors.green
                                        : Colors.orange,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                _buildStatCard('مراكز التكلفة', filteredData.length.toString(),
                    Colors.blue),
                _buildStatCard(
                    'حقق الهدف',
                    _getAchievedTargetCount(filteredData).toString(),
                    Colors.green),
                _buildStatCard(
                    'لم يحقق الهدف',
                    _getNotAchievedTargetCount(filteredData).toString(),
                    Colors.red),
                _buildStatCard('متوسط النمو',
                    '${_getAverageGrowth(filteredData)}%', Colors.orange),
              ],
            ),
          ),

          // قائمة مراكز التكلفة
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: filteredData.length,
              itemBuilder: (context, index) {
                final costCenter = filteredData[index];

                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor:
                          _getAchievementColor(costCenter['achievement']),
                      child: Text(
                        costCenter['costCenterCode'].substring(2),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    title: Text(
                      '${costCenter['costCenterCode']} - ${costCenter['costCenterName']}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('المدير: ${costCenter['manager']}'),
                        Text(
                            'الإيرادات: ${costCenter['totalRevenue'].toStringAsFixed(2)} ر.س'),
                        Text(
                            'الهدف: ${costCenter['targetRevenue'].toStringAsFixed(2)} ر.س'),
                        Text(
                            'التحقيق: ${costCenter['achievement'].toStringAsFixed(1)}%'),
                        const SizedBox(height: 4),
                        LinearProgressIndicator(
                          value:
                              (costCenter['achievement'] / 100).clamp(0.0, 1.0),
                          backgroundColor: Colors.grey[300],
                          valueColor: AlwaysStoppedAnimation<Color>(
                            _getAchievementColor(costCenter['achievement']),
                          ),
                        ),
                      ],
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard(
                                      'إجمالي الإيرادات',
                                      '${costCenter['totalRevenue'].toStringAsFixed(2)} ر.س',
                                      Icons.attach_money,
                                      Colors.green),
                                ),
                                Expanded(
                                  child: _buildDetailCard(
                                      'الهدف المحدد',
                                      '${costCenter['targetRevenue'].toStringAsFixed(2)} ر.س',
                                      Icons.flag,
                                      Colors.blue),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDetailCard(
                                      'الشهر الماضي',
                                      '${costCenter['lastMonthRevenue'].toStringAsFixed(2)} ر.س',
                                      Icons.history,
                                      Colors.grey),
                                ),
                                Expanded(
                                  child: _buildDetailCard(
                                      'النمو',
                                      '${costCenter['growth']}%',
                                      Icons.trending_up,
                                      costCenter['growth'] >= 0
                                          ? Colors.green
                                          : Colors.red),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            const Text(
                              'مصادر الإيرادات:',
                              style: TextStyle(
                                  fontWeight: FontWeight.bold, fontSize: 16),
                            ),
                            const SizedBox(height: 8),
                            ...costCenter['revenueStreams']
                                .map<Widget>((stream) {
                              return ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: Colors.cyan,
                                  radius: 15,
                                  child: Text(
                                    '${stream['percentage'].toStringAsFixed(0)}%',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                title: Text(stream['stream']),
                                trailing: Text(
                                  '${stream['amount'].toStringAsFixed(2)} ر.س',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                subtitle: LinearProgressIndicator(
                                  value: stream['percentage'] / 100,
                                  backgroundColor: Colors.grey[300],
                                  valueColor:
                                      const AlwaysStoppedAnimation<Color>(
                                          Colors.cyan),
                                ),
                              );
                            }).toList(),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshData,
        backgroundColor: Colors.cyan,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث البيانات'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailCard(
      String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Icon(icon, size: 20, color: color),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                color: color,
              ),
            ),
            Text(
              title,
              style: const TextStyle(fontSize: 10),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Color _getAchievementColor(double achievement) {
    if (achievement >= 100) return Colors.green;
    if (achievement >= 80) return Colors.orange;
    return Colors.red;
  }

  List<Map<String, dynamic>> _getFilteredData() {
    if (_selectedCostCenter == 'all') {
      return _costCenterRevenues;
    }
    return _costCenterRevenues
        .where((center) => center['costCenterCode'] == _selectedCostCenter)
        .toList();
  }

  double _getTotalRevenue(List<Map<String, dynamic>> data) {
    return data.fold(0.0, (sum, center) => sum + center['totalRevenue']);
  }

  double _getTotalTarget(List<Map<String, dynamic>> data) {
    return data.fold(0.0, (sum, center) => sum + center['targetRevenue']);
  }

  double _getOverallAchievement(List<Map<String, dynamic>> data) {
    double totalTarget = _getTotalTarget(data);
    double totalRevenue = _getTotalRevenue(data);
    if (totalTarget == 0) return 0.0;
    return (totalRevenue / totalTarget) * 100;
  }

  int _getAchievedTargetCount(List<Map<String, dynamic>> data) {
    return data.where((center) => center['achievement'] >= 100).length;
  }

  int _getNotAchievedTargetCount(List<Map<String, dynamic>> data) {
    return data.where((center) => center['achievement'] < 100).length;
  }

  String _getAverageGrowth(List<Map<String, dynamic>> data) {
    if (data.isEmpty) return '0.0';
    double total = data.fold(0.0, (sum, center) => sum + center['growth']);
    return (total / data.length).toStringAsFixed(1);
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة تقرير إيرادات مركز التكلفة')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير تقرير إيرادات مركز التكلفة')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث بيانات إيرادات مركز التكلفة'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
