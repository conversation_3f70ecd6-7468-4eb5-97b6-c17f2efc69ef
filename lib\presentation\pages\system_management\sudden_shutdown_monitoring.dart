import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة متابعة المستخدمين في الإغلاق المفاجئ
/// تراقب وتتابع المستخدمين الذين تعرضوا لإغلاق مفاجئ للنظام
class SuddenShutdownMonitoringPage extends StatefulWidget {
  const SuddenShutdownMonitoringPage({super.key});

  @override
  State<SuddenShutdownMonitoringPage> createState() =>
      _SuddenShutdownMonitoringPageState();
}

class _SuddenShutdownMonitoringPageState
    extends State<SuddenShutdownMonitoringPage> {
  final _searchController = TextEditingController();

  String _searchQuery = '';
  String _selectedFilter = 'all';
  String _selectedPeriod = 'today';

  final List<Map<String, dynamic>> _shutdownEvents = [
    {
      'id': 'event1',
      'userName': 'أحمد محمد',
      'userId': 'user1',
      'role': 'مدير',
      'department': 'المبيعات',
      'shutdownTime': '2024/01/25 11:45',
      'lastActivity': '2024/01/25 11:44',
      'sessionDuration': '3 ساعات 15 دقيقة',
      'reason': 'انقطاع الكهرباء',
      'dataLoss': 'محتمل',
      'recoveryStatus': 'مكتمل',
      'affectedDocuments': 3,
      'ipAddress': '*************',
      'device': 'Windows 11',
      'severity': 'متوسط',
    },
    {
      'id': 'event2',
      'userName': 'فاطمة علي',
      'userId': 'user2',
      'role': 'محاسب',
      'department': 'المحاسبة',
      'shutdownTime': '2024/01/25 14:20',
      'lastActivity': '2024/01/25 14:19',
      'sessionDuration': '5 ساعات 5 دقائق',
      'reason': 'خطأ في النظام',
      'dataLoss': 'لا يوجد',
      'recoveryStatus': 'مكتمل',
      'affectedDocuments': 0,
      'ipAddress': '*************',
      'device': 'Windows 10',
      'severity': 'منخفض',
    },
    {
      'id': 'event3',
      'userName': 'محمد سالم',
      'userId': 'user3',
      'role': 'موظف',
      'department': 'المخازن',
      'shutdownTime': '2024/01/24 16:30',
      'lastActivity': '2024/01/24 16:28',
      'sessionDuration': '6 ساعات 30 دقيقة',
      'reason': 'إغلاق قسري للتطبيق',
      'dataLoss': 'مؤكد',
      'recoveryStatus': 'جاري الاسترداد',
      'affectedDocuments': 7,
      'ipAddress': '*************',
      'device': 'Android',
      'severity': 'عالي',
    },
    {
      'id': 'event4',
      'userName': 'نورا أحمد',
      'userId': 'user4',
      'role': 'مشرف',
      'department': 'المشتريات',
      'shutdownTime': '2024/01/24 10:15',
      'lastActivity': '2024/01/24 10:14',
      'sessionDuration': '2 ساعة 30 دقيقة',
      'reason': 'انقطاع الشبكة',
      'dataLoss': 'محتمل',
      'recoveryStatus': 'فشل',
      'affectedDocuments': 2,
      'ipAddress': '*************',
      'device': 'MacOS',
      'severity': 'عالي',
    },
  ];

  List<Map<String, dynamic>> get _filteredEvents {
    List<Map<String, dynamic>> filtered = _shutdownEvents;

    // تطبيق فلتر الحالة
    if (_selectedFilter == 'recovered') {
      filtered = filtered
          .where((event) => event['recoveryStatus'] == 'مكتمل')
          .toList();
    } else if (_selectedFilter == 'recovering') {
      filtered = filtered
          .where((event) => event['recoveryStatus'] == 'جاري الاسترداد')
          .toList();
    } else if (_selectedFilter == 'failed') {
      filtered =
          filtered.where((event) => event['recoveryStatus'] == 'فشل').toList();
    } else if (_selectedFilter == 'data_loss') {
      filtered =
          filtered.where((event) => event['dataLoss'] != 'لا يوجد').toList();
    }

    // تطبيق البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered
          .where((event) =>
              event['userName']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              event['department']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              event['reason']
                  .toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()))
          .toList();
    }

    return filtered;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.suddenShutdownMonitoring),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.auto_fix_high),
            onPressed: _autoRecovery,
            tooltip: 'الاسترداد التلقائي',
          ),
          IconButton(
            icon: const Icon(Icons.report),
            onPressed: _generateReport,
            tooltip: 'تقرير الأحداث',
          ),
        ],
      ),
      body: Column(
        children: [
          // بطاقة البحث والفلترة
          Card(
            margin: const EdgeInsets.all(16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // شريط البحث
                  TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      labelText: 'البحث في الأحداث',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),

                  const SizedBox(height: 16),

                  Row(
                    children: [
                      // فلتر الحالة
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedFilter,
                          decoration: const InputDecoration(
                            labelText: 'حالة الاسترداد',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 8),
                          ),
                          items: const [
                            DropdownMenuItem(
                                value: 'all', child: Text('جميع الأحداث')),
                            DropdownMenuItem(
                                value: 'recovered',
                                child: Text('تم الاسترداد')),
                            DropdownMenuItem(
                                value: 'recovering',
                                child: Text('جاري الاسترداد')),
                            DropdownMenuItem(
                                value: 'failed', child: Text('فشل الاسترداد')),
                            DropdownMenuItem(
                                value: 'data_loss',
                                child: Text('فقدان بيانات')),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedFilter = value!;
                            });
                          },
                        ),
                      ),

                      const SizedBox(width: 16),

                      // فلتر الفترة
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedPeriod,
                          decoration: const InputDecoration(
                            labelText: 'الفترة الزمنية',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 8),
                          ),
                          items: const [
                            DropdownMenuItem(
                                value: 'today', child: Text('اليوم')),
                            DropdownMenuItem(
                                value: 'week', child: Text('هذا الأسبوع')),
                            DropdownMenuItem(
                                value: 'month', child: Text('هذا الشهر')),
                            DropdownMenuItem(
                                value: 'all', child: Text('جميع الفترات')),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedPeriod = value!;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // إحصائيات سريعة
          Card(
            margin: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatCard('المجموع', _shutdownEvents.length.toString(),
                      Colors.blue),
                  _buildStatCard(
                      'تم الاسترداد',
                      _shutdownEvents
                          .where((e) => e['recoveryStatus'] == 'مكتمل')
                          .length
                          .toString(),
                      Colors.green),
                  _buildStatCard(
                      'جاري الاسترداد',
                      _shutdownEvents
                          .where((e) => e['recoveryStatus'] == 'جاري الاسترداد')
                          .length
                          .toString(),
                      Colors.orange),
                  _buildStatCard(
                      'فشل',
                      _shutdownEvents
                          .where((e) => e['recoveryStatus'] == 'فشل')
                          .length
                          .toString(),
                      Colors.red),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // قائمة أحداث الإغلاق المفاجئ
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _filteredEvents.length,
              itemBuilder: (context, index) {
                final event = _filteredEvents[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: _getSeverityColor(event['severity']),
                      child: Icon(
                        _getSeverityIcon(event['severity']),
                        color: Colors.white,
                      ),
                    ),
                    title: Text(
                      event['userName'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('${event['role']} - ${event['department']}'),
                        Text('${event['shutdownTime']} | ${event['reason']}',
                            style: const TextStyle(fontSize: 12)),
                      ],
                    ),
                    trailing: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getRecoveryStatusColor(event['recoveryStatus'])
                            .withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        event['recoveryStatus'],
                        style: TextStyle(
                          color:
                              _getRecoveryStatusColor(event['recoveryStatus']),
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            _buildDetailRow(
                                'وقت الإغلاق:', event['shutdownTime']),
                            _buildDetailRow('آخر نشاط:', event['lastActivity']),
                            _buildDetailRow(
                                'مدة الجلسة:', event['sessionDuration']),
                            _buildDetailRow('سبب الإغلاق:', event['reason']),
                            _buildDetailRow(
                                'فقدان البيانات:', event['dataLoss']),
                            _buildDetailRow('المستندات المتأثرة:',
                                '${event['affectedDocuments']} مستند'),
                            _buildDetailRow('عنوان IP:', event['ipAddress']),
                            _buildDetailRow('الجهاز:', event['device']),
                            _buildDetailRow(
                                'مستوى الخطورة:', event['severity']),

                            const SizedBox(height: 16),

                            // أزرار العمليات
                            Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: [
                                ElevatedButton.icon(
                                  onPressed: () => _attemptRecovery(event),
                                  icon: const Icon(Icons.restore, size: 16),
                                  label: const Text('محاولة الاسترداد'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                                ElevatedButton.icon(
                                  onPressed: () => _viewDetails(event),
                                  icon: const Icon(Icons.info, size: 16),
                                  label: const Text('التفاصيل'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                                ElevatedButton.icon(
                                  onPressed: () => _contactUser(event),
                                  icon:
                                      const Icon(Icons.contact_phone, size: 16),
                                  label: const Text('اتصال'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                                if (event['recoveryStatus'] == 'فشل')
                                  ElevatedButton.icon(
                                    onPressed: () => _escalateIssue(event),
                                    icon: const Icon(Icons.priority_high,
                                        size: 16),
                                    label: const Text('تصعيد'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.red,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshEvents,
        backgroundColor: Colors.red,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Column(
      children: [
        CircleAvatar(
          backgroundColor: color,
          child: Text(
            value,
            style: const TextStyle(
                color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  Color _getSeverityColor(String severity) {
    switch (severity) {
      case 'عالي':
        return Colors.red;
      case 'متوسط':
        return Colors.orange;
      case 'منخفض':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  IconData _getSeverityIcon(String severity) {
    switch (severity) {
      case 'عالي':
        return Icons.error;
      case 'متوسط':
        return Icons.warning;
      case 'منخفض':
        return Icons.info;
      default:
        return Icons.help;
    }
  }

  Color _getRecoveryStatusColor(String status) {
    switch (status) {
      case 'مكتمل':
        return Colors.green;
      case 'جاري الاسترداد':
        return Colors.orange;
      case 'فشل':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  void _refreshEvents() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث قائمة الأحداث'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _attemptRecovery(Map<String, dynamic> event) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('محاولة استرداد بيانات ${event['userName']}')),
    );
  }

  void _viewDetails(Map<String, dynamic> event) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض تفاصيل حدث ${event['userName']}')),
    );
  }

  void _contactUser(Map<String, dynamic> event) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('الاتصال بالمستخدم ${event['userName']}')),
    );
  }

  void _escalateIssue(Map<String, dynamic> event) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
          content: Text('تصعيد مشكلة ${event['userName']} للإدارة العليا')),
    );
  }

  void _autoRecovery() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تشغيل الاسترداد التلقائي لجميع الأحداث')),
    );
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء تقرير أحداث الإغلاق المفاجئ')),
    );
  }
}
