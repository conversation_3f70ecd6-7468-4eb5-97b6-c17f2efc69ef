import 'package:flutter/material.dart';

// استيراد جميع ملفات الوحدات
import 'modules/common_localizations.dart';
import 'modules/general_localizations.dart';
import 'modules/cards_localizations.dart';
import 'modules/purchases_localizations.dart';
import 'modules/sales_localizations.dart';
import 'modules/vouchers_localizations.dart';
import 'modules/inventory_reports_localizations.dart';
import 'modules/account_reports_localizations.dart';
import 'modules/statistical_reports_localizations.dart';
import 'modules/system_management_localizations.dart';
import 'modules/tools_localizations.dart';
import 'modules/help_localizations.dart';

class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  // دالة مساعدة للحفاظ على الكود مختصر في الواجهات
  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  // عضو ثابت للوصول السهل للمفوض من MaterialApp
  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  // دمج جميع ملفات الوحدات
  static final Map<String, Map<String, String>> _localizedValues = {
    'en': {
      // دمج جميع الوحدات للغة الإنجليزية
      ...CommonLocalizations.values['en']!,
      ...GeneralLocalizations.values['en']!,
      ...CardsLocalizations.values['en']!,
      ...PurchasesLocalizations.values['en']!,
      ...SalesLocalizations.values['en']!,
      ...VouchersLocalizations.values['en']!,
      ...InventoryReportsLocalizations.values['en']!,
      ...AccountReportsLocalizations.values['en']!,
      ...StatisticalReportsLocalizations.values['en']!,
      ...SystemManagementLocalizations.values['en']!,
      ...ToolsLocalizations.values['en']!,
      ...HelpLocalizations.values['en']!,
    },
    'ar': {
      // دمج جميع الوحدات للغة العربية
      ...CommonLocalizations.values['ar']!,
      ...GeneralLocalizations.values['ar']!,
      ...CardsLocalizations.values['ar']!,
      ...PurchasesLocalizations.values['ar']!,
      ...SalesLocalizations.values['ar']!,
      ...VouchersLocalizations.values['ar']!,
      ...InventoryReportsLocalizations.values['ar']!,
      ...AccountReportsLocalizations.values['ar']!,
      ...StatisticalReportsLocalizations.values['ar']!,
      ...SystemManagementLocalizations.values['ar']!,
      ...ToolsLocalizations.values['ar']!,
      ...HelpLocalizations.values['ar']!,
    },
  };

  String get appTitle =>
      _localizedValues[locale.languageCode]?['app_title'] ?? '';
  String get home => _localizedValues[locale.languageCode]?['home'] ?? '';
  String get general => _localizedValues[locale.languageCode]?['general'] ?? '';
  String get cards => _localizedValues[locale.languageCode]?['cards'] ?? '';
  String get purchases =>
      _localizedValues[locale.languageCode]?['purchases'] ?? '';
  String get sales => _localizedValues[locale.languageCode]?['sales'] ?? '';
  String get vouchers =>
      _localizedValues[locale.languageCode]?['vouchers'] ?? '';
  String get inventoryReports =>
      _localizedValues[locale.languageCode]?['inventory_reports'] ?? '';
  String get accountReports =>
      _localizedValues[locale.languageCode]?['account_reports'] ?? '';
  String get statisticalReports =>
      _localizedValues[locale.languageCode]?['statistical_reports'] ?? '';
  String get systemManagement =>
      _localizedValues[locale.languageCode]?['system_management'] ?? '';
  String get tools => _localizedValues[locale.languageCode]?['tools'] ?? '';
  String get help => _localizedValues[locale.languageCode]?['help'] ?? '';
  String get changeLanguage =>
      _localizedValues[locale.languageCode]?['change_language'] ?? '';
  String get language =>
      _localizedValues[locale.languageCode]?['language'] ?? '';
  String get english => _localizedValues[locale.languageCode]?['english'] ?? '';
  String get arabic => _localizedValues[locale.languageCode]?['arabic'] ?? '';
  String get selectLanguage =>
      _localizedValues[locale.languageCode]?['select_language'] ?? '';
  String get save => _localizedValues[locale.languageCode]?['save'] ?? '';
  String get cancel => _localizedValues[locale.languageCode]?['cancel'] ?? '';
  String get settings =>
      _localizedValues[locale.languageCode]?['settings'] ?? '';
  String get dashboard =>
      _localizedValues[locale.languageCode]?['dashboard'] ?? '';
  String get logout => _localizedValues[locale.languageCode]?['logout'] ?? '';
  String get profile => _localizedValues[locale.languageCode]?['profile'] ?? '';
  String get notifications =>
      _localizedValues[locale.languageCode]?['notifications'] ?? '';
  String get userName =>
      _localizedValues[locale.languageCode]?['user_name'] ?? '';
  String get userEmail =>
      _localizedValues[locale.languageCode]?['user_email'] ?? '';

  // صفحات وحدة العام
  String get createEditFiscalYear =>
      _localizedValues[locale.languageCode]?['create_edit_fiscal_year'] ?? '';

  String get pageSettings =>
      _localizedValues[locale.languageCode]?['page_settings'] ?? '';
  String get printerSettings =>
      _localizedValues[locale.languageCode]?['printer_settings'] ?? '';
  String get changeTheme =>
      _localizedValues[locale.languageCode]?['change_theme'] ?? '';
  String get companyData =>
      _localizedValues[locale.languageCode]?['company_data'] ?? '';
  String get backupRestore =>
      _localizedValues[locale.languageCode]?['backup_restore'] ?? '';
  String get homePage =>
      _localizedValues[locale.languageCode]?['home_page'] ?? '';
  String get loginPage =>
      _localizedValues[locale.languageCode]?['login_page'] ?? '';

  // صفحات وحدة الكروت
  String get financialGuide =>
      _localizedValues[locale.languageCode]?['financial_guide'] ?? '';
  String get costCenter =>
      _localizedValues[locale.languageCode]?['cost_center'] ?? '';
  String get customers =>
      _localizedValues[locale.languageCode]?['customers'] ?? '';
  String get suppliers =>
      _localizedValues[locale.languageCode]?['suppliers'] ?? '';
  String get items => _localizedValues[locale.languageCode]?['items'] ?? '';
  String get stores => _localizedValues[locale.languageCode]?['stores'] ?? '';

  // صفحات وحدة المشتريات
  String get purchaseInvoice =>
      _localizedValues[locale.languageCode]?['purchase_invoice'] ?? '';
  String get purchaseReturnInvoice =>
      _localizedValues[locale.languageCode]?['purchase_return_invoice'] ?? '';
  String get purchaseOrder =>
      _localizedValues[locale.languageCode]?['purchase_order'] ?? '';
  String get requestPriceQuotes =>
      _localizedValues[locale.languageCode]?['request_price_quotes'] ?? '';

  // صفحات وحدة المبيعات
  String get salesInvoice =>
      _localizedValues[locale.languageCode]?['sales_invoice'] ?? '';
  String get salesReturnInvoice =>
      _localizedValues[locale.languageCode]?['sales_return_invoice'] ?? '';
  String get salesOrder =>
      _localizedValues[locale.languageCode]?['sales_order'] ?? '';
  String get priceQuote =>
      _localizedValues[locale.languageCode]?['price_quote'] ?? '';
  String get salesOfferPrice =>
      _localizedValues[locale.languageCode]?['sales_offer_price'] ?? '';

  // صفحات وحدة السندات
  String get journalEntry =>
      _localizedValues[locale.languageCode]?['journal_entry'] ?? '';
  String get receiptVoucher =>
      _localizedValues[locale.languageCode]?['receipt_voucher'] ?? '';
  String get paymentVoucher =>
      _localizedValues[locale.languageCode]?['payment_voucher'] ?? '';

  // صفحات وحدة تقارير المخزون
  String get itemMovementReport =>
      _localizedValues[locale.languageCode]?['item_movement_report'] ?? '';
  String get itemBalanceReport =>
      _localizedValues[locale.languageCode]?['item_balance_report'] ?? '';
  String get itemCardReport =>
      _localizedValues[locale.languageCode]?['item_card_report'] ?? '';
  String get itemMovementDuringPeriod =>
      _localizedValues[locale.languageCode]?['item_movement_during_period'] ??
      '';

  // صفحات وحدة تقارير الحسابات
  String get trialBalance =>
      _localizedValues[locale.languageCode]?['trial_balance'] ?? '';
  String get incomeStatement =>
      _localizedValues[locale.languageCode]?['income_statement'] ?? '';
  String get balanceSheet =>
      _localizedValues[locale.languageCode]?['balance_sheet'] ?? '';
  String get generalLedger =>
      _localizedValues[locale.languageCode]?['general_ledger'] ?? '';
  String get dailyRestrictionReport =>
      _localizedValues[locale.languageCode]?['daily_restriction_report'] ?? '';
  String get continuousAccountStatement =>
      _localizedValues[locale.languageCode]?['continuous_account_statement'] ??
      '';
  String get mainAccountStatement =>
      _localizedValues[locale.languageCode]?['main_account_statement'] ?? '';

  // صفحات وحدة التقارير الإحصائية
  String get salesDetails =>
      _localizedValues[locale.languageCode]?['sales_details'] ?? '';
  String get purchasesDetails =>
      _localizedValues[locale.languageCode]?['purchases_details'] ?? '';
  String get monthlyBranchSalesMovement =>
      _localizedValues[locale.languageCode]?['monthly_branch_sales_movement'] ??
      '';
  String get monthlyBranchPurchasesMovement =>
      _localizedValues[locale.languageCode]
          ?['monthly_branch_purchases_movement'] ??
      '';

  // صفحات وحدة إدارة النظام (45 صفحة)
  String get paymentServicesSettings =>
      _localizedValues[locale.languageCode]?['payment_services_settings'] ?? '';
  String get whatsappConnectionSettings =>
      _localizedValues[locale.languageCode]?['whatsapp_connection_settings'] ??
      '';
  String get vatSettings =>
      _localizedValues[locale.languageCode]?['vat_settings'] ?? '';
  String get systemMonitoring =>
      _localizedValues[locale.languageCode]?['system_monitoring'] ?? '';
  String get userPermissions =>
      _localizedValues[locale.languageCode]?['user_permissions'] ?? '';
  String get changeUserPassword =>
      _localizedValues[locale.languageCode]?['change_user_password'] ?? '';
  String get changeUserBranchWarehouse =>
      _localizedValues[locale.languageCode]?['change_user_branch_warehouse'] ??
      '';
  String get activateUsers =>
      _localizedValues[locale.languageCode]?['activate_users'] ?? '';
  String get serverMainFile =>
      _localizedValues[locale.languageCode]?['server_main_file'] ?? '';
  String get systemComparisons =>
      _localizedValues[locale.languageCode]?['system_comparisons'] ?? '';
  String get changeItemNumber =>
      _localizedValues[locale.languageCode]?['change_item_number'] ?? '';
  String get connectedUsers =>
      _localizedValues[locale.languageCode]?['connected_users'] ?? '';
  String get suddenShutdownMonitoring =>
      _localizedValues[locale.languageCode]?['sudden_shutdown_monitoring'] ??
      '';
  String get settlePreviousSalesInvoices =>
      _localizedValues[locale.languageCode]
          ?['settle_previous_sales_invoices'] ??
      '';
  String get settlePreviousPurchaseInvoices =>
      _localizedValues[locale.languageCode]
          ?['settle_previous_purchase_invoices'] ??
      '';
  String get itemSalesPurchasesMovement =>
      _localizedValues[locale.languageCode]?['item_sales_purchases_movement'] ??
      '';
  String get printSpecificItemsData =>
      _localizedValues[locale.languageCode]?['print_specific_items_data'] ?? '';
  String get printBarcode =>
      _localizedValues[locale.languageCode]?['print_barcode'] ?? '';
  String get printBarcodeSpecificItems =>
      _localizedValues[locale.languageCode]?['print_barcode_specific_items'] ??
      '';
  String get printItemLabels =>
      _localizedValues[locale.languageCode]?['print_item_labels'] ?? '';
  String get salesRepresentatives =>
      _localizedValues[locale.languageCode]?['sales_representatives'] ?? '';
  String get miscellaneousAccounts =>
      _localizedValues[locale.languageCode]?['miscellaneous_accounts'] ?? '';
  String get branches =>
      _localizedValues[locale.languageCode]?['branches'] ?? '';
  String get warehouses =>
      _localizedValues[locale.languageCode]?['warehouses'] ?? '';
  String get automaticPricing =>
      _localizedValues[locale.languageCode]?['automatic_pricing'] ?? '';
  String get alternatives =>
      _localizedValues[locale.languageCode]?['alternatives'] ?? '';
  String get inventoryCount =>
      _localizedValues[locale.languageCode]?['inventory_count'] ?? '';
  String get inventoryAssignment =>
      _localizedValues[locale.languageCode]?['inventory_assignment'] ?? '';
  String get units => _localizedValues[locale.languageCode]?['units'] ?? '';
  String get systemBackupRestore =>
      _localizedValues[locale.languageCode]?['system_backup_restore'] ?? '';
  String get inventoryTransfers =>
      _localizedValues[locale.languageCode]?['inventory_transfers'] ?? '';
  String get maintainReservations =>
      _localizedValues[locale.languageCode]?['maintain_reservations'] ?? '';
  String get maintainCustomerItems =>
      _localizedValues[locale.languageCode]?['maintain_customer_items'] ?? '';
  String get maintainSupplierItems =>
      _localizedValues[locale.languageCode]?['maintain_supplier_items'] ?? '';
  String get maintenanceTools =>
      _localizedValues[locale.languageCode]?['maintenance_tools'] ?? '';
  String get systemPrinterSettings =>
      _localizedValues[locale.languageCode]?['system_printer_settings'] ?? '';
  String get networkSettings =>
      _localizedValues[locale.languageCode]?['network_settings'] ?? '';
  String get databaseSettings =>
      _localizedValues[locale.languageCode]?['database_settings'] ?? '';
  String get securitySettings =>
      _localizedValues[locale.languageCode]?['security_settings'] ?? '';
  String get activityLog =>
      _localizedValues[locale.languageCode]?['activity_log'] ?? '';
  String get generalSystemSettings =>
      _localizedValues[locale.languageCode]?['general_system_settings'] ?? '';
  String get reportsSettings =>
      _localizedValues[locale.languageCode]?['reports_settings'] ?? '';
  String get currencyReceipt =>
      _localizedValues[locale.languageCode]?['currency_receipt'] ?? '';
  String get terminals =>
      _localizedValues[locale.languageCode]?['terminals'] ?? '';
  String get barcodeDefinition =>
      _localizedValues[locale.languageCode]?['barcode_definition'] ?? '';
  String get labelDefinition =>
      _localizedValues[locale.languageCode]?['label_definition'] ?? '';
  String get suspendApplication =>
      _localizedValues[locale.languageCode]?['suspend_application'] ?? '';
  String get editPrintedInvoices =>
      _localizedValues[locale.languageCode]?['edit_printed_invoices'] ?? '';
  String get editPrintedReturns =>
      _localizedValues[locale.languageCode]?['edit_printed_returns'] ?? '';
  String get approveTransferVouchers =>
      _localizedValues[locale.languageCode]?['approve_transfer_vouchers'] ?? '';
  String get shareStoreInvoices =>
      _localizedValues[locale.languageCode]?['share_store_invoices'] ?? '';
  String get signAndShareInvoices =>
      _localizedValues[locale.languageCode]?['sign_and_share_invoices'] ?? '';
  String get transferAccountMovement =>
      _localizedValues[locale.languageCode]?['transfer_account_movement'] ?? '';
  String get userActivityReport =>
      _localizedValues[locale.languageCode]?['user_activity_report'] ?? '';
  String get signAndShareDiscountNotifications =>
      _localizedValues[locale.languageCode]
          ?['sign_and_share_discount_notifications'] ??
      '';

  // صفحات إضافية لإدارة النظام
  String get copyDocumentsPreviousYear =>
      _localizedValues[locale.languageCode]?['copy_previous_year_documents'] ??
      '';

  // دوال الوصول القديمة للتوافق مع الإصدارات السابقة
  String get usersManagement =>
      _localizedValues[locale.languageCode]?['users_management'] ?? '';
  String get roles => _localizedValues[locale.languageCode]?['roles'] ?? '';
  String get whatsappServiceConnection =>
      _localizedValues[locale.languageCode]?['whatsapp_connection_settings'] ??
      '';

  // دوال وصول إضافية لإدارة النظام
  String get transferAccountBalances =>
      _localizedValues[locale.languageCode]?['transfer_account_balances'] ?? '';
  String get transferInventoryBalances =>
      _localizedValues[locale.languageCode]?['transfer_inventory_balances'] ??
      '';
  String get transferStockBalances =>
      _localizedValues[locale.languageCode]?['transfer_stock_balances'] ?? '';
  String get copyPreviousYearDocuments =>
      _localizedValues[locale.languageCode]?['copy_previous_year_documents'] ??
      '';
  String get hijriDatesEntry =>
      _localizedValues[locale.languageCode]?['hijri_dates_entry'] ?? '';
  String get prepareStatisticsFiles =>
      _localizedValues[locale.languageCode]?['prepare_statistics_files'] ?? '';
  String get recalculateAverageCost =>
      _localizedValues[locale.languageCode]?['recalculate_average_cost'] ?? '';
  String get maintainInventoryQuantities =>
      _localizedValues[locale.languageCode]?['maintain_inventory_quantities'] ??
      '';
  String get maintainFixedOrders =>
      _localizedValues[locale.languageCode]?['maintain_fixed_orders'] ?? '';
  String get maintainAccountBalances =>
      _localizedValues[locale.languageCode]?['maintain_account_balances'] ?? '';

  // دوال وصول إدارة المستخدمين
  String get searchUser =>
      _localizedValues[locale.languageCode]?['search_user'] ?? '';
  String get filter => _localizedValues[locale.languageCode]?['filter'] ?? '';
  String get allUsers =>
      _localizedValues[locale.languageCode]?['all_users'] ?? '';
  String get activeUsers =>
      _localizedValues[locale.languageCode]?['active_users'] ?? '';
  String get inactiveUsers =>
      _localizedValues[locale.languageCode]?['inactive_users'] ?? '';
  String get blockedUsers =>
      _localizedValues[locale.languageCode]?['blocked_users'] ?? '';
  String get total => _localizedValues[locale.languageCode]?['total'] ?? '';
  String get active => _localizedValues[locale.languageCode]?['active'] ?? '';
  String get inactive =>
      _localizedValues[locale.languageCode]?['inactive'] ?? '';
  String get blocked => _localizedValues[locale.languageCode]?['blocked'] ?? '';
  String get addNewUser =>
      _localizedValues[locale.languageCode]?['add_new_user'] ?? '';
  String get userSettings =>
      _localizedValues[locale.languageCode]?['user_settings'] ?? '';
  String get activate =>
      _localizedValues[locale.languageCode]?['activate'] ?? '';
  String get deactivate =>
      _localizedValues[locale.languageCode]?['deactivate'] ?? '';
  String get edit => _localizedValues[locale.languageCode]?['edit'] ?? '';
  String get resetPassword =>
      _localizedValues[locale.languageCode]?['reset_password'] ?? '';
  String get resetAttempts =>
      _localizedValues[locale.languageCode]?['reset_attempts'] ?? '';
  String get delete => _localizedValues[locale.languageCode]?['delete'] ?? '';
  String get bulkOperations =>
      _localizedValues[locale.languageCode]?['bulk_operations'] ?? '';
  String get activateAllUsers =>
      _localizedValues[locale.languageCode]?['activate_all_users'] ?? '';
  String get deactivateAllUsers =>
      _localizedValues[locale.languageCode]?['deactivate_all_users'] ?? '';
  String get resetAllLoginAttempts =>
      _localizedValues[locale.languageCode]?['reset_all_login_attempts'] ?? '';
  String get exportUsersList =>
      _localizedValues[locale.languageCode]?['export_users_list'] ?? '';
  String get confirmDelete =>
      _localizedValues[locale.languageCode]?['confirm_delete'] ?? '';

  // دوال وصول إعدادات الواتساب
  String get enableWhatsappBusiness =>
      _localizedValues[locale.languageCode]?['enable_whatsapp_business'] ?? '';
  String get whatsappBusinessPhone =>
      _localizedValues[locale.languageCode]?['whatsapp_business_phone'] ?? '';
  String get apiToken =>
      _localizedValues[locale.languageCode]?['api_token'] ?? '';

  // صفحات وحدة الأدوات
  String get calculator =>
      _localizedValues[locale.languageCode]?['calculator'] ?? '';
  String get notes => _localizedValues[locale.languageCode]?['notes'] ?? '';
  String get phoneDirectory =>
      _localizedValues[locale.languageCode]?['phone_directory'] ?? '';

  // صفحات وحدة التعليمات
  String get contactUs =>
      _localizedValues[locale.languageCode]?['contact_us'] ?? '';
  String get aboutUs =>
      _localizedValues[locale.languageCode]?['about_us'] ?? '';
  String get programContent =>
      _localizedValues[locale.languageCode]?['program_content'] ?? '';
  String get quickTips =>
      _localizedValues[locale.languageCode]?['quick_tips'] ?? '';

  // دوال وصول للإحصائيات والبطاقات
  String get totalCustomers =>
      _localizedValues[locale.languageCode]?['total_customers'] ?? '';
  String get totalSuppliers =>
      _localizedValues[locale.languageCode]?['total_suppliers'] ?? '';
  String get totalWarehouses =>
      _localizedValues[locale.languageCode]?['total_warehouses'] ?? '';
  String get quickOperations =>
      _localizedValues[locale.languageCode]?['quick_operations'] ?? '';
  String get availableTools =>
      _localizedValues[locale.languageCode]?['available_tools'] ?? '';
  String get savedNotes =>
      _localizedValues[locale.languageCode]?['saved_notes'] ?? '';
  String get contacts =>
      _localizedValues[locale.languageCode]?['contacts'] ?? '';
  String get calculations =>
      _localizedValues[locale.languageCode]?['calculations'] ?? '';
  String get todaySalesTotal =>
      _localizedValues[locale.languageCode]?['today_sales_total'] ?? '';
  String get invoicesCount =>
      _localizedValues[locale.languageCode]?['invoices_count'] ?? '';
  String get activeUsersCount =>
      _localizedValues[locale.languageCode]?['active_users_count'] ?? '';
  String get definedRoles =>
      _localizedValues[locale.languageCode]?['defined_roles'] ?? '';
  String get lastBackup =>
      _localizedValues[locale.languageCode]?['last_backup'] ?? '';
  String get systemStatus =>
      _localizedValues[locale.languageCode]?['system_status'] ?? '';
  String get today => _localizedValues[locale.languageCode]?['today'] ?? '';
  String get excellent =>
      _localizedValues[locale.languageCode]?['excellent'] ?? '';
  String get managementTools =>
      _localizedValues[locale.languageCode]?['management_tools'] ?? '';
  String get cardsManagement =>
      _localizedValues[locale.languageCode]?['cards_management'] ?? '';
  String get completed =>
      _localizedValues[locale.languageCode]?['completed'] ?? '';
  String get comingSoon =>
      _localizedValues[locale.languageCode]?['coming_soon'] ?? '';
  String get fiscalYear =>
      _localizedValues[locale.languageCode]?['fiscal_year'] ?? '';
  String get createEditFiscalYearFull =>
      _localizedValues[locale.languageCode]?['create_edit_fiscal_year_full'] ??
      '';

  // دوال وصول للمفاتيح الإضافية
  String get categories =>
      _localizedValues[locale.languageCode]?['categories'] ?? '';
  String get currencies =>
      _localizedValues[locale.languageCode]?['currencies'] ?? '';
  String get warehouseLinking =>
      _localizedValues[locale.languageCode]?['warehouse_linking'] ?? '';
  String get facilities =>
      _localizedValues[locale.languageCode]?['facilities'] ?? '';
  String get expenseTypes =>
      _localizedValues[locale.languageCode]?['expense_types'] ?? '';
  String get expenseData =>
      _localizedValues[locale.languageCode]?['expense_data'] ?? '';

  // دوال وصول لتقارير المخزون الإضافية
  String get noReportsFound =>
      _localizedValues[locale.languageCode]?['no_reports_found'] ?? '';
  String get reportType =>
      _localizedValues[locale.languageCode]?['report_type'] ?? '';
  String get reportStatus =>
      _localizedValues[locale.languageCode]?['report_status'] ?? '';
  String get allTypes =>
      _localizedValues[locale.languageCode]?['all_types'] ?? '';
  String get dailyReports =>
      _localizedValues[locale.languageCode]?['daily_reports'] ?? '';
  String get monthlyReports =>
      _localizedValues[locale.languageCode]?['monthly_reports'] ?? '';
  String get yearlyReports =>
      _localizedValues[locale.languageCode]?['yearly_reports'] ?? '';
  String get customReports =>
      _localizedValues[locale.languageCode]?['custom_reports'] ?? '';
  String get allStatuses =>
      _localizedValues[locale.languageCode]?['all_statuses'] ?? '';
  String get activeStatus =>
      _localizedValues[locale.languageCode]?['active_status'] ?? '';
  String get popularStatus =>
      _localizedValues[locale.languageCode]?['popular_status'] ?? '';
  String get newStatus =>
      _localizedValues[locale.languageCode]?['new_status'] ?? '';
  String get updatedStatus =>
      _localizedValues[locale.languageCode]?['updated_status'] ?? '';
  String get openingReport =>
      _localizedValues[locale.languageCode]?['opening_report'] ?? '';
  String get reportNotAvailable =>
      _localizedValues[locale.languageCode]?['report_not_available'] ?? '';
  String get customReportFeature =>
      _localizedValues[locale.languageCode]?['custom_report_feature'] ?? '';

  // دوال وصول للأدوات الإضافية
  String get currencyConverter =>
      _localizedValues[locale.languageCode]?['currency_converter'] ?? '';
  String get barcodeGenerator =>
      _localizedValues[locale.languageCode]?['barcode_generator'] ?? '';
  String get calendar =>
      _localizedValues[locale.languageCode]?['calendar'] ?? '';
  String get timer => _localizedValues[locale.languageCode]?['timer'] ?? '';
  String get textTools =>
      _localizedValues[locale.languageCode]?['text_tools'] ?? '';
  String get availableToolsList =>
      _localizedValues[locale.languageCode]?['available_tools_list'] ?? '';
  String get navigateToTool =>
      _localizedValues[locale.languageCode]?['navigate_to_tool'] ?? '';

  // دوال وصول للمبيعات الإضافية
  String get totalSalesToday =>
      _localizedValues[locale.languageCode]?['total_sales_today'] ?? '';
  String get invoiceCount =>
      _localizedValues[locale.languageCode]?['invoice_count'] ?? '';
  String get navigateTo =>
      _localizedValues[locale.languageCode]?['navigate_to'] ?? '';

  // دوال وصول للمشتريات الإضافية
  String get totalPurchasesToday =>
      _localizedValues[locale.languageCode]?['total_purchases_today'] ?? '';

  // دوال وصول للسندات الإضافية
  String get vouchersToday =>
      _localizedValues[locale.languageCode]?['vouchers_today'] ?? '';
  String get totalAmount =>
      _localizedValues[locale.languageCode]?['total_amount'] ?? '';
  String get receiptVouchers =>
      _localizedValues[locale.languageCode]?['receipt_vouchers'] ?? '';
  String get paymentVouchers =>
      _localizedValues[locale.languageCode]?['payment_vouchers'] ?? '';
  String get vouchersManagement =>
      _localizedValues[locale.languageCode]?['vouchers_management'] ?? '';
  String get vouchersReport =>
      _localizedValues[locale.languageCode]?['vouchers_report'] ?? '';

  // دوال وصول للمساعدة الإضافية
  String get appVersion =>
      _localizedValues[locale.languageCode]?['app_version'] ?? '';
  String get lastUpdate =>
      _localizedValues[locale.languageCode]?['last_update'] ?? '';
  String get availableArticles =>
      _localizedValues[locale.languageCode]?['available_articles'] ?? '';
  String get quickTipsCount =>
      _localizedValues[locale.languageCode]?['quick_tips_count'] ?? '';
  String get helpAndSupport =>
      _localizedValues[locale.languageCode]?['help_and_support'] ?? '';
  String get faq => _localizedValues[locale.languageCode]?['faq'] ?? '';
  String get userGuide =>
      _localizedValues[locale.languageCode]?['user_guide'] ?? '';
  String get tutorialVideos =>
      _localizedValues[locale.languageCode]?['tutorial_videos'] ?? '';
  String get technicalSupport =>
      _localizedValues[locale.languageCode]?['technical_support'] ?? '';

  // دوال وصول لإحصائيات إدارة النظام
  String get systemManagementTools =>
      _localizedValues[locale.languageCode]?['system_management_tools'] ?? '';
  String get stopAutoRefresh =>
      _localizedValues[locale.languageCode]?['stop_auto_refresh'] ?? '';
  String get enableAutoRefresh =>
      _localizedValues[locale.languageCode]?['enable_auto_refresh'] ?? '';
  String get sessionAnalytics =>
      _localizedValues[locale.languageCode]?['session_analytics'] ?? '';

  // دوال وصول لتقارير الحسابات الإضافية
  String get printReports =>
      _localizedValues[locale.languageCode]?['print_reports'] ?? '';
  String get exportReports =>
      _localizedValues[locale.languageCode]?['export_reports'] ?? '';
  String get searchAccountReports =>
      _localizedValues[locale.languageCode]?['search_reports'] ?? '';
  String get accountReportType =>
      _localizedValues[locale.languageCode]?['report_type'] ?? '';
  String get accountFinancialReports =>
      _localizedValues[locale.languageCode]?['financial_reports'] ?? '';
  String get accountingReports =>
      _localizedValues[locale.languageCode]?['accounting_reports'] ?? '';
  String get analyticalReports =>
      _localizedValues[locale.languageCode]?['analytical_reports'] ?? '';
  String get reportsCount =>
      _localizedValues[locale.languageCode]?['reports_count'] ?? '';
  String get accountsCount =>
      _localizedValues[locale.languageCode]?['accounts_count'] ?? '';
  String get entries => _localizedValues[locale.languageCode]?['entries'] ?? '';
  String get balances =>
      _localizedValues[locale.languageCode]?['balances'] ?? '';
  String get navigateToReport =>
      _localizedValues[locale.languageCode]?['navigate_to_report'] ?? '';

  // دوال وصول جديدة لتقارير المخزون
  String get itemsMovementReport =>
      _localizedValues[locale.languageCode]?['items_movement_report'] ?? '';
  String get itemMovementPreviousYear =>
      _localizedValues[locale.languageCode]?['item_movement_previous_year'] ??
      '';
  String get itemsOperationsReport =>
      _localizedValues[locale.languageCode]?['items_operations_report'] ?? '';
  String get itemsInSpecificLocation =>
      _localizedValues[locale.languageCode]?['items_in_specific_location'] ??
      '';
  String get monthlySalesAverage =>
      _localizedValues[locale.languageCode]?['monthly_sales_average'] ?? '';
  String get itemsProfitReport =>
      _localizedValues[locale.languageCode]?['items_profit_report'] ?? '';
  String get itemsSoldBelowCost =>
      _localizedValues[locale.languageCode]?['items_sold_below_cost'] ?? '';
  String get itemsAddedDuringPeriod =>
      _localizedValues[locale.languageCode]?['items_added_during_period'] ?? '';
  String get outOfStockItems =>
      _localizedValues[locale.languageCode]?['out_of_stock_items'] ?? '';
  String get lowStockItems =>
      _localizedValues[locale.languageCode]?['low_stock_items'] ?? '';

  String get purchaseOrdersMemo =>
      _localizedValues[locale.languageCode]?['purchase_orders_memo'] ?? '';

  // دوال وصول مصطلحات التقارير المشتركة
  String get printReport =>
      _localizedValues[locale.languageCode]?['print_report'] ?? '';
  String get exportReport =>
      _localizedValues[locale.languageCode]?['export_report'] ?? '';
  String get generateReport =>
      _localizedValues[locale.languageCode]?['generate_report'] ?? '';
  String get reportPeriod =>
      _localizedValues[locale.languageCode]?['report_period'] ?? '';
  String get fromDate =>
      _localizedValues[locale.languageCode]?['from_date'] ?? '';
  String get toDate => _localizedValues[locale.languageCode]?['to_date'] ?? '';
  String get warehouse =>
      _localizedValues[locale.languageCode]?['warehouse'] ?? '';
  String get category =>
      _localizedValues[locale.languageCode]?['category'] ?? '';
  String get allWarehouses =>
      _localizedValues[locale.languageCode]?['all_warehouses'] ?? '';
  String get mainWarehouse =>
      _localizedValues[locale.languageCode]?['main_warehouse'] ?? '';
  String get branchWarehouse =>
      _localizedValues[locale.languageCode]?['branch_warehouse'] ?? '';
  String get allCategories =>
      _localizedValues[locale.languageCode]?['all_categories'] ?? '';
  String get electronics =>
      _localizedValues[locale.languageCode]?['electronics'] ?? '';
  String get clothing =>
      _localizedValues[locale.languageCode]?['clothing'] ?? '';
  String get food => _localizedValues[locale.languageCode]?['food'] ?? '';
  String get reportSummary =>
      _localizedValues[locale.languageCode]?['report_summary'] ?? '';
  String get totalItems =>
      _localizedValues[locale.languageCode]?['total_items'] ?? '';
  String get totalIncoming =>
      _localizedValues[locale.languageCode]?['total_incoming'] ?? '';
  String get totalOutgoing =>
      _localizedValues[locale.languageCode]?['total_outgoing'] ?? '';
  String get totalValue =>
      _localizedValues[locale.languageCode]?['total_value'] ?? '';
  String get itemCode =>
      _localizedValues[locale.languageCode]?['item_code'] ?? '';
  String get itemName =>
      _localizedValues[locale.languageCode]?['item_name'] ?? '';
  String get unit => _localizedValues[locale.languageCode]?['unit'] ?? '';
  String get openingBalance =>
      _localizedValues[locale.languageCode]?['opening_balance'] ?? '';
  String get incoming =>
      _localizedValues[locale.languageCode]?['incoming'] ?? '';
  String get outgoing =>
      _localizedValues[locale.languageCode]?['outgoing'] ?? '';
  String get closingBalance =>
      _localizedValues[locale.languageCode]?['closing_balance'] ?? '';
  String get averageCost =>
      _localizedValues[locale.languageCode]?['average_cost'] ?? '';
  String get value => _localizedValues[locale.languageCode]?['value'] ?? '';
  String get year => _localizedValues[locale.languageCode]?['year'] ?? '';
  String get month => _localizedValues[locale.languageCode]?['month'] ?? '';
  String get operationType =>
      _localizedValues[locale.languageCode]?['operation_type'] ?? '';
  String get documentNumber =>
      _localizedValues[locale.languageCode]?['document_number'] ?? '';
  String get quantity =>
      _localizedValues[locale.languageCode]?['quantity'] ?? '';
  String get price => _localizedValues[locale.languageCode]?['price'] ?? '';
  String get balance => _localizedValues[locale.languageCode]?['balance'] ?? '';
  String get reportNotes =>
      _localizedValues[locale.languageCode]?['notes'] ?? '';
  String get location =>
      _localizedValues[locale.languageCode]?['location'] ?? '';
  String get availableQuantity =>
      _localizedValues[locale.languageCode]?['available_quantity'] ?? '';
  String get minimumLimit =>
      _localizedValues[locale.languageCode]?['minimum_limit'] ?? '';
  String get costPrice =>
      _localizedValues[locale.languageCode]?['cost_price'] ?? '';
  String get sellingPrice =>
      _localizedValues[locale.languageCode]?['selling_price'] ?? '';
  String get status => _localizedValues[locale.languageCode]?['status'] ?? '';
  String get available =>
      _localizedValues[locale.languageCode]?['available'] ?? '';
  String get low => _localizedValues[locale.languageCode]?['low'] ?? '';
  String get outOfStock =>
      _localizedValues[locale.languageCode]?['out_of_stock'] ?? '';
  String get inventoryValuation =>
      _localizedValues[locale.languageCode]?['inventory_valuation'] ?? '';
  String get inventoryCountReport =>
      _localizedValues[locale.languageCode]?['inventory_count_report'] ?? '';
  String get branchInventoryCount =>
      _localizedValues[locale.languageCode]?['branch_inventory_count'] ?? '';

  // دوال وصول جديدة لصفحة تقارير المخزون
  String get searchReports =>
      _localizedValues[locale.languageCode]?['search_reports'] ?? '';
  String get filterByCategory =>
      _localizedValues[locale.languageCode]?['filter_by_category'] ?? '';
  String get allReports =>
      _localizedValues[locale.languageCode]?['all_reports'] ?? '';
  String get movementReports =>
      _localizedValues[locale.languageCode]?['movement_reports'] ?? '';
  String get balanceReports =>
      _localizedValues[locale.languageCode]?['balance_reports'] ?? '';
  String get analysisReports =>
      _localizedValues[locale.languageCode]?['analysis_reports'] ?? '';
  String get customReport =>
      _localizedValues[locale.languageCode]?['custom_report'] ?? '';
  String get createCustomReport =>
      _localizedValues[locale.languageCode]?['create_custom_report'] ?? '';
  String get printingReports =>
      _localizedValues[locale.languageCode]?['printing_reports'] ?? '';
  String get monitoringReports =>
      _localizedValues[locale.languageCode]?['monitoring_reports'] ?? '';
  String get financialReports =>
      _localizedValues[locale.languageCode]?['financial_reports'] ?? '';
  String get operationalReports =>
      _localizedValues[locale.languageCode]?['operational_reports'] ?? '';
  String get clearFilter =>
      _localizedValues[locale.languageCode]?['clear_filter'] ?? '';
  String get applyFilter =>
      _localizedValues[locale.languageCode]?['apply_filter'] ?? '';
  String get advancedFilter =>
      _localizedValues[locale.languageCode]?['advanced_filter'] ?? '';
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['en', 'ar', 'fr', 'es', 'id'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
