import 'package:flutter/material.dart';

/// صفحة أرصدة مجموعة حسابات
/// تعرض أرصدة مجموعة من الحسابات المحددة
class AccountGroupBalancesPage extends StatefulWidget {
  const AccountGroupBalancesPage({super.key});

  @override
  State<AccountGroupBalancesPage> createState() => _AccountGroupBalancesPageState();
}

class _AccountGroupBalancesPageState extends State<AccountGroupBalancesPage> {
  String _selectedGroup = 'assets';
  String _selectedPeriod = 'current_month';

  // بيانات تجريبية لمجموعات الحسابات
  final Map<String, Map<String, dynamic>> _accountGroups = {
    'assets': {
      'name': 'مجموعة الأصول',
      'accounts': [
        {'code': '1101', 'name': 'النقدية في الصندوق', 'balance': 15000.0, 'type': 'مدين'},
        {'code': '1102', 'name': 'البنك الأهلي', 'balance': 85000.0, 'type': 'مدين'},
        {'code': '1103', 'name': 'بنك الراجحي', 'balance': 45000.0, 'type': 'مدين'},
        {'code': '1201', 'name': 'العملاء', 'balance': 65000.0, 'type': 'مدين'},
        {'code': '1301', 'name': 'المخزون', 'balance': 120000.0, 'type': 'مدين'},
        {'code': '1401', 'name': 'الأثاث والمعدات', 'balance': 50000.0, 'type': 'مدين'},
      ],
    },
    'liabilities': {
      'name': 'مجموعة الخصوم',
      'accounts': [
        {'code': '2101', 'name': 'الموردين', 'balance': 25000.0, 'type': 'دائن'},
        {'code': '2102', 'name': 'أوراق الدفع', 'balance': 15000.0, 'type': 'دائن'},
        {'code': '2201', 'name': 'قروض طويلة الأجل', 'balance': 100000.0, 'type': 'دائن'},
        {'code': '2301', 'name': 'مصروفات مستحقة', 'balance': 8000.0, 'type': 'دائن'},
      ],
    },
    'revenues': {
      'name': 'مجموعة الإيرادات',
      'accounts': [
        {'code': '4101', 'name': 'إيرادات المبيعات', 'balance': 200000.0, 'type': 'دائن'},
        {'code': '4102', 'name': 'إيرادات الخدمات', 'balance': 35000.0, 'type': 'دائن'},
        {'code': '4201', 'name': 'إيرادات أخرى', 'balance': 15000.0, 'type': 'دائن'},
        {'code': '4301', 'name': 'أرباح استثمارات', 'balance': 8000.0, 'type': 'دائن'},
      ],
    },
    'expenses': {
      'name': 'مجموعة المصروفات',
      'accounts': [
        {'code': '5101', 'name': 'مصروفات الرواتب', 'balance': 80000.0, 'type': 'مدين'},
        {'code': '5102', 'name': 'مصروفات الإيجار', 'balance': 24000.0, 'type': 'مدين'},
        {'code': '5201', 'name': 'مصروفات التسويق', 'balance': 15000.0, 'type': 'مدين'},
        {'code': '5301', 'name': 'مصروفات إدارية', 'balance': 12000.0, 'type': 'مدين'},
        {'code': '5401', 'name': 'مصروفات أخرى', 'balance': 5000.0, 'type': 'مدين'},
      ],
    },
  };

  @override
  Widget build(BuildContext context) {
    final currentGroup = _accountGroups[_selectedGroup]!;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('أرصدة مجموعة حسابات'),
        backgroundColor: Colors.cyan,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedGroup,
                        decoration: const InputDecoration(
                          labelText: 'مجموعة الحسابات',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'assets', child: Text('مجموعة الأصول')),
                          DropdownMenuItem(value: 'liabilities', child: Text('مجموعة الخصوم')),
                          DropdownMenuItem(value: 'revenues', child: Text('مجموعة الإيرادات')),
                          DropdownMenuItem(value: 'expenses', child: Text('مجموعة المصروفات')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedGroup = value!;
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedPeriod,
                        decoration: const InputDecoration(
                          labelText: 'الفترة',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'current_month', child: Text('الشهر الحالي')),
                          DropdownMenuItem(value: 'current_quarter', child: Text('الربع الحالي')),
                          DropdownMenuItem(value: 'current_year', child: Text('السنة الحالية')),
                          DropdownMenuItem(value: 'custom', child: Text('فترة مخصصة')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedPeriod = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // معلومات المجموعة
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              color: _getGroupColor(_selectedGroup),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      currentGroup['name'],
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'إجمالي الأرصدة: ${_getTotalBalance(currentGroup)} ر.س',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'عدد الحسابات: ${currentGroup['accounts'].length}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                _buildStatCard('إجمالي الأرصدة', '${_getTotalBalance(currentGroup)} ر.س', Colors.blue),
                _buildStatCard('عدد الحسابات', currentGroup['accounts'].length.toString(), Colors.green),
                _buildStatCard('متوسط الرصيد', '${_getAverageBalance(currentGroup)} ر.س', Colors.orange),
                _buildStatCard('أكبر رصيد', '${_getMaxBalance(currentGroup)} ر.س', Colors.purple),
              ],
            ),
          ),

          // قائمة الحسابات
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: currentGroup['accounts'].length,
              itemBuilder: (context, index) {
                final account = currentGroup['accounts'][index];
                double percentage = (account['balance'] / _getTotalBalance(currentGroup)) * 100;
                
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: _getAccountTypeColor(account['type']),
                      child: Text(
                        account['code'].substring(0, 2),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    title: Text(
                      '${account['code']} - ${account['name']}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('النوع: ${account['type']}'),
                        Text('النسبة: ${percentage.toStringAsFixed(1)}%'),
                        const SizedBox(height: 4),
                        LinearProgressIndicator(
                          value: percentage / 100,
                          backgroundColor: Colors.grey[300],
                          valueColor: AlwaysStoppedAnimation<Color>(
                            _getAccountTypeColor(account['type']),
                          ),
                        ),
                      ],
                    ),
                    trailing: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '${account['balance'].toStringAsFixed(2)} ر.س',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _getAccountTypeColor(account['type']),
                          ),
                        ),
                        Text(
                          '${percentage.toStringAsFixed(1)}%',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                    isThreeLine: true,
                    onTap: () => _showAccountDetails(account),
                  ),
                );
              },
            ),
          ),

          // ملخص المجموعة
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: _getGroupColor(_selectedGroup).withValues(alpha: 0.1),
              border: Border(top: BorderSide(color: _getGroupColor(_selectedGroup))),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Text(
                  'إجمالي الأرصدة: ${_getTotalBalance(currentGroup)} ر.س',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _getGroupColor(_selectedGroup),
                  ),
                ),
                Text(
                  'عدد الحسابات: ${currentGroup['accounts'].length}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _getGroupColor(_selectedGroup),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _refreshData,
        backgroundColor: Colors.cyan,
        icon: const Icon(Icons.refresh),
        label: const Text('تحديث البيانات'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getGroupColor(String group) {
    switch (group) {
      case 'assets':
        return Colors.green;
      case 'liabilities':
        return Colors.red;
      case 'revenues':
        return Colors.blue;
      case 'expenses':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  Color _getAccountTypeColor(String type) {
    return type == 'مدين' ? Colors.green : Colors.red;
  }

  double _getTotalBalance(Map<String, dynamic> group) {
    return group['accounts'].fold(0.0, (sum, account) => sum + account['balance']);
  }

  String _getAverageBalance(Map<String, dynamic> group) {
    List accounts = group['accounts'];
    if (accounts.isEmpty) return '0.00';
    double total = _getTotalBalance(group);
    return (total / accounts.length).toStringAsFixed(2);
  }

  String _getMaxBalance(Map<String, dynamic> group) {
    List accounts = group['accounts'];
    if (accounts.isEmpty) return '0.00';
    double max = accounts.map((acc) => acc['balance'] as double).reduce((a, b) => a > b ? a : b);
    return max.toStringAsFixed(2);
  }

  void _showAccountDetails(Map<String, dynamic> account) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(account['name']),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('رقم الحساب: ${account['code']}'),
            Text('الرصيد: ${account['balance'].toStringAsFixed(2)} ر.س'),
            Text('النوع: ${account['type']}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('عرض تفاصيل ${account['name']}')),
              );
            },
            child: const Text('عرض التفاصيل'),
          ),
        ],
      ),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة أرصدة مجموعة الحسابات')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير أرصدة مجموعة الحسابات')),
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث بيانات أرصدة مجموعة الحسابات'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
