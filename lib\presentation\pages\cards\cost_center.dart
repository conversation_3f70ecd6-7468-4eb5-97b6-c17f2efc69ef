import 'package:flutter/material.dart';
import '../../widgets/global_buttons/new_button.dart';
import '../../widgets/global_buttons/query_button.dart';
import '../../widgets/global_buttons/delete_button.dart';
import '../../widgets/global_buttons/undo_button.dart';
import '../../widgets/global_buttons/save_button.dart';
import '../../widgets/global_buttons/print_button.dart';

/// نموذج بيانات مركز التكلفة
class CostCenterData {
  String branchNumber;
  String centerCode;
  String arabicName;
  String englishName;
  String centerType;
  String freezeType;
  String currency;
  String securityLevel;
  String subCenterType;
  String balanceWarning;
  bool showNetMovement;
  bool isFolder;

  CostCenterData({
    this.branchNumber = '001',
    this.centerCode = '',
    this.arabicName = '',
    this.englishName = '',
    this.centerType = 'تشغيلي',
    this.freezeType = 'بدون',
    this.currency = 'ريال سعودي',
    this.securityLevel = 'عادي',
    this.subCenterType = '',
    this.balanceWarning = 'بدون',
    this.showNetMovement = false,
    this.isFolder = true,
  });
}

/// نموذج عقدة شجرة مراكز التكلفة
class CostCenterTreeNode {
  final String code;
  final String name;
  final int level;
  final bool hasChildren;
  final List<CostCenterTreeNode> children;
  bool isExpanded;
  bool isSelected;

  CostCenterTreeNode({
    required this.code,
    required this.name,
    required this.level,
    required this.hasChildren,
    required this.children,
    this.isExpanded = false,
    this.isSelected = false,
  });
}

/// صفحة مراكز التكلفة
class CostCenter extends StatefulWidget {
  const CostCenter({super.key});

  @override
  State<CostCenter> createState() => _CostCenterState();
}

class _CostCenterState extends State<CostCenter> {
  // متغيرات التحكم في الشجرة
  int _selectedTreeLevel = 6; // عرض جميع المستويات افتراضياً
  bool _showAllLevels = true;
  CostCenterTreeNode? _selectedNode;
  bool _includeSubCenters = false;

  /// قائمة مراكز التكلفة المحفوظة
  List<CostCenterData> _savedCenters = [
    // مراكز تجريبية لاختبار النظام الجديد
    CostCenterData(
      centerCode: '1',
      arabicName: 'مراكز الإنتاج',
      englishName: 'Production Centers',
      isFolder: true,
    ),
    CostCenterData(
      centerCode: '11',
      arabicName: 'مركز الإنتاج الرئيسي',
      englishName: 'Main Production Center',
      isFolder: true,
    ),
    CostCenterData(
      centerCode: '111',
      arabicName: 'خط الإنتاج الأول',
      englishName: 'Production Line 1',
      isFolder: false,
    ),
    CostCenterData(
      centerCode: '112',
      arabicName: 'خط الإنتاج الثاني',
      englishName: 'Production Line 2',
      isFolder: false,
    ),
    CostCenterData(
      centerCode: '2',
      arabicName: 'مراكز الخدمات',
      englishName: 'Service Centers',
      isFolder: true,
    ),
    CostCenterData(
      centerCode: '21',
      arabicName: 'مركز الصيانة',
      englishName: 'Maintenance Center',
      isFolder: false,
    ),
    CostCenterData(
      centerCode: '22',
      arabicName: 'مركز النظافة',
      englishName: 'Cleaning Center',
      isFolder: false,
    ),
    CostCenterData(
      centerCode: '3',
      arabicName: 'مراكز الإدارة',
      englishName: 'Administrative Centers',
      isFolder: true,
    ),
    CostCenterData(
      centerCode: '31',
      arabicName: 'إدارة الموارد البشرية',
      englishName: 'Human Resources',
      isFolder: false,
    ),
    CostCenterData(
      centerCode: '32',
      arabicName: 'إدارة المالية',
      englishName: 'Finance Department',
      isFolder: false,
    ),
    CostCenterData(
      centerCode: '4',
      arabicName: 'مراكز المبيعات',
      englishName: 'Sales Centers',
      isFolder: true,
    ),
    CostCenterData(
      centerCode: '41',
      arabicName: 'مبيعات المنطقة الشرقية',
      englishName: 'Eastern Region Sales',
      isFolder: false,
    ),
    CostCenterData(
      centerCode: '42',
      arabicName: 'مبيعات المنطقة الغربية',
      englishName: 'Western Region Sales',
      isFolder: false,
    ),
    CostCenterData(
      centerCode: '5',
      arabicName: 'مراكز التطوير',
      englishName: 'Development Centers',
      isFolder: true,
    ),
    CostCenterData(
      centerCode: '51',
      arabicName: 'البحث والتطوير',
      englishName: 'Research & Development',
      isFolder: false,
    ),
    CostCenterData(
      centerCode: '52',
      arabicName: 'التدريب والتأهيل',
      englishName: 'Training & Qualification',
      isFolder: false,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: Column(
        children: [
          // أزرار التحكم
          _buildControlButtons(),

          // محتوى الصفحة
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // شجرة مراكز التكلفة (في الأعلى)
                  Expanded(
                    flex: 3,
                    child: _buildCostCenterTreeCard(),
                  ),

                  const SizedBox(height: 16),

                  // بطاقة بيانات المركز (في الأسفل)
                  Expanded(
                    flex: 2,
                    child: _buildCenterDataCard(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء أزرار التحكم
  Widget _buildControlButtons() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان
          Text(
            'مراكز التكلفة',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.blue[800],
            ),
          ),

          const SizedBox(height: 12),

          // الأزرار
          Wrap(
            spacing: 6,
            runSpacing: 6,
            children: [
              // زر إضافة
              NewButton(
                tooltip: 'إضافة مركز تكلفة جديد',
                onPressed: _selectedNode != null ? _addNewCenter : null,
              ),

              const SizedBox(width: 6),

              // زر استعلام
              QueryButton(
                tooltip: 'استعلام عن مركز التكلفة',
                onPressed: _selectedNode != null && !_selectedNode!.hasChildren
                    ? _queryCenter
                    : null,
              ),

              const SizedBox(width: 6),

              // زر حذف
              DeleteButton(
                tooltip: 'حذف مركز التكلفة',
                onPressed: _selectedNode != null ? _deleteCenter : null,
              ),

              const SizedBox(width: 6),

              // زر تراجع
              UndoButton(
                tooltip: 'تراجع عن آخر عملية',
                onPressed: _undoLastAction,
              ),

              const SizedBox(width: 6),

              // زر حفظ
              SaveButton(
                tooltip: 'حفظ التغييرات',
                onPressed: _saveChanges,
              ),

              const SizedBox(width: 6),

              // زر طباعة
              PrintButton(
                tooltip: 'طباعة شجرة مراكز التكلفة',
                onPressed: _printCenterTree,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة شجرة مراكز التكلفة
  Widget _buildCostCenterTreeCard() {
    return Card(
      elevation: 2,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس البطاقة
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.account_tree, color: Colors.blue[700]),
                const SizedBox(width: 8),
                Text(
                  'شجرة مراكز التكلفة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[700],
                  ),
                ),
                const Spacer(),
                // أزرار التحكم في الطي والتوسعة
                _buildTreeControlButtons(),
                const SizedBox(width: 16),
                // اختيار مستوى العرض
                _buildLevelSelector(),
              ],
            ),
          ),

          // محتوى الشجرة
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: _buildTreeView(),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء أزرار التحكم في الطي والتوسعة
  Widget _buildTreeControlButtons() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // زر توسع الكل
        Tooltip(
          message: 'توسع جميع المجلدات',
          child: InkWell(
            onTap: _expandAllNodes,
            borderRadius: BorderRadius.circular(4),
            child: Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: Colors.green[300]!),
              ),
              child: Icon(
                Icons.unfold_more,
                size: 16,
                color: Colors.green[700],
              ),
            ),
          ),
        ),

        const SizedBox(width: 4),

        // زر طي الكل
        Tooltip(
          message: 'طي جميع المجلدات',
          child: InkWell(
            onTap: _collapseAllNodes,
            borderRadius: BorderRadius.circular(4),
            child: Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Colors.orange[50],
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: Colors.orange[300]!),
              ),
              child: Icon(
                Icons.unfold_less,
                size: 16,
                color: Colors.orange[700],
              ),
            ),
          ),
        ),

        const SizedBox(width: 4),

        // زر توسع حسب المستوى
        Tooltip(
          message: 'توسع حسب المستوى المحدد',
          child: InkWell(
            onTap: () => _expandToLevel(_selectedTreeLevel),
            borderRadius: BorderRadius.circular(4),
            child: Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: Colors.blue[300]!),
              ),
              child: Icon(
                Icons.account_tree,
                size: 16,
                color: Colors.blue[700],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// بناء منتقي مستوى العرض
  Widget _buildLevelSelector() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          'عرض المستويات:',
          style: TextStyle(
            fontSize: 14,
            color: Colors.blue[600],
          ),
        ),
        const SizedBox(width: 8),
        ...List.generate(6, (index) {
          final level = index + 1;
          return Padding(
            padding: const EdgeInsets.only(left: 4),
            child: ChoiceChip(
              label: Text(level.toString()),
              selected: _selectedTreeLevel == level || _showAllLevels,
              onSelected: (selected) {
                setState(() {
                  if (level == 6) {
                    _showAllLevels = !_showAllLevels;
                    if (_showAllLevels) _selectedTreeLevel = 6;
                  } else {
                    _showAllLevels = false;
                    _selectedTreeLevel = level;
                  }
                });
              },
              selectedColor: Colors.blue[100],
              labelStyle: TextStyle(
                color: (_selectedTreeLevel == level || _showAllLevels)
                    ? Colors.blue[700]
                    : Colors.grey[600],
                fontSize: 12,
              ),
            ),
          );
        }),
        Padding(
          padding: const EdgeInsets.only(left: 4),
          child: ChoiceChip(
            label: const Text('*'),
            selected: _showAllLevels,
            onSelected: (selected) {
              setState(() {
                _showAllLevels = selected;
                if (_showAllLevels) _selectedTreeLevel = 6;
              });
            },
            selectedColor: Colors.blue[100],
            labelStyle: TextStyle(
              color: _showAllLevels ? Colors.blue[700] : Colors.grey[600],
              fontSize: 12,
            ),
          ),
        ),
      ],
    );
  }

  /// بناء عرض الشجرة
  Widget _buildTreeView() {
    final treeData = _buildTreeData();

    if (treeData.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد مراكز تكلفة',
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    }

    return ListView.builder(
      itemCount: treeData.length,
      itemBuilder: (context, index) {
        return _buildTreeNode(treeData[index]);
      },
    );
  }

  /// بناء عقدة الشجرة
  Widget _buildTreeNode(CostCenterTreeNode node) {
    return Column(
      children: [
        _buildNodeItem(node),
        if (node.isExpanded && node.children.isNotEmpty)
          ...node.children.map((child) => _buildTreeNode(child)),
      ],
    );
  }

  /// بناء عنصر العقدة
  Widget _buildNodeItem(CostCenterTreeNode node) {
    final isSelected = _selectedNode?.code == node.code;

    return Container(
      margin: EdgeInsets.only(right: node.level * 20.0),
      child: InkWell(
        onTap: () => _selectNode(node),
        onDoubleTap: () => _onNodeDoubleClick(node),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: isSelected ? Colors.blue[100] : null,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Row(
            children: [
              // أيقونة التوسع/الطي
              if (node.hasChildren)
                InkWell(
                  onTap: () => _toggleNodeExpansion(node),
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color:
                          node.isExpanded ? Colors.blue[50] : Colors.grey[100],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: node.isExpanded
                            ? Colors.blue[300]!
                            : Colors.grey[300]!,
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      node.isExpanded ? Icons.remove : Icons.add,
                      size: 12,
                      color:
                          node.isExpanded ? Colors.blue[700] : Colors.grey[600],
                    ),
                  ),
                )
              else
                const SizedBox(width: 16),

              const SizedBox(width: 4),

              // أيقونة النوع
              Icon(
                node.hasChildren
                    ? (node.isExpanded ? Icons.folder_open : Icons.folder)
                    : Icons.insert_drive_file,
                size: 16,
                color: node.hasChildren
                    ? (node.isExpanded ? Colors.blue[700] : Colors.blue[600])
                    : Colors.grey[600],
              ),

              const SizedBox(width: 8),

              // رقم المركز
              Text(
                node.code,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[700],
                ),
              ),

              const SizedBox(width: 8),

              // اسم المركز
              Expanded(
                child: Text(
                  node.name,
                  style: const TextStyle(fontSize: 14),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء بطاقة بيانات المركز
  Widget _buildCenterDataCard() {
    return Card(
      elevation: 2,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس البطاقة
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.green[700]),
                const SizedBox(width: 8),
                Text(
                  'بيانات مركز التكلفة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green[700],
                  ),
                ),
              ],
            ),
          ),

          // محتوى البطاقة
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: _selectedNode == null
                  ? const Center(
                      child: Text(
                        'اختر مركز تكلفة من الشجرة لعرض بياناته',
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                        textAlign: TextAlign.center,
                      ),
                    )
                  : _buildCenterDetails(),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء تفاصيل المركز
  Widget _buildCenterDetails() {
    if (_selectedNode == null) return const SizedBox();

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDetailRow('رقم المركز', _selectedNode!.code),
          _buildDetailRow('اسم المركز', _selectedNode!.name),
          _buildDetailRow(
              'نوع المركز', _selectedNode!.hasChildren ? 'مجلد' : 'ملف'),
          _buildDetailRow('المستوى', '${_selectedNode!.level + 1}'),
          if (_selectedNode!.hasChildren) ...[
            const SizedBox(height: 16),
            Text(
              'المراكز الفرعية:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.blue[700],
              ),
            ),
            const SizedBox(height: 8),
            ..._selectedNode!.children.map(
              (child) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Text('• ${child.code} - ${child.name}'),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء صف التفاصيل
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بيانات الشجرة
  List<CostCenterTreeNode> _buildTreeData() {
    // تحويل البيانات المحفوظة إلى شجرة
    final Map<String, CostCenterTreeNode> nodeMap = {};
    final List<CostCenterTreeNode> rootNodes = [];

    // إنشاء العقد
    for (final center in _savedCenters) {
      final node = CostCenterTreeNode(
        code: center.centerCode,
        name: center.arabicName,
        level: center.centerCode.length - 1,
        hasChildren: center.isFolder,
        children: [],
      );
      nodeMap[center.centerCode] = node;
    }

    // بناء الهيكل الهرمي
    for (final center in _savedCenters) {
      final node = nodeMap[center.centerCode]!;

      if (center.centerCode.length == 1) {
        // عقدة جذر
        rootNodes.add(node);
      } else {
        // البحث عن العقدة الأب
        for (int i = center.centerCode.length - 1; i > 0; i--) {
          final parentCode = center.centerCode.substring(0, i);
          final parentNode = nodeMap[parentCode];
          if (parentNode != null) {
            parentNode.children.add(node);
            break;
          }
        }
      }
    }

    // تطبيق فلتر المستوى
    return _filterByLevel(rootNodes);
  }

  /// تطبيق فلتر المستوى
  List<CostCenterTreeNode> _filterByLevel(List<CostCenterTreeNode> nodes) {
    if (_showAllLevels) return nodes;

    List<CostCenterTreeNode> filtered = [];

    for (final node in nodes) {
      if (node.level < _selectedTreeLevel) {
        final filteredNode = CostCenterTreeNode(
          code: node.code,
          name: node.name,
          level: node.level,
          hasChildren: node.hasChildren && node.level < _selectedTreeLevel - 1,
          children: _filterByLevel(node.children),
        );
        filtered.add(filteredNode);
      }
    }

    return filtered;
  }

  /// اختيار عقدة
  void _selectNode(CostCenterTreeNode node) {
    setState(() {
      _selectedNode = node;
    });
  }

  /// التعامل مع النقر المزدوج على العقدة
  void _onNodeDoubleClick(CostCenterTreeNode node) {
    if (node.hasChildren) {
      _toggleNodeExpansion(node);
    } else {
      // عرض تفاصيل المركز
      _queryCenter();
    }
  }

  /// تبديل توسع العقدة
  void _toggleNodeExpansion(CostCenterTreeNode node) {
    setState(() {
      node.isExpanded = !node.isExpanded;
    });
  }

  /// توسع جميع العقد
  void _expandAllNodes() {
    setState(() {
      _setAllNodesExpansion(true);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم توسع جميع المجلدات'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// طي جميع العقد
  void _collapseAllNodes() {
    setState(() {
      _setAllNodesExpansion(false);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم طي جميع المجلدات'),
        backgroundColor: Colors.orange,
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// تعيين حالة التوسع لجميع العقد
  void _setAllNodesExpansion(bool expanded) {
    final treeData = _buildTreeData();
    for (final node in treeData) {
      _setNodeExpansionRecursive(node, expanded);
    }
  }

  /// تعيين حالة التوسع للعقدة وجميع أطفالها بشكل تكراري
  void _setNodeExpansionRecursive(CostCenterTreeNode node, bool expanded) {
    if (node.hasChildren) {
      node.isExpanded = expanded;
      for (final child in node.children) {
        _setNodeExpansionRecursive(child, expanded);
      }
    }
  }

  /// توسع المستوى المحدد فقط
  void _expandToLevel(int level) {
    setState(() {
      final treeData = _buildTreeData();
      for (final node in treeData) {
        _setNodeExpansionToLevel(node, level);
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم توسع المجلدات حتى المستوى $level'),
        backgroundColor: Colors.blue,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// تعيين حالة التوسع للعقدة حتى مستوى معين
  void _setNodeExpansionToLevel(CostCenterTreeNode node, int targetLevel) {
    if (node.hasChildren) {
      node.isExpanded = node.level < targetLevel;
      for (final child in node.children) {
        _setNodeExpansionToLevel(child, targetLevel);
      }
    }
  }

  // ===== دوال الأزرار =====

  /// إضافة مركز تكلفة جديد
  void _addNewCenter() {
    if (_selectedNode == null) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('سيتم إضافة مركز جديد تحت "${_selectedNode!.name}"'),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// استعلام عن مركز التكلفة
  void _queryCenter() {
    if (_selectedNode == null) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('استعلام عن مركز التكلفة "${_selectedNode!.name}"'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// حذف مركز التكلفة
  void _deleteCenter() {
    if (_selectedNode == null) return;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text(
            'هل أنت متأكد من حذف مركز التكلفة "${_selectedNode!.name}"؟\n'
            'هذا الإجراء لا يمكن التراجع عنه.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                _performDelete();
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('حذف', style: TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    );
  }

  /// تنفيذ عملية الحذف
  void _performDelete() {
    if (_selectedNode == null) return;

    // حذف المركز من القائمة
    _savedCenters.removeWhere(
      (center) => center.centerCode == _selectedNode!.code,
    );

    // عرض رسالة نجاح
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم حذف مركز التكلفة "${_selectedNode!.name}" بنجاح'),
        backgroundColor: Colors.red,
      ),
    );

    // إلغاء التحديد
    setState(() {
      _selectedNode = null;
    });
  }

  /// تراجع عن آخر عملية
  void _undoLastAction() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('وظيفة التراجع قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  /// حفظ التغييرات
  void _saveChanges() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم حفظ جميع التغييرات بنجاح'),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// طباعة شجرة مراكز التكلفة
  void _printCenterTree() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('وظيفة الطباعة قيد التطوير'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
