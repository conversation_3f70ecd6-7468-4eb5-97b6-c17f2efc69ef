# 📁 هيكل ملفات الترجمة المنظم

## 🎯 نظرة عامة

تم تقسيم ملف الترجمة الكبير `app_localizations.dart` إلى ملفات أصغر منظمة حسب الوحدات لتسهيل الصيانة والتطوير.

## 📂 هيكل المجلد

```
lib/core/localization/modules/
├── common_localizations.dart           # المصطلحات المشتركة
├── general_localizations.dart          # وحدة العام
├── cards_localizations.dart            # وحدة الكروت
├── purchases_localizations.dart        # وحدة المشتريات
├── sales_localizations.dart            # وحدة المبيعات
├── vouchers_localizations.dart         # وحدة السندات
├── inventory_reports_localizations.dart # تقارير المخزون
├── account_reports_localizations.dart  # تقارير الحسابات
├── statistical_reports_localizations.dart # التقارير الإحصائية
├── system_management_localizations.dart # إدارة النظام
├── tools_localizations.dart            # الأدوات
├── help_localizations.dart             # التعليمات
└── README.md                           # هذا الملف
```

## 🔧 كيفية العمل

### **1. الملف الرئيسي (app_localizations.dart)**
- يستورد جميع ملفات الوحدات
- يدمج البيانات من جميع الوحدات
- يحتفظ بنفس الـ getters الموجودة

### **2. ملفات الوحدات**
كل ملف وحدة يحتوي على:
- البيانات العربية والإنجليزية للوحدة
- تعليقات عربية واضحة
- تنظيم منطقي للمفاتيح

## 📋 مثال على ملف وحدة

```dart
class CardsLocalizations {
  static const Map<String, Map<String, String>> values = {
    'en': {
      // صفحات وحدة الكروت
      'financial_guide': 'Financial Guide',
      'customers': 'Customers',
      // ... باقي المفاتيح
    },
    'ar': {
      // صفحات وحدة الكروت
      'financial_guide': 'الدليل المالي',
      'customers': 'العملاء',
      // ... باقي المفاتيح
    },
  };
}
```

## ✅ المزايا

### **1. تنظيم أفضل**
- كل وحدة في ملف منفصل
- سهولة العثور على المفاتيح
- تقليل حجم الملفات

### **2. سهولة الصيانة**
- تعديل وحدة واحدة فقط عند الحاجة
- تقليل التعارضات في Git
- إضافة مفاتيح جديدة بسهولة

### **3. تطوير متوازي**
- عدة مطورين يعملون على وحدات مختلفة
- تقليل التداخل في العمل

### **4. إضافة لغات جديدة**
- إضافة لغة جديدة في كل ملف وحدة
- سهولة التتبع والمراجعة

## 🚀 إضافة مفتاح جديد

### **الخطوة 1: تحديد الوحدة**
حدد الوحدة المناسبة للمفتاح الجديد

### **الخطوة 2: إضافة المفتاح**
```dart
// في ملف الوحدة المناسب
'en': {
  'new_key': 'English Text',
},
'ar': {
  'new_key': 'النص العربي',
},
```

### **الخطوة 3: إضافة Getter (اختياري)**
```dart
// في app_localizations.dart
String get newKey =>
    _localizedValues[locale.languageCode]?['new_key'] ?? '';
```

## 🔄 إضافة لغة جديدة

### **الخطوة 1: إضافة في كل ملف وحدة**
```dart
static const Map<String, Map<String, String>> values = {
  'en': { /* ... */ },
  'ar': { /* ... */ },
  'fr': { /* النصوص الفرنسية */ },
};
```

### **الخطوة 2: تحديث الملف الرئيسي**
```dart
static final Map<String, Map<String, String>> _localizedValues = {
  'en': { /* ... */ },
  'ar': { /* ... */ },
  'fr': {
    ...CommonLocalizations.values['fr']!,
    ...GeneralLocalizations.values['fr']!,
    // ... باقي الوحدات
  },
};
```

## 📊 إحصائيات الوحدات

| الوحدة | عدد المفاتيح التقريبي | الوصف |
|--------|---------------------|--------|
| Common | 80+ | المصطلحات المشتركة |
| General | 8 | صفحات العام |
| Cards | 20+ | صفحات الكروت |
| Purchases | 8 | صفحات المشتريات |
| Sales | 10 | صفحات المبيعات |
| Vouchers | 6 | صفحات السندات |
| Inventory Reports | 20+ | تقارير المخزون |
| Account Reports | 8 | تقارير الحسابات |
| Statistical Reports | 4 | التقارير الإحصائية |
| System Management | 50+ | إدارة النظام |
| Tools | 4 | الأدوات |
| Help | 4 | التعليمات |

## 🛠️ نصائح للمطورين

### **1. اتبع نمط التسمية**
- استخدم snake_case للمفاتيح
- أسماء واضحة ومعبرة

### **2. أضف التعليقات**
- تعليقات عربية لكل قسم
- وصف واضح للغرض

### **3. اختبر التغييرات**
- تأكد من عمل جميع المفاتيح
- اختبر اللغتين العربية والإنجليزية

### **4. حافظ على التنظيم**
- ضع المفاتيح في الوحدة المناسبة
- رتب المفاتيح منطقياً

## 🔍 استكشاف الأخطاء

### **مشكلة: مفتاح غير موجود**
```dart
// تأكد من إضافة المفتاح في ملف الوحدة المناسب
// تأكد من استيراد الوحدة في app_localizations.dart
```

### **مشكلة: ترجمة مفقودة**
```dart
// تأكد من إضافة الترجمة في كلا اللغتين
'en': { 'key': 'English' },
'ar': { 'key': 'العربية' },
```

---

**📝 ملاحظة:** هذا الهيكل يسهل الصيانة ويحسن تجربة المطور. في حالة وجود أي مشاكل، راجع الملف الرئيسي `app_localizations.dart` والتأكد من استيراد جميع الوحدات.
