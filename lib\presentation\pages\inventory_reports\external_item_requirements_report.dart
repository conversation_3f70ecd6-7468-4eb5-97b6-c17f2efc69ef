import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير بالكميات المطلوبة خارجي لصنف
/// يعرض الكميات المطلوبة من الأصناف للعملاء الخارجيين
class ExternalItemRequirementsReportPage extends StatefulWidget {
  const ExternalItemRequirementsReportPage({super.key});

  @override
  State<ExternalItemRequirementsReportPage> createState() =>
      _ExternalItemRequirementsReportPageState();
}

class _ExternalItemRequirementsReportPageState
    extends State<ExternalItemRequirementsReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedItem;
  String? _selectedCustomer;
  String? _requestStatus = 'all';
  String? _priority = 'all';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الكميات المطلوبة خارجي لصنف'),
        backgroundColor: Colors.deepOrange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: _sendNotifications,
            tooltip: 'إرسال إشعارات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          _buildFilterSection(localizations),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildPrioritySection(),
                  const SizedBox(height: 16),
                  _buildRequestsTableSection(),
                  const SizedBox(height: 16),
                  _buildCustomerAnalysisSection(),
                  const SizedBox(height: 16),
                  _buildItemDemandSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.deepOrange[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.fromDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, true),
                  controller: TextEditingController(
                    text: _startDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.toDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, false),
                  controller: TextEditingController(
                    text: _endDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'الصنف',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedItem,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الأصناف')),
                    DropdownMenuItem(
                        value: 'laptop', child: Text('لابتوب ديل XPS 13')),
                    DropdownMenuItem(
                        value: 'phone', child: Text('هاتف آيفون 15')),
                    DropdownMenuItem(
                        value: 'printer', child: Text('طابعة HP LaserJet')),
                    DropdownMenuItem(
                        value: 'tablet', child: Text('تابلت سامسونج')),
                  ],
                  onChanged: (value) => setState(() => _selectedItem = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'العميل',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedCustomer,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع العملاء')),
                    DropdownMenuItem(
                        value: 'customer1',
                        child: Text('شركة الأعمال المتقدمة')),
                    DropdownMenuItem(
                        value: 'customer2',
                        child: Text('مؤسسة التطوير الحديث')),
                    DropdownMenuItem(
                        value: 'customer3', child: Text('شركة الحلول التقنية')),
                  ],
                  onChanged: (value) =>
                      setState(() => _selectedCustomer = value),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'حالة الطلب',
                    border: OutlineInputBorder(),
                  ),
                  value: _requestStatus,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الحالات')),
                    DropdownMenuItem(
                        value: 'pending', child: Text('قيد الانتظار')),
                    DropdownMenuItem(
                        value: 'approved', child: Text('موافق عليه')),
                    DropdownMenuItem(
                        value: 'processing', child: Text('قيد التنفيذ')),
                    DropdownMenuItem(value: 'completed', child: Text('مكتمل')),
                    DropdownMenuItem(value: 'cancelled', child: Text('ملغي')),
                  ],
                  onChanged: (value) => setState(() => _requestStatus = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'الأولوية',
                    border: OutlineInputBorder(),
                  ),
                  value: _priority,
                  items: const [
                    DropdownMenuItem(
                        value: 'all', child: Text('جميع الأولويات')),
                    DropdownMenuItem(value: 'urgent', child: Text('عاجل')),
                    DropdownMenuItem(value: 'high', child: Text('عالي')),
                    DropdownMenuItem(value: 'medium', child: Text('متوسط')),
                    DropdownMenuItem(value: 'low', child: Text('منخفض')),
                  ],
                  onChanged: (value) => setState(() => _priority = value),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.request_quote,
                    color: Colors.deepOrange, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص الطلبات الخارجية',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي الطلبات', '185', Colors.deepOrange,
                    Icons.request_quote),
                _buildSummaryCard(
                    'قيد الانتظار', '45', Colors.orange, Icons.pending),
                _buildSummaryCard(
                    'قيد التنفيذ', '85', Colors.blue, Icons.build),
                _buildSummaryCard(
                    'مكتملة', '55', Colors.green, Icons.check_circle),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrioritySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.priority_high, color: Colors.red, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'توزيع الأولويات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildPriorityCard('عاجل', '25', Colors.red, Icons.warning),
                _buildPriorityCard(
                    'عالي', '45', Colors.orange, Icons.priority_high),
                _buildPriorityCard('متوسط', '85', Colors.blue, Icons.remove),
                _buildPriorityCard(
                    'منخفض', '30', Colors.green, Icons.low_priority),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRequestsTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل الطلبات الخارجية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('رقم الطلب')),
                  DataColumn(label: Text('العميل')),
                  DataColumn(label: Text('الصنف')),
                  DataColumn(label: Text('الكمية المطلوبة')),
                  DataColumn(label: Text('الكمية المتوفرة')),
                  DataColumn(label: Text('النقص')),
                  DataColumn(label: Text('تاريخ الطلب')),
                  DataColumn(label: Text('تاريخ التسليم')),
                  DataColumn(label: Text('الأولوية')),
                  DataColumn(label: Text('الحالة')),
                  DataColumn(label: Text('إجراءات')),
                ],
                rows: _buildRequestRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerAnalysisSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.business, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'تحليل العملاء',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildCustomerAnalysisList(),
          ],
        ),
      ),
    );
  }

  Widget _buildItemDemandSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.trending_up, color: Colors.purple, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'الأصناف الأكثر طلباً',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildItemDemandList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _approveRequests,
                    icon: const Icon(Icons.check),
                    label: const Text('موافقة مجمعة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _createPurchaseOrder,
                    icon: const Icon(Icons.shopping_cart),
                    label: const Text('إنشاء أمر شراء'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _updateDeliveryDates,
                    icon: const Icon(Icons.schedule),
                    label: const Text('تحديث مواعيد التسليم'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _generateForecast,
                    icon: const Icon(Icons.analytics),
                    label: const Text('توقعات الطلب'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildCustomerAnalysisList() {
    final customers = [
      {
        'name': 'شركة الأعمال المتقدمة',
        'requests': '45',
        'value': '850,000 ر.س',
        'status': 'عميل مميز'
      },
      {
        'name': 'مؤسسة التطوير الحديث',
        'requests': '35',
        'value': '620,000 ر.س',
        'status': 'عميل نشط'
      },
      {
        'name': 'شركة الحلول التقنية',
        'requests': '28',
        'value': '485,000 ر.س',
        'status': 'عميل جديد'
      },
      {
        'name': 'عملاء آخرين',
        'requests': '77',
        'value': '1,245,000 ر.س',
        'status': 'متنوع'
      },
    ];

    return customers
        .map((customer) => Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.indigo.withValues(alpha: 0.3)),
                borderRadius: BorderRadius.circular(8),
                color: Colors.indigo.withValues(alpha: 0.05),
              ),
              child: Row(
                children: [
                  const Icon(Icons.business, color: Colors.indigo, size: 24),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          customer['name']!,
                          style: const TextStyle(
                              fontSize: 14, fontWeight: FontWeight.bold),
                        ),
                        Text(
                          'الطلبات: ${customer['requests']} • القيمة: ${customer['value']}',
                          style:
                              TextStyle(fontSize: 12, color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getCustomerStatusColor(customer['status']!)
                          .withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      customer['status']!,
                      style: TextStyle(
                        color: _getCustomerStatusColor(customer['status']!),
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ))
        .toList();
  }

  List<Widget> _buildItemDemandList() {
    final items = [
      {
        'name': 'لابتوب ديل XPS 13',
        'requests': '85',
        'shortage': '25',
        'priority': 'عاجل'
      },
      {
        'name': 'هاتف آيفون 15',
        'requests': '65',
        'shortage': '15',
        'priority': 'عالي'
      },
      {
        'name': 'طابعة HP LaserJet',
        'requests': '45',
        'shortage': '8',
        'priority': 'متوسط'
      },
      {
        'name': 'تابلت سامسونج',
        'requests': '35',
        'shortage': '12',
        'priority': 'عالي'
      },
    ];

    return items
        .map((item) => Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.purple.withValues(alpha: 0.3)),
                borderRadius: BorderRadius.circular(8),
                color: Colors.purple.withValues(alpha: 0.05),
              ),
              child: Row(
                children: [
                  const Icon(Icons.trending_up, color: Colors.purple, size: 24),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item['name']!,
                          style: const TextStyle(
                              fontSize: 14, fontWeight: FontWeight.bold),
                        ),
                        Text(
                          'الطلبات: ${item['requests']} • النقص: ${item['shortage']}',
                          style:
                              TextStyle(fontSize: 12, color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getPriorityColor(item['priority']!)
                          .withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      item['priority']!,
                      style: TextStyle(
                        color: _getPriorityColor(item['priority']!),
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ))
        .toList();
  }

  List<DataRow> _buildRequestRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('REQ001')),
        const DataCell(Text('شركة الأعمال المتقدمة')),
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('50')),
        const DataCell(Text('25')),
        const DataCell(Text('25')),
        const DataCell(Text('2024-01-15')),
        const DataCell(Text('2024-02-01')),
        DataCell(_buildPriorityBadge('عاجل', Colors.red)),
        DataCell(_buildStatusBadge('قيد التنفيذ', Colors.blue)),
        DataCell(IconButton(
          icon: const Icon(Icons.edit, color: Colors.blue, size: 16),
          onPressed: () => _editRequest('REQ001'),
        )),
      ]),
      DataRow(cells: [
        const DataCell(Text('REQ002')),
        const DataCell(Text('مؤسسة التطوير الحديث')),
        const DataCell(Text('هاتف آيفون 15')),
        const DataCell(Text('30')),
        const DataCell(Text('15')),
        const DataCell(Text('15')),
        const DataCell(Text('2024-01-18')),
        const DataCell(Text('2024-02-05')),
        DataCell(_buildPriorityBadge('عالي', Colors.orange)),
        DataCell(_buildStatusBadge('قيد الانتظار', Colors.orange)),
        DataCell(IconButton(
          icon: const Icon(Icons.edit, color: Colors.blue, size: 16),
          onPressed: () => _editRequest('REQ002'),
        )),
      ]),
    ];
  }

  Widget _buildSummaryCard(
      String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                    fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title,
                  style: const TextStyle(fontSize: 12),
                  textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPriorityCard(
      String title, String count, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                count,
                style: TextStyle(
                    fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title,
                  style: const TextStyle(fontSize: 12),
                  textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(status,
          style: TextStyle(
              color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Widget _buildPriorityBadge(String priority, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(priority,
          style: TextStyle(
              color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Color _getCustomerStatusColor(String status) {
    switch (status) {
      case 'عميل مميز':
        return Colors.amber;
      case 'عميل نشط':
        return Colors.green;
      case 'عميل جديد':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  Color _getPriorityColor(String priority) {
    switch (priority) {
      case 'عاجل':
        return Colors.red;
      case 'عالي':
        return Colors.orange;
      case 'متوسط':
        return Colors.blue;
      case 'منخفض':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _sendNotifications() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إرسال إشعارات للعملاء')),
    );
  }

  void _approveRequests() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('موافقة مجمعة على الطلبات')),
    );
  }

  void _createPurchaseOrder() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء أمر شراء للأصناف المطلوبة')),
    );
  }

  void _updateDeliveryDates() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحديث مواعيد التسليم')),
    );
  }

  void _generateForecast() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء توقعات الطلب المستقبلي')),
    );
  }

  void _editRequest(String requestId) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تعديل الطلب: $requestId')),
    );
  }
}
