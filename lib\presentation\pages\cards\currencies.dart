import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة العملات
/// تتيح إدارة العملات وأسعار الصرف
class CurrenciesPage extends StatefulWidget {
  const CurrenciesPage({super.key});

  @override
  State<CurrenciesPage> createState() => _CurrenciesPageState();
}

class _CurrenciesPageState extends State<CurrenciesPage> {
  String _searchQuery = '';
  String _selectedType = 'all';

  // بيانات تجريبية للعملات
  final List<Map<String, dynamic>> _currencies = [
    {
      'id': 'SAR',
      'name': 'الريال السعودي',
      'code': 'SAR',
      'symbol': 'ر.س',
      'exchangeRate': 1.0,
      'isBaseCurrency': true,
      'isActive': true,
      'country': 'السعودية',
      'lastUpdated': '2024-01-15 10:30:00',
      'flag': '🇸🇦',
    },
    {
      'id': 'USD',
      'name': 'الدولار الأمريكي',
      'code': 'USD',
      'symbol': '\$',
      'exchangeRate': 3.75,
      'isBaseCurrency': false,
      'isActive': true,
      'country': 'الولايات المتحدة',
      'lastUpdated': '2024-01-15 10:25:00',
      'flag': '🇺🇸',
    },
    {
      'id': 'EUR',
      'name': 'اليورو',
      'code': 'EUR',
      'symbol': '€',
      'exchangeRate': 4.10,
      'isBaseCurrency': false,
      'isActive': true,
      'country': 'الاتحاد الأوروبي',
      'lastUpdated': '2024-01-15 10:20:00',
      'flag': '🇪🇺',
    },
    {
      'id': 'GBP',
      'name': 'الجنيه الإسترليني',
      'code': 'GBP',
      'symbol': '£',
      'exchangeRate': 4.75,
      'isBaseCurrency': false,
      'isActive': true,
      'country': 'المملكة المتحدة',
      'lastUpdated': '2024-01-15 10:15:00',
      'flag': '🇬🇧',
    },
    {
      'id': 'AED',
      'name': 'الدرهم الإماراتي',
      'code': 'AED',
      'symbol': 'د.إ',
      'exchangeRate': 1.02,
      'isBaseCurrency': false,
      'isActive': false,
      'country': 'الإمارات العربية المتحدة',
      'lastUpdated': '2024-01-14 15:30:00',
      'flag': '🇦🇪',
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('العملات'),
        backgroundColor: Colors.amber,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addCurrency,
            tooltip: 'إضافة عملة جديدة',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _updateExchangeRates,
            tooltip: 'تحديث أسعار الصرف',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والفلترة
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Column(
              children: [
                TextField(
                  decoration: const InputDecoration(
                    hintText: 'البحث في العملات...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedType,
                        decoration: const InputDecoration(
                          labelText: 'نوع العملة',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع العملات')),
                          DropdownMenuItem(value: 'base', child: Text('العملة الأساسية')),
                          DropdownMenuItem(value: 'foreign', child: Text('العملات الأجنبية')),
                          DropdownMenuItem(value: 'active', child: Text('النشطة فقط')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedType = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                _buildStatCard('المجموع', _currencies.length.toString(), Colors.blue),
                _buildStatCard('النشطة', _currencies.where((c) => c['isActive']).length.toString(), Colors.green),
                _buildStatCard('معطلة', _currencies.where((c) => !c['isActive']).length.toString(), Colors.red),
                _buildStatCard('الأساسية', _currencies.where((c) => c['isBaseCurrency']).length.toString(), Colors.amber),
              ],
            ),
          ),

          // قائمة العملات
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _currencies.length,
              itemBuilder: (context, index) {
                final currency = _currencies[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: currency['isActive'] ? Colors.amber : Colors.grey,
                      child: Text(
                        currency['flag'],
                        style: const TextStyle(fontSize: 20),
                      ),
                    ),
                    title: Row(
                      children: [
                        Text(
                          currency['name'],
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        if (currency['isBaseCurrency'])
                          Container(
                            margin: const EdgeInsets.only(right: 8),
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.amber,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Text(
                              'أساسية',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('الكود: ${currency['code']} | الرمز: ${currency['symbol']}'),
                        Text('البلد: ${currency['country']}'),
                        if (!currency['isBaseCurrency'])
                          Text('سعر الصرف: ${currency['exchangeRate']} ${currency['symbol']} = 1 ر.س'),
                        Text('آخر تحديث: ${currency['lastUpdated']}'),
                      ],
                    ),
                    trailing: PopupMenuButton<String>(
                      onSelected: (value) => _handleAction(value, currency),
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'edit',
                          child: ListTile(
                            leading: Icon(Icons.edit),
                            title: Text('تعديل'),
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'toggle',
                          child: ListTile(
                            leading: Icon(Icons.toggle_on),
                            title: Text('تغيير الحالة'),
                          ),
                        ),
                        if (!currency['isBaseCurrency'])
                          const PopupMenuItem(
                            value: 'update_rate',
                            child: ListTile(
                              leading: Icon(Icons.refresh),
                              title: Text('تحديث السعر'),
                            ),
                          ),
                        const PopupMenuItem(
                          value: 'history',
                          child: ListTile(
                            leading: Icon(Icons.history),
                            title: Text('تاريخ الأسعار'),
                          ),
                        ),
                        if (!currency['isBaseCurrency'])
                          const PopupMenuItem(
                            value: 'delete',
                            child: ListTile(
                              leading: Icon(Icons.delete, color: Colors.red),
                              title: Text('حذف', style: TextStyle(color: Colors.red)),
                            ),
                          ),
                      ],
                    ),
                    isThreeLine: true,
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addCurrency,
        backgroundColor: Colors.amber,
        icon: const Icon(Icons.add),
        label: const Text('إضافة عملة'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _addCurrency() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إضافة عملة جديدة')),
    );
  }

  void _updateExchangeRates() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث أسعار الصرف'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _handleAction(String action, Map<String, dynamic> currency) {
    switch (action) {
      case 'edit':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تعديل العملة ${currency['name']}')),
        );
        break;
      case 'toggle':
        setState(() {
          currency['isActive'] = !currency['isActive'];
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم ${currency['isActive'] ? 'تفعيل' : 'تعطيل'} العملة'),
          ),
        );
        break;
      case 'update_rate':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تحديث سعر صرف ${currency['name']}')),
        );
        break;
      case 'history':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تاريخ أسعار ${currency['name']}')),
        );
        break;
      case 'delete':
        _showDeleteConfirmation(currency);
        break;
    }
  }

  void _showDeleteConfirmation(Map<String, dynamic> currency) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف العملة ${currency['name']}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _currencies.removeWhere((c) => c['id'] == currency['id']);
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف العملة ${currency['name']}'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
