import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/theme/theme_provider.dart';

/// معلومات المظهر
class ThemeInfo {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final Color primaryColor;
  final bool isDark;

  const ThemeInfo({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.primaryColor,
    required this.isDark,
  });
}

/// صفحة تغيير المظهر المتقدمة لعام 2025
class ChangeTheme extends StatefulWidget {
  const ChangeTheme({super.key});

  @override
  State<ChangeTheme> createState() => _ChangeThemeState();
}

class _ChangeThemeState extends State<ChangeTheme>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _colorAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;

  int _selectedTabIndex = 0;
  bool _showAdvancedSettings = false;

  // قائمة المظاهر المتوفرة
  final List<ThemeInfo> _availableThemes = [
    ThemeInfo(
      id: 'system',
      name: 'تلقائي',
      description: 'يتبع إعدادات النظام',
      icon: Icons.brightness_auto,
      primaryColor: Colors.blue,
      isDark: false,
    ),
    ThemeInfo(
      id: 'light',
      name: 'فاتح',
      description: 'مظهر فاتح كلاسيكي',
      icon: Icons.light_mode,
      primaryColor: Colors.blue,
      isDark: false,
    ),
    ThemeInfo(
      id: 'dark',
      name: 'داكن',
      description: 'مظهر داكن مريح للعين',
      icon: Icons.dark_mode,
      primaryColor: Colors.blue,
      isDark: true,
    ),
  ];

  // قائمة الألوان المتوفرة
  final List<Color> _availableColors = [
    Colors.blue,
    Colors.purple,
    Colors.green,
    Colors.orange,
    Colors.red,
    Colors.teal,
    Colors.indigo,
    Colors.pink,
    Colors.amber,
    Colors.cyan,
    Colors.deepOrange,
    Colors.deepPurple,
  ];

  // قائمة الخطوط المتوفرة
  final List<String> _availableFonts = [
    'Roboto',
    'Cairo',
    'Poppins',
    'Montserrat',
    'Open Sans',
    'Lato',
    'Source Sans Pro',
    'Nunito',
  ];

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _colorAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _colorAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return AnimatedBuilder(
          animation: _fadeAnimation,
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: Scaffold(
                  body: CustomScrollView(
                    slivers: [
                      _buildSliverAppBar(context, themeProvider),
                      SliverToBoxAdapter(
                        child: _buildTabBar(context),
                      ),
                      SliverToBoxAdapter(
                        child: _buildTabContent(context, themeProvider),
                      ),
                    ],
                  ),
                  floatingActionButton:
                      _buildFloatingActionButton(context, themeProvider),
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// بناء شريط التطبيق المتقدم
  Widget _buildSliverAppBar(BuildContext context, ThemeProvider themeProvider) {
    return SliverAppBar(
      expandedHeight: 200,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: themeProvider.primaryColor,
      foregroundColor: Colors.white,
      flexibleSpace: FlexibleSpaceBar(
        title: const Text(
          'تخصيص المظهر',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                offset: Offset(0, 1),
                blurRadius: 3,
                color: Colors.black26,
              ),
            ],
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                themeProvider.primaryColor,
                themeProvider.primaryColor.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: Stack(
            children: [
              // تأثير الجسيمات المتحركة
              ...List.generate(20, (index) => _buildFloatingParticle(index)),

              // أيقونة المظهر الرئيسية
              Center(
                child: ScaleTransition(
                  scale: _scaleAnimation,
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.3),
                        width: 2,
                      ),
                    ),
                    child: const Icon(
                      Icons.palette,
                      size: 40,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: Icon(_showAdvancedSettings ? Icons.expand_less : Icons.tune),
          onPressed: () {
            setState(() {
              _showAdvancedSettings = !_showAdvancedSettings;
            });
          },
          tooltip: 'الإعدادات المتقدمة',
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) => _handleMenuAction(value, themeProvider),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'reset',
              child: Row(
                children: [
                  Icon(Icons.refresh),
                  SizedBox(width: 8),
                  Text('إعادة تعيين'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'export',
              child: Row(
                children: [
                  Icon(Icons.download),
                  SizedBox(width: 8),
                  Text('تصدير الإعدادات'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'import',
              child: Row(
                children: [
                  Icon(Icons.upload),
                  SizedBox(width: 8),
                  Text('استيراد الإعدادات'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء جسيمة متحركة
  Widget _buildFloatingParticle(int index) {
    final random = (index * 123) % 100;
    final size = 4.0 + (random % 8);
    final left = (random * 3.7) % 300;
    final top = (random * 2.3) % 150;

    return Positioned(
      left: left,
      top: top,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.translate(
            offset: Offset(
              0,
              -20 * _animationController.value * (1 + random % 3),
            ),
            child: Opacity(
              opacity: 0.3 + (0.4 * _animationController.value),
              child: Container(
                width: size,
                height: size,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.6),
                  shape: BoxShape.circle,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// بناء شريط التبويبات
  Widget _buildTabBar(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          _buildTabItem('المظاهر', Icons.brightness_6, 0),
          _buildTabItem('الألوان', Icons.color_lens, 1),
          _buildTabItem('الخطوط', Icons.font_download, 2),
          if (_showAdvancedSettings) _buildTabItem('متقدم', Icons.settings, 3),
        ],
      ),
    );
  }

  /// بناء عنصر تبويب
  Widget _buildTabItem(String title, IconData icon, int index) {
    final isSelected = _selectedTabIndex == index;

    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedTabIndex = index;
          });
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          decoration: BoxDecoration(
            color: isSelected
                ? Theme.of(context).primaryColor
                : Colors.transparent,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                color: isSelected ? Colors.white : Colors.grey[600],
                size: 20,
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: TextStyle(
                  color: isSelected ? Colors.white : Colors.grey[600],
                  fontSize: 12,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء محتوى التبويبات
  Widget _buildTabContent(BuildContext context, ThemeProvider themeProvider) {
    switch (_selectedTabIndex) {
      case 0:
        return _buildThemesTab(context, themeProvider);
      case 1:
        return _buildColorsTab(context, themeProvider);
      case 2:
        return _buildFontsTab(context, themeProvider);
      case 3:
        return _buildAdvancedTab(context, themeProvider);
      default:
        return _buildThemesTab(context, themeProvider);
    }
  }

  /// بناء تبويب المظاهر
  Widget _buildThemesTab(BuildContext context, ThemeProvider themeProvider) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'اختر المظهر المفضل',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // بطاقات المظاهر
          ...List.generate(_availableThemes.length, (index) {
            final theme = _availableThemes[index];
            final isSelected = themeProvider.selectedTheme == theme.id;

            return AnimatedContainer(
              duration: Duration(milliseconds: 300 + (index * 100)),
              margin: const EdgeInsets.only(bottom: 12),
              child: _buildThemeCard(
                context,
                theme: theme,
                isSelected: isSelected,
                onTap: () => _selectTheme(context, theme.id, themeProvider),
              ),
            );
          }),

          const SizedBox(height: 24),

          // معاينة المظهر
          _buildThemePreview(context, themeProvider),
        ],
      ),
    );
  }

  /// بناء بطاقة مظهر
  Widget _buildThemeCard(
    BuildContext context, {
    required ThemeInfo theme,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: isSelected ? 8 : 2,
      shadowColor:
          isSelected ? theme.primaryColor.withValues(alpha: 0.3) : null,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: isSelected ? theme.primaryColor : Colors.transparent,
          width: 2,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: isSelected
                ? LinearGradient(
                    colors: [
                      theme.primaryColor.withValues(alpha: 0.1),
                      Colors.white,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                : null,
          ),
          child: Row(
            children: [
              // أيقونة المظهر
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: theme.isDark ? Colors.grey[800] : Colors.grey[100],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: theme.isDark ? Colors.grey[600]! : Colors.grey[300]!,
                  ),
                ),
                child: Icon(
                  theme.icon,
                  size: 30,
                  color: isSelected ? theme.primaryColor : Colors.grey[600],
                ),
              ),
              const SizedBox(width: 16),

              // معلومات المظهر
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      theme.name,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: isSelected ? theme.primaryColor : null,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      theme.description,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),

              // مؤشر الاختيار
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                width: isSelected ? 40 : 30,
                height: isSelected ? 40 : 30,
                decoration: BoxDecoration(
                  color: isSelected ? theme.primaryColor : Colors.grey[300],
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  isSelected ? Icons.check : theme.icon,
                  color: Colors.white,
                  size: isSelected ? 20 : 16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// اختيار مظهر
  void _selectTheme(
      BuildContext context, String themeId, ThemeProvider themeProvider) async {
    await themeProvider.setThemeMode(themeId);

    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 8),
              Text(
                  'تم تغيير المظهر إلى ${_availableThemes.firstWhere((t) => t.id == themeId).name}'),
            ],
          ),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
    }
  }

  /// بناء معاينة المظهر
  Widget _buildThemePreview(BuildContext context, ThemeProvider themeProvider) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معاينة المظهر',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Container(
              height: 120,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Row(
                children: [
                  // جانب فاتح
                  Expanded(
                    child: Container(
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(12),
                          bottomLeft: Radius.circular(12),
                        ),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.light_mode,
                              color: themeProvider.primaryColor),
                          const SizedBox(height: 4),
                          const Text('فاتح', style: TextStyle(fontSize: 12)),
                        ],
                      ),
                    ),
                  ),
                  // جانب داكن
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.grey[900],
                        borderRadius: const BorderRadius.only(
                          topRight: Radius.circular(12),
                          bottomRight: Radius.circular(12),
                        ),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.dark_mode,
                              color: themeProvider.primaryColor),
                          const SizedBox(height: 4),
                          const Text('داكن',
                              style:
                                  TextStyle(fontSize: 12, color: Colors.white)),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء تبويب الألوان المتقدم
  Widget _buildColorsTab(BuildContext context, ThemeProvider themeProvider) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'اختر اللون الأساسي',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'سيتم تطبيق هذا اللون على جميع عناصر التطبيق',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),

          // عجلة الألوان المخصصة
          _buildColorWheel(context, themeProvider),

          const SizedBox(height: 24),

          // الألوان المحددة مسبقاً
          const Text(
            'الألوان المحددة مسبقاً',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // شبكة الألوان
          _buildColorGrid(context, themeProvider),

          const SizedBox(height: 24),

          // معاينة اللون
          _buildColorPreview(context, themeProvider),

          const SizedBox(height: 24),

          // ألوان متدرجة
          _buildGradientColors(context, themeProvider),
        ],
      ),
    );
  }

  /// بناء تبويب الخطوط المتقدم
  Widget _buildFontsTab(BuildContext context, ThemeProvider themeProvider) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'اختر الخط المفضل',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'سيتم تطبيق هذا الخط على جميع نصوص التطبيق',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),

          // تفعيل الخط المخصص
          _buildCustomFontToggle(context, themeProvider),

          const SizedBox(height: 24),

          // قائمة الخطوط
          if (themeProvider.useCustomFont) ...[
            const Text(
              'الخطوط المتوفرة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildFontsList(context, themeProvider),

            const SizedBox(height: 24),

            // معاينة الخط
            _buildFontPreview(context, themeProvider),
          ] else ...[
            _buildDefaultFontInfo(context),
          ],
        ],
      ),
    );
  }

  /// بناء تبويب الإعدادات المتقدمة
  Widget _buildAdvancedTab(BuildContext context, ThemeProvider themeProvider) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'الإعدادات المتقدمة',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'تخصيص متقدم لمظهر التطبيق',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),

          // Material Design 3
          _buildMaterial3Toggle(context, themeProvider),

          const SizedBox(height: 16),

          // التأثيرات البصرية
          _buildVisualEffectsSection(context, themeProvider),

          const SizedBox(height: 16),

          // إعدادات الحواف والأشكال
          _buildShapeSettings(context, themeProvider),

          const SizedBox(height: 16),

          // إعدادات الحركات
          _buildAnimationSettings(context, themeProvider),

          const SizedBox(height: 16),

          // إعدادات الأداء
          _buildPerformanceSettings(context, themeProvider),

          const SizedBox(height: 24),

          // أزرار الإجراءات
          _buildActionButtons(context, themeProvider),
        ],
      ),
    );
  }

  /// بناء زر الإجراء العائم
  Widget _buildFloatingActionButton(
      BuildContext context, ThemeProvider themeProvider) {
    return FloatingActionButton.extended(
      onPressed: () => _showThemeCustomizer(context, themeProvider),
      icon: const Icon(Icons.tune),
      label: const Text('تخصيص'),
      backgroundColor: themeProvider.primaryColor,
    );
  }

  /// إظهار مخصص المظهر المتقدم
  void _showThemeCustomizer(BuildContext context, ThemeProvider themeProvider) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Column(
          children: [
            // مقبض السحب
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // شريط العنوان
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(Icons.tune, color: themeProvider.primaryColor),
                  const SizedBox(width: 8),
                  const Text(
                    'مخصص المظهر السريع',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),

            // المحتوى
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                    // اختيار سريع للألوان
                    _buildQuickColorPicker(context, themeProvider),

                    const SizedBox(height: 16),

                    // اختيار سريع للمظهر
                    _buildQuickThemePicker(context, themeProvider),

                    const SizedBox(height: 16),

                    // إعدادات سريعة
                    _buildQuickSettings(context, themeProvider),

                    const SizedBox(height: 16),

                    // معاينة مباشرة
                    _buildLivePreview(context, themeProvider),

                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// معالجة إجراءات القائمة
  void _handleMenuAction(String action, ThemeProvider themeProvider) {
    switch (action) {
      case 'reset':
        _resetTheme(themeProvider);
        break;
      case 'export':
        _exportSettings();
        break;
      case 'import':
        _importSettings();
        break;
    }
  }

  /// إعادة تعيين المظهر
  void _resetTheme(ThemeProvider themeProvider) async {
    await themeProvider.resetToDefaults();
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إعادة تعيين جميع إعدادات المظهر'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  /// تصدير الإعدادات
  void _exportSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير الإعدادات - قريباً')),
    );
  }

  /// استيراد الإعدادات
  void _importSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('استيراد الإعدادات - قريباً')),
    );
  }

  /// بناء عجلة الألوان المخصصة
  Widget _buildColorWheel(BuildContext context, ThemeProvider themeProvider) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.palette, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'منتقي الألوان المتقدم',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: themeProvider.primaryColor,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.grey[300]!, width: 2),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // شريط تمرير الأحمر
            _buildColorSlider(
              'أحمر',
              themeProvider.primaryColor.r.toDouble(),
              (value) => _updateColorComponent(themeProvider, 'red', value),
              Colors.red,
            ),

            // شريط تمرير الأخضر
            _buildColorSlider(
              'أخضر',
              themeProvider.primaryColor.g.toDouble(),
              (value) => _updateColorComponent(themeProvider, 'green', value),
              Colors.green,
            ),

            // شريط تمرير الأزرق
            _buildColorSlider(
              'أزرق',
              themeProvider.primaryColor.b.toDouble(),
              (value) => _updateColorComponent(themeProvider, 'blue', value),
              Colors.blue,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء شريط تمرير اللون
  Widget _buildColorSlider(
    String label,
    double value,
    Function(double) onChanged,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          SizedBox(
            width: 50,
            child: Text(
              label,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: SliderTheme(
              data: SliderTheme.of(context).copyWith(
                activeTrackColor: color,
                thumbColor: color,
                overlayColor: color.withValues(alpha: 0.2),
              ),
              child: Slider(
                value: value,
                min: 0,
                max: 255,
                divisions: 255,
                onChanged: onChanged,
              ),
            ),
          ),
          SizedBox(
            width: 40,
            child: Text(
              value.round().toString(),
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  /// تحديث مكون اللون
  void _updateColorComponent(
      ThemeProvider themeProvider, String component, double value) {
    final currentColor = themeProvider.primaryColor;
    Color newColor;

    switch (component) {
      case 'red':
        newColor = Color.fromARGB(
            255, value.round(), currentColor.g.round(), currentColor.b.round());
        break;
      case 'green':
        newColor = Color.fromARGB(
            255, currentColor.r.round(), value.round(), currentColor.b.round());
        break;
      case 'blue':
        newColor = Color.fromARGB(
            255, currentColor.r.round(), currentColor.g.round(), value.round());
        break;
      default:
        return;
    }

    themeProvider.setPrimaryColor(newColor);
  }

  /// بناء شبكة الألوان
  Widget _buildColorGrid(BuildContext context, ThemeProvider themeProvider) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 6,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1,
      ),
      itemCount: _availableColors.length,
      itemBuilder: (context, index) {
        final color = _availableColors[index];
        final isSelected = themeProvider.primaryColor == color;

        return GestureDetector(
          onTap: () => _selectColor(themeProvider, color),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
              border: Border.all(
                color: isSelected ? Colors.white : Colors.grey[300]!,
                width: isSelected ? 4 : 2,
              ),
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: color.withValues(alpha: 0.4),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : null,
            ),
            child: isSelected
                ? const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 20,
                  )
                : null,
          ),
        );
      },
    );
  }

  /// اختيار لون
  void _selectColor(ThemeProvider themeProvider, Color color) async {
    await themeProvider.setPrimaryColor(color);

    // تأثير اهتزاز بصري
    _colorAnimationController.forward().then((_) {
      _colorAnimationController.reverse();
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              const Text('تم تغيير اللون الأساسي'),
            ],
          ),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
    }
  }

  /// بناء معاينة اللون
  Widget _buildColorPreview(BuildContext context, ThemeProvider themeProvider) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معاينة اللون',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // معاينة العناصر
            Row(
              children: [
                // زر مثال
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {},
                    style: ElevatedButton.styleFrom(
                      backgroundColor: themeProvider.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('زر مثال'),
                  ),
                ),
                const SizedBox(width: 8),

                // أيقونة ملونة
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: themeProvider.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.favorite,
                    color: themeProvider.primaryColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 8),

                // نص ملون
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    border: Border.all(color: themeProvider.primaryColor),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'نص مثال',
                    style: TextStyle(
                      color: themeProvider.primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // معلومات اللون
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  _buildColorInfo('كود اللون',
                      '#${themeProvider.primaryColor.toString().substring(10, 16).toUpperCase()}'),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                          child: _buildColorInfo('أحمر',
                              themeProvider.primaryColor.r.round().toString())),
                      const SizedBox(width: 8),
                      Expanded(
                          child: _buildColorInfo('أخضر',
                              themeProvider.primaryColor.g.round().toString())),
                      const SizedBox(width: 8),
                      Expanded(
                          child: _buildColorInfo('أزرق',
                              themeProvider.primaryColor.b.round().toString())),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء معلومات اللون
  Widget _buildColorInfo(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// بناء الألوان المتدرجة
  Widget _buildGradientColors(
      BuildContext context, ThemeProvider themeProvider) {
    final baseColor = themeProvider.primaryColor;
    final gradientColors = [
      baseColor.withValues(alpha: 0.3),
      baseColor.withValues(alpha: 0.5),
      baseColor.withValues(alpha: 0.7),
      baseColor,
      _darkenColor(baseColor, 0.2),
      _darkenColor(baseColor, 0.4),
    ];

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تدرجات اللون',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // شريط التدرج
            Container(
              height: 60,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                gradient: LinearGradient(
                  colors: gradientColors,
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
              ),
            ),

            const SizedBox(height: 16),

            // دوائر التدرج
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: gradientColors.map((color) {
                return GestureDetector(
                  onTap: () => themeProvider.setPrimaryColor(color),
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.grey[300]!, width: 2),
                      boxShadow: [
                        BoxShadow(
                          color: color.withValues(alpha: 0.3),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  /// تغميق اللون
  Color _darkenColor(Color color, double factor) {
    return Color.fromARGB(
      color.a.round(),
      (color.r * (1 - factor)).round(),
      (color.g * (1 - factor)).round(),
      (color.b * (1 - factor)).round(),
    );
  }

  /// بناء مفتاح تفعيل الخط المخصص
  Widget _buildCustomFontToggle(
      BuildContext context, ThemeProvider themeProvider) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              Icons.font_download,
              color: themeProvider.primaryColor,
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'استخدام خط مخصص',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'تفعيل الخطوط المخصصة للتطبيق',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Switch(
              value: themeProvider.useCustomFont,
              onChanged: (value) {
                themeProvider.setUseCustomFont(value);
                if (value) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم تفعيل الخطوط المخصصة'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              },
              activeColor: themeProvider.primaryColor,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة الخطوط
  Widget _buildFontsList(BuildContext context, ThemeProvider themeProvider) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _availableFonts.length,
      itemBuilder: (context, index) {
        final font = _availableFonts[index];
        final isSelected = themeProvider.fontFamily == font;

        return AnimatedContainer(
          duration: Duration(milliseconds: 300 + (index * 50)),
          margin: const EdgeInsets.only(bottom: 8),
          child: Card(
            elevation: isSelected ? 6 : 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
              side: BorderSide(
                color: isSelected
                    ? themeProvider.primaryColor
                    : Colors.transparent,
                width: 2,
              ),
            ),
            child: InkWell(
              onTap: () => _selectFont(themeProvider, font),
              borderRadius: BorderRadius.circular(12),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    // أيقونة الخط
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: isSelected
                            ? themeProvider.primaryColor.withValues(alpha: 0.1)
                            : Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.text_fields,
                        color: isSelected
                            ? themeProvider.primaryColor
                            : Colors.grey[600],
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),

                    // معلومات الخط
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            font,
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              fontFamily: font,
                              color: isSelected
                                  ? themeProvider.primaryColor
                                  : null,
                            ),
                          ),
                          Text(
                            'مثال على النص بخط $font',
                            style: TextStyle(
                              fontSize: 14,
                              fontFamily: font,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // مؤشر الاختيار
                    if (isSelected)
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: themeProvider.primaryColor,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// اختيار خط
  void _selectFont(ThemeProvider themeProvider, String font) async {
    await themeProvider.setFontFamily(font);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم تغيير الخط إلى $font',
            style: TextStyle(fontFamily: font),
          ),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
    }
  }

  /// بناء معاينة الخط
  Widget _buildFontPreview(BuildContext context, ThemeProvider themeProvider) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معاينة الخط: ${themeProvider.fontFamily}',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // نصوص تجريبية بأحجام مختلفة
            _buildFontSample(
                'عنوان كبير', 24, FontWeight.bold, themeProvider.fontFamily),
            const SizedBox(height: 8),
            _buildFontSample(
                'عنوان متوسط', 18, FontWeight.w600, themeProvider.fontFamily),
            const SizedBox(height: 8),
            _buildFontSample('نص عادي للقراءة والمحتوى الأساسي', 16,
                FontWeight.normal, themeProvider.fontFamily),
            const SizedBox(height: 8),
            _buildFontSample('نص صغير للتفاصيل والملاحظات', 14,
                FontWeight.normal, themeProvider.fontFamily),

            const SizedBox(height: 16),

            // نص تجريبي طويل
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'هذا نص تجريبي طويل لمعاينة شكل الخط في الفقرات الطويلة. يمكنك من خلال هذا النص تقييم مدى وضوح الخط وسهولة قراءته في المحتوى الطويل.',
                style: TextStyle(
                  fontSize: 16,
                  fontFamily: themeProvider.fontFamily,
                  height: 1.5,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عينة خط
  Widget _buildFontSample(
      String text, double fontSize, FontWeight fontWeight, String fontFamily) {
    return Text(
      text,
      style: TextStyle(
        fontSize: fontSize,
        fontWeight: fontWeight,
        fontFamily: fontFamily,
      ),
    );
  }

  /// بناء معلومات الخط الافتراضي
  Widget _buildDefaultFontInfo(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(
              Icons.font_download_off,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            const Text(
              'الخط الافتراضي',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'يتم استخدام خط النظام الافتراضي\nقم بتفعيل الخطوط المخصصة لاختيار خط مختلف',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء مفتاح Material 3
  Widget _buildMaterial3Toggle(
      BuildContext context, ThemeProvider themeProvider) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: themeProvider.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.auto_awesome,
                color: themeProvider.primaryColor,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Material Design 3',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'تفعيل أحدث تصميم من Google',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Switch(
              value: themeProvider.useMaterial3,
              onChanged: (value) {
                themeProvider.setUseMaterial3(value);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                        value ? 'تم تفعيل Material 3' : 'تم إلغاء Material 3'),
                    backgroundColor: value ? Colors.green : Colors.orange,
                  ),
                );
              },
              activeColor: themeProvider.primaryColor,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم التأثيرات البصرية
  Widget _buildVisualEffectsSection(
      BuildContext context, ThemeProvider themeProvider) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.auto_fix_high, color: themeProvider.primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'التأثيرات البصرية',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // التأثير الزجاجي
            _buildEffectToggle(
              'التأثير الزجاجي',
              'تأثير شفافية حديث',
              Icons.blur_on,
              themeProvider.useGlassEffect,
              (value) => themeProvider.setUseGlassEffect(value),
              themeProvider.primaryColor,
            ),

            const SizedBox(height: 12),

            // Neuomorphism
            _buildEffectToggle(
              'تصميم Neuomorphism',
              'تأثير ثلاثي الأبعاد ناعم',
              Icons.layers,
              themeProvider.useNeuomorphism,
              (value) => themeProvider.setUseNeuomorphism(value),
              themeProvider.primaryColor,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء مفتاح تأثير
  Widget _buildEffectToggle(
    String title,
    String subtitle,
    IconData icon,
    bool value,
    Function(bool) onChanged,
    Color primaryColor,
  ) {
    return Row(
      children: [
        Icon(icon, color: value ? primaryColor : Colors.grey[400], size: 20),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style:
                    const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              ),
              Text(
                subtitle,
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ],
          ),
        ),
        Switch(
          value: value,
          onChanged: onChanged,
          activeColor: primaryColor,
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
      ],
    );
  }

  /// بناء إعدادات الأشكال
  Widget _buildShapeSettings(
      BuildContext context, ThemeProvider themeProvider) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.rounded_corner, color: themeProvider.primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'الحواف والأشكال',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // شريط تمرير نصف قطر الحواف
            Row(
              children: [
                const Text('نصف قطر الحواف:', style: TextStyle(fontSize: 14)),
                const Spacer(),
                Text('${themeProvider.borderRadius.round()}px',
                    style: TextStyle(
                        fontSize: 14,
                        color: themeProvider.primaryColor,
                        fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 8),
            SliderTheme(
              data: SliderTheme.of(context).copyWith(
                activeTrackColor: themeProvider.primaryColor,
                thumbColor: themeProvider.primaryColor,
                overlayColor: themeProvider.primaryColor.withValues(alpha: 0.2),
              ),
              child: Slider(
                value: themeProvider.borderRadius,
                min: 0,
                max: 30,
                divisions: 30,
                onChanged: (value) => themeProvider.setBorderRadius(value),
              ),
            ),

            const SizedBox(height: 16),

            // معاينة الأشكال
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildShapePreview(
                    'مربع', themeProvider.borderRadius, Colors.blue),
                _buildShapePreview('دائرة', 25, Colors.green),
                _buildShapePreview(
                    'مستطيل', themeProvider.borderRadius, Colors.orange),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء معاينة الشكل
  Widget _buildShapePreview(String label, double radius, Color color) {
    return Column(
      children: [
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(radius),
            border: Border.all(color: color, width: 2),
          ),
        ),
        const SizedBox(height: 4),
        Text(label, style: const TextStyle(fontSize: 12)),
      ],
    );
  }

  /// بناء إعدادات الحركات
  Widget _buildAnimationSettings(
      BuildContext context, ThemeProvider themeProvider) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.animation, color: themeProvider.primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'إعدادات الحركات',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // تفعيل الحركات
            _buildEffectToggle(
              'تفعيل الحركات',
              'تأثيرات حركية للواجهة',
              Icons.play_arrow,
              themeProvider.useAnimations,
              (value) => themeProvider.setUseAnimations(value),
              themeProvider.primaryColor,
            ),

            if (themeProvider.useAnimations) ...[
              const SizedBox(height: 16),

              // سرعة الحركات
              Row(
                children: [
                  const Text('سرعة الحركات:', style: TextStyle(fontSize: 14)),
                  const Spacer(),
                  Text('${(themeProvider.animationSpeed * 100).round()}%',
                      style: TextStyle(
                          fontSize: 14,
                          color: themeProvider.primaryColor,
                          fontWeight: FontWeight.bold)),
                ],
              ),
              const SizedBox(height: 8),
              SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: themeProvider.primaryColor,
                  thumbColor: themeProvider.primaryColor,
                  overlayColor:
                      themeProvider.primaryColor.withValues(alpha: 0.2),
                ),
                child: Slider(
                  value: themeProvider.animationSpeed,
                  min: 0.5,
                  max: 2.0,
                  divisions: 15,
                  onChanged: (value) => themeProvider.setAnimationSpeed(value),
                ),
              ),

              const SizedBox(height: 16),

              // معاينة الحركة
              Center(
                child: GestureDetector(
                  onTap: () => _playAnimationDemo(),
                  child: AnimatedContainer(
                    duration: Duration(
                        milliseconds:
                            (500 / themeProvider.animationSpeed).round()),
                    curve: Curves.elasticOut,
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: themeProvider.primaryColor,
                      borderRadius:
                          BorderRadius.circular(themeProvider.borderRadius),
                      boxShadow: [
                        BoxShadow(
                          color:
                              themeProvider.primaryColor.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: const Icon(Icons.play_arrow,
                        color: Colors.white, size: 30),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              const Center(
                child:
                    Text('اضغط لتجربة الحركة', style: TextStyle(fontSize: 12)),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء إعدادات الأداء
  Widget _buildPerformanceSettings(
      BuildContext context, ThemeProvider themeProvider) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.speed, color: themeProvider.primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'إعدادات الأداء',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // معلومات الأداء
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  _buildPerformanceInfo(
                      'استهلاك الذاكرة', 'منخفض', Colors.green),
                  const SizedBox(height: 8),
                  _buildPerformanceInfo('سرعة الرسم', 'عالية', Colors.blue),
                  const SizedBox(height: 8),
                  _buildPerformanceInfo(
                      'استهلاك البطارية', 'محسن', Colors.orange),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // نصائح الأداء
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: themeProvider.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                    color: themeProvider.primaryColor.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.lightbulb,
                      color: themeProvider.primaryColor, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'نصيحة: إلغاء التأثيرات البصرية يحسن الأداء على الأجهزة القديمة',
                      style: TextStyle(
                        fontSize: 12,
                        color: themeProvider.primaryColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء معلومات الأداء
  Widget _buildPerformanceInfo(String label, String value, Color color) {
    return Row(
      children: [
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Text(label, style: const TextStyle(fontSize: 14)),
        const Spacer(),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons(
      BuildContext context, ThemeProvider themeProvider) {
    return Column(
      children: [
        // زر إعادة التعيين
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () => _showResetDialog(context, themeProvider),
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة تعيين جميع الإعدادات'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(themeProvider.borderRadius),
              ),
            ),
          ),
        ),

        const SizedBox(height: 12),

        // أزرار التصدير والاستيراد
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _exportThemeSettings(context, themeProvider),
                icon: const Icon(Icons.download),
                label: const Text('تصدير'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: themeProvider.primaryColor,
                  side: BorderSide(color: themeProvider.primaryColor),
                  shape: RoundedRectangleBorder(
                    borderRadius:
                        BorderRadius.circular(themeProvider.borderRadius),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _importThemeSettings(context, themeProvider),
                icon: const Icon(Icons.upload),
                label: const Text('استيراد'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: themeProvider.primaryColor,
                  side: BorderSide(color: themeProvider.primaryColor),
                  shape: RoundedRectangleBorder(
                    borderRadius:
                        BorderRadius.circular(themeProvider.borderRadius),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// تشغيل عرض توضيحي للحركة
  void _playAnimationDemo() {
    _colorAnimationController.forward().then((_) {
      _colorAnimationController.reverse();
    });
  }

  /// إظهار حوار إعادة التعيين
  void _showResetDialog(BuildContext context, ThemeProvider themeProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين الإعدادات'),
        content: const Text('هل أنت متأكد من إعادة تعيين جميع إعدادات المظهر؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _resetTheme(themeProvider);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('إعادة تعيين'),
          ),
        ],
      ),
    );
  }

  /// تصدير إعدادات المظهر
  void _exportThemeSettings(BuildContext context, ThemeProvider themeProvider) {
    // هنا يمكن إضافة منطق التصدير الفعلي
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Row(
          children: [
            Icon(Icons.download, color: Colors.white),
            SizedBox(width: 8),
            Text('تم تصدير إعدادات المظهر بنجاح'),
          ],
        ),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// استيراد إعدادات المظهر
  void _importThemeSettings(BuildContext context, ThemeProvider themeProvider) {
    // هنا يمكن إضافة منطق الاستيراد الفعلي
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Row(
          children: [
            Icon(Icons.upload, color: Colors.white),
            SizedBox(width: 8),
            Text('تم استيراد إعدادات المظهر بنجاح'),
          ],
        ),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// بناء منتقي الألوان السريع
  Widget _buildQuickColorPicker(
      BuildContext context, ThemeProvider themeProvider) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الألوان السريعة',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _availableColors.take(8).map((color) {
                final isSelected = themeProvider.primaryColor == color;
                return GestureDetector(
                  onTap: () => themeProvider.setPrimaryColor(color),
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    width: isSelected ? 50 : 40,
                    height: isSelected ? 50 : 40,
                    decoration: BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: isSelected ? Colors.white : Colors.grey[300]!,
                        width: isSelected ? 3 : 1,
                      ),
                      boxShadow: isSelected
                          ? [
                              BoxShadow(
                                color: color.withValues(alpha: 0.4),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ]
                          : null,
                    ),
                    child: isSelected
                        ? const Icon(Icons.check, color: Colors.white, size: 20)
                        : null,
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء منتقي المظهر السريع
  Widget _buildQuickThemePicker(
      BuildContext context, ThemeProvider themeProvider) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'وضع المظهر',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: _availableThemes.map((theme) {
                final isSelected = themeProvider.selectedTheme == theme.id;
                return Expanded(
                  child: GestureDetector(
                    onTap: () => themeProvider.setThemeMode(theme.id),
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? themeProvider.primaryColor.withValues(alpha: 0.1)
                            : Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isSelected
                              ? themeProvider.primaryColor
                              : Colors.transparent,
                          width: 2,
                        ),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            theme.icon,
                            color: isSelected
                                ? themeProvider.primaryColor
                                : Colors.grey[600],
                            size: 24,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            theme.name,
                            style: TextStyle(
                              fontSize: 12,
                              color: isSelected
                                  ? themeProvider.primaryColor
                                  : Colors.grey[600],
                              fontWeight: isSelected
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء الإعدادات السريعة
  Widget _buildQuickSettings(
      BuildContext context, ThemeProvider themeProvider) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إعدادات سريعة',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),

            // Material 3
            _buildQuickToggle(
              'Material 3',
              Icons.auto_awesome,
              themeProvider.useMaterial3,
              (value) => themeProvider.setUseMaterial3(value),
              themeProvider.primaryColor,
            ),

            const SizedBox(height: 8),

            // الخطوط المخصصة
            _buildQuickToggle(
              'خطوط مخصصة',
              Icons.font_download,
              themeProvider.useCustomFont,
              (value) => themeProvider.setUseCustomFont(value),
              themeProvider.primaryColor,
            ),

            const SizedBox(height: 8),

            // الحركات
            _buildQuickToggle(
              'الحركات',
              Icons.animation,
              themeProvider.useAnimations,
              (value) => themeProvider.setUseAnimations(value),
              themeProvider.primaryColor,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء مفتاح سريع
  Widget _buildQuickToggle(
    String title,
    IconData icon,
    bool value,
    Function(bool) onChanged,
    Color primaryColor,
  ) {
    return Row(
      children: [
        Icon(icon, color: value ? primaryColor : Colors.grey[400], size: 20),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            title,
            style: const TextStyle(fontSize: 14),
          ),
        ),
        Switch(
          value: value,
          onChanged: onChanged,
          activeColor: primaryColor,
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
      ],
    );
  }

  /// بناء المعاينة المباشرة
  Widget _buildLivePreview(BuildContext context, ThemeProvider themeProvider) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معاينة مباشرة',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),

            // عناصر المعاينة
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  // زر مثال
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {},
                      style: ElevatedButton.styleFrom(
                        backgroundColor: themeProvider.primaryColor,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(themeProvider.borderRadius),
                        ),
                      ),
                      child: const Text('زر مثال'),
                    ),
                  ),

                  const SizedBox(height: 12),

                  // بطاقة مثال
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius:
                          BorderRadius.circular(themeProvider.borderRadius),
                      border: Border.all(
                          color: themeProvider.primaryColor
                              .withValues(alpha: 0.3)),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: themeProvider.primaryColor
                                .withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(
                                themeProvider.borderRadius),
                          ),
                          child: Icon(
                            Icons.star,
                            color: themeProvider.primaryColor,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'عنوان البطاقة',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  fontFamily: themeProvider.useCustomFont
                                      ? themeProvider.fontFamily
                                      : null,
                                ),
                              ),
                              Text(
                                'وصف البطاقة',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                  fontFamily: themeProvider.useCustomFont
                                      ? themeProvider.fontFamily
                                      : null,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
