class CardsLocalizations {
  static const Map<String, Map<String, String>> values = {
    'en': {
      // صفحات وحدة الكروت
      'cards': 'Cards',
      'financial_guide': 'Financial Guide',
      'cost_center': 'Cost Center',
      'customers': 'Customers',
      'suppliers': 'Suppliers',
      'items': 'Items',
      'stores': 'Stores',
      'categories': 'Categories',
      'currencies': 'Currencies',
      'warehouse_linking': 'Warehouse Linking',
      'facilities': 'Facilities',
      'expense_types': 'Expense Types',
      'expense_data': 'Expense Data',
      'sales_representatives': 'Sales Representatives',
      'miscellaneous_accounts': 'Miscellaneous Accounts',
      'branches': 'Branches',
      'warehouses': 'Warehouses',
      'automatic_pricing': 'Automatic Pricing',
      'alternatives': 'Alternatives',
      'inventory_count': 'Inventory Count',
      'inventory_assignment': 'Inventory Assignment',
      'units': 'Units',
    },
    'ar': {
      // صفحات وحدة الكروت
      'cards': 'الكروت',
      'financial_guide': 'الدليل المالي',
      'cost_center': 'مركز التكلفة',
      'customers': 'العملاء',
      'suppliers': 'الموردين',
      'items': 'الأصناف',
      'stores': 'المخازن',
      'sales_representatives': 'المندوبين',
      'miscellaneous_accounts': 'حسابات متنوعة',
      'branches': 'الفروع',
      'warehouses': 'المستودعات',
      'automatic_pricing': 'التسعير الآلي',
      'alternatives': 'البدائل',
      'inventory_count': 'جرد المخزون',
      'inventory_assignment': 'تكليف الجرد',
      'units': 'الوحدة',
    },
  };
}
