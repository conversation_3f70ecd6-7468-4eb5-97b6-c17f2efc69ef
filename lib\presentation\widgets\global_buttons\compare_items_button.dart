import 'package:flutter/material.dart';

class CompareItemsButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String? tooltip;
  final bool isLoading;
  final bool isDisabled;

  const CompareItemsButton({
    super.key,
    this.onPressed,
    this.tooltip = 'Compare Items',
    this.isLoading = false,
    this.isDisabled = false,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip ?? 'Compare Items',
      child: ElevatedButton.icon(
        onPressed: isDisabled || isLoading ? null : onPressed,
        icon: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              )
            : const Icon(Icons.compare_arrows),
        label: const Text('Compare Items'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.indigo.shade800,
          foregroundColor: Colors.white,
        ),
      ),
    );
  }
}
