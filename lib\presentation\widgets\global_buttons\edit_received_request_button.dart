import 'package:flutter/material.dart';

class EditReceivedRequestButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String? tooltip;
  final bool isLoading;
  final bool isDisabled;

  const EditReceivedRequestButton({
    super.key,
    this.onPressed,
    this.tooltip = 'Edit Received Request',
    this.isLoading = false,
    this.isDisabled = false,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip ?? 'Edit Received Request',
      child: ElevatedButton.icon(
        onPressed: isDisabled || isLoading ? null : onPressed,
        icon: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              )
            : const Icon(Icons.edit_note),
        label: const Text('Edit Received Request'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.deepOrange.shade700,
          foregroundColor: Colors.white,
        ),
      ),
    );
  }
}
