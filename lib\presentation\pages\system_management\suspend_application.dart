import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تعليق التطبيق
/// تتيح تعليق التطبيق مؤقتاً لأغراض الصيانة أو الطوارئ
class SuspendApplicationPage extends StatefulWidget {
  const SuspendApplicationPage({super.key});

  @override
  State<SuspendApplicationPage> createState() => _SuspendApplicationPageState();
}

class _SuspendApplicationPageState extends State<SuspendApplicationPage> {
  final _formKey = GlobalKey<FormState>();
  final _reasonController = TextEditingController();
  final _messageController = TextEditingController();
  final _durationController = TextEditingController();

  String? _selectedReason;
  String? _selectedDuration;
  DateTime? _scheduledTime;
  bool _isImmediate = true;
  bool _notifyUsers = true;
  bool _allowAdminAccess = true;
  bool _isProcessing = false;

  final List<Map<String, String>> _suspensionReasons = [
    {
      'id': 'maintenance',
      'name': 'صيانة دورية',
      'description': 'صيانة مجدولة للنظام'
    },
    {
      'id': 'emergency',
      'name': 'طوارئ',
      'description': 'حالة طوارئ تتطلب إيقاف فوري'
    },
    {
      'id': 'update',
      'name': 'تحديث النظام',
      'description': 'تحديث البرنامج أو قاعدة البيانات'
    },
    {
      'id': 'security',
      'name': 'أمني',
      'description': 'مشكلة أمنية تتطلب إيقاف النظام'
    },
    {
      'id': 'backup',
      'name': 'نسخ احتياطي',
      'description': 'إنشاء نسخة احتياطية شاملة'
    },
    {'id': 'other', 'name': 'أخرى', 'description': 'سبب آخر'},
  ];

  final List<Map<String, String>> _durations = [
    {'id': '15min', 'name': '15 دقيقة', 'minutes': '15'},
    {'id': '30min', 'name': '30 دقيقة', 'minutes': '30'},
    {'id': '1hour', 'name': 'ساعة واحدة', 'minutes': '60'},
    {'id': '2hours', 'name': 'ساعتان', 'minutes': '120'},
    {'id': '4hours', 'name': '4 ساعات', 'minutes': '240'},
    {'id': 'custom', 'name': 'مدة مخصصة', 'minutes': '0'},
  ];

  final List<Map<String, dynamic>> _suspensionHistory = [
    {
      'id': 'susp1',
      'date': '2024/01/20',
      'time': '02:00',
      'reason': 'صيانة دورية',
      'duration': '2 ساعة',
      'user': 'مدير النظام',
      'status': 'مكتمل',
      'affectedUsers': 25,
    },
    {
      'id': 'susp2',
      'date': '2024/01/15',
      'time': '14:30',
      'reason': 'تحديث النظام',
      'duration': '45 دقيقة',
      'user': 'مدير النظام',
      'status': 'مكتمل',
      'affectedUsers': 18,
    },
    {
      'id': 'susp3',
      'date': '2024/01/10',
      'time': '23:00',
      'reason': 'نسخ احتياطي',
      'duration': '3 ساعات',
      'user': 'مدير النظام',
      'status': 'مكتمل',
      'affectedUsers': 5,
    },
  ];

  @override
  void dispose() {
    _reasonController.dispose();
    _messageController.dispose();
    _durationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.suspendApplication),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: _showSuspensionHistory,
            tooltip: 'سجل التعليق',
          ),
          IconButton(
            icon: const Icon(Icons.warning),
            onPressed: _showWarnings,
            tooltip: 'تحذيرات مهمة',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // تحذير مهم
            Card(
              color: Colors.red.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    const Icon(Icons.warning, color: Colors.red, size: 32),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تحذير مهم',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.red,
                            ),
                          ),
                          const SizedBox(height: 4),
                          const Text(
                            'تعليق التطبيق سيمنع جميع المستخدمين من الوصول إلى النظام. تأكد من إشعار المستخدمين مسبقاً.',
                            style: TextStyle(fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة إعدادات التعليق
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'إعدادات التعليق',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // سبب التعليق
                    DropdownButtonFormField<String>(
                      value: _selectedReason,
                      decoration: const InputDecoration(
                        labelText: 'سبب التعليق',
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                      ),
                      items: _suspensionReasons
                          .map<DropdownMenuItem<String>>((reason) {
                        return DropdownMenuItem<String>(
                          value: reason['id'],
                          child: Text(reason['name']!),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedReason = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار سبب التعليق';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // تفاصيل السبب
                    if (_selectedReason == 'other')
                      TextFormField(
                        controller: _reasonController,
                        decoration: const InputDecoration(
                          labelText: 'تفاصيل السبب',
                          border: OutlineInputBorder(),
                          contentPadding:
                              EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                        ),
                        maxLines: 2,
                        validator: (value) {
                          if (_selectedReason == 'other' &&
                              (value == null || value.isEmpty)) {
                            return 'يرجى إدخال تفاصيل السبب';
                          }
                          return null;
                        },
                      ),

                    const SizedBox(height: 16),

                    // مدة التعليق
                    DropdownButtonFormField<String>(
                      value: _selectedDuration,
                      decoration: const InputDecoration(
                        labelText: 'مدة التعليق',
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                      ),
                      items:
                          _durations.map<DropdownMenuItem<String>>((duration) {
                        return DropdownMenuItem<String>(
                          value: duration['id'],
                          child: Text(duration['name']!),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedDuration = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار مدة التعليق';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // مدة مخصصة
                    if (_selectedDuration == 'custom')
                      TextFormField(
                        controller: _durationController,
                        decoration: const InputDecoration(
                          labelText: 'المدة بالدقائق',
                          border: OutlineInputBorder(),
                          contentPadding:
                              EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (_selectedDuration == 'custom' &&
                              (value == null || value.isEmpty)) {
                            return 'يرجى إدخال المدة';
                          }
                          if (_selectedDuration == 'custom' &&
                              (int.tryParse(value!) == null ||
                                  int.parse(value) <= 0)) {
                            return 'يرجى إدخال رقم صحيح';
                          }
                          return null;
                        },
                      ),

                    const SizedBox(height: 16),

                    // رسالة للمستخدمين
                    TextFormField(
                      controller: _messageController,
                      decoration: const InputDecoration(
                        labelText: 'رسالة للمستخدمين',
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                        hintText:
                            'سيتم عرض هذه الرسالة للمستخدمين أثناء التعليق',
                      ),
                      maxLines: 3,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة خيارات التوقيت
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'توقيت التعليق',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                    const SizedBox(height: 16),
                    RadioListTile<bool>(
                      title: const Text('فوري'),
                      subtitle: const Text('تعليق النظام فوراً'),
                      value: true,
                      groupValue: _isImmediate,
                      onChanged: (value) {
                        setState(() {
                          _isImmediate = value!;
                        });
                      },
                    ),
                    RadioListTile<bool>(
                      title: const Text('مجدول'),
                      subtitle: const Text('تعليق النظام في وقت محدد'),
                      value: false,
                      groupValue: _isImmediate,
                      onChanged: (value) {
                        setState(() {
                          _isImmediate = value!;
                        });
                      },
                    ),
                    if (!_isImmediate) ...[
                      const SizedBox(height: 16),
                      InkWell(
                        onTap: _selectScheduledTime,
                        child: InputDecorator(
                          decoration: const InputDecoration(
                            labelText: 'وقت التعليق المجدول',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 8),
                          ),
                          child: Text(
                            _scheduledTime != null
                                ? '${_scheduledTime!.day}/${_scheduledTime!.month}/${_scheduledTime!.year} ${_scheduledTime!.hour}:${_scheduledTime!.minute.toString().padLeft(2, '0')}'
                                : 'اختر التوقيت',
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة خيارات إضافية
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'خيارات إضافية',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SwitchListTile(
                      title: const Text('إشعار المستخدمين'),
                      subtitle:
                          const Text('إرسال إشعار للمستخدمين قبل التعليق'),
                      value: _notifyUsers,
                      onChanged: (value) {
                        setState(() {
                          _notifyUsers = value;
                        });
                      },
                    ),
                    SwitchListTile(
                      title: const Text('السماح بوصول المدير'),
                      subtitle:
                          const Text('السماح للمدير بالوصول أثناء التعليق'),
                      value: _allowAdminAccess,
                      onChanged: (value) {
                        setState(() {
                          _allowAdminAccess = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // أزرار العمليات
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isProcessing ? null : _suspendApplication,
                    icon: _isProcessing
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.pause_circle),
                    label: Text(
                        _isProcessing ? 'جاري التعليق...' : 'تعليق التطبيق'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _resetForm,
                    icon: const Icon(Icons.refresh),
                    label: const Text('إعادة تعيين'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // سجل التعليق السابق
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'سجل التعليق السابق',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _suspensionHistory.length,
                      itemBuilder: (context, index) {
                        final suspension = _suspensionHistory[index];
                        return ListTile(
                          leading: CircleAvatar(
                            backgroundColor: Colors.red,
                            child: const Icon(Icons.pause, color: Colors.white),
                          ),
                          title: Text(
                              '${suspension['reason']} - ${suspension['duration']}'),
                          subtitle: Text(
                              '${suspension['date']} ${suspension['time']} | ${suspension['affectedUsers']} مستخدم متأثر'),
                          trailing: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.green.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              suspension['status'],
                              style: const TextStyle(
                                color: Colors.green,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectScheduledTime() async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(hours: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    );

    if (pickedDate != null) {
      final TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.now(),
      );

      if (pickedTime != null) {
        setState(() {
          _scheduledTime = DateTime(
            pickedDate.year,
            pickedDate.month,
            pickedDate.day,
            pickedTime.hour,
            pickedTime.minute,
          );
        });
      }
    }
  }

  Future<void> _suspendApplication() async {
    if (_formKey.currentState!.validate()) {
      if (!_isImmediate && _scheduledTime == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يرجى اختيار وقت التعليق المجدول'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // تأكيد التعليق
      final bool? confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('تأكيد تعليق التطبيق'),
          content: const Text(
            'هل أنت متأكد من تعليق التطبيق؟ سيتم منع جميع المستخدمين من الوصول إلى النظام.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context, true),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('تأكيد التعليق',
                  style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        setState(() {
          _isProcessing = true;
        });

        // محاكاة عملية التعليق
        await Future.delayed(const Duration(seconds: 3));

        setState(() {
          _isProcessing = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(_isImmediate
                  ? 'تم تعليق التطبيق فوراً'
                  : 'تم جدولة تعليق التطبيق'),
              backgroundColor: Colors.red,
            ),
          );
          _resetForm();
        }
      }
    }
  }

  void _resetForm() {
    setState(() {
      _selectedReason = null;
      _selectedDuration = null;
      _scheduledTime = null;
      _isImmediate = true;
      _notifyUsers = true;
      _allowAdminAccess = true;
      _reasonController.clear();
      _messageController.clear();
      _durationController.clear();
    });
  }

  void _showSuspensionHistory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض سجل تعليق التطبيق الكامل')),
    );
  }

  void _showWarnings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض التحذيرات والاحتياطات المهمة')),
    );
  }
}
