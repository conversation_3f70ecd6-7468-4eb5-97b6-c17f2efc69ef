import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير صافي المبيعات خلال فترة
/// يعرض صافي المبيعات بعد خصم المرتجعات والخصومات
class NetSalesPeriodReportPage extends StatefulWidget {
  const NetSalesPeriodReportPage({super.key});

  @override
  State<NetSalesPeriodReportPage> createState() => _NetSalesPeriodReportPageState();
}

class _NetSalesPeriodReportPageState extends State<NetSalesPeriodReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedBranch;
  String? _reportType = 'daily';
  String? _selectedCategory;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('صافي المبيعات خلال فترة'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.trending_up),
            onPressed: _showTrendAnalysis,
            tooltip: 'تحليل الاتجاهات',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilterSection(localizations),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildSalesBreakdownSection(),
                  const SizedBox(height: 16),
                  _buildPeriodComparisonSection(),
                  const SizedBox(height: 16),
                  _buildBranchPerformanceSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.green[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.fromDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, true),
                  controller: TextEditingController(
                    text: _startDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.toDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, false),
                  controller: TextEditingController(
                    text: _endDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'الفرع',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedBranch,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الفروع')),
                    DropdownMenuItem(value: 'main', child: Text('الفرع الرئيسي')),
                    DropdownMenuItem(value: 'branch1', child: Text('الفرع الأول')),
                    DropdownMenuItem(value: 'branch2', child: Text('الفرع الثاني')),
                    DropdownMenuItem(value: 'branch3', child: Text('الفرع الثالث')),
                  ],
                  onChanged: (value) => setState(() => _selectedBranch = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'نوع التقرير',
                    border: OutlineInputBorder(),
                  ),
                  value: _reportType,
                  items: const [
                    DropdownMenuItem(value: 'daily', child: Text('يومي')),
                    DropdownMenuItem(value: 'weekly', child: Text('أسبوعي')),
                    DropdownMenuItem(value: 'monthly', child: Text('شهري')),
                    DropdownMenuItem(value: 'quarterly', child: Text('ربع سنوي')),
                  ],
                  onChanged: (value) => setState(() => _reportType = value),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.monetization_on, color: Colors.green, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص صافي المبيعات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي المبيعات', '2,850,000 ر.س', Colors.green, Icons.trending_up),
                _buildSummaryCard('المرتجعات', '185,000 ر.س', Colors.red, Icons.keyboard_return),
                _buildSummaryCard('الخصومات', '125,000 ر.س', Colors.orange, Icons.discount),
                _buildSummaryCard('صافي المبيعات', '2,540,000 ر.س', Colors.blue, Icons.account_balance),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalesBreakdownSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.pie_chart, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'تفصيل المبيعات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('البيان')),
                  DataColumn(label: Text('المبلغ')),
                  DataColumn(label: Text('النسبة')),
                  DataColumn(label: Text('مقارنة بالشهر السابق')),
                ],
                rows: [
                  DataRow(cells: [
                    const DataCell(Text('إجمالي المبيعات')),
                    const DataCell(Text('2,850,000 ر.س')),
                    const DataCell(Text('100%')),
                    DataCell(_buildComparisonBadge('+12%', Colors.green)),
                  ]),
                  DataRow(cells: [
                    const DataCell(Text('المرتجعات')),
                    const DataCell(Text('185,000 ر.س')),
                    const DataCell(Text('6.5%')),
                    DataCell(_buildComparisonBadge('-2%', Colors.green)),
                  ]),
                  DataRow(cells: [
                    const DataCell(Text('الخصومات')),
                    const DataCell(Text('125,000 ر.س')),
                    const DataCell(Text('4.4%')),
                    DataCell(_buildComparisonBadge('+1%', Colors.red)),
                  ]),
                  DataRow(cells: [
                    const DataCell(Text('صافي المبيعات')),
                    const DataCell(Text('2,540,000 ر.س')),
                    const DataCell(Text('89.1%')),
                    DataCell(_buildComparisonBadge('+15%', Colors.green)),
                  ]),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPeriodComparisonSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.compare, color: Colors.teal, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'مقارنة الفترات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildComparisonCard('هذا الشهر', '2,540,000 ر.س', '+15%', Colors.green),
                _buildComparisonCard('الشهر السابق', '2,210,000 ر.س', '+8%', Colors.blue),
                _buildComparisonCard('نفس الشهر العام الماضي', '2,150,000 ر.س', '+18%', Colors.orange),
                _buildComparisonCard('متوسط 3 أشهر', '2,300,000 ر.س', '+10%', Colors.purple),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBranchPerformanceSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.store, color: Colors.amber, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'أداء الفروع',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildBranchPerformanceList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _generateForecast,
                    icon: const Icon(Icons.trending_up),
                    label: const Text('توقع المبيعات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _analyzeReturns,
                    icon: const Icon(Icons.keyboard_return),
                    label: const Text('تحليل المرتجعات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _optimizeDiscounts,
                    icon: const Icon(Icons.discount),
                    label: const Text('تحسين الخصومات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _setBudgetTargets,
                    icon: const Icon(Icons.flag),
                    label: const Text('تحديد أهداف الميزانية'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildBranchPerformanceList() {
    final branches = [
      {'name': 'الفرع الرئيسي', 'sales': '1,285,000 ر.س', 'percentage': '50.6%', 'growth': '+18%'},
      {'name': 'الفرع الأول', 'sales': '685,000 ر.س', 'percentage': '27.0%', 'growth': '+12%'},
      {'name': 'الفرع الثاني', 'sales': '385,000 ر.س', 'percentage': '15.2%', 'growth': '+8%'},
      {'name': 'الفرع الثالث', 'sales': '185,000 ر.س', 'percentage': '7.3%', 'growth': '+25%'},
    ];

    return branches.map((branch) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.amber.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.amber.withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          const Icon(Icons.store, color: Colors.amber, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  branch['name']!,
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  'مبيعات: ${branch['sales']} • نسبة: ${branch['percentage']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              branch['growth']!,
              style: const TextStyle(
                color: Colors.green,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildComparisonCard(String period, String amount, String change, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                amount,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(period, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
              const SizedBox(height: 4),
              Text(
                change,
                style: TextStyle(fontSize: 10, color: color, fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildComparisonBadge(String comparison, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(comparison, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showTrendAnalysis() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض تحليل اتجاهات المبيعات')),
    );
  }

  void _generateForecast() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء توقعات المبيعات')),
    );
  }

  void _analyzeReturns() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحليل أسباب المرتجعات')),
    );
  }

  void _optimizeDiscounts() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحسين استراتيجية الخصومات')),
    );
  }

  void _setBudgetTargets() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحديد أهداف الميزانية')),
    );
  }
}
