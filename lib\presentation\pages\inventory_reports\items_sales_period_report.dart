import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';

/// صفحة تقرير مبيعات الأصناف خلال فترة
/// يعرض تفاصيل مبيعات الأصناف خلال فترة زمنية محددة
class ItemsSalesPeriodReportPage extends StatefulWidget {
  const ItemsSalesPeriodReportPage({super.key});

  @override
  State<ItemsSalesPeriodReportPage> createState() => _ItemsSalesPeriodReportPageState();
}

class _ItemsSalesPeriodReportPageState extends State<ItemsSalesPeriodReportPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedCategory;
  String? _sortBy = 'sales_amount';
  String? _selectedBranch;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('مبيعات الأصناف خلال فترة'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: localizations.printReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: localizations.exportReport,
          ),
          IconButton(
            icon: const Icon(Icons.trending_up),
            onPressed: _showSalesTrends,
            tooltip: 'اتجاهات المبيعات',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilterSection(localizations),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummarySection(),
                  const SizedBox(height: 16),
                  _buildTopSellingItemsSection(),
                  const SizedBox(height: 16),
                  _buildItemsSalesTableSection(),
                  const SizedBox(height: 16),
                  _buildCategoryPerformanceSection(),
                  const SizedBox(height: 16),
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(AppLocalizations localizations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.teal[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.fromDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, true),
                  controller: TextEditingController(
                    text: _startDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: localizations.toDate,
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate(context, false),
                  controller: TextEditingController(
                    text: _endDate?.toString().split(' ')[0] ?? '',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: localizations.category,
                    border: const OutlineInputBorder(),
                  ),
                  value: _selectedCategory,
                  items: [
                    DropdownMenuItem(value: 'all', child: Text(localizations.allCategories)),
                    DropdownMenuItem(value: 'electronics', child: Text(localizations.electronics)),
                    DropdownMenuItem(value: 'clothing', child: Text(localizations.clothing)),
                    DropdownMenuItem(value: 'food', child: Text(localizations.food)),
                  ],
                  onChanged: (value) => setState(() => _selectedCategory = value),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'ترتيب حسب',
                    border: OutlineInputBorder(),
                  ),
                  value: _sortBy,
                  items: const [
                    DropdownMenuItem(value: 'sales_amount', child: Text('مبلغ المبيعات')),
                    DropdownMenuItem(value: 'quantity_sold', child: Text('الكمية المباعة')),
                    DropdownMenuItem(value: 'profit_margin', child: Text('هامش الربح')),
                    DropdownMenuItem(value: 'item_name', child: Text('اسم الصنف')),
                  ],
                  onChanged: (value) => setState(() => _sortBy = value),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.shopping_cart, color: Colors.teal, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'ملخص مبيعات الأصناف',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard('إجمالي الأصناف', '485', Colors.teal, Icons.inventory),
                _buildSummaryCard('إجمالي المبيعات', '2,850,000 ر.س', Colors.green, Icons.monetization_on),
                _buildSummaryCard('إجمالي الكمية', '12,485', Colors.blue, Icons.shopping_basket),
                _buildSummaryCard('متوسط السعر', '228 ر.س', Colors.orange, Icons.calculate),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopSellingItemsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.star, color: Colors.amber, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'الأصناف الأكثر مبيعاً',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._buildTopSellingItemsList(),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsSalesTableSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل مبيعات الأصناف',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('اسم الصنف')),
                  DataColumn(label: Text('الفئة')),
                  DataColumn(label: Text('الكمية المباعة')),
                  DataColumn(label: Text('مبلغ المبيعات')),
                  DataColumn(label: Text('متوسط السعر')),
                  DataColumn(label: Text('هامش الربح')),
                  DataColumn(label: Text('عدد المعاملات')),
                  DataColumn(label: Text('آخر بيع')),
                  DataColumn(label: Text('الأداء')),
                ],
                rows: _buildItemsSalesRows(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryPerformanceSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.category, color: Colors.purple, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'أداء الفئات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildCategoryCard('إلكترونيات', '1,285,000 ر.س', '45.1%', Colors.blue),
                _buildCategoryCard('ملابس', '685,000 ر.س', '24.0%', Colors.green),
                _buildCategoryCard('أغذية', '485,000 ر.س', '17.0%', Colors.orange),
                _buildCategoryCard('أخرى', '395,000 ر.س', '13.9%', Colors.purple),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _promoteTopItems,
                    icon: const Icon(Icons.campaign),
                    label: const Text('ترويج الأصناف الرائجة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _restockLowItems,
                    icon: const Icon(Icons.inventory_2),
                    label: const Text('تجديد المخزون'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _analyzeProfitability,
                    icon: const Icon(Icons.analytics),
                    label: const Text('تحليل الربحية'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _createSalesForecast,
                    icon: const Icon(Icons.trending_up),
                    label: const Text('توقع المبيعات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildTopSellingItemsList() {
    final topItems = [
      {'name': 'لابتوب ديل XPS 13', 'sales': '485,000 ر.س', 'quantity': '125', 'rank': '1'},
      {'name': 'هاتف آيفون 15', 'sales': '385,000 ر.س', 'quantity': '95', 'rank': '2'},
      {'name': 'طابعة HP LaserJet', 'sales': '285,000 ر.س', 'quantity': '85', 'rank': '3'},
      {'name': 'شاشة سامسونج 27"', 'sales': '185,000 ر.س', 'quantity': '65', 'rank': '4'},
    ];

    return topItems.map((item) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: _getRankColor(item['rank']!).withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: _getRankColor(item['rank']!).withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: _getRankColor(item['rank']!),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Center(
              child: Text(
                item['rank']!,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item['name']!,
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Text(
                  'مبيعات: ${item['sales']} • كمية: ${item['quantity']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          const Icon(Icons.star, color: Colors.amber, size: 20),
        ],
      ),
    )).toList();
  }

  List<DataRow> _buildItemsSalesRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('لابتوب ديل XPS 13')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('125')),
        const DataCell(Text('485,000 ر.س')),
        const DataCell(Text('3,880 ر.س')),
        const DataCell(Text('25%')),
        const DataCell(Text('85')),
        const DataCell(Text('2024-01-20')),
        DataCell(_buildPerformanceBadge('ممتاز', Colors.green)),
      ]),
      DataRow(cells: [
        const DataCell(Text('هاتف آيفون 15')),
        const DataCell(Text('إلكترونيات')),
        const DataCell(Text('95')),
        const DataCell(Text('385,000 ر.س')),
        const DataCell(Text('4,053 ر.س')),
        const DataCell(Text('30%')),
        const DataCell(Text('65')),
        const DataCell(Text('2024-01-19')),
        DataCell(_buildPerformanceBadge('ممتاز', Colors.green)),
      ]),
    ];
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(title, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryCard(String category, String sales, String percentage, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                sales,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
              const SizedBox(height: 4),
              Text(category, style: const TextStyle(fontSize: 12), textAlign: TextAlign.center),
              const SizedBox(height: 4),
              Text(
                percentage,
                style: TextStyle(fontSize: 10, color: color, fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPerformanceBadge(String performance, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(performance, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Color _getRankColor(String rank) {
    switch (rank) {
      case '1':
        return Colors.amber;
      case '2':
        return Colors.grey;
      case '3':
        return Colors.brown;
      case '4':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _showSalesTrends() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض اتجاهات المبيعات')),
    );
  }

  void _promoteTopItems() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ترويج الأصناف الأكثر مبيعاً')),
    );
  }

  void _restockLowItems() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تجديد مخزون الأصناف')),
    );
  }

  void _analyzeProfitability() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تحليل ربحية الأصناف')),
    );
  }

  void _createSalesForecast() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء توقعات المبيعات')),
    );
  }
}
